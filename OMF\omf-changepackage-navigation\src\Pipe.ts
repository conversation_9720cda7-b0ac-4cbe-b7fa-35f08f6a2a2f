import { CommonFeatures } from "bwtk";
import { Actions, Volt } from "omf-changepackage-components";
import { Action } from "redux-actions";
import { setSummaryTotals, Store } from "./store";
import { setTVHistoryProvider, setAppointmentVisited } from "./utils/History";

const { BasePipe } = CommonFeatures;

/**
 * rxjs pipe provider
 * this fascilitates the direct connection
 * between widgets through rxjs Observable
 * @export
 * @class Pipe
 * @extends {BasePipe}
 */
export class Pipe extends BasePipe {
  static Subscriptions(store: Store) {
    return {
      [Actions.historyGo.toString()]: ({ payload, meta }: any) => {
        store.dispatch(Actions.historyGo(payload));
      },
      [Actions.historyBack.toString()]: () => {
        store.dispatch(Actions.historyBack());
      },
      [Actions.historyForward.toString()]: () => {
        store.dispatch(Actions.historyForward());
      },
      [Actions.onContinue.toString()]: () => {
        store.dispatch(Actions.onContinue());
      },
      [Actions.acceptRestriction.toString()]: ({ payload }: any) => {
        // Handle restriction acceptance by navigating to appropriate page
        if (payload && payload.redirectURLKey) {
          store.dispatch(Actions.historyGo(payload.redirectURLKey));
        } else {
          // Check if we're coming from a summary modal (preview flow)
          const previewModal = document.querySelector('#PREVIEW_MODAL');
          const restrictionModal = document.querySelector('#NAVIGATION_RESTRICTION_MODAL');

          if ((previewModal && previewModal.classList.contains('show')) ||
              (restrictionModal && restrictionModal.classList.contains('show'))) {
            // Coming from summary/preview flow - close modals first
            store.dispatch(Actions.closeLightbox("PREVIEW_MODAL"));
            store.dispatch(Actions.closeLightbox("NAVIGATION_RESTRICTION_MODAL"));
            // Navigate directly to review page (same as other working flows)
            store.dispatch(Actions.historyGo("REVIEW"));
          } else {
            // Default: navigate to review page for most restriction cases
            store.dispatch(Actions.historyGo("REVIEW"));
          }
        }
      },
      [Actions.applicationExit.toString()]: () => {
        store.dispatch(Actions.applicationExit());
      },
      [Actions.applicationLogout.toString()]: () => {
        store.dispatch(Actions.applicationLogout());
      },
      [Actions.refreshTotals.toString()]: () => {
        store.dispatch(Actions.refreshTotals());
      },
      [Actions.setProductConfigurationTotal.toString()]: ({
        payload
      }: Action<Volt.IProductConfigurationTotal>) => {
        store.dispatch(setSummaryTotals(payload));
      },
      [Actions.closeLightbox.toString()]: ({ payload }: any) => {
        store.dispatch(Actions.closeLightbox(payload));
      },
      [Actions.setHistoryProvider.toString()]: ({ payload }: any) => {
        setTVHistoryProvider(payload);
      },
      [Actions.setAppointmentVisited.toString()]: () => {
        setAppointmentVisited();
      },
    };
  }
  /**
     *Creates a static instance of Pipe.
     * @param {*} arg
     * @memberof Pipe
     */
  static instance: Pipe;
  constructor(arg: any) {
    super(arg);
    Pipe.instance = this;
  }
}
