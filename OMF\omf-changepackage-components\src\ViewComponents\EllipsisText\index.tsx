import * as React from "react";

export interface IProps {
  text: string;
  maxLength: number;
  className?: string;
}

export interface IEllipsisText extends React.FC<IProps> {}

export const EllipsisText: IEllipsisText = (props: any) => {
  const { text, maxLength, className } = props;

  return <p className={`ellipsis-text ${className}`}>{text.length <= maxLength ? text : `${text.substring(0, maxLength)}...`}</p>;
};

EllipsisText.defaultProps = {
  className: ""
};

EllipsisText.displayName = "EllipsisText";
