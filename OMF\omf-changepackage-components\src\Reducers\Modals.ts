import { ReducersMapObject } from "redux";
import { handleActions } from "redux-actions";
import { CommonFeatures } from "bwtk";
import { Actions } from "../Actions";

const { actionsToComputedPropertyName } = CommonFeatures;

const {
  closeLightbox,
  setlightboxData
} = actionsToComputedPropertyName(Actions);
export function getWidgetLightboxes(): ReducersMapObject {
  return {
    lightboxData: handleActions<any | null>({
      [setlightboxData]: (state, { payload }) => payload,
      [closeLightbox]: () => null
    }, null) as any
  };
}
