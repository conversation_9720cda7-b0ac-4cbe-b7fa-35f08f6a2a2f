import { LocalizationState } from "bwtk";
import { merge, of , ObservableInput } from "rxjs";
import { Utils } from "../Utils";

import { Actions } from "../Actions";
import { Volt } from "./VOLT";

export * from "./VOLT";

export enum EModals {
  PREVIEWMODAL = "PREVIEW_MODAL"
}

export enum EWidgetStatus {
  INIT,
  RENDERED,
  UPDATING,
  ERROR,
  OUTAGERROR
}

export enum EWidgetRoute {
  INTERNET = "/Internet",
  TV = "/TV",
  TV_Packages = "/Packages",
  TV_MoviesSeries = "/Movies",
  TV_Addons = "/Addons",
  TV_Alacarte = "/Alacarte",
  TV_International = "/International",
  TV_InternationalCombos = "/International/Combos",
  TV_InternationalAlacarte = "/International/Alacarte",
  TV_Browse = "/Browse",
  TV_Search = "/Search",
  APPOINTMENT = "/Appointment",
  REVIEW = "/Review",
  CONFIRMATION = "/Confirmation"
}

export enum EReviewMode {
  Summary = "summary",
  Review = "review",
  Confirmation = "confirmation"
}

export enum EWidgetName {
  NAVIGATION = "omf-changepackage-navigation",
  INTERNET = "omf-changepackage-internet",
  TV = "omf-changepackage-tv",
  APPOINTMENT = "omf-changepackage-appointment",
  // Preview, Review and Confirmation are done using the same widget!
  PREVIEW = "omf-changepackage-review",
  REVIEW = "omf-changepackage-review",
  CONFIRMATION = "omf-changepackage-review"
}

export enum EFlowType {
  INTERNET = "Internet",
  TV = "TV",
  ADDTV = "AddTV",
  BUNDLE = "Bundle"
}

export interface AjaxResponse<T> {
  headers: {
    [key: string]: any;
  };
  redirected: boolean;
  status: number;
  statusText: string;
  type: string;
  url: string;
  dataType: string;
  data: T;
}


export interface ILightboxPayload {
  lightboxId: string;
  data?: any;
}

export namespace Models {

  /**
   * Bell brands enumerator
   * @export
   * @interface IBaseWidgetAPI
   */
  export enum EBrand {
    BELL = "B",
    VIRGIN = "V",
    LUCKY = "L"
  }

  /**
   * Base set of params passed into
   * the widget via config system
   * @export
   * @interface IBaseEnvironmentVariables
   */
  export interface IBaseEnvironmentVariables {
    province: string;
    brand: EBrand;
    language: "en" | "en-ca" | "fr" | "fr-ca";
    transactionIdentifier: string;
    useMockData: boolean;
  }

  /**
   * Basic widget config property set
   * @export
   * @interface IBaseConfig
   */
  export interface IBaseConfig {
    api: IBaseWidgetAPI;
    headers: { [key: string]: string };
    mockdata?: { [key: string]: { [key: string]: any } };
    environmentVariables: IBaseEnvironmentVariables;
  }

  /**
   * props used in every common
   * component implementstion
   * @export
   * @interface IBaseComponentProps
   */
  export interface IBaseComponentProps {
    id?: string;
    className?: string;
    style?: { [key: string]: string };
  }

  /**
  * props passed to hte widget from it's parent
  * or on DomRender
  * @export
  * @interface IBaseWidgetProps
  */
  export interface IBaseWidgetProps {
  }

  export interface IBaseWidgetAPI {
    base: string;
  }

  /**
   *
   *
   * @export
   * @interface IBaseStoreState
   */
  export interface IBaseStoreState {
    localization: LocalizationState;
    widgetStatus: EWidgetStatus;
    lightboxData?: any;
    restriction?: Volt.IMessage;
    error: IErrorHandlerProps;
  }

  /**
   *
   *
   * @export
   * @interface IBaseAppProps
   */
  export interface IBaseAppProps {
    localization: LocalizationState;
    widgetStatus: EWidgetStatus;
    errorHandlerProps: IErrorHandlerProps;
  }

  /**
   * immutable props set onto widget
   * context and accessible from
   * any component
   * @export
   * @interface IWidgetContext
   */
  export interface IWidgetContext<T> {
    config: T;
  }

  /**
   * immutable props set onto widget
   * context and accessible from
   * any component
   * @export
   * @interface IWidgetContext
   */
  export interface IWidgetContext<T> {
    config: T;
  }

  /**
   * report execuion errors
   * @export
   * @interface IErrorHandlerProps
   */
  export interface IErrorHandlerProps extends Error {
    type: "API" | "widget" | "logic";
    response?: Response;
    componentStack?: any;
    debug: boolean;
  }
  export class ErrorHandler extends Error implements IErrorHandlerProps {
    type: "API" | "widget" | "logic";
    response?: Response;
    componentStack?: any;
    debug: boolean = false;
    constructor(message: string | Error, details?: any, debug: boolean = false) {
      super(typeof message === "object" ? message.message : message);
      this.debug = debug || Boolean(Utils.getCookie("debugwidget"));
      if (typeof message === "object") {
        this.stack = (message as Error).stack;
      }
      if (typeof details === "object") {
        switch (true) {
          case Boolean(details["url"]):
            this.type = "API";
            this.response = details;
            break;
          case Boolean(details["componentStack"]):
            this.type = "widget";
            this.componentStack = details.componentStack;
            break;
          default:
            // No specific handling needed for other detail types
            break;
        }
      } else {
        this.type = "logic";
      }
    }
  }

  export function ErrorHandlerObservable(action: any) {
    return function (response: { error: Error }, source: ObservableInput<any>) {
      const err: Error = response.error || response;
      return merge(
        of(Actions.errorOccured(new ErrorHandler(action.toString(), err))),
        source
      );
    };
  }

  /* Regular Expressions */
  export const noSpecialCharRegex = RegExp(
    /^[a-zA-Z0-9]+$/i
  );
  export const emailRegex = RegExp(
    /^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i
  );
  export const phoneRegex = RegExp(
    /^[0-9]\d{2}-\d{3}-\d{4}$/i
  );
  export const hashCommaRegex = RegExp(
    /[\,\#]/i
  );
  export const onlyNumbersRegex = RegExp(
    /^[0-9]+$/i
  );
}
