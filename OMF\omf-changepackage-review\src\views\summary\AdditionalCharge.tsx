import * as React from "react";
import { FormattedMessage, FormattedNumber } from "react-intl";
import { Volt } from "omf-changepackage-components";

interface IComponentProps {
  additionalCharge: Volt.IPriceDetail;
}

const AdditionalCharge: React.FunctionComponent<IComponentProps> = React.memo(({ additionalCharge }) => (
  additionalCharge.price ?
    <React.Fragment>
      <div className="pad-30-left pad-40-right padding-25-xs rate-plan-total txtBlack">
        <div className="flexBlock flex-justify-space-between block-xs">
          <div>
            <span className="block txtSize14"><FormattedMessage id="Additional charges" /></span>
          </div>
          <div>
            <span className="txtCurrency item-price bellSlimSemibold txtBlack txtSize14 txtBold no-margin-bottom">
              <FormattedNumber value={additionalCharge.price} format="CAD" />{
                additionalCharge.priceType === "Recurring" && <FormattedMessage id="PER_MO" />
              }
            </span>
          </div>
        </div>
      </div>
      <span className="spacer25 d-none d-md-block d-lg-block d-xl-block" aria-hidden="true"></span>
      <span className="spacer15 d-block d-sm-none" aria-hidden="true"></span>
    </React.Fragment>
    : null
));

export default AdditionalCharge;
