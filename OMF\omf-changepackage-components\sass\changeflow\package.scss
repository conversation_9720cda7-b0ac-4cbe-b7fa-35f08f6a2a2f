@import "mixins";

/* Bell package box module
 * 
 * Base box for a package
 * Movie, or series pack
*/
.bell-tv-package {
  margin-bottom: 15px;
  border: 2px solid #000;
  background-color: #fff;
  border-radius: 3px;
  label, input {
    cursor: pointer;
  }
  &.disabled {
    background-color: #F4F4F4;
    border-color: #F4F4F4;
    label, input {
      opacity: 1;
      cursor: auto;
    }
  }
  &.selected {
    border: 2px solid $virginBlack;
    .bell-tv-package-header {
      background-color: #00549a;
      color: #fff;
    }
  }
  hr {
    border-color: #D4D4D4;
    margin: 15px 0;
  }
  .bell-tv-package-body {
    padding: 15px;
    .bell-tv-package-left {
      padding-left: 30px;
      margin-left: 15px;
      margin-top: 5px;
      margin-bottom: 15px;
      @media #{$media-tablet} {
        padding-left: 35px;
      }
      @media #{$media-mobile} {
        padding-left: unset;
      }
    }
  }
  .bell-tv-package-footer {
    &:focus {
      outline: none;
    }
    @include expandable();
  }
  .bell-tv-package-icons img {
    width: auto;
    height: auto;
    padding-right: 40px;
    max-width: 100%;
    padding-bottom: 20px;
    align-self: center;
    @media #{$media-mobile}{
        margin-right: 5px;
    }
  }
}

/* Virgin Internet Change Home Internet Package */
// .virgin-internet-box {
//   .package-desc {
//     @media #{$media-tablet} {
//       margin-left: -40px;
//     }
//   }
// }

.currentPackage {
  li {
    margin-bottom: 5px;
    @media #{$media-mobile} {
      margin-bottom: 15px;
      &:last-child {
        margin-bottom: 0px;
      }
    }
  }
}