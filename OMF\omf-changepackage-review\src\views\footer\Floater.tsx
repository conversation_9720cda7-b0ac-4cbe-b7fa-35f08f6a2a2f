import * as React from "react";
import { FormattedMessage } from "react-intl";

const refId = "_overlayFloaterRefID";
let scrollTop: number = 0;

const Floater: React.FunctionComponent = () => {
  const [visible, setState] = React.useState(true);
  React.useEffect(
    () => {
      const $el = document.getElementById(refId) as HTMLDivElement;
      scrollTop = $el.getBoundingClientRect().top || 0;
      const onScroll = (e: Event) => {
        if (visible && window.scrollY + window.innerHeight > scrollTop) setState(false);
        if (!visible && window.scrollY + window.innerHeight < scrollTop) setState(true);
      };
      window.addEventListener("scroll", onScroll);
      return () => window.removeEventListener("scroll", onScroll);
    }
  );

  return <>
    <div id={refId} />
    <div className="block bgBlack txtWhite txtCenter pad-10-top pad-10-bottom" style={{
      position: "fixed",
      bottom: "0", left: "0",
      width: "100%", zIndex: 999,
      pointerEvents: "none",
      opacity: visible ? "1" : "0"
    }}>
      <i className="virgin-icon icon-Down_arrow1 pad-15-right" style={{ fontSize: "50%" }} />
      <FormattedMessage id="SCROLL_TO_THE_BOTTOM">
        {(scrollTo) => <span aria-hidden={`${visible ? false : true}`}>{scrollTo}</span>}
      </FormattedMessage>
      <i className="virgin-icon icon-Down_arrow1 pad-15-left" style={{ fontSize: "50%" }} />
    </div>
  </>;
};

export default Floater;
