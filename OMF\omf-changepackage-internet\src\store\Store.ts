import { combineReducers } from "redux";
import { Action, handleActions } from "redux-actions";
import { combineEpics } from "redux-observable";
import { Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics } from "omf-changepackage-components";

import { Store as BwtkStore, Injectable, CommonFeatures } from "bwtk";

import * as actions from "./Actions";

import { IStoreState, IAccountDetails, IPackage } from "../models";
import { Epics } from "./Epics";
import { Localization } from "../Localization";
import { Client } from "../Client";

const { BaseStore, actionsToComputedPropertyName } = CommonFeatures;
const {
  setAccountDetails,
  setInternetCatalog,
  updateInternetCatalog
} = actionsToComputedPropertyName(actions);

@Injectable
export class Store extends BaseStore<IStoreState> {
  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {
    super(store);
  }

  get reducer() {
    return combineReducers({
      // =========== Widget lifecycle methods =============
      ...Reducers.WidgetBaseLifecycle(this.localization) as any,
      ...Reducers.WidgetLightboxes() as any,
      ...Reducers.WidgetRestrictions() as any,
      // =========== Widget data ===============
      accountDetails: handleActions<Array<IAccountDetails>>({
        [setAccountDetails]: (state, { payload }: Action<Array<IAccountDetails>>) => payload || state,
      }, [{}] as Array<IAccountDetails>),
      catalog: handleActions<Array<IPackage>>({
        [setInternetCatalog]: (state, { payload }: Action<Array<IPackage>>) => payload || state,
        [updateInternetCatalog]: (state, { payload }: Action<Array<IPackage>>) => payload || state,
      }, []),
    }) as any;
  }

  /**
   * Middlewares are collected bottom-to-top
   * so, the bottom-most epic will receive the
   * action first, while the top-most -- last
   * @readonly
   * @memberof Store
   */
  get middlewares(): any {
    return combineEpics(this.epics.omniture.combineEpics(), this.epics.userAccountEpics.combineEpics(),
      this.epics.catalogEpics.combineEpics(), this.epics.combineEpics(), new ModalEpics().combineEpics(),
      new RestricitonsEpics(this.client, "INTERNET_RESTRICTION_MODAL").combineEpics(), new LifecycleEpics().combineEpics());
  }
}
