import { Actions, Components, EModals, EReviewMode, EWidgetRoute, ValueOf, Volt, WidgetContext, Omniture, Utils, EFlowType } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage, FormattedDate } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import { IOrderConfirmation, IStoreState } from "../../../models";
import { isModeMatching } from "../../../utils";
import { Spacer } from "../Spacer";
import { Flag } from "../ChargeItem";

interface IComponentProps {
  productOfferings: Array<Volt.IProductOffering>;
}
// temporary

const NewInternet: React.FunctionComponent<IComponentProps> = React.memo(({ productOfferings }) => {
  const { mode }: any = React.useContext(WidgetContext),
    confirmation: IOrderConfirmation = useSelector((state: IStoreState) => state.confirmation),
    dispatch = useDispatch(),
    isReview = isModeMatching(mode, EReviewMode.Review, EReviewMode.Summary);
  const flowType = React.useMemo(() => Utils.getFlowType(), []);

  return (
    <React.Fragment>
      {
        productOfferings
          .map((productOffering: Volt.IProductOffering, idx: number) => (
            <React.Fragment>
              <div className="flexCol" key={productOffering.id}>
                <div className="flexRow flex-justify-space-between">
                  <p className="txtBlack txtSize18 no-margin floatL" id={`${productOffering.displayGroupKey}_${idx}`}>
                    <FormattedMessage id={`${productOffering.displayGroupKey}`} />
                    <Components.Visible when={
                      isReview &&
                      flowType !== EFlowType.TV &&
                      flowType !== EFlowType.ADDTV &&
                      !ValueOf<boolean>(confirmation, "confirmationNumber", false) &&
                      ValueOf<Volt.EDIsplayGroupKey>(productOffering, "displayGroupKey") !== Volt.EDIsplayGroupKey.PROMOTION
                    }>
                      <span className="flexRow flexCenter floatR accss-focus-outline-override">
                        <button id={`EDIT_NEWPLAN_${productOffering.id}`}
                          role="link"
                          aria-describedby={`${productOffering.displayGroupKey}_${idx}`}
                          className="txtBlue txtSize14 margin-20-left noBorder bgTransparent pointer accss-text-blue-on-bg-white"
                          onClick={
                            () => {
                              Omniture.useOmniture().trackAction({
                                id: "editClick",
                                s_oAPT: {
                                  actionId: 647
                                },
                                s_oBTN: "Edit " + productOffering.displayGroupKey
                              });
                              if (isModeMatching(mode, EReviewMode.Summary)) {
                                dispatch(Actions.closeLightbox(EModals.PREVIEWMODAL) as any);
                              }
                              dispatch(Actions.broadcastUpdate(Actions.historyGo(EWidgetRoute.INTERNET)) as any);
                            }
                          }
                        >
                          <FormattedMessage id="Edit" /> <span className="virgin-icon icon-edit txtBlue txtSize14 margin-5-left accss-text-blue-on-bg-white" aria-hidden="true"></span>
                        </button>
                      </span>
                    </Components.Visible>
                  </p>
                </div>
                <div className="flexRow flex-justify-space-between block-xs">
                  <p className="noMargin">
                    {productOffering.name || productOffering.id}
                    <Components.Visible when={
                      ValueOf(productOffering, "state") === Volt.EOfferingState.Add ||
                      ValueOf(productOffering, "state") === Volt.EOfferingState.Added ||
                      ValueOf(productOffering, "state") === Volt.EOfferingState.Create ||
                      ValueOf(productOffering, "state") === Volt.EOfferingState.NewlySelected
                    }>
                      <Flag message={"new"} />
                    </Components.Visible>
                    <Components.Visible when={
                      ValueOf(productOffering, "displayGroupKey") === Volt.EDIsplayGroupKey.PROMOTION &&
                      ValueOf(productOffering, "promotionDetails.expiryDate", false)
                    }>
                      <span className="d-sm-block pad-5-left-xs">
                        <FormattedDate value={ValueOf(productOffering, "promotionDetails.expiryDate", "")} format="yMMMMd" timeZone="UTC">
                          {
                            (expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
                          }
                        </FormattedDate>
                      </span>
                    </Components.Visible>
                  </p>
                  <p className="noMargin txtBold txtSize14">
                    <Components.BellCurrency value={ValueOf(productOffering, "regularPrice.price") || 0} />{
                      productOffering.regularPrice && productOffering.regularPrice.priceType === "Recurring" && <FormattedMessage id="PER_MO" />
                    }
                  </p>
                </div>
                {
                  Boolean(ValueOf(productOffering, "promotionDetails.description")) &&
                  <div className="flexRow flex-justify-space-between block-xs">
                    <p className="noMargin">
                      <span className="spacer15 d-none d-sm-block" aria-hidden="true" />
                      {ValueOf(productOffering, "promotionDetails.description")}
                      <Components.Visible when={
                        ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.Add ||
                        ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.Added ||
                        ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.Create ||
                        ValueOf(productOffering, "promotionDetails.state") === Volt.EOfferingState.NewlySelected
                      }>
                        <Flag message={"new"} />
                      </Components.Visible>
                      <Components.Visible when={ValueOf(productOffering, "promotionDetails.expiryDate", false)}>
                        <br />
                        <span className="d-sm-block pad-5-left-xs">
                          <FormattedDate value={ValueOf(productOffering, "promotionDetails.expiryDate", "")} format="yMMMMd" timeZone="UTC">
                            {
                              (expiryDate) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />
                            }
                          </FormattedDate>
                        </span>
                      </Components.Visible>
                      <Components.Visible when={ValueOf(productOffering, "promotionDetails.discountDuration", false)}>
                        <span className="d-sm-block pad-5-left-xs">
                          <FormattedMessage id="PromotionValid" values={{ price: Math.abs(ValueOf(productOffering, "promotionDetails.discountPrice.price", 0)), discountDuration: ValueOf(productOffering, "promotionDetails.discountDuration", "") }} />
                        </span>
                      </Components.Visible>
                    </p>
                    <p className="noMargin txtBold txtSize14">
                      <span className="spacer15 d-none d-sm-block" aria-hidden="true" />
                      <Components.BellCurrency value={ValueOf(productOffering, "promotionDetails.discountPrice.price") || 0} />{
                        ValueOf(productOffering, "promotionDetails.discountPrice.priceType", "") === "Recurring" && <FormattedMessage id="PER_MO" />
                      }
                    </p>
                  </div>
                }
              </div>
              <Spacer />
            </React.Fragment>
          ))}
    </React.Fragment >
  );
});

export default NewInternet;
