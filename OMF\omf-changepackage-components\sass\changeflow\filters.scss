@import "mixins";

/* Bell tv filters module
 * 
 * rows of filters for
 * channel grids
 *
*/

.section-bell-tv-package {
    .bell-tv-package-filters-row {
        padding: 15px 7.5px;
    }
}

.bell-tv-package-filters-row {
    padding: 15px 20px;
    // overflow: auto;
    // -ms-overflow-style: none;
    @media #{$media-mobile} {
        flex-direction: column;
        align-items: normal;
    }
    select {
        line-height: unset;
    }
    ul.bell-tv-package-filters-nav {
        display: table;
        li {
            display: table-cell;
            padding: 0 7.5px;
            white-space: nowrap; // line-height: 1.75;
            &:last-child {
                padding-right: 0;
            }
            &:first-child {
                padding-left: 0;
            }
            &.active label {
                color: $virginOrange;
                border-bottom: 1px solid #e10a0a; // padding-bottom:4px;
                font-weight: bold;
            }
            label {
                margin-top: 5px;
            }
        }
    }
    .tray-icon-sort {
        font-size: 5px;
        &:before {
            top: 1px;
        }
    }
    .icon-link:hover>span,
    .icon-link:focus>span {
        text-decoration: none;
    }
    .bell-tv-package-filters-no-result {
        display: flex;
        padding: 30px 20px 40px;
    }
    .underline {
        border-bottom: 1px solid #003778;
        padding-bottom: 2px;
        text-decoration: none;
    }
    .filter_arrow {
        position: relative;
        font-size: 14px;
        font-weight: normal;
        padding: 0 15px;
        &:after {
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 5px 5px 0 5px;
            border-color: #257FA3 transparent transparent transparent;
            position: absolute;
            top: 42%;
            right: 0px;
        }
    }
}

.advance-filter-button {
    padding: 15px;
    @media #{$media-desktop-tab} {
        padding-left: 0;
    }
    .icon-link {
        display: block;
        margin-top:1px;
    }
}

.bell-tv-package-filters-tray {
    @include expandable();
    background: -moz-linear-gradient(top, rgba(0,0,0,0.1) 0, transparent 23px);
    background: -webkit-linear-gradient(top, rgba(0,0,0,0.1) 0,transparent 23px);
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0,transparent 23px);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a6000000', endColorstr='#00000000',GradientType=0 );
    .bell-tv-package-filters-tray-actions {
        margin-top: -40px;
        float: right;
        @media #{$media-mobile} {
            margin-top: 0;
            float: unset;
        }
    }
    .bell-tv-package-filters-advance {
        padding: 23px 20px;
        overflow: auto;
    }
}

.tvcsfilters-filtersContainer {
    margin-bottom: 15px;
    border: 2px solid #000;
    background-color: #f4f4f4;
    .form-control-select-box {
        .tvcsfilters-select-dropdown-filter {
            height: 40px;
            padding: 8px 10px;
            line-height: 1.4; // Fix for MBM 13229
        }
        &::after {
            padding: 6px 10px 5px 0;
            height: 36px;
        }
    }
}

.compareBox {
    // Please do not set custom sizes to UI elements
    // according to the WCIG standard the smallest
    // clickable element MUST be 48x48px
    
    .form-control-select {
        line-height: 1.2em;
    }
    // &:after {
    //     padding: 6px 12px 6px 0;
    //     height: 35px;
    // }
    // .btn {
    //     padding: 10px 25px;
    // }
}

.border-filter {
    margin-left: -20px;
    width: calc(100% + 40px) !important;
}