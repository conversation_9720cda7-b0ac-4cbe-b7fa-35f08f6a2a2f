const { widgetTemplate } = require("webpack-common");
const package = require('./package.json');
const path = require('path');

module.exports = (env) => widgetTemplate(
  package, 
  {
    widget: path.resolve(__dirname, "src"),
    node_modules: path.resolve(__dirname, "node_modules"),
    dist: path.resolve(__dirname, "dist")
  },  // PATHS
  {}, // override
  {}, // buildOnlyOverride
  {} // debugOnlyOverride
);