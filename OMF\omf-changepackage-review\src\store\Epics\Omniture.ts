import { Injectable, ParamsProvider } from "bwtk";
import { Actions, EWidgetStatus, Omniture, Utils, EFlowType, EWidgetRoute, EReviewMode, ValueOf } from "omf-changepackage-components";
import { combineEpics, Epic, ofType } from "redux-observable";
import { of , mergeMap, catchError } from "rxjs";

import { IStoreState, IWidgetProps } from "../../models";
import { collectOmnitureProducts, isModeMatching } from "../../utils";
// import { Localization } from "../../Localization";
import { submitOrder } from "../Actions";

const {
  omniPageLoaded,
  omniModalOpen
} = Actions;

@Injectable
export class OmnitureEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private params: ParamsProvider<IWidgetProps, any>) { }

  combineEpics() {
    return combineEpics(
      this.pageLoadedEpic,
      this.summaryLightboxEpic,
      this.submitEpic
    );
  }

  private get pageLoadedEpic(): OmnitureEpic {
    return (action$, store) =>
      action$.pipe(
        ofType(omniPageLoaded.toString()),
        mergeMap(() => {
          const omniture = Omniture.useOmniture();
          const state = store.value;
          const { summary, confirmation } = state;
          const currentFlowType = Utils.getFlowType();
          let s_oSS2, s_oSS3, actionId, applicationState, actionresult,
            page = isModeMatching(this.params.props.mode, EReviewMode.Review) ?
              "Review" : "Confirmation";
          switch (currentFlowType) {
            case EFlowType.INTERNET:
              // flow = "Internet ";
              s_oSS2 = "Internet";
              actionId = 523;
              applicationState = 1;
              actionresult = 2;
              break;
            case EFlowType.TV:
              // flow = "TV ";
              s_oSS2 = "TV";
              actionId = 394;
              applicationState = 1;
              actionresult = 2;
              break;
            case EFlowType.ADDTV:
              s_oSS2 = "TV";
              s_oSS3 = "Add Tv";
              actionId = 507;
              actionresult = 2;
              applicationState = 1;
              break;
            case EFlowType.BUNDLE:
              s_oSS2 = "Bundle";
              s_oSS3 = "Add Tv";
              actionId = 508;
              actionresult = 2;
              applicationState = 1;
              break;
          }
          switch (page) {
            case "Review":
              omniture.trackPage({
                id: "ReviewPage",
                s_oSS1: "~",
                s_oSS2,
                s_oSS3: s_oSS3 ? s_oSS3 : "Change package",
                s_oPGN: page,
                s_oPLE: {
                  type: Omniture.EMessageType.Warning,
                  content: {
                    ref: `MSG_PARAGRAPH_${currentFlowType}`
                  }
                },
                s_oPRD: collectOmnitureProducts(summary)
              });
              break;
            case "Confirmation":
              omniture.trackPage({
                id: "ConfirmationPage",
                s_oSS1: "~",
                s_oSS2,
                s_oSS3: s_oSS3 ? s_oSS3 : "Change package",
                s_oPGN: page,
                s_oPLE: [{
                  type: Omniture.EMessageType.Confirmation,
                  content: {
                    ref: `ORDER_SUBMITTED`
                  }
                }, {
                  type: Omniture.EMessageType.Warning,
                  content: {
                    ref: `IMPORTANT_MESSAGE_${currentFlowType}`
                  }
                }],
                s_oPRD: collectOmnitureProducts(summary),
                s_oPID: ValueOf<string>(confirmation, "confirmationNumber"),
                s_oAPT: {
                  actionId,
                  applicationState,
                  actionresult
                }
              });
              break;
          }
          return of();
        }),
        catchError((error: Response) => of())
      );
  }

  private get summaryLightboxEpic(): OmnitureEpic {
    return (action$, store) =>
      action$.pipe(
        ofType(omniModalOpen.toString()),
        mergeMap(() => {
          const omniture = Omniture.useOmniture();
          let flow = "", action = 104;
          switch (Utils.getFlowType()) {
            case EFlowType.INTERNET:
              flow = "Internet ";
              break;
            case EFlowType.ADDTV:
            case EFlowType.TV:
              flow = "TV ";
              break;
            case EFlowType.BUNDLE:
              flow = "Bundle ";
              break;
          }
          switch (Utils.getPageRoute()) {
            case EWidgetRoute.INTERNET:
            case EWidgetRoute.TV:
              action = 104;
          }
          omniture.trackAction({
            id: "summaryLigthbox",
            s_oAPT: {
              actionId: action
            },
            s_oPRM: flow + "Preview order"
          });
          return of();
        }),
        catchError((error: Response) => of())
      );
  }

  private get submitEpic(): OmnitureEpic {
    return (action$, store) =>
      action$.pipe(
        ofType(submitOrder.toString()),
        mergeMap(() => {
          const omniture = Omniture.useOmniture();
          const state = store.value;
          const { summary } = state;
          omniture.trackAction({
            id: "submit",
            s_oAPT: {
              actionId: 647
            },
            s_oBTN: {
              ref: "ACCEPT_TERMS_AND_SUBMIT"
            },
            s_oPRD: collectOmnitureProducts(summary)
          });
          return of();
        }),
        catchError((error: Response) => of())
      );
  }
}

type OmnitureEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, IStoreState>;
