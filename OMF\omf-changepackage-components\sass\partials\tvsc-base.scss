/*
 * Global styles included into every other styling file of the MIRD project
 * Contains the page style, overal global changes to buttons, tabs, and tables
 * not used on it's own.
*/

@import "../includes/mixins";

/*Print*/

.visible-print {
  display: none !important;
}
sup {
  vertical-align: middle;
}

@media #{$media-print} {
  body,
  #maincontent {
    background: none !important;
    * {
      box-shadow: none !important;
    }
  }
  .visible-print {
    display: block !important;
  }
  .hidden-print {
    display: none !important;
  }
  a[href]:after {
    content: none !important;
  }
  main {
    overflow: visible !important;
  }
}

// To fix color contrast issue
.btn-default:hover,
  .btn-default:focus {
    background-color: #eee;
    border: 2px solid #ccc;  
  }


/*Accessibility*/

a,
button {
  &:hover,
  &:active,
  &:not(:focus) {
    outline: none;
  }
}

.aria-silence {
  speak: none;
}

.aria-spell-all {
  speak: spell-out;
}

.aria-spell-digits {
  speak-numeral: digits;
}

.aria-pause {
  &:after {
    content: ".";
    position: absolute;
    opacity: 0.01;
  }
}

.aria-visible {
  position: absolute;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
  -webkit-clip-path: inset(0px 0px 99.9% 99.9%);
  clip-path: inset(0px 0px 99.9% 99.9%);
  overflow: hidden;
  height: 1px;
  width: 1px;
  padding: 0;
  border: 0;
  top: 50%;
}

.noStyleBtn {
  background: transparent;
  border: none;
  width: 100%;
}

/*General*/

main {
  overflow-x: initial;
}

.panel-body.block {
  display: block !important;
}

.noPaddingImp {
  padding: 0 !important;
}

.border-radius-5 {
  border-radius: 5px;
}
.border-radius-3 {
  border-radius: 3px;
}
.border-radius-bottom-3 {
  border-radius: 0 0 3px 3px;
}
.border-radius-top-3 {
  border-radius: 3px 3px 0 0;
}

/*Icons*/

.icon-reversed {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
  display: inline-block;
  margin-left: -3px;
  &:before {
    top: 0;
  }
}

.iconSizeHuge {
  font-size: 74px;
  &:after {
    top: 0;
  }
}

.more-link:after {
  top: 1px;
}

// UI elements

/*Panels*/

.panel-body {
  padding: 17px 30px;
  border: 1px solid #d4d4d4;
  &.bgBlue {
    border: 1px solid #00549a;
  }
  &:first-child:not(:last-child) {
    border-bottom: none;
  }
  @media #{$media-tablet} {

  }
  @media #{$media-mobile} {
    padding: 20px 15px;
    border-left: none;
    border-right: none;
  }
}

/*UI Elements*/

.graphical_ctrl.graphical_ctrl_ext {
  .ctrl_element {
    color: #fff;
    .chk-check,
    .chk-asterisk,
    .chk-radio,
    .chk-axe {
      display: none;
    }
    &:after {
      content: none;
    }
  }
  input:checked {
    ~ .ctrl_element {
      .chk-check {
        display: block;
      }
      .chk-asterisk,
      .chk-axe {
        display: none;
      }
    }
  }
  &.graphical_ctrl_partly {
    .ctrl_element {
      background: #003778;
      border-color: #003778;
      .chk-asterisk {
        display: block;
      }
    }
  }
  &.graphical_ctrl_removed {
    .ctrl_element {
      background: #bd2025;
      border-color: #bd2025;
      .chk-axe {
        display: block;
      }
    }
  }
}

// .btn-primary,
// .btn-primary:active,
// .btn-primary:focus {
//     border-color: #E10A0A;
//     color: #fff;
//     background-color: transparent;
// }

.btn-group {
  .btn {
    margin-right: 15px;
    &:last-of-type {
      margin-right: 0;
    }
    @media #{$media-mobile} {
      margin-right: 0;
      &:not(:last-child) {
        margin-bottom: 10px;
      }
    }
  }
}

.btn-icon {
  padding-left: 24px;
  > .icon {
    top: 8px;
    left: 0;
  }
}

.form-group {
  .form-group-more {
    margin-top: 5px;
  }
  &.form-error {
    .form-group-more {
      margin-top: 0;
    }
  }
}

/*Disabled states*/

.disabled {
  opacity: 1;
  > a {
    opacity: 0.5;
    cursor: default;
    &:hover,
    &:focus {
      text-decoration: none;
    }
  }
  .icon-link:hover > span {
    text-decoration: none;
  }
  .btn-default,
  .btn-default:hover,
  .btn-default:focus {
    background-color: #babec2;
    border: 2px solid #babec2;
    cursor: default;
  }
  .btn-default-blue,
  .btn-default-blue:hover,
  .btn-default-blue:focus {
    background-color: #babec2;
    border: 2px solid #babec2;
    cursor: default;
  }
  .btn-default-white,
  .btn-default-white:active,
  .btn-default-white:focus {
    background-color: #6698c2;
    border-color: #6698c2;
    cursor: default;
  }
  .btn-primary,
  .btn-primary:hover,
  .btn-primary:focus {
    color: #babec2;
    background-color: transparent;
    border: 2px solid #babec2;
    cursor: default;
  }
  .btn-primary-white,
  .btn-primary-white:active,
  .btn-primary-white:focus {
    color: #6698c2;
    border: 2px solid #6698c2;
    cursor: default;
  }
  > img {
    opacity: 0.5;
  }
}

/* Scrollable content */

// Removed because available in bell.css now with class scrollAdjust
// .scrollable-content {
//     overflow-y: auto; // padding-right: 20px;
//     position: relative;
//     &::-webkit-scrollbar {
//         width: 8px;
//         background: transparent;
//     }
//     &::-webkit-scrollbar-track {
//         -webkit-box-shadow: none;
//         border-radius: 4px;
//         background-color: #babdc2;
//         margin-bottom: 30px;
//         margin-top:30px;
//     }
//     &::-webkit-scrollbar-thumb {
//         border-radius: 5px;
//         -webkit-box-shadow: none;
//         background-color: #063674;
//     }
// }

/*Decorators*/

// Insures we do not underline icons in a link with icons in it
//  <a href="#"><span>Link text</span><i class="icon"></i></a>
.icon-link {
  text-decoration: none;
  > * {
    vertical-align: middle;
  }
  &:hover,
  &:focus {
    text-decoration: none;
    > span {
      text-decoration: underline;
    }
  }
}

.avoid-clicks {
  pointer-events: none;
}

.centerIn {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
}

.noShaddow {
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
}

legend.disableLegendStyle {
  width: auto;
  font-size: inherit;
  line-height: inherit;
  border: none;
  margin: initial;
}

/*flex model*/

.flexBlock {
  display: flex;
}

.flexInline {
  display: inline-flex;
}

.flexBlock.hide, .flexRow.hide, .flexCol.hide {
  display: none;
}

.flexRow {
  display: flex;
  flex-direction: row;
}

.flexCol {
  display: flex;
  flex-direction: column;
}

.flexWrap {
  flex-wrap: wrap;
}
.flexWrap-xs {
  @media #{$media-mobile} {
    flex-wrap: wrap;
  }
}

.fullWidth, .fill {
  width: 100%;
}

.flexEqualize > div > * {
  height: 100%;
}

.flexStatic {
  flex-grow: 0;
  flex-shrink: 0;
}

.flexShrink {
  flex-shrink: 1;
}

.flexGrow {
  flex-grow: 1;
}

.flex1 {
  flex: 1;
}

.flexJustify {
  justify-content: center;
}

.flexJustifySpace {
  justify-content: space-between;
}

.flexJustifyEnd {
  justify-content: flex-end;
}

.flexCenter {
  align-items: center;
}

.flexEnd {
  align-items: flex-end;
}

.flexStretch {
  align-items: stretch;
}

.alginSelfStart {
  align-self: flex-start;
}

/* Flex Basis */

.flexBasis33 {
  flex-basis: 33.33333%;
}

.flexBasis50 {
  flex-basis: 50%;
}

.flexBasis100 {
  flex-basis: 100%;
}

/* flex orders */

.order1 {
  order: 1;
}

.order2 {
  order: 2;
}

.order3 {
  order: 3;
}

.order4 {
  order: 4;
}

.order5 {
  order: 5;
}

.order6 {
  order: 6;
}

.order7 {
  order: 7;
}

.order8 {
  order: 8;
}

.order9 {
  order: 9;
}

.order10 {
  order: 10;
}

/*Grid*/
.connector-active-lob ul {
  li{
    a{  font-size: 18px;}
  }
  li.active{
    a{
      font-weight: bold;
    
    }
  }
}
.grid {
  @extend .flexRow;
  @extend .flexWrap;
  @extend .flexEqualize;
}

.grid-spaced {
  margin: 0 0 -30px -30px;
  @media #{$media-mobile} {
    margin-right: 0;
  }
  > * {
    padding: 0 0 30px 30px;
    @media #{$media-mobile} {
      padding-right: 0;
    }
  }
}

/* -- Icon Colors -- */

.yellowIcon {
  &:before {
    color: $virginYellow;
  }
}

/*borders colours*/

.noBorder {
  border: none;
}
.noBorderT {
  border-top: none !important;
}

.border {
  border: 1px solid;
}

.borderL {
  border-left: 1px solid;
}

.borderR {
  border-right: 1px solid;
}

.borderT {
  border-top: 1px solid;
}

.borderB {
  border-bottom: 1px solid;
}

/*border colours*/

.borderWhite {
  border-color: #fff;
}

.borderBlack {
  border-color: black;
}

.borderBlack2 {
  border-color: #111;
}

.borderBlackLight {
  border-color: #212121;
}

.borderGray {
  border-color: #ccc;
}

.borderGray2 {
  border-color: #d6d6d6;
}

.borderGray4 {
  border-color: #eee;
}

.borderGray9 {
  border-color: #e2e2e2;
}

.borderGray19 {
  border-color: #f5f5f5;
}

.borderGrayLight {
  border-color: #eee;
}

.borderGrayLight2 {
  border-color: #e1e1e1;
}

.borderGrayLight3 {
  border-color: #d7d7d7;
}

.borderGrayLight4 {
  border-color: #f0f0f0;
}

.borderGrayLight5 {
  border-color: #f5f5f5;
}

.borderGrayLight6 {
  border-color: #d4d4d4;
}

.borderGrayLight7 {
  border-color: #6b6b6b;
}

.borderGrayLight8 {
  border-color: #d3d3d3;
}

.borderGrayMedium2 {
  border-color: #404040;
}

.borderLightGray {
  border-color: #999;
}

.borderMediumGray {
  border-color: #555;
}

.borderBlue {
  border-color: #00549a;
}

.borderBlueDark {
  border-color: #003778;
}

.borderBlueExtraDark {
  border-color: #003075;
}

.borderBlueExtraDark2 {
  border-color: #01215e;
}

.borderBlueExtraLight {
  border-color: #c2cedf;
}
.txtGrey{
  color: $virginGray;
}

.txtCurrency {
  sup:last-child {
    left: 0px;
  }
}

// .txtCurrency {
//   sup {
//     top: -5px;
//   }
// }

// .txtCurrency.txtSize20,
// .txtCurrency.txtSize20-sm {
//   sup {
//     top: -4px;
//   }
// }

// .txtCurrency.txtSize24-xs sup {
//   @media #{$media-mobile} {
//     top: -8px;
//   }
// }

// .bell-tv-cart .txtCurrency sup:last-child {
//     left: -6px;
// }
.fiancialNumber {
  .negativeText {
    font-weight: bold;
  }
}

.txtUnderlineDotted {
  border-bottom: 1px dotted;
}

.txtItalic {
  font-style: italic;
}

/*spacers:*/

[class^="vSpacer"] {
  display: inline-block;
}

.vSpacer25 {
  width: 25px;
}

.spacer1 {
  height: 1px;
}

.spacer6 {
  height: 6px;
}

.spacer25 {
  height: 25px;
}

.spacer35 {
  height: 35px;
}

/*font sizes:*/

.txtSize22 {
  font-size: 22px;
}

.txtSize28 {
  font-size: 28px;
}

.txtSize36 {
  font-size: 36px;
}

.txtSize40 {
  font-size: 40px;
}

/* -- spacings -- */
.noSpacing {
  letter-spacing: 0;
}

.pad-0-top {
  padding-top: 0 !important;
}
.pad-0-left {
  padding-left: 0 !important;
}
.pad-0-bottom {
  padding-bottom: 0 !important;
}
.margin-0-top {
  margin-top: 0;
}
.margin-30-bottom {
  margin-bottom: 30px !important;
}
.margin-0-bottom {
  margin-bottom: 0;
}
.pad30 {
  padding: 30px;
}
.pad45 {
  padding: 45px;
}
.pad40 {
  padding: 40px;
}
.bgVirginGray {
  background-color: $virginGray;
}
.bgVirginCustomGray1 {
  background-color: $virginCustomGray1;
}
.txtNoUnderline{
  text-decoration: none;
  > *{
    vertical-align: middle;
  }
}
.txtHoverNoUnderline {
  color: $colorWhite;
  &:hover {
    text-decoration: none;
  }
}
.virginCustomGray {
  color: $virginCustomGray;
}
.bgVirginCustomGray {
  background-color: $virginCustomGray;
}

/* -- Line Height -- */
.lineHeight18 {
  line-height: 18px;
}
.lineHeight14 {
  line-height: 14px;
}
.HeaderlineHeight {
  line-height: 1.2;
}

/*-sm*/

@media #{$media-tablet} {
  .txtSize12-sm {
    font-size: 12px;
  }
  .txtSize14-sm {
    font-size: 14px;
  }
  .txtSize15-sm {
    font-size: 15px;
  }
  .txtSize16-sm {
    font-size: 16px;
  }
  .txtSize17-sm {
    font-size: 17px;
  }
  .txtSize18-sm {
    font-size: 18px;
  }
  .txtSize20-sm {
    font-size: 20px;
  }
  .txtSize22-sm {
    font-size: 22px;
  }
  .txtSize24-sm {
    font-size: 24px;
  }
  .txtSize26-sm {
    font-size: 26px;
  }
  .txtSize30-sm {
    font-size: 30px;
  }
  .txtSize32-sm {
    font-size: 32px;
  }
  .txtBold-sm {
    font-weight: bold;
  }
  .txtNormal-sm {
    font-weight: normal;
  }
  .fontSans-sm {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  }
  .margin-15-sm {
    margin: 15px;
  }
  .margin-15-left-right-sm {
    margin-left: 15px;
    margin-right: 15px;
  }
  .pad-0-left-sm {
    padding-left: 0px;
  }
}

/*-xs*/

@media #{$media-mobile} {
  .txtSize12-xs {
    font-size: 12px !important;
  }
  .txtSize14-xs {
    font-size: 14px !important;
  }
  .txtSize15-xs {
    font-size: 15px !important;
  }
  .txtSize16-xs {
    font-size: 16px !important;
  }
  .txtSize17-xs {
    font-size: 17px !important;
  }
  .txtSize18-xs {
    font-size: 18px !important;
  }
  .txtSize20-xs {
    font-size: 20px !important;
  }
  .txtSize22-xs {
    font-size: 22px !important;
  }
  .txtSize24-xs {
    font-size: 24px !important;
  }
  .txtSize28-xs {
    font-size: 28px !important;
  }
  .txtSize30-xs {
    font-size: 30px !important;
  }
  .txtSize32-xs {
    font-size: 32px !important;
  }
  .txtBold-xs {
    font-weight: bold;
  }
  .txtNormal-xs {
    font-weight: normal;
  }
  .fontSans-xs {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  }
  .fontUltra-xs {font-family:"VMUltramagneticNormalRegular";font-weight: normal;}
  .margin-xs {
    margin-left: 15px;
    margin-right: 15px;
  }
  .txtLeft-xs {
    text-align: left;
  }
  .pad-0-right-xs {
    padding-right: 0;
  }
  .pad-0-left-xs {
    padding-left: 0;
  }
  .flexJustifyStart-xs {
    justify-content: flex-start;
  }
  .flexJustifyBetween-xs {
    justify-content: space-between;
  }
  .noRadius-xs {
    border-radius: 0;
  }
  .noBorder-xs{
    border-width: 0px;
  }
  .pad-15-bottom-xs{padding-bottom: 15px;}
  .pad-30-15-xs {
    padding: 30px 15px;
  }
  .pad-30-0-xs {
    padding: 30px 0px;
  }
  .pad-20-top-xs {
    padding-top: 20px;
  }
  .pad-20-left-right-xs {
    padding-left: 20px;
    padding-right: 20px;
  }
  .fullWidth-xs, .fill-xs {
      width: 100%;
  }
}

/*-- Colors --*/

.txtLightGray3 {
  color: #4a4a4a;
}

.bgBlack1 {
  background-color: #000000;
}

/*positioners:*/

@media #{$media-large-desktop} {
  .pad-15-left-right-lg {
    padding-left: 15px;
    padding-right: 15px;
  }
  .pad-0-top-lg {
    padding-top: 0px;
  }
}

/*-md*/

@media #{$media-desktop} {
  .flexRow-md {
    display: flex;
    flex-direction: row;
  }
  .flexCol-md {
    display: flex;
    flex-direction: column;
  }
  .block-md {
    display: block;
  }
  .inlineBlock-md {
    display: inline-block;
  }
  .floatR-md {
    float: right;
  }
  .floatL-md {
    float: left;
  }
  .fill-md {
    width: 100%;
  }
  .noPadding-md {
    padding: 0;
  }
  .txtCenter-md {
    text-align: center;
  }
  .connector-search {
    display: inline-block;
    height: 60px;
    vertical-align: middle;
  }
}

/*-sm*/

@media #{$media-tablet} {
  .flexRow-sm {
    display: flex;
    flex-direction: row;
  }
  .flexCol-sm {
    display: flex;
    flex-direction: column;
  }
  .block-sm {
    display: block;
  }
  .inlineBlock-sm {
    display: inline-block;
  }
  .floatR-sm {
    float: right;
  }
  .floatL-sm {
    float: left;
  }
  .fill-sm {
    width: 100%;
  }
  .noPadding-sm {
    padding: 0;
  }
  .txtCenter-sm {
    text-align: center;
  }
}

/*-xs*/

@media #{$media-mobile} {
  .hidden-m {
    display: none !important;
  }
  .flexRow-xs {
    display: flex;
    flex-direction: row;
  }
  .flexCol-xs {
    display: flex;
    flex-direction: column !important;
  }
  .flexBasis50-xs {
    flex-basis: 50%;
  }
  .flexBasis100-xs {
    flex-basis: 100%;
  }
  .block-xs {
    display: block;
  }
  .inlineBlock-xs {
    display: inline-block;
  }
  .floatR-xs {
    float: right;
  }
  .floatL-xs {
    float: left;
  }
  .noFloat-xs {
    float: none;
  }
  .fill-xs {
    width: 100% !important;
  }
  .noPadding-xs {
    padding: 0;
  }
  .noMargin-xs {
    margin: 0;
  }
  .txtCenter-xs {
    text-align: center;
  }
  .padding15-xs {
    padding: 0 15px;
  }
  .margin-30-left-right-xs {
    margin-left: 30px;
    margin-right: 30px;
  }
  .margin-15-top-xs {
    margin-top: 15px;
  }
  .margin-15-bottom-xs {
    margin-bottom: 15px;
  }
  .margin-10-bottom-xs {
    margin-bottom: 10px !important;
  }
  .flex-reverse-xs {
    flex-direction: row-reverse;
  }
  .pad20-xs {
    padding: 20px !important;
  }
  .height-auto-xs{
    height: auto !important;
  }
  .tooltip-inner {
    width: 260px;
    padding: 30px 55px 30px 30px;
    .tooltip-close {
      text-decoration: none;
      float: right;
      margin-right: -40px;
      margin-top: -10px;
    }
  } // On mobile give the (i) icon a bit of padding
  // it's otherwise gets cut off on some devices
  .i-icon:before {
    padding-left: 1px;
  }
  .container-full-width-xs {
    margin-left: -15px;
    margin-right: -15px;
  }
  .bell-header-message {
    .close {
      float: none !important;
    }
  }
  .connector-mobile-bar{height: 45px;}
  .connector-active-lob.secondary-active-lob{background: #1f1f1f;}
  .spacer55{height: 50px;}
  .connector-active-lob ul {
    li { 
      a{
      &:link{color: #fff !important;}
    } 
  }
  }
}
@media #{$media-tab-mobile} {
  .border-radius-3-sm {
    border-radius: 3px !important;
  }
}

@media #{$media-desktop} {
  .tooltip-close {
    display: none;
    visibility: hidden;
  }
  .container-full-width-lg {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media #{$media-tablet} {
  .tooltip-inner {
    .tooltip-close {
      text-decoration: none;
      float: right;
      margin-right: -40px;
      margin-top: -10px;
    }
  }
}

.tooltip-inner {
  float: right;
  max-width: 350px;
  width: 350px;
  padding: 20px 20px;
}

.tooltip.bs-tooltip-top .arrow::before {
  border-width: 15px 15px 0;
}

.bgVirginGradiant {
 background: $virginBGGradiant;
}

.logo-footer {
  height: 60px;
  @media #{$media-mobile} {
    margin: 0 auto;
  }
}

.bell-header-message {
  .close {
    float: right;
    background: none;
    font-weight: normal;
  }
}

.modal {
  .modal-padding {
    padding: 30px;
    @media #{$media-mobile} {
      padding: 30px 15px;
    }
  }
  > * {
    -webkit-animation: none !important;
    animation: none !important;
    transform: translate3d(0, 0, 0) !important;
    -webkit-transform: translate3d(0, 0, 0) !important;
  }
  .modal-title {
    @media #{$media-mobile} {
      // Blackberry title alignment issue fix
      width: 90%;
    }
  }
}

.tooltip.top {
  margin-top: -20px;
}
.virginSlickNext,
.virginSlickPrev {
  position: absolute;
  top: 34%;
  color: $colorWhite;
  background-color: $virginOrange;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  padding: 0;
  font-size: 8px;
  z-index: 1;
  text-decoration: none;
  &:hover {
    background-color: $bellBlack;
  }
}
.virginSlickPrev {
  left: -3px;
  transform: rotate(90deg);
  &.slick-disabled {
    display: none !important;
  }
}
.virginSlickNext {
  right: -1px;
  transform: rotate(270deg);
  &.slick-disabled {
    display: none !important;
  }
}

.redIcon {
  background-color: $virginOrange;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  color: $colorWhite;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 0;
  @media #{$media-mobile} {
    width: 33px;
    height: 33px;
  }
}
.toolTipBtn  .tooltip-inner {padding:10px;background: #fff;}
.toolTipBtn .tooltip.top .tooltip-arrow{border-top-color: #fff;bottom:-10px;border-width: 15px 15px 0;}
.toolTipBtn .tooltip.top{margin-top: -30px;}

.bgOrange {
  background-color: $virginOrange;
}

.bgTransparent {
  background: transparent;
}

.borderStyle {
  border-style: solid;
}

.borderWidth2 {
  border-width: 2px;
}

.borderBlack {
  border-color: $virginBlack;
}

.widthAuto {
  width: auto;
}

.widthFitContent {
  width: fit-content;
}

.icon-blue, .txtVirginBlue {
  color: $virginIconBlue;
}

.bgGrayLight6{background:$virginCustomGray1}
.lineUpToolTip .tooltip-inner{max-width: 366px;word-spacing: -1px;line-height: 17px;}
.lineUpToolTip .tooltip{width: 366px;}
.lineUpToolTip .tooltip-arrow{box-shadow:-3px 3px 3px 0 rgba(0, 0, 0, 0.4);transform-origin: 0 0;
  transform: rotate(-45deg);
  box-shadow: -2px 2px 2px 0 rgba(0, 0, 0, 0.1);
  border-width:17px 17px 0px 0px !important;background: #fff;
}
.lineUpToolTip .tooltip.top{margin-top: -15px;}
