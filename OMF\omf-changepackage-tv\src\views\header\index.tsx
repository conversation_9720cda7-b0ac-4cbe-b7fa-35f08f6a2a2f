import * as React from "react";
import { connect } from "react-redux";
import { FormattedMessage, FormattedDate } from "react-intl";
import { ValueOf, Components, Omniture, Volt } from "omf-changepackage-components";
import { IStoreState, IProductOffering, IAccountDetail } from "../../models";

const {
  BellCurrency,
  Visible
} = Components;

interface IComponentProps {
  accountDetails: Array<IAccountDetail>;
}

const OfferingView: React.FC<IProductOffering & { displayGroupKey: Volt.EDIsplayGroupKey }> = ({
  Name,
  RegularPrice,
  PromotionDetails,
  ChannelCount,
  displayGroupKey
}) => <div>
  <div className="flexRow">
    <div className="flexGrow">{Name}<Visible when={displayGroupKey === Volt.EDIsplayGroupKey.ALACARTE && ChannelCount > 0}><FormattedMessage id="Count of channels" values={{ count: ChannelCount }} /></Visible></div>
    <div>
      <BellCurrency value={ValueOf(RegularPrice, "Price", 0)} />
      <span aria-hidden><FormattedMessage id="PER_MO" /></span>
      <span className="sr-only"><FormattedMessage id="PER_MONTH">{(txt) => <>{txt}</>}</FormattedMessage></span>
    </div>
  </div>
  <Visible when={!!PromotionDetails}>
    <div className="spacer5" aria-hidden="true" />
    <Visible when={ValueOf(PromotionDetails, "Description", false)}>
      <div className="flexRow">
        <div className="flexGrow">{ValueOf(PromotionDetails, "Description", "")}</div>
        <div>
          <BellCurrency value={ValueOf(PromotionDetails, "PromotionalPrice.Price", 0)} />
          <span aria-hidden><FormattedMessage id="PER_MO" /></span>
          <span className="sr-only"><FormattedMessage id="PER_MONTH">{(txt) => <>{txt}</>}</FormattedMessage></span>
        </div>
      </div>
    </Visible>
    <Visible when={ValueOf(PromotionDetails, "ExpiryDate", false)}>
      <div>
        <FormattedDate value={ValueOf(PromotionDetails, "ExpiryDate", "")} format="yMMMMd" timeZone="UTC">
          {(expiryDate: string) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />}
        </FormattedDate>
      </div>
    </Visible>
  </Visible>
</div>;

const Component: React.FC<IComponentProps> = ({
  accountDetails
}) => {
  const [expanded, toggleState] = React.useState(false);
  const collapseIcon = expanded ? "icon-Collapse" : "icon-Expand";
  // Omniture tracking for expand Interent package
  React.useEffect(() => {
    // we only care about when the menu expands
    if (expanded) {
      Omniture.useOmniture().trackAction({
        id: "myCurrentPackageCTA",
        s_oAPT: {
          actionId: 648
        },
        s_oEPN: "My current TV package"
      });
    }
  }, [expanded]);
  // ---
  return <Visible when={Array.isArray(accountDetails) && accountDetails.length > 0}>
    <section className="bgVirginGradiant">
      <div className="container liquid-container sans-serif">
        <div className="accordion-group internet-current-package flexCol accss-focus-outline-override-black-bg">
          <div className="accordion-heading col-xs-12 noPaddingImp accss-focus-outline-override-black-bg">
            <a role="button" id="my_currentPack" href="javascript:void(0)" onClick={() => toggleState(!expanded)} aria-controls="div1-accessible"
              className="accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content"
              aria-expanded={expanded}>
              <span className="sr-only accordion-label" aria-live="polite" aria-atomic="true"
                aria-hidden="true"><FormattedMessage id={expanded ? "Collapse" : "Expand"} /></span>
              <span className={`${collapseIcon} virgin-icon txtSize24 virginRedIcon`}
                aria-hidden="true">
                <span className={`virgin-icon path1 ${collapseIcon}`} />
                <span className={`virgin-icon path2 ${collapseIcon}`} />
              </span>
              <div className="margin-15-left flexCol">
                <span className="txtWhite txtBold txtSize18"><FormattedMessage id="My current TV package" /></span>
                <span className="expand txtWhite txtSize12 no-margin-top" style={{ display: expanded ? "none" : undefined }}><FormattedMessage id="Expand to view details" /></span>
              </div>
            </a>
          </div>
          <div id="div1-accessible"
            className="collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left"
            style={{ display: expanded ? "block" : "none" }}>
            <div className="accordion-inner flexWrap flexJustifySpace flexRow">
              {
                accountDetails.map(({
                  displayGroupKey,
                  offerings
                }) => <div className="col-sm-5 margin-15-bottom">
                  <strong><FormattedMessage id={`HEADER_${displayGroupKey}`} /></strong>
                  {offerings.map(offering => <OfferingView {...offering} displayGroupKey={displayGroupKey} />)}
                </div>)
              }
            </div>
          </div>
        </div>
      </div>
    </section>
  </Visible>;
};


export const Header = connect<IComponentProps>(
  ({ accountDetails }: IStoreState) => ({ accountDetails: accountDetails || [] })
)(Component);
