
export interface IVisibleProps extends React.PropsWithChildren {
  when: boolean;
  placeholder?: any;
}

export /**
 * Conditional visibility container component
 * returns component children is [when] prop
 * evaluates to true. Otherwise returns [placeholder/null]
 * @param {boolean} when - test statement
 * @param {any} [placeholder] - placeholder component (optional)
 */
const VisibleComponent: React.FC<IVisibleProps> = (props) =>
  (typeof props.when === "boolean" ? props.when : <PERSON><PERSON><PERSON>(props.when)) ?
    props.children :
    props.placeholder;

