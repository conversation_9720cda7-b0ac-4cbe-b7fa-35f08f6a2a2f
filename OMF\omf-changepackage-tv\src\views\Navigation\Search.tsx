import { EWidgetRoute, Components, ValueOf, Omniture, FormattedHTMLMessage } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { useHistory, useLocation } from "react-router-dom";
import { IStoreState, ITVCatalog, ITVChannel } from "../../models";
import { getSearchSuggestions } from "../../utils/Search";

interface IComponentConnectedProps {
  catalog: ITVCatalog;
}

interface IComponentDispatches {
}

declare const $: any;

export const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  catalog
}) => {
  const history = useHistory();
  const location = useLocation();
  const [suggestions, setSuggestions] = React.useState<Array<ITVChannel>>([]);
  const [isNavigating, setNavigating] = React.useState(true);
  const [searchTerm, setSearchTerm] = React.useState("");

  React.useEffect(() => {
    if (location.pathname !== "/Search")
      setSearchTerm("");
  }, [location]);

  function handleInput(e: React.ChangeEvent<HTMLInputElement>) {
    const { value } = e.target;
    setSearchTerm(value);
    if (Boolean(value))
      setNavigating(false);
    setSuggestions(getSearchSuggestions(value));
  }

  function redirect(e: any) {
    e && e.preventDefault && e.preventDefault();
    const query = ((typeof e === "string" ? e : searchTerm) || "").trim();
    if (!Boolean(query)) return;
    Omniture.useOmniture().trackAction({
      id: "searchSubmit",
      s_oAPT: {
        actionId: 395,
        actionresult: 0,
        applicationState: 0
      },
      s_oSRT: query
    });
    history.push({
      pathname: EWidgetRoute.TV_Search,
      search: "?query=" + encodeURIComponent(query)
    });
    setSearchTerm(query);
    setNavigating(true);
    setSuggestions([]);
  }

  React.useEffect(() => {
    function detectClick(e: any) {
      $(e.target).closest("#search_area").length === 0 && setSuggestions([]);
    }
    document.addEventListener("click", detectClick);
    return () => {
      document.removeEventListener("click", detectClick);
    };
  }, []);

  const handleNavAway = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    setSuggestions([]);
    setNavigating(true);
  };

  return <div id="search_area">
    <form id="search-bar" className="bell-tv-search-bar" onSubmit={redirect}>
      <div className="bell-search-field relative accss-focus-outline-override-grey-bg">
        <FormattedHTMLMessage id="Search channels">
          {
            (txt: string) => <input aria-label="Search"
              onChange={(e) => handleInput(e)}
              value={searchTerm}
              type="text"
              className="form-control bell-search-field-input"
              placeholder={txt}
              aria-autocomplete="both"
              onFocus={() => setNavigating(false)}
            />
          }
        </FormattedHTMLMessage>
        <div className="absolute bell-search-field-button">
          <button id="SEARCH_ICON" type="submit" className="btn btn-search-submi txtSize20" onFocus={(e: any) => handleNavAway(e)} aria-label="Search">
            <span className="volt-icon icon-search txtDarkGrey"></span>
          </button>
        </div>
      </div>
    </form>
    <div role="status" aria-live="assertive" aria-relevant="additions" className="sr-only">
      {
        !isNavigating && Boolean(searchTerm) ?
          ValueOf(suggestions, "length", 0) > 0
            ? <FormattedMessage id="AVAILABLE_SEARCH_RESULTS" values={{ value: suggestions.length }} />
            : <FormattedMessage id="NO_SEARCH_RESULTS" />
          : null
      }
    </div>
    <Components.Visible when={ValueOf(suggestions, "length", 0) > 0}>
      <div role="tooltip" aria-label="Search suggestions" className="bell-search-suggestions tooltip fade bottom in bs-tooltip-bottom">
        <div className="arrow" style={{ left: "50%" }} aria-hidden="true" />
        {
          suggestions && Boolean(suggestions.length) &&
                    <div className="tooltip-inner">
                      <ul className="noBullets" role="listbox">
                        {
                          suggestions.map((suggestion: ITVChannel) => <li>
                            <button id={`SEARCH_${suggestion.name}`} role="option" onClick={() => {
                              redirect(suggestion.name);
                            }} className="btn txtLeft pad-0 txtNormal">{suggestion.name}</button>
                          </li>)
                        }
                      </ul>
                    </div>
        }
      </div>
    </Components.Visible>
  </div>;
};

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog }: IStoreState) => ({ catalog }),
  (dispatch) => ({
  })
)(Component);
