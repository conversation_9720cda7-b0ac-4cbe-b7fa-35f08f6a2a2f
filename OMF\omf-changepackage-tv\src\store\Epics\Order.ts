import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { of, concat , filter, mergeMap, catchError } from "rxjs";

import { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, ValueOf, EWidgetName } from "omf-changepackage-components";
import { Client } from "../../Client";
import {
  IStoreState
} from "../../models";
import {
  toggleSelection,
  updateCatalog,
  setNavigation
} from "../Actions";

const {
  setWidgetStatus,
  finalizeRestriction,
  clearCachedState
} = Actions;

@Injectable
export class OrderingEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client) { }

  combineEpics() {
    return combineEpics(
      this.toggleSelectionEpic,
      this.finalizeRestrictionEpic
    );
  }

  private get toggleSelectionEpic(): CatalogEpic {
    return (action$: any, state$) =>
      action$.pipe(
        ofType(toggleSelection.toString()),
        filter(({ payload }: any) => Boolean(payload) && this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(({ payload }: ReduxActions.Action<Volt.IHypermediaAction>) =>
          concat(
            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
            this.client.action<AjaxResponse<Volt.IAPIResponse>>(payload).pipe(
              mergeMap((response) => FilterRestrictionObservable(response, [
                updateCatalog(response.data, state$.value.catalog),
                setNavigation(response.data),
                clearCachedState([EWidgetName.PREVIEW]),
                setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)
              ]))
            )
          )
        ),
        catchError(Models.ErrorHandlerObservable(toggleSelection))
      );
  }

  private get finalizeRestrictionEpic(): CatalogEpic {
    return (action$: any, state$) =>
      action$.pipe(
        ofType(finalizeRestriction.toString()),
        filter(({ payload }: ReduxActions.Action<Volt.IAPIResponse>) => 
          Boolean(payload) && Boolean(payload.productOfferingDetail) && this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(({ payload }: ReduxActions.Action<Volt.IAPIResponse>) => 
          of(
            Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(payload, "productOfferingDetail.productConfigurationTotal"))),
            updateCatalog(payload, state$.value.catalog),
            setNavigation(payload),
            clearCachedState([EWidgetName.PREVIEW]),
            setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)
          )
        )
      );
  }
}

type CatalogEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, IStoreState>;
