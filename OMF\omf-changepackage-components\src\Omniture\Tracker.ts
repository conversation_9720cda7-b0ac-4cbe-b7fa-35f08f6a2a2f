import { Omniture } from ".";

export declare class OmnitureTracker {
  history: Array<Omniture.IProps>;
  static getInstance(): OmnitureTracker;
  constructor();
  private _track(props, isPage?);
  trackPage(props: Omniture.IProps): void;
  trackFragment(props: Omniture.IProps): void;
  trackAction(props: Omniture.IProps): void;
  trackError(err: Omniture.IError | Array<Omniture.IError>, actionId?: string | number): void;
  trackFailure(err: any): void;
  updateContext(props: Omniture.IOmniture): void;
}
