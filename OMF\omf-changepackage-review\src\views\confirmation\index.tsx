import * as React from "react";
import { FormattedDate, FormattedMessage } from "react-intl";
import { useSelector, shallowEqual, useDispatch } from "react-redux";
import { IOrderConfirmation, IStoreState } from "../../models";
import { Actions, FormattedHTMLMessage, Utils, Volt } from "omf-changepackage-components";

const CurrentFlowType = Utils.getFlowType();

export const Confirmation: React.FunctionComponent = () => {
  const messages: Array<Volt.IMessage> = useSelector((state: IStoreState) => state.messages, shallowEqual);
  const confirmation: IOrderConfirmation = useSelector((state: IStoreState) => state.confirmation, shallowEqual),
    dispatch = useDispatch();

  return (
    <React.Fragment>
      <div className="panel-body bgWhite section-packages-listing notification-container">
        <div className="notification success">
          <span className="virgin-icon icon-Big_check_confirm txtSize40 float-left" style={{ marginLeft: "-55px", marginTop: "-5px" }}>
            <span className="virgin-icon path1 greenIcon"></span>
            <span className="volt-icon path2"></span>
          </span>
          <div id={`CONFIRMATION_MESSAGE_${CurrentFlowType}`}>
            <div id="ORDER_SUBMITTED" className="txtSize18 notification-title txtBlack2 pad-5-top txtBold txtUppercase" role="heading" aria-level={2}>
              <FormattedMessage id="ORDER_SUBMITTED" />
            </div>
            <div className="spacer15" aria-hidden={true} />
            <div className="notification-message">
              <span className="block"><strong><FormattedMessage id="CONFIRMATION_NUMBER" />: </strong>{confirmation.confirmationNumber}</span>
              <span className="block"><strong><FormattedMessage id="CONFIRMATION_DATE" />: </strong><FormattedDate value={confirmation.orderDate as string} year="numeric" month="long" day="2-digit" /></span>
              <div className="spacer15" aria-hidden={true} />
              <span className="block">
                <FormattedMessage id={`CONFIRMATION_MESSAGE_${CurrentFlowType}`} />
              </span>
            </div>
          </div>
          <div className="spacer15" aria-hidden={true} />
          <button id="app_exit_btn" className="btn btn-primary fill-xs" role="link" onClick={() => dispatch(Actions.broadcastUpdate(Actions.applicationExit()) as any)}>
            <FormattedMessage id={`RETURN_TO_ACCOUNT_${CurrentFlowType}`} />
          </button>
          <div className="bgWhite flexBlock pad-20-right pad-20-top margin-15-bottom txtBlack">
            <span className="virgin-icon icon-warning txtSize36">
              <span className="virgin-icon path1 yellowIcon"></span>
              <span className="volt-icon path2"></span>
            </span>
            <div id={`IMPORTANT_MESSAGE_${CurrentFlowType}`} className="flexCol pad-15-left content-width valign-top pad-0-xs">
              <h3 className="virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase">
                <FormattedMessage id="IMPORTANT_INFO" />
              </h3>
              <p className="txtSize14 txtGray4A sans-serif no-margin">
                <FormattedHTMLMessage id={`IMPORTANT_MESSAGE_${CurrentFlowType}`} />
              </p>
              {
                (messages || []).map((message: Volt.IMessage) => (
                  <p className="txtSize14 txtGray4A sans-serif no-margin" key={message.messageCode} dangerouslySetInnerHTML={{ __html: message.messageBody }} />
                ))
              }
            </div>
          </div>
        </div>
      </div>
      <div className="spacer10 clear"></div>
    </React.Fragment>
  );
};
