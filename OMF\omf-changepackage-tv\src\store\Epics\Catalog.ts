import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { mergeMap, filter, catchError , of, concat } from "rxjs";

import { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, Utils, EFlowType } from "omf-changepackage-components";
import { Client } from "../../Client";
import {
  IStoreState
} from "../../models";
import {
  getCatalog,
  setCatalog,
  setNavigation
} from "../Actions";
import { Config } from "../../Config";

const {
  errorOccured,
  setWidgetStatus
} = Actions;

@Injectable
export class CatalogEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.requestCatalogEpic,
    );
  }

  private get requestCatalogEpic(): CatalogEpic {
    return (action$: any) =>
      action$.pipe(
        ofType(getCatalog.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => concat(
          of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
          this.client.get<AjaxResponse<Volt.IAPIResponse>>(Utils.appendRefreshOnce(
            Utils.getURLByFlowType({
              [EFlowType.TV]: this.config.api.catalogAPI,
              [EFlowType.ADDTV]: this.config.api.addCatalogAPI,
              [EFlowType.BUNDLE]: this.config.api.bundleCatalogAPI
            })
          )).pipe(
            mergeMap((response) => FilterRestrictionObservable(response, [
              setCatalog(response.data),
              setNavigation(response.data),
              Actions.omniPageLoaded(),
              setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)
            ]))
          )
        )),
        catchError((error: Response) => of(
          // Set widget to rendered state even on error so UI remains functional
          setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED),
          // Dispatch error action to show error message
          errorOccured(new Models.ErrorHandler("getCatalog", error))
        ))
      );
  }

}

type CatalogEpic = Epic<any, any, IStoreState, any>;
