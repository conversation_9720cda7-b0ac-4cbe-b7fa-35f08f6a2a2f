import { Injectable, CommonFeatures, ServiceLocator, CommonServices } from "bwtk";
import { EWidgetName } from "omf-changepackage-components";

const { BaseLocalization } = CommonFeatures;

@Injectable
export class Localization extends BaseLocalization {
  static Instance = null;
  static getLocalizedString(id: string): string {
    Localization.Instance = Localization.Instance || ServiceLocator.instance.getService(CommonServices.Localization);
    const instance: any = Localization.Instance;
    return instance ? instance.getLocalizedString(EWidgetName.REVIEW, id, instance.locale) : id;
  }
}
