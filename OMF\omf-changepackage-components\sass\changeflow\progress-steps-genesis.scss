@import "mixins";

/* Bell channel steps module
 * 
 * A ribbon-like interface
 * used to track the A la carte
 * activity
*/
/* Genesis Specific Styles */

.section-bell-tv-genesis {
    .genesis-stepflow {
        @media #{$media-mobile} {
            flex-direction: column;
        }
        .bell-tv-channels-stepflow {
            padding-right: 38px;
            .genesis-stepflow-message {
                left: 0;
            }
            .bell-tv-channel-steps {
                margin: 0;
                @media #{$media-mobile} {
                    height: 72px;
                }
                .bell-tv-channel-steps-container {
                    .bell-tv-channel-steps-track {
                        @media #{$media-mobile} {
                            height: 45px;
                        }
                    }
                    .bell-tv-channel-steps-list .bell-tv-channel-step {
                        .description {
                            width: auto;
                            min-width: 44px;
                            @media #{$media-mobile} {
                                text-align: center;
                                padding-left: unset;
                            }
                        }
                    }
                }
            }
            .step-arrow {
                @media #{$media-mobile} {
                    height: 105px;
                }
            }
            .step-next {
                margin-right: 0;
            }
        }
        .bell-tv-channel-selection-indicator-container {
            padding: 15px 10px;
            position: unset;
            left: unset;
            width: 144px;
            flex: 0 0 144px;
            background: #FFF;
            margin-left: 15px;
            border: 1px solid #d4d4d4;
            @media #{$media-mobile} {
                width: 100%;
                margin-left: 0;
                margin-top: 15px;
                flex: 0 0 80px;
            }
            .second {
                display: none;
            }
            .selection-indicator-type {
                display: none;
            }
            &.glue {
                border: none;
                background: transparent;
                left: 50%;
                width: 1200px;
                @media (max-width: 1200px) {
                    width: 100%;
                }
                padding: 0 15px;
                .bell-tv-channel-selection-indicator {
                    width: 72px;
                    height: 72px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.95);
                    box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.25);
                    top: 34px;
                    right: 80px;
                    position: absolute;
                    z-index: 2;
                    @media #{$media-mobile} {
                        right: 40px;
                        top: 0;
                    }
                    .selection-indicator-text {
                        display: none;
                    }
                    .selection-indicator-number {
                        line-height: unset;
                        padding-top: 8px;
                        font-size: 26px;
                        margin-bottom: 0;
                        @media #{$media-mobile} {
                            padding-top: 3px;
                            margin-top: 5px;
                        }
                    }
                    .selection-indicator-price {
                        font-size: 12px;
                        @media #{$media-mobile} {
                            margin-top: 0;
                        }
                    }
                    .selection-indicator-type {
                        font-size: 12px;
                        display: block;
                        line-height: 16px;
                    }
                    &.second {
                        display: block;
                        width: 83px;
                        height: 83px;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.95);
                        top: 70px;
                        right: 20px;
                        position: absolute;
                        z-index: 1;
                        @media #{$media-mobile} {
                            right: -9px;
                            top: 44px;
                        }
                        // @media #{$media-tablet} {
                        //     right: 96px;
                        // }
                        .selection-indicator-number {
                            line-height: 28px;
                            padding-top: 8px;
                            font-size: 26px;
                            margin-bottom: 0;
                            @media #{$media-mobile} {
                                padding-top: 5px;
                            }
                        }
                        .selection-indicator-price {
                            font-size: 12px;
                            position: relative;
                            top: -5px;
                        }
                        .selection-indicator-type {
                            font-size: 12px;
                            position: relative;
                            top: -3px;
                        }
                    }
                    .selection-indicator-price-container {
                        flex-basis: 100%;
                    }
                }
            }
            .bell-tv-channel-selection-indicator {
                width: 100%;
                height: unset;
                border-radius: unset;
                box-shadow: none;
                margin-top: 0;
                padding-top: 0;
                background: transparent;
                span:last-child {
                    @media #{$media-mobile} {
                        margin-top: 10px;
                    }
                }
            }
        }
        .bell-tv-channel-steps-indicator-container,
        .bell-tv-channel-steps-indicator {
            display: none;
        }
    }
}