import { EReviewMode, Volt, WidgetContext } from "omf-changepackage-components";
import * as React from "react";
import { useSelector } from "react-redux";
import { IOrderConfirmation, IStoreState } from "../../../models";
import { isModeMatching, selectors } from "../../../utils";
import { ChargeItem } from "../ChargeItem";

interface IComponentProps {
  productOfferings: Array<Volt.IProductOffering>;
}

const NewTV: React.FunctionComponent<IComponentProps> = React.memo(({ productOfferings }) => {
  const confirmation: IOrderConfirmation = useSelector((state: IStoreState) => state.confirmation),
    { mode }: any = React.useContext(WidgetContext),
    isReview = isModeMatching(mode, EReviewMode.Review, EReviewMode.Summary);
  const offerings = productOfferings && selectors.sortAndGroupByOfferingType(productOfferings);
  return (
    <React.Fragment>
      {
        offerings && Object.keys(offerings).map(offer =>
          <ChargeItem
            isCurrent={false}
            offerings={offerings[offer]}
            isReview={isReview}
            confirmation={confirmation}
          />
        )
      }
    </React.Fragment >
  );
});

export default NewTV;
