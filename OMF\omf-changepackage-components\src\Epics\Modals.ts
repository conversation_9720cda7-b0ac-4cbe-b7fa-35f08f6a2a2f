import { Epic, combineEpics, ofType } from "redux-observable";
import { Actions } from "../Actions";
import { ILightboxPayload } from "../Models";
import { Utils } from "../Utils";
import { filter, mergeMap, tap } from "rxjs";

const {
  openLightbox,
  closeLightbox,
  setlightboxData
} = Actions;

/**
 * Basic widget lifecycle events
 * * Status update
 * * Error handeling
 * @export
 * @class ModalEpics
 */
export class ModalEpics {
  combineEpics(): any {
    return combineEpics(
      this.onOpenLightboxEpic,
      this.closeLightbox
    );
  }

  /**
   * Epic activated on open lightbox action
   *
   * @readonly
   * @private
   * @type {GeneralEpic}
   * @memberof Epics
   * @requires lightboxData store entry in widget
   */
  private get onOpenLightboxEpic(): GeneralEpic {
    return (action$) =>
      action$.pipe(
        ofType(openLightbox.toString()),
        filter(({ payload }: ReduxActions.Action<ILightboxPayload>) =>
          !Utils.isLightboxOpen(
            typeof payload === 'string' ? payload : payload.lightboxId
          )
        ),
        tap(({ payload }: ReduxActions.Action<ILightboxPayload | string>) =>
          Utils.showLightbox(
            typeof payload === 'string' ? payload : payload.lightboxId
          )
        ),
        mergeMap(({ payload }: ReduxActions.Action<ILightboxPayload>) => [
          setlightboxData(
            typeof payload === 'string' ? payload : payload.data
          )
        ])
      );
  }

  /**
   * Epic activated on open lightbox action
   *
   * @readonly
   * @private
   * @type {GeneralEpic}
   * @memberof Epics
   * @requires lightboxData store entry in widget
   */
  private get closeLightbox(): GeneralEpic {
    return (action$) =>
      action$.pipe(
        ofType(closeLightbox.toString()),
        filter(({ payload }: ReduxActions.Action<ILightboxPayload>) =>
          Utils.isLightboxOpen(
            typeof payload === 'string' ? payload : payload.lightboxId
          )
        ),
        tap(({ payload }: ReduxActions.Action<ILightboxPayload>) =>
          Utils.hideLightbox(
            typeof payload === 'string' ? payload : payload.lightboxId
          )
        )
      );
  }

}

type GeneralEpic = Epic<ReduxActions.Action<any>, any>;
