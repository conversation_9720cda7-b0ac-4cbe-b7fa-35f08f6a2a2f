import { Injectable, ParamsProvider } from "bwtk";
import { Actions, AjaxResponse, EFlowType, EWidgetStatus, Models, Utils, ValueOf } from "omf-changepackage-components";
import { combineEpics, Epic, ofType } from "redux-observable";
import { Observable, of, merge , filter, mergeMap, catchError } from "rxjs";

import { Client } from "../../Client";
import { Config } from "../../Config";
import { IOrderAPIResponse, IStoreState, IWidgetProps } from "../../models";
import { getOrderDetails, getOrderSummary, setAppointmentDetails, setOrderSummary, setReviewMessages } from "../Actions";

const {
  errorOccured,
  setWidgetStatus,
  broadcastUpdate,
  setProductConfigurationTotal
} = Actions;

@Injectable
export class ReviewEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config, private params: ParamsProvider<IWidgetProps, any>) { }

  combineEpics() {
    return combineEpics(
      this.requestOrderSummaryEpic,
      this.requestOrderDetailsEpic
    );
  }

  private get requestOrderSummaryEpic(): ReviewEpic {
    return (action$, store) =>
      action$.pipe(
        ofType(getOrderSummary.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING))),
        mergeMap(() =>
          this.client.get<AjaxResponse<IOrderAPIResponse>>(ValueOf(this.params, "props.summaryAPI")).pipe(
            mergeMap((response: AjaxResponse<IOrderAPIResponse>) => [
              setOrderSummary(response.data),
              setReviewMessages(response.data),
              setAppointmentDetails(response.data),
              setWidgetStatus(EWidgetStatus.RENDERED),
              Actions.omniModalOpen()
            ]),
            catchError((error: any) => of(
              // Set widget to rendered state even on error so UI remains functional
              setWidgetStatus(EWidgetStatus.RENDERED),
              // Dispatch error action to show error message
              errorOccured(new Models.ErrorHandler("getOrderSummary", error)),
              // Also dispatch modal open for summary mode
              Actions.omniModalOpen()
            ))
          )
        ),
        catchError((response: Response, source: Observable<any>) =>
          merge(
            of(errorOccured(new Models.ErrorHandler(getOrderSummary.toString(), response))),
            source
          )
        ));
  }

  private get requestOrderDetailsEpic(): ReviewEpic {
    return (action$) =>
      action$.pipe(
        ofType(getOrderDetails.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING))),
        mergeMap(() => 
          this.client.get<AjaxResponse<IOrderAPIResponse>>(Utils.getURLByFlowType({
            [EFlowType.INTERNET]: this.config.api.internetOrderDetailsAPI,
            [EFlowType.TV]: this.config.api.tvOrderDetailsAPI,
            [EFlowType.ADDTV]: this.config.api.tvAddDetailsAPI,
            [EFlowType.BUNDLE]: this.config.api.bundleOrderDetailsAPI
          })).pipe(
            mergeMap(({ data }: AjaxResponse<IOrderAPIResponse>) => [
              setReviewMessages(data),
              setOrderSummary(data),
              setAppointmentDetails(data),
              setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED),
              broadcastUpdate(setProductConfigurationTotal(ValueOf(data, "productOfferingDetail.productConfigurationTotal"))),
              Actions.omniPageLoaded()
            ])
          )
        ),
        catchError((error: Response) => of(
          // Set widget to rendered state even on error so UI remains functional
          setWidgetStatus(EWidgetStatus.RENDERED),
          // Dispatch error action to show error message
          errorOccured(new Models.ErrorHandler("getOrderDetails", error)),
          // Also dispatch page loaded to complete the loading cycle
          Actions.omniPageLoaded()
        )));
  }
}

type ReviewEpic = Epic<ReduxActions.Action<any>, any, IStoreState>;
