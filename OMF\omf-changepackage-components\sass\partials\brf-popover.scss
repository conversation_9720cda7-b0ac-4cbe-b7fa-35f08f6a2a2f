@import "mixins";
.tooltip {
    z-index: 10000000 !important;
}

// reset bell.css margin top for extra-bill-details
.tooltip-position.top {
    margin-top: 0px;
    .tooltip-arrow {
        margin-left: -11px;
        border-width: 20px 20px 20px 0;
    }
}

.tooltip-popover {
    max-width: 340px;
    .tooltip-popover-close {
        position: absolute;
        top: 20px;
        right: 30px;
        font-size: 10px;
        text-decoration: none!important; // Increase hit area size for mobile
        margin: -18px;
        border: 18px solid transparent;
        .icon {
            font-weight: 600;
        }
    }
    @media #{$media-tab-mobile} {
        &:before {
            content: " ";
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 2;
            background-color: #000;
            opacity: 0.5;
            display: block;
            pointer-events: all;
        }
        .tooltip-arrow {
            display: none;
        }
        .tooltip-inner {
            position: fixed;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            z-index: 3;
            max-height: 90%;
            overflow: auto;
            @media #{$media-mobile} {
                width: 90%;
            }
        }
    }
}