// X.X - Lightbox - Restriction
// https://projects.invisionapp.com/d/main#/console/14385632/305962610/preview
import * as React from "react";
import { connect } from "react-redux";
import { Actions } from "../../Actions";
import { FormattedMessage, FormattedDate } from "react-intl";
import { Volt } from "../../Models";
import { Omniture } from "../../Omniture";
import { ValueOf } from "../../Utils";
import { LightboxContainer } from "../Lightbox";
import { VisibleComponent } from "../VisibilityContainer";

export interface IComponentProps {
  id?: string;
}

export interface IComponentConnectedProps extends Volt.IRestriction {
  onComplete?: any;
}

export interface IComponentDispatches {
  onAction: (button: Volt.IHypermediaAction) => void;
  onDismiss: () => void;
}

function getButtonClass(i: number) {
  switch (true) {
    case i === 0: return "btn btn-primary fill-xs";
    case i === 1: return "btn btn-default fill-xs";
    default: return "btn btn-link";
  }
}

function getMessageIcon(type: string) {
  switch (type) {
    case "Error": return <span className="icon2 icon-alert-circled txtSize38 txtRed pad-15-right" />;
    case "Information": return <span className="icon2 icon-alert-circled txtSize38 txtYellow pad-15-right" />;
    case "Warning": return <span className="icon2 icon-alert-circled txtSize38 txtYellow pad-15-right" />;
    default: return null;
  }
}

let lightboxType: string = "";
function onShowOmniture(id: string) {
  let messageType;
  const s_oAPT = {
    actionId: 104,
    actionresult: 0,
    applicationState: 0
  };
  switch (lightboxType) {
    case "Warning":
    case "Error":
      s_oAPT.actionresult = 2;
      s_oAPT.applicationState = 2;
      messageType = Omniture.EMessageType.Warning;
      break;
    case "Information":
    default:
      messageType = Omniture.EMessageType.Information;
  }
  Omniture.useOmniture().trackFragment({
    id: "restrictionLightbox",
    s_oAPT,
    s_oPRM: {
      ref: `${id}_label`
    },
    s_oLBC: {
      ref: `${id}_description`
    },
    s_oPLE: {
      content: {
        ref: `${id}_description`
      },
      type: messageType
    }
  });
};

const Component: React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches> = ({
  id,
  type,
  title,
  description,
  dynamicData,
  footerDescription,
  actionLinks,
  onDismiss,
  onAction,
  onComplete
}) => {
  lightboxType = type;
  return <LightboxContainer modalId={id || "RESTRICTIONS_MODAL"} permanent={true}
    onClose={() => { onDismiss(); onComplete && onComplete("close"); }}
    onShown={() => onShowOmniture(id as string)}
    title={title}>
    <div className="modal-body bgWhite">
      <div className="flex">
        {getMessageIcon(type || "")}
        <div id={`${id}_description`}>
          <p dangerouslySetInnerHTML={{ __html: description }} />
          <VisibleComponent when={ValueOf(dynamicData, "productList.length", false)}>
            <ul>
              {
                ValueOf(dynamicData, "productList", []).map((product: string) => <li className="txtBold txtBlack">{product}</li>)
              }
            </ul>
          </VisibleComponent>
          <VisibleComponent when={ValueOf(dynamicData, "promotion.length", false)}>
            <ul>
              {
                ValueOf(dynamicData, "promotion", []).map((promo: any) => <li>
                  <span className="txtBold txtBlack">{promo.promoName}</span><br />
                  <VisibleComponent when={Boolean(promo.promoExpiry)}>
                    <FormattedDate value={promo.promoExpiry} format="yMMMd" timeZone="UTC">
                      {(expiryDate: any) => <FormattedMessage id="PromotionExpires" values={{ expiryDate }} />}
                    </FormattedDate>
                  </VisibleComponent>
                </li>)
              }
            </ul>
          </VisibleComponent>
          <VisibleComponent when={Boolean(footerDescription)}>
            <p className="txtBold" dangerouslySetInnerHTML={{ __html: footerDescription }} />
          </VisibleComponent>
        </div>
      </div>
    </div>
    <VisibleComponent when={Boolean(actionLinks && actionLinks.length > 0)}>
      <div className="spacer1 bgGrayLight6" aria-hidden="true"></div>
      <div className="bgGray19 pad-30 pad-15-left-right-xs">
        {
          ValueOf<Array<Volt.IHypermediaAction>>(actionLinks, undefined, [])
            .map((action, i) => <React.Fragment>
              <button id={`ACTION_SUBMIT`} className={getButtonClass(i)} onClick={() => { onAction(action); onComplete && onComplete(action.rel); }}>{action.name}</button>
              <div className="vSpacer15" aria-hidden="true"></div>
            </React.Fragment>)
        }
      </div>
    </VisibleComponent>
  </LightboxContainer>;
};

export const RestrictionModalView = connect<IComponentConnectedProps, IComponentDispatches, IComponentProps>(
  ({ restriction }: any) => (restriction ? { ...restriction } : {}) as IComponentConnectedProps,
  (dispatch) => ({
    onAction: (action) => {
      Omniture.useOmniture().trackAction({
        id: "restrictionLightbox",
        s_oAPT: {
          actionId: 647,
          actionresult: 0,
          applicationState: 0
        },
        s_oBTN: action.name
      });
      switch (action.name) {
        case "Cancel": dispatch(Actions.declineRestriction(action)); break;
        default: dispatch(Actions.acceptRestriction(action));
      }
    },
    onDismiss: () => dispatch(Actions.declineRestriction())
  })
)(Component);
