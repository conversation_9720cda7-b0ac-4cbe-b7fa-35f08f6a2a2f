import * as React from "react";
import { FormattedNumber, FormattedMessage } from "react-intl";

export interface ICurrencyComponetProps {
  className?: string;
  prefixClassName?: string;
  fractionClassName?: string;
  value: number;
  monthly?: boolean;
}

export interface ICurrencyComponet extends React.FC<ICurrencyComponetProps> {

}

const Currency: React.FC<any> = ({
  str,
  prefixClassName,
  fractionClassName
}) => {
  let whole: string = "", fraction: string = "", prefix: string = "";
  if (str.indexOf("$") === 0) {
    const parts: Array<string> = str.split(".");
    prefix = parts[0].substr(0, 1);
    whole = parts[0].substr(1);
    fraction = parts[1];
  } else {
    const parts: Array<string> = str.split(",");
    whole = parts[0]; fraction = parts[1];
  }
  return <>
    {Boolean(prefix) ? <sup className={prefixClassName}>{prefix}</sup> : null}
    {whole}
    <sup className={fractionClassName} aria-hidden>{fraction}</sup>
    {fraction !== "00" && <span className="sr-only">.{fraction} cents</span>}
  </>;
};

export const CurrencyComponent: ICurrencyComponet = ({
  className,
  prefixClassName,
  fractionClassName,
  value,
  monthly
}) => <FormattedNumber value={value} format="CAD">
  {
    (str: string) => <span className={`formatted-currency ${className}`}>
      <Currency str={str}
        prefixClassName={prefixClassName}
        fractionClassName={fractionClassName} />
      {monthly ?
        <sup className={prefixClassName}>
          <FormattedMessage id="PER_MO">{(per_mo) => <span aria-hidden="true">{per_mo}</span>}</FormattedMessage>
          <FormattedMessage id="PER_MONTH">{(per_month) => <span className="sr-only">{per_month}</span>}</FormattedMessage>
        </sup> : null}
    </span>
  }
</FormattedNumber>;

CurrencyComponent.defaultProps = {
  className: "",
  prefixClassName: "txtSize22",
  fractionClassName: "txtSize22"
};
