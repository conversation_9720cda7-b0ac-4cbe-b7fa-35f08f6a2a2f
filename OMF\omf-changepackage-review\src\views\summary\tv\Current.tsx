import { EReviewMode, Volt, WidgetContext } from "omf-changepackage-components";
import * as React from "react";
import { isModeMatching, selectors } from "../../../utils";
import { ChargeItem } from "../ChargeItem";

interface IComponentProps {
  productOfferings: Array<Volt.IProductOffering>;
}
const CurrentTV: React.FunctionComponent<IComponentProps> = React.memo(({ productOfferings }) => {
  const { mode }: any = React.useContext(WidgetContext),
    isReview = isModeMatching(mode, EReviewMode.Review);
  const offerings = productOfferings && selectors.sortAndGroupByOfferingType(productOfferings);

  return (
    <React.Fragment>
      {
        offerings && Object.keys(offerings).map(offer =>
          <ChargeItem isCurrent={true} offerings={offerings[offer]} isReview={isReview} />
        )
      }
    </React.Fragment >
  );
});

export default CurrentTV;
