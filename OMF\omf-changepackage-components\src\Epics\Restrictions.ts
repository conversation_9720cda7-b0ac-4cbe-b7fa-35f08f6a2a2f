import { Epic, combineEpics, ofType } from "redux-observable";
import { Action } from "redux-actions";
import { Actions } from "../Actions";
import { Utils, ValueOf, FilterRestrictionObservable } from "../Utils";
import { Volt } from "../Models/VOLT";
import { Models, AjaxResponse, EWidgetStatus } from "../Models";
import { BaseClient } from "../Client";
import { catchError, concat, EMPTY, filter, mergeMap, of } from "rxjs";

const {
  raiseRestriction,
  acceptRestriction,
  declineRestriction,
  finalizeRestriction,
  broadcastUpdate,
  setWidgetStatus,
  historyGo
} = Actions;

/**
 * Sample general epic illustrating
 * API communication from outside
 * of widget cycle
 * @export
 * @class SampleEpics
 */
export class RestricitonsEpics {

  /**
     *Creates an instance of RestricitonsEpics.
     * @param {string} [restrictionModalId] Element id of the restrictions dialog modal view
     * @memberof RestricitonsEpics
     */
  constructor(private client: BaseClient, private restrictionModalId?: string) { }

  combineEpics(): any {
    return combineEpics(
      this.raiseRestrictionEpic,
      this.fadeRestrictionEpic,
      this.restrictionActionsEpic
    );
  }

  private get raiseRestrictionEpic(): RestricitonsEpic {
    return (action$) =>
      action$.pipe(
        ofType(raiseRestriction.toString()),
        mergeMap(() => {
        // Side effect
          Utils.showLightbox(this.restrictionModalId || 'RESTRICTIONS_MODAL');
          return EMPTY; // no further actions
        })
      );
  }

  private get fadeRestrictionEpic(): RestricitonsEpic {
    return (action$) =>
      action$.pipe(
        ofType(acceptRestriction.toString(), declineRestriction.toString()),
        mergeMap(() => {
        // Side effect
          Utils.hideLightbox(this.restrictionModalId || 'RESTRICTIONS_MODAL');
          return EMPTY; // no further actions
        })
      );
  }

  /**
     * Run this when restriction is accepted in lightbox
     * it should either redirect user to wherever redirectURLKey
     * is pointing to, or run whatever action required (not implements)
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get restrictionActionsEpic(): RestricitonsEpic {
    return (action$) =>
      action$.pipe(
        ofType(
          Actions.acceptRestriction.toString(),
          Actions.declineRestriction.toString()
        ),
        filter(({ payload }: Action<Volt.IHypermediaAction>) => Boolean(payload)),
        mergeMap(({ payload }: Action<Volt.IHypermediaAction>) => {
          if (payload.redirectURLKey) {
          // Do the redirection whenever restriction action has a redirectURLKey
            return of(broadcastUpdate(historyGo(payload.redirectURLKey)));
          }

          if (payload.href) {
          // Run restriction action when available
            return concat(
              of(setWidgetStatus(EWidgetStatus.UPDATING)),
              this.client.action<AjaxResponse<any>>(payload).pipe(
                mergeMap((response) =>
                  FilterRestrictionObservable(response, [
                    ValueOf(
                      response,
                      'data.productOfferingDetail.redirectURLKey',
                      false
                    )
                      ? historyGo(
                        ValueOf(
                          response,
                          'data.productOfferingDetail.redirectURLKey'
                        )
                      )
                      : finalizeRestriction(response.data)
                  ])
                )
              )
            );
          }

          // Just dismiss
          return of();
        }),
        catchError(Models.ErrorHandlerObservable(Actions.acceptRestriction))
      );
  }

}

type RestricitonsEpic = Epic<ReduxActions.Action<any>, any>;
