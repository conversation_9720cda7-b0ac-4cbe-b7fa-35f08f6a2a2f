import { Components, FormattedHTMLMessage, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { INavigationItem, ITVChannel } from "../../models";
import { filterLanguage, sortOfferings } from "../../utils/Characteristics";
import { manageFloater } from "../../utils/Floater";
import Channel from "../Components/Channel";
import { OmniturePage } from "../Components/Omniture";
import SelectedChannels from "../Modals/SelectedChannels";


interface IComponentConnectedProps {
  languages: Array<string>;
  channels: Array<ITVChannel>;
  navigation: Array<INavigationItem>;
  openLightbox: Function;
  refresh: any;
}

const getAlaCarteNav = (navigation: Array<INavigationItem>) => {
  const international = navigation.find(data => data.key === Volt.EDIsplayGroupKey.INTERNATIONAL);
  return ValueOf<Array<Volt.IDisplayGroupOffering>>(international, "children", []).find(data => data.key === Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE);
};

export const Alacarte: React.FC<IComponentConnectedProps> = ({
  languages,
  channels,
  navigation,
  openLightbox,
  refresh
}) => {
  const floaterData = getAlaCarteNav(navigation);
  const floater$: HTMLDivElement | any = React.useRef(null);
  const [floater, setIsFloating] = React.useState({ isFloating: false, leftPos: "auto" });
  const totalPrice = ValueOf(floaterData, "subTotalPrice.price", 0);

  React.useEffect(() => {
    const scrollSpy = () => {
      manageFloater(floater$, setIsFloating);
    };
    window.addEventListener("scroll", scrollSpy);
    return () => {
      window.removeEventListener("scroll", scrollSpy);
    };
  }, []);

  return <OmniturePage name="International Alacarte">
    <div className="section-bell-tv-international-channels-individual-tv-channels clearfix">
      <div className="spacer5" />
      <div className="flexRow flex-justify-space-between">
        <div className="margin-xs">
          <h2 id={Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE} className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs">
            <FormattedMessage id="International a la carte page" />
          </h2>
          <FormattedHTMLMessage id="International a la carte page description">
            {
              (__html: any) => <Components.Visible when={Boolean(__html)}>
                <div className="spacer5"></div>
                <p className="noMargintxtSize14">{__html}</p>
              </Components.Visible>
            }
          </FormattedHTMLMessage>
        </div>
        <div
          id="wrap"
          ref={floater$}
          onClick={() => openLightbox("wrap")}
          tabIndex={0}
          role="button"
          className={`floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer ${floater.isFloating ? "fix-floating-notification" : null}`}
          style={{ left: floater.leftPos }}>
          <div className=" txtSize26 virginUltraReg pad-10-top">{ValueOf(floaterData, "count", 0)}</div>
          <span className=""><FormattedMessage id="CHANNELS_SELECTED" /></span>
          <div className="txtBold"><Components.BellCurrency value={ValueOf(floaterData, "subTotalPrice.price", 0)} /></div>
        </div>
      </div>
      <div className="spacer15" />
      <div className="panel-body bell-tv-package-filters-row bgWhite">
        {
          languages.map(langauge => {
            const _channels = filterLanguage(channels, langauge);
            return <Components.Visible key={langauge} when={_channels.length > 0}>
              <fieldset>
                <legend className="txtSize18 txtBlack txtBold"><FormattedMessage id={langauge} /></legend>
                <div className="bell-tv-channels bell-tv-channel-picker flexRow">
                  {sortOfferings(_channels).map((channel: ITVChannel) => <Channel key={channel.id} {...channel} multipleWaysToAdd={[]} />)}
                </div>
              </fieldset>
              <div className="spacer40" />
            </Components.Visible>;
          })
        }
      </div>
    </div>
    <SelectedChannels
      channels={channels.filter(channel => channel.isSelected)}
      totalPrice={totalPrice}
    />
  </OmniturePage>;
};
