import { combineReducers } from "redux";
import { Action } from "redux-actions";
import { combineEpics } from "redux-observable";
import { Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics, Volt, Utils } from "omf-changepackage-components";

import { Store as BwtkStore, Injectable, CommonFeatures } from "bwtk";

import {
  setOrderSummary,
  setAppointmentDetails,
  setReviewMessages,
  setAcceptedTerms,
  setOrderConfirmation
} from "./Actions";

import { IStoreState, IOrderSummary, IAppointmentDetails, PersistConfig, IOrderConfirmation } from "../models";
import { Epics } from "./Epics";
import { Localization } from "../Localization";
import { Client } from "../Client";

const { BaseStore } = CommonFeatures;

// const initialState: IStoreState = {
//   confirmation: {} as IOrderConfirmation,
//   messages: [],
//   summary: {} as IOrderSummary,
//   appointment: {} as IAppointmentDetails,
//   acceptedTerms: null
// };

@Injectable
export class Store extends BaseStore<IStoreState> {
  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {
    super(store);
  }

  get reducer() {
    return Utils.reducer(
      PersistConfig,
      combineReducers({
        ...Reducers.WidgetBaseLifecycle(this.localization),
        ...Reducers.WidgetLightboxes(),
        ...Reducers.WidgetRestrictions(),
        summary: (state: IOrderSummary = {} as IOrderSummary, action: Action<IOrderSummary>) => {
          if (action.type === setOrderSummary.toString()) {
            return action.payload || state;
          }
          return state;
        },
        messages: (state: Array<Volt.IMessage> = [], action: Action<Array<Volt.IMessage>>) => {
          if (action.type === setReviewMessages.toString()) {
            return action.payload || state;
          }
          return state;
        },
        acceptedTerms: (state: any = [], action: Action<any>) => {
          if (action.type === setAcceptedTerms.toString()) {
            return action.payload || state;
          }
          return state;
        },
        appointment: (state: IAppointmentDetails = {} as IAppointmentDetails, action: Action<IAppointmentDetails>) => {
          if (action.type === setAppointmentDetails.toString()) {
            return action.payload || state;
          }
          return state;
        },
        confirmation: (state: IOrderConfirmation = {} as IOrderConfirmation, action: Action<IOrderConfirmation>) => {
          if (action.type === setOrderConfirmation.toString()) {
            return action.payload || state;
          }
          return state;
        }
      } as any)
    );
  }

  get middlewares(): any {
    return combineEpics(
      this.epics.omnitureEpics.combineEpics(),
      this.epics.reviewEpics.combineEpics(),
      this.epics.orderEpics.combineEpics(), 
      this.epics.combineEpics(),
      new ModalEpics().combineEpics(),
      new RestricitonsEpics(this.client, "REVIEW_RESTRICTION_MODAL").combineEpics(),
      new LifecycleEpics().combineEpics()
    );
  }
}
