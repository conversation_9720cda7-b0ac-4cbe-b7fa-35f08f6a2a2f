import * as React from "react";
import { connect } from "react-redux";
import { Volt, ValueOf, Components, FormattedHTMLMessage } from "omf-changepackage-components";
import { FormattedMessage } from "react-intl";
import { IStoreState } from "../../models";

import Package from "./Package";
import { Footer } from "../Components/Legal";
import { sortOfferings } from "../../utils/Characteristics";
import { OmniturePage } from "../Components/Omniture";

interface IComponentConnectedProps {
  packages: Array<Volt.IProductOffering>;
}

interface IComponentDispatches {
}

export const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  packages
}) => <OmniturePage name="Your TV package">
  <h2 className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs">
    <FormattedMessage id="Available Core Packages page" />
  </h2>
  <FormattedHTMLMessage id="Available Core Packages page Description">
    {
      (__html: any) => <Components.Visible when={Boolean(__html)}>
        <div className="spacer15"></div>
        <p className="noMargintxtSize14">{__html}</p>
      </Components.Visible>
    }
  </FormattedHTMLMessage>
  <div className="spacer30 hidden-xs" aria-hidden={true} />
  {
    sortOfferings(packages).map(pack => <Package key={pack.displayGroupKey} isSingle={packages.length < 2} {...pack} isDisabled={!ValueOf(pack, "offeringAction.href", false)} />)
  }
  <Footer pageName={Volt.EDIsplayGroupKey.BASE_PROGRAMMING}
    label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}
    content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`} />
</OmniturePage>;

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog }: IStoreState) => ({
    packages: ValueOf<Array<Volt.IProductOffering>>(catalog, "offerings." + Volt.EDIsplayGroupKey.BASE_PROGRAMMING, [])
      .filter(pack => pack.productOfferingType === Volt.EProductOfferingType.PACKAGE)
  })
)(Component);
