import { IStoreState } from "./Store";
import { IAvailableDates } from "./App";

export interface AppointmentData {
  dateAndTime: string;
  PREFERED_METHOD_OF_CONTACT: string;
  Phone_LABEL?: string;
  Phone_EXT?: string;
  Email_LABEL?: string;
  TextMessage_LABEL?: string;
  ADDITIONAL_PHONE_NUMBER: string;
  ADDITIONAL_PHONE_EXT: string;
  APPARTMENT: string;
  ENTRY_CODE: string;
  SUPERINTENDANT_NAME: string;
  SUPERINTENDANT_PHONE: string;
  INFORMED_SUPERINTENDANT: boolean;
  SPECIAL_INSTRUCTIONS: string;
}

export const Request = {
  availableDates: null,
  duration: "",
  installationAddress: {
    address1: "",
    address2: "",
    city: "",
    province: "",
    postalCode: "",
    apartmentType: "",
    apartmentNumber: ""
  },
  contactInformation: {
    preferredContactMethod: "",
    primaryPhone: {
      phoneNumber: "",
      phoneExtension: ""
    },
    mobileNumber: null,
    additionalPhone: {
      phoneNumber: "",
      phoneExtension: ""
    },
    textMessage: "",
    email: ""
  },
  additionalDetails: {
    apartment: "",
    entryCode: "",
    specialInstructions: "",
    superintendantName: "",
    superintendantPhone: "",
    informedSuperintendant: null
  },
  isInstallationRequired: null
};

export class MapRequestData {
  public static create(payload: AppointmentData, request: typeof Request, store: IStoreState) {
    // Prefered Date
    // request.preferredDate.date = payload.dateAndTime;
    // request.preferredDate.timeSlots[0].intervalType = "";

    // Installation Address
    request.installationAddress.address1 = store.installationAddress && store.installationAddress.address1 ? store.installationAddress.address1 : "";
    request.installationAddress.address2 = store.installationAddress && store.installationAddress.address2 ? store.installationAddress.address2 : "";
    request.installationAddress.city = store.installationAddress && store.installationAddress.city ? store.installationAddress.city : "";
    request.installationAddress.postalCode = store.installationAddress && store.installationAddress.postalCode ? store.installationAddress.postalCode : "";
    request.installationAddress.province = store.installationAddress && store.installationAddress.province ? store.installationAddress.province : "";
    request.installationAddress.apartmentType = store.installationAddress && store.installationAddress.province ? store.installationAddress.apartmentType : "";
    request.installationAddress.apartmentNumber = store.installationAddress && store.installationAddress.province ? store.installationAddress.apartmentNumber : "";
    // Installation Required
    request.isInstallationRequired = store.isInstallationRequired as any;

    // Duration
    request.duration = store.duration as string;

    // Contact Information
    request.contactInformation.primaryPhone.phoneNumber = payload.Phone_LABEL ? payload.Phone_LABEL : "";
    request.contactInformation.primaryPhone.phoneExtension = payload.Phone_EXT ? payload.Phone_EXT : "";
    request.contactInformation.additionalPhone.phoneNumber = payload.ADDITIONAL_PHONE_NUMBER;
    request.contactInformation.additionalPhone.phoneExtension = payload.ADDITIONAL_PHONE_EXT;
    request.contactInformation.preferredContactMethod = payload.PREFERED_METHOD_OF_CONTACT;
    request.contactInformation.email = payload.Email_LABEL ? payload.Email_LABEL : "";
    request.contactInformation.textMessage = payload.TextMessage_LABEL ? payload.TextMessage_LABEL as any : "";

    // Available Dates
    request.availableDates = updateAvailableDates(store.availableDates, JSON.parse(payload.dateAndTime)) as any;

    // Additional Details
    request.additionalDetails.apartment = payload.APPARTMENT;
    request.additionalDetails.entryCode = payload.ENTRY_CODE;
    request.additionalDetails.informedSuperintendant = payload.INFORMED_SUPERINTENDANT as any;
    request.additionalDetails.specialInstructions = payload.SPECIAL_INSTRUCTIONS;
    request.additionalDetails.superintendantName = payload.SUPERINTENDANT_NAME;
    request.additionalDetails.superintendantPhone = payload.SUPERINTENDANT_PHONE;
    // console.log(request);
    // console.log("payload: ", payload);
    return request;
  }
}

function updateAvailableDates(dates: Array<IAvailableDates> | undefined, selectedDate: IAvailableDates) {
  // Set all isPreferredDate and isSelected to false
  dates?.forEach(date => {
    // date.isPreferredDate = false;
    date.timeSlots.forEach(time => time.isSelected = false);
  });
  // Set Prefered True
  dates?.forEach(date => (
    // Set isSelected to true
    date.timeSlots.forEach(time => time.isSelected = (date.date === selectedDate.date && selectedDate.timeSlots.map(selectedTime => selectedTime.intervalType === time.intervalType)) ? true : false)
  ));

  return dates;
}
