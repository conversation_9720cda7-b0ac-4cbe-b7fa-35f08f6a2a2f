import { Actions, Components, EModals, EReviewMode, Utils, WidgetContext, EWidgetRoute, EWidgetStatus } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { IOrderConfirmation, IStoreState } from "../models";
import { getOrderDetails, getOrderSummary } from "../store/Actions";
import { isModeMatching } from "../utils";
import { Appointment } from "./appointment";
import { Confirmation } from "./confirmation";
import { Footer } from "./footer";
import { Legal } from "./legal";
import { Messages } from "./messages";
import { Summary } from "./summary";

const {
    RestrictionModal
  } = Components,
  {
    showHideLoader,
    setWidgetStatus
  } = Actions,
  ReviewTitle: React.FunctionComponent = () => (
    <div className="pad-30-top pad-20-bottom">
      <h2 className="txtSize28 txtBlack txtSize26-xs noMargin pad-0-xs virginUltraReg txtUppercase">
        <FormattedMessage id="Review and submit changes" />
      </h2>
    </div>
  );

export const Application: React.FunctionComponent = () => {
  const { mode }: any = React.useContext(WidgetContext),
    summaryReview: boolean = isModeMatching(mode, EReviewMode.Summary),
    confirmation: IOrderConfirmation = useSelector((state: IStoreState) => state.confirmation, shallowEqual),
    summary = useSelector((state: IStoreState) => state.summary, shallowEqual),
    isOrderProcessed = Boolean(confirmation.confirmationNumber),
    dispatch = useDispatch(),
    // isReviewStep = isModeMatching(mode, EReviewMode.Review),
    isConfirmationStep = isModeMatching(mode, EReviewMode.Confirmation);

  React.useEffect(
    () => {
      if (Utils.getPageRoute() !== EWidgetRoute.CONFIRMATION) {
        // Check if we already have summary data (from preview modal flow)
        const hasExistingData = summary && (summary.currentTotal || summary.newTotal);

        if (hasExistingData) {
          // Data already loaded (likely from preview modal), just set widget as rendered
          dispatch(setWidgetStatus(EWidgetStatus.RENDERED) as any);
          dispatch(Actions.omniPageLoaded() as any);
        } else {
          // No existing data, load fresh data
          dispatch(summaryReview ? getOrderSummary() : getOrderDetails() as any);
        }
      } else {
        dispatch(setWidgetStatus(EWidgetStatus.RENDERED) as any);
        dispatch(Actions.omniPageLoaded() as any);
        dispatch(showHideLoader(null) as any);
      }
      if (summaryReview) {
        const modal: any = document.querySelector(`#${EModals.PREVIEWMODAL} .modal-dialog`);
        if (modal) {
          modal.style.width = "962px";
        }
      }
    },
    [summary]
  );

  /*
   awaiting the arrival of React.useError hook to replace ComponentDidCatch
 */
  return (
    <div id="modalContent" className="container liquid-container fullWidthContainer-xs accss-overflow-unset">
      <div className={`${summaryReview ? "" : "review-page"}`}>
        <div className="spacer20" aria-hidden="true"></div>
        {isOrderProcessed && <Confirmation />}
        {(!isOrderProcessed && isModeMatching(mode, EReviewMode.Review)) && <Messages />}
        <div className="review-tier-block">
          {(!isOrderProcessed && isModeMatching(mode, EReviewMode.Review)) && <ReviewTitle />}
          <Summary isConfirmationStep={isConfirmationStep} />
          {!summaryReview && <Appointment />}
          {
            (!isOrderProcessed && isModeMatching(mode, EReviewMode.Review)) && (
              <React.Fragment>
                <Legal />
                <Footer />
              </React.Fragment>
            )
          }
        </div>
        <RestrictionModal id="REVIEW_RESTRICTION_MODAL" />
      </div>
    </div>
  );
};
