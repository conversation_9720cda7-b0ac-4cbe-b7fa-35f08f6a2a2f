import { CommonFeatures } from "bwtk";
// import { Action } from "redux-actions";
import { Actions } from "omf-changepackage-components";
import { Store } from "./store";

const { BasePipe } = CommonFeatures;

/**
 * rxjs pipe provider
 * this fascilitates the direct connection
 * between widgets through rxjs Observable
 * @export
 * @class Pipe
 * @extends {BasePipe}
 */
export class Pipe extends BasePipe {
  static Subscriptions(store: Store) {
    return {
      [Actions.onContinue.toString()]: () => {
        store.dispatch(Actions.omniPageSubmit());
        Actions.broadcastUpdate(Actions.historyForward());
      },
      [Actions.omniPageSubmit.toString()]: () => {
        Actions.broadcastUpdate(Actions.omniPageSubmit());
      }
    };
  }
  /**
     *Creates a static instance of Pipe.
     * @param {*} arg
     * @memberof Pipe
     */
  static instance: Pipe;
  constructor(arg: any) {
    super(arg);
    Pipe.instance = this;
  }
}
