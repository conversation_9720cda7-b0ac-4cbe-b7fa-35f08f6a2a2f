import { Models, Volt, Utils } from "omf-changepackage-components";
import { IOrderSummary, IAppointmentDetails, IOrderConfirmation } from "./Review";

export interface IStoreState extends Models.IBaseStoreState {
  confirmation: IOrderConfirmation;
  messages: Array<Volt.IMessage>;
  summary: IOrderSummary;
  appointment: IAppointmentDetails;
  acceptedTerms: any;
}

export const PersistConfig: any = Utils.persistConfig(require("../../package.json").name);
