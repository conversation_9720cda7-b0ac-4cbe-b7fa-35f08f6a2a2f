/*! omf-changepackage-navigation (widget) 0.1.0 | bwtk 6.1.0 | 2025-08-26T15:19:09.354Z */
!function(e,t){var n,r;if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("bwtk"),require("omf-changepackage-components"),require("react"),require("react-redux"),require("react-router-dom"),require("react-intl"),require("react-dom"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"));else if("function"==typeof define&&define.amd)define(["bwtk","omf-changepackage-components","react","react-redux","react-router-dom","react-intl","react-dom","redux","redux-actions","redux-observable","rxjs"],t);else for(r in n="object"==typeof exports?t(require("bwtk"),require("omf-changepackage-components"),require("react"),require("react-redux"),require("react-router-dom"),require("react-intl"),require("react-dom"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs")):t(e.bwtk,e.OMFChangepackageComponents,e.React,e.ReactRedux,e.ReactRouterDOM,e.ReactIntl,e.ReactDOM,e.Redux,e.ReduxActions,e.ReduxObservable,e.rxjs))("object"==typeof exports?exports:e)[r]=n[r]}(self,function(e,t,n,r,i,a,o,c,s,l,u){return function(){"use strict";function d(e){var t,n=ut[e];return void 0!==n?n.exports:(t=ut[e]={exports:{}},lt[e](t,t.exports,d),t.exports)}function m(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");h(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function p(e,t,n,r){var i,a,o=arguments.length,c=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)c=Reflect.decorate(e,t,n,r);else for(a=e.length-1;a>=0;a--)(i=e[a])&&(c=(o<3?i(c):o>3?i(t,n,c):i(t,n))||c);return o>3&&c&&Object.defineProperty(t,n,c),c}function E(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function f(){return v}var g,T,h,b,x,y,N,O,I,R,C,v,A,w,L,P,V,_,S,k,W,M,F,U,B,G,D,j,z,q,K,X,H,J,Y,Q,Z,$,ee,te,ne,re,ie,ae,oe,ce,se,le,ue,de,me,pe,Ee,fe,ge,Te,he,be,xe,ye,Ne,Oe,Ie,Re,Ce,ve,Ae,we,Le,Pe,Ve,_e,Se,ke,We,Me,Fe,Ue,Be,Ge,De,je,ze,qe,Ke,Xe,He,Je,Ye,Qe,Ze,$e,et,tt,nt,rt,it,at,ot,ct,st,lt={3:function(e){e.exports=o},102:function(t){t.exports=e},418:function(e){e.exports=u},419:function(e){e.exports=a},442:function(e){e.exports=n},446:function(e){e.exports=t},541:function(e){e.exports=s},634:function(e){e.exports=i},750:function(e){e.exports=c},769:function(e){e.exports=l},999:function(e){e.exports=r}},ut={};return d.d=function(e,t){for(var n in t)d.o(t,n)&&!d.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},d.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},d.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},g={},d.r(g),d.d(g,{default:function(){return st}}),T={},d.r(T),d.d(T,{checkRestrictions:function(){return Ve},setFlowType:function(){return Le},setSummaryTotals:function(){return Pe}}),h=function(e,t){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},h(e,t)},b=function(){return b=Object.assign||function(e){var t,n,r,i;for(n=1,r=arguments.length;n<r;n++)for(i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},b.apply(this,arguments)},Object.create,Object.create,x=function(e){return x=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},x(e)},"function"==typeof SuppressedError&&SuppressedError,y=d(102),N=d(446),O=d(442),I=d(999),R=d(634),v=null,A=null,w=!1,(C={})[N.EFlowType.INTERNET]=["/Changepackage/Internet","/Changepackage/Internet/Appointment","/Changepackage/Internet/Review","/Changepackage/Internet/Confirmation"],C[N.EFlowType.TV]=["/Changepackage/TV","/Changepackage/TV/Review","/Changepackage/TV/Confirmation"],C[N.EFlowType.ADDTV]=["/Add/TV","/Add/TV/Review","/Add/TV/Confirmation"],C[N.EFlowType.BUNDLE]=["/Bundle/Internet","/Bundle/Internet/Appointment","/Bundle/TV","/Bundle/Review","/Bundle/Confirmation"],L=C,P=d(419),V=y.CommonFeatures.BaseLocalization,_=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return m(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||y.ServiceLocator.instance.getService(y.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString(N.EWidgetName.NAVIGATION,e,t.locale):e},t.Instance=null,n=p([y.Injectable],t)}(V),S=N.Components.Modal,k="APPLICATION_LOGOUT",W=function(e){var t=e.onContinueClick,n=e.closeLightbox;return O.createElement(S,{modalId:k,onShown:function(){N.Omniture.useOmniture().trackFragment({id:"logoutLightbox",s_oAPT:{actionId:104},s_oPRM:_.getLocalizedString("APPLICATION_LOGOUT_TITLE"),s_oLBC:_.getLocalizedString("APPLICATION_LOGOUT_TEXT")})},title:O.createElement(P.FormattedMessage,{id:"APPLICATION_LOGOUT_TITLE"})},O.createElement("div",{className:"pad-30"},O.createElement(N.FormattedHTMLMessage,{id:"APPLICATION_LOGOUT_TEXT"})),O.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),O.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},O.createElement("button",{id:"APP_LOGOUT_CONTINUE",className:"btn btn-primary fill-xs",onClick:t},O.createElement(P.FormattedMessage,{id:"APPLICATION_LOGOUT_CONTINUE"})),O.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),O.createElement("button",{id:"APP_LOGOUT_CLOSE",className:"btn btn-default fill-xs",onClick:n},O.createElement(P.FormattedMessage,{id:"APPLICATION_LOGOUT_CLOSE"}))))},M=(0,I.connect)(function(e){return{}},function(e){return{onContinueClick:function(){N.Omniture.useOmniture().trackAction({id:"logoutLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_LOGOUT_CONTINUE"}}),e(N.Actions.applicationLogout())},closeLightbox:function(){N.Omniture.useOmniture().trackAction({id:"logoutLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_LOGOUT_CLOSE"}}),e(N.Actions.closeLightbox(k))}}})(W),F=function(e){var t=e.onLogoutClick;return O.createElement(N.Context,null,function(e){var n=e.config,r=n.linkURL,i=n.isOneTrustDPEnabled;return O.createElement("footer",{className:"accss-focus-outline-override-grey-bg"},O.createElement("a",{id:"skipToMain",href:"#mainContent",className:"skip-to-main-link"},O.createElement(P.FormattedMessage,{id:"Skip to main content"})),O.createElement("div",{className:"simplified-footer pad-15-top pad-30-top-xs container container-fluid flex flex-justify-space-between flexCol-xs pad-20-left pad-20-right"},O.createElement("div",{className:"flex-vCenter"},O.createElement("ul",{className:"footer-links flex list-unstyled no-margin flexCol-xs"},O.createElement("li",{className:"width-100-percent-xs noBorder"},O.createElement("a",{id:"privacy",href:r.privacyURL,className:"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray"},O.createElement(P.FormattedMessage,{id:"Privacy"}))),O.createElement("li",{className:"width-100-percent-xs noBorder"},O.createElement("a",{id:"legal_context",href:r.legalURL,className:"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray"},O.createElement(P.FormattedMessage,{id:"Legal"}))),"true"===i&&O.createElement("li",{className:"width-100-percent-xs noBorder"},O.createElement("a",{className:"vrui-no-underline sm:vrui-mx-8 vrui-align-middle ot-sdk-show-settings",role:"button",href:"javascript:void(0)",id:"ot-sdk-btn"},O.createElement("span",{className:"vrui-font-poppins-Regular vrui-text-12 vrui-leading-14"},O.createElement(P.FormattedMessage,{id:"COOKIE_SETTINGS"})))),O.createElement("li",{className:"width-100-percent-xs"},O.createElement("a",{id:"feedback",href:r.feedbackURL,className:"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray"},O.createElement(P.FormattedMessage,{id:"FEEDBACK"})))),O.createElement("div",{className:"spacer15","aria-hidden":"true"}),O.createElement("div",{className:"txtSize14 txtCenter-xs "},O.createElement(P.FormattedMessage,{id:"Copyright"}))),O.createElement("div",{className:"flex flexCol-xs"},O.createElement("span",{className:"spacer30 d-block d-sm-none","aria-hidden":"true"}),O.createElement("button",{id:"footerLogout",onClick:function(){return t("footerLogout")},className:"btn btn-secondary flex middle-align-self line-height-1",type:"button"},O.createElement(P.FormattedMessage,{id:"Log out"})),O.createElement("div",{className:"vSpacer30 hidden-m"}),O.createElement("span",{className:"spacer30 d-block d-sm-none","aria-hidden":"true"}),O.createElement("div",{className:"width-100-percent-xs txtCenter-xs"},O.createElement("img",{className:"img-responsive logo-footer",role:"link",tabIndex:0,src:r.entrustIMGURL,alt:"Entrust label"})))),O.createElement("div",{className:"spacer40","aria-hidden":"true"}))})},U=(0,I.connect)(function(e){return{}},function(e){return{onLogoutClick:function(t){return e(N.Actions.openLightbox({lightboxId:k,data:{relativeId:t}}))}}})(F),B=N.Components.Modal,G="APPLICATION_EXIT",D=function(e){var t=e.onContinueClick,n=e.closeLightbox;return O.createElement(B,{modalId:G,onShown:function(){N.Omniture.useOmniture().trackFragment({id:"exitLightbox",s_oAPT:{actionId:104},s_oPRM:_.getLocalizedString("APPLICATION_EXIT_TITLE"),s_oLBC:_.getLocalizedString("APPLICATION_EXIT_TEXT")})},title:O.createElement(P.FormattedMessage,{id:"APPLICATION_EXIT_TITLE"})},O.createElement("div",{id:"APPLICATION_EXIT_TEXT",className:"pad-30 pad-15-left-right-xs"},O.createElement(N.FormattedHTMLMessage,{id:"APPLICATION_EXIT_TEXT"})),O.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},O.createElement("button",{id:"APP_EXIT_CLOSE",className:"btn btn-primary fill-xs",onClick:n},O.createElement(P.FormattedMessage,{id:"APPLICATION_EXIT_CLOSE"})),O.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),O.createElement("button",{id:"APP_EXIT_CONTINUE",className:"btn btn-default fill-xs",onClick:t},O.createElement(P.FormattedMessage,{id:"APPLICATION_EXIT_CONTINUE"}))))},j=(0,I.connect)(function(e){return{}},function(e){return{onContinueClick:function(){N.Omniture.useOmniture().trackAction({id:"exitLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_EXIT_CONTINUE"}}),e(N.Actions.applicationExit())},closeLightbox:function(){N.Omniture.useOmniture().trackAction({id:"exitLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_EXIT_CLOSE"}}),e(N.Actions.closeLightbox(G))}}})(D),z=function(e){var t,n=e.flowType,r=e.location.pathname,i=e.onBackClick,a=e.onExitClick,o=N.Utils.getPageRoute()===N.EWidgetRoute.CONFIRMATION,c=function(e){switch(!0){case e.indexOf("Review")>0:return"REVIEW";case e.indexOf("Confirm")>0:return"CONFIRMATION";case e.indexOf("Appoint")>0:return"APPOINTMENT";case e.indexOf("Internet")>0:return"INTERNET";case e.indexOf("TV")>0:return"TV";default:return""}}(r),s="".concat(c,"_AT_").concat((t=n,(t||"").replace(/[\W_]+/g,"").toUpperCase())).concat([N.EWidgetRoute.REVIEW,N.EWidgetRoute.CONFIRMATION].indexOf(N.Utils.getPageRoute())>-1&&w?"+":""),l="/ordering/changepackage/internet/review"===window.location.pathname.toLowerCase()?"Back to step 1":"/ordering/changepackage/internet"===window.location.pathname.toLowerCase()?"Back to Overview":"Back";return O.createElement(N.Context,null,function(e){var t=e.config.linkURL;return O.createElement("header",{className:"bgPrimary simplified-header container-flex-box-wrap",role:"banner"},O.createElement("div",{className:"container container-fluid container-flex-box-wrap flex-justify-space-between accss-focus-outline-override-red-bg"},O.createElement("div",{className:"page-back-button container-flex-box-wrap fullHeight align-items-center flex"},!o&&O.createElement("a",{id:"back",onClick:function(e){return i(e,"back")},"aria-label":l,href:t.exitURL,className:"responsive-simplified-header-back txtDecorationNoneHover txtWhite"},O.createElement("span",{className:"virgin-icon icon-Left_arrow txtSize15 inlineBlock","aria-hidden":"true"}),O.createElement("span",{className:"txtSize14 hidden-m margin-10-left txtDecoration_hover"},O.createElement(P.FormattedMessage,{id:"Back"})))),O.createElement("div",{className:"page-heading container-flex-box-wrap fullHeight overflow-ellipsis-parent container-flex-grow-fill justify-center","aria-live":"assertive"},O.createElement("div",{className:"middle-align-self overflow-ellipsis"},O.createElement("h1",{className:"virginUltraReg txtWhite no-margin overflow-ellipsis txtCenter txtSize22 txtUppercase"},O.createElement(N.FormattedHTMLMessage,{id:"PAGE_NAME_FOR_".concat(s)})),!o&&O.createElement(N.FormattedHTMLMessage,{id:"STEP_COUNT_FOR_".concat(s)},function(e){return O.createElement(N.Components.Visible,{when:!!e&&e!=="STEP_COUNT_FOR_".concat(s)},O.createElement("p",{className:"txtWhite txtSize14 no-margin-bottom sans-serif txtCenter header-steps",dangerouslySetInnerHTML:{__html:e}}))}))),O.createElement("div",{className:"page-right-button flex-vCenter d-none d-md-flex d-lg-flex"},O.createElement("button",{id:"exit",onClick:function(){return a("exit",o)},"data-href":t.exitURL,className:"btn btn-secondary-inverted margin-5-right"},O.createElement(P.FormattedMessage,{id:"EXIT_CTA"})))))})},q=(0,I.connect)(function(e){return{routes:e.routes}},function(e){return{onBackClick:function(t,n){t.preventDefault(),t.stopPropagation(),e(N.Actions.historyBack(n))},onExitClick:function(t,n){e(n?N.Actions.applicationExit():N.Actions.openLightbox({lightboxId:G,data:{relativeId:t}}))}}})(z),K=N.Components.Modal,X="APPLICATION_RESET",H=function(e){var t=e.onContinueClick,n=e.closeLightbox;return O.createElement(K,{modalId:X,onShown:function(){N.Omniture.useOmniture().trackFragment({id:"exitLightbox",s_oAPT:{actionId:104},s_oPRM:_.getLocalizedString("APPLICATION_RESET_TITLE"),s_oLBC:_.getLocalizedString("APPLICATION_RESET_TEXT")})},title:O.createElement(P.FormattedMessage,{id:"APPLICATION_RESET_TITLE"})},O.createElement("div",{className:"pad-30"},O.createElement(N.FormattedHTMLMessage,{id:"APPLICATION_RESET_TEXT"})),O.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),O.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},O.createElement("button",{id:"APP_RESET_CONTINUE",className:"btn btn-primary fill-xs",onClick:t},O.createElement(P.FormattedMessage,{id:"APPLICATION_RESET_CONTINUE"})),O.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),O.createElement("button",{id:"APP_RESET_CLOSE",className:"btn btn-default fill-xs",onClick:n},O.createElement(P.FormattedMessage,{id:"APPLICATION_RESET_CLOSE"}))))},J=(0,I.connect)(function(e){return{}},function(e){return{onContinueClick:function(){return e(N.Actions.applicationReset())},closeLightbox:function(){return e(N.Actions.closeLightbox(X))}}})(H),Y=N.Components.Modal,Q=N.EModals.PREVIEWMODAL,Z=function(e){var t=e.isOpen,n=e.summaryAction,r=e.isContinueEnabled,i=e.onContinueClick,a=e.closeLightbox,o=e.dismissLightbox;return O.createElement(Y,{modalId:Q,className:"do-not-center-in",onDismiss:o,title:O.createElement(P.FormattedMessage,{id:"".concat(Q,"_TITLE")})},t&&O.createElement(y.WidgetLoader,{widget:N.EWidgetName.PREVIEW,mode:N.EReviewMode.Summary,summaryAPI:(0,N.ValueOf)(n,"href")}),O.createElement("div",{className:"spacer30","aria-hidden":"true"}),O.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),O.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},O.createElement("button",{id:"BUTTON_CONTINUE_".concat(Q),disabled:!r,className:"btn btn-primary fill-xs",onClick:i},O.createElement(P.FormattedMessage,{id:"".concat(Q,"_CONTINUE")})),O.createElement("span",{className:"vSpacer15","aria-hidden":"true"}),O.createElement("button",{id:"BUTTON_CLOSE_".concat(Q),className:"btn btn-secondary fill-xs",onClick:a},O.createElement(P.FormattedMessage,{id:"".concat(Q,"_CLOSE")}))))},$=(0,I.connect)(function(e){var t=e.lightboxData,n=e.summary;return{summaryAction:(0,N.ValueOf)(n,"summaryAction",null),isContinueEnabled:!!(0,N.ValueOf)(n,"nextAction",!1),isOpen:t&&t.lightbox===Q}},function(e){return{onContinueClick:function(){e(N.Actions.closeLightbox(Q)),e(N.Actions.broadcastUpdate(N.Actions.onContinue()))},closeLightbox:function(){N.Omniture.useOmniture().trackAction({id:"previewLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"BUTTON_CLOSE_".concat(Q)}}),e(N.Actions.closeLightbox(Q))},dismissLightbox:function(){N.Omniture.useOmniture().trackAction({id:"previewLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"}),e(N.Actions.setlightboxData(""))}}})(Z),ee=function(e){var t=e.title;return O.useEffect(function(){sessionStorage.setItem("omf:hasAppointmentRoute","yes"),document.title="".concat(t," - ").concat(document.title)},[]),O.createElement(y.WidgetLoader,{widget:N.EWidgetName.APPOINTMENT})},te=function(e){var t=e.title;return document.title="".concat(t," - ").concat(document.title),O.createElement(y.WidgetLoader,{widget:N.EWidgetName.CONFIRMATION,mode:N.EWidgetRoute.CONFIRMATION})},ne=function(e){var t=e.title;return document.title="".concat(t," - ").concat(document.title),O.createElement(y.WidgetLoader,{widget:N.EWidgetName.INTERNET})},re=function(e){var t=e.title;return document.title="".concat(t," - ").concat(document.title),O.createElement(y.WidgetLoader,{widget:N.EWidgetName.REVIEW,mode:N.EWidgetRoute.REVIEW})},ie=function(e){var t=e.title;return document.title="".concat(t," - ").concat(document.title),O.createElement(y.WidgetLoader,{widget:N.EWidgetName.TV})},ae=d(3),oe=N.Components.Visible,ce=N.Components.Currency,se=function(e){var t=e.summary,n=e.isContinueEnabled,r=e.onContinueClick;return O.createElement(O.Fragment,null,O.createElement(oe,{when:(0,N.ValueOf)(t,"TV",!1)},O.createElement(oe,{when:(0,N.ValueOf)(t,"TV.currentPrice")},O.createElement("div",{className:"virgin-menu-dockbar flexStatic flexJustifyBetween-sm bgBlack"},O.createElement("p",{className:"noMargin txtSize12 flexGrow txtWhite"},O.createElement(P.FormattedMessage,{id:"CurrentTV"})),O.createElement("span",{className:"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite"},O.createElement(ce,{value:(0,N.ValueOf)(t,"TV.currentPrice.price",0),monthly:!0,prefixClassName:"txtSize16 txtUppercase",fractionClassName:"txtSize16"})))),O.createElement(oe,{when:(0,N.ValueOf)(t,"TV.newPrice")},O.createElement("div",{className:"virgin-menu-dockbar flexStatic bgOrange flexJustifyBetween-sm"},O.createElement("p",{className:"noMargin txtSize12 flexGrow txtWhite"},O.createElement(P.FormattedMessage,{id:"NewTV"})),O.createElement("span",{className:"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite"},O.createElement(ce,{value:(0,N.ValueOf)(t,"TV.newPrice.price",0),monthly:!0,prefixClassName:"txtSize16 txtUppercase",fractionClassName:"txtSize16"}))))),O.createElement("div",{className:"flexBlock preview-btn bgBlack flexJustify pad-25-top pad-25-bottom"},O.createElement("button",{onClick:r,disabled:!n,id:"mobileTVContinue",className:"btn btn-primary txtWhite txtSize16 relative ".concat(n?"":"disabled")},O.createElement(P.FormattedMessage,{id:"Review changes"}),O.createElement(oe,{when:(0,N.ValueOf)(t,"productOfferingCount",!1)},O.createElement("span",{className:"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification"},O.createElement("span",null,(0,N.ValueOf)(t,"productOfferingCount",0)))))))},le=se,ue=N.Components.Visible,de=N.Components.Currency,me=function(){var e=(N.Utils.getPageRoute()||"").replace("/","").toUpperCase(),t="TOOLTIP_".concat(e),n=function(e,t){var n,r,i,a,o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;n=o.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)i.push(r.value)}catch(c){a={error:c}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(a)throw a.error}}return i}(O.useState(!1),2),r=n[0],i=n[1];return O.createElement("button",{id:"tierContinue",onClick:function(){return i(!r)},onKeyUp:function(){return i(!r)},onMouseOver:function(){return i(!0)},onMouseOut:function(){return i(!1)},className:"btn btn-primary fill-xs tooltip-interactive relative alignIconWithText disabled","aria-disabled":"true"},O.createElement(P.FormattedMessage,{id:"Continue"}),O.createElement(ue,{when:r},O.createElement(P.FormattedMessage,{id:t},function(e){return Boolean(e)&&e!==t?O.createElement("div",{className:"tooltip fade bs-tooltip-top show",role:"tooltip",id:"tooltip504192",style:{position:"absolute",width:"240px",top:"-110px",left:"50%",transform:"translateX(-50%)"}},O.createElement("div",{className:"arrow",style:{left:"50%"}}),O.createElement("div",{className:"tooltip-inner",style:{width:"100%"}},O.createElement("div",{className:"flexRow bgWhite txtBlack"},O.createElement("div",{className:"olt-icon icon-warning txtSize22 margin-10-right"},O.createElement("span",{className:"volt-icon path1 yellowIcon"}),O.createElement("span",{className:"volt-icon path2"})),O.createElement("div",{className:"margin-5-top"},O.createElement(P.FormattedMessage,{id:t}))))):O.createElement("span",null)})))},pe=function(e){var t=e.label,n=e.price,r=e.regularPrice,i=e.className,a=void 0===i?"":i;return O.createElement("div",{className:"virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify "+a},O.createElement("p",{className:"noMargin txtSize12 txtWhite"},t),O.createElement("div",null,O.createElement(ue,{when:(0,N.ValueOf)(r,void 0,!1)},O.createElement("p",{className:"noMargin txtSize12 txtWhite"},O.createElement(P.FormattedMessage,{id:"Price after credit"}))),O.createElement("span",{className:"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite"},O.createElement(de,{value:(0,N.ValueOf)(n,"price",0),monthly:!0,prefixClassName:"txtSize16 txtUppercase",fractionClassName:"txtSize16"})),O.createElement(ue,{when:(0,N.ValueOf)(r,void 0,!1)},O.createElement("p",{className:"noMargin txtSize12 txtWhite"},O.createElement(P.FormattedMessage,{id:"Current price",values:(0,N.ValueOf)(r,void 0)})))))},Ee=function(e){var t=e.summary,n=(e.flowType,e.location),r=e.isContinueEnabled,i=e.onSummaryClick,a=e.onCancelClick,o=e.onContinueClick,c=(e.onCategoriesClick,e.handleNav),s=window.location.href.indexOf(N.EWidgetRoute.TV)>-1,l=!(n.pathname.indexOf(N.EWidgetRoute.REVIEW)>0||n.pathname.indexOf(N.EWidgetRoute.CONFIRMATION)>0),u=document.getElementById("tv-sedebar-summary-portal");return O.createElement(ue,{when:l},O.createElement("nav",null,O.createElement("div",{className:"virgin-dockbar col1 scrollTop"},O.createElement("div",{className:"nopad bgBlack accss-focus-outline-override-black-bg",style:{opacity:"92%"}},O.createElement("div",{className:"virgin-dockbar-panel flexRow block-xs container container-fluid no-pad-xs"},O.createElement("div",{className:"flexRow block-xs"},O.createElement(ue,{when:(0,N.ValueOf)(t,"Internet",!1)},O.createElement(ue,{when:(0,N.ValueOf)(t,"?Internet.currentPrice")},O.createElement(pe,{label:O.createElement(P.FormattedMessage,{id:"Current"}),price:(0,N.ValueOf)(t,"?Internet.currentPrice"),regularPrice:(0,N.ValueOf)(t,"?Internet.regularCurrentPrice")})),O.createElement(ue,{when:(0,N.ValueOf)(t,"?Internet.newPrice")},O.createElement(pe,{label:O.createElement(P.FormattedMessage,{id:"NewInternet"}),className:"bgOrange",price:(0,N.ValueOf)(t,"?Internet.newPrice"),regularPrice:(0,N.ValueOf)(t,"?Internet.regularNewPrice")}))),O.createElement(ue,{when:(0,N.ValueOf)(t,"TV",!1)},O.createElement(ue,{when:(0,N.ValueOf)(t,"?TV.currentPrice")},O.createElement(pe,{label:O.createElement(P.FormattedMessage,{id:"CurrentTV"}),price:(0,N.ValueOf)(t,"?TV.currentPrice"),regularPrice:(0,N.ValueOf)(t,"?TV.regularCurrentPrice")})),O.createElement(ue,{when:(0,N.ValueOf)(t,"?TV.newPrice")},O.createElement(pe,{label:O.createElement(P.FormattedMessage,{id:"NewTV"}),className:"bgOrange",price:(0,N.ValueOf)(t,"?TV.newPrice"),regularPrice:(0,N.ValueOf)(t,"?TV.regularNewPrice")})))),O.createElement(ue,{when:(0,N.ValueOf)(t,"summaryAction")},O.createElement("div",{className:"virgin-dockbar-row flexCol-xs preview-btn"},O.createElement("button",{id:"orderReview",onClick:function(){return i("orderReview")},className:"btn btn-link txtUnderline txtWhite txtSize12 pad-10-left accss-changeplan-preview"},O.createElement(P.FormattedMessage,{id:"Preview"})))),O.createElement(ue,{when:(0,N.ValueOf)(t,"resetAction")},O.createElement("div",{className:"flexStatic flexCol-xs preview-btn"},O.createElement("button",{id:"orderCancel",onClick:function(){return a("orderCancel")},className:"btn btn-link txtWhite txtUnderline txtSize12 dockbar-cancel"},O.createElement(P.FormattedMessage,{id:"Reset"})))),O.createElement("div",{className:"spacer10 visible-m","aria-hidden":"true"}),O.createElement("div",{className:"flexGrow"}),O.createElement("div",{className:"flex dockbar-buttons continue-button fullWidth-xs align-items-center flexCol-xs bgBlack"},O.createElement(ue,{when:s},O.createElement("div",{className:"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs d-md-none"},O.createElement("button",{id:"TV_CATEGORIES",className:"btn btn-secondary-inverted p-2",onClick:c},O.createElement(P.FormattedMessage,{id:"TV_CATEGORIES"})))),O.createElement("div",{className:"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs"},O.createElement(ue,{when:r,placeholder:O.createElement(me,null)},O.createElement("button",{onClick:o,id:"tier_Continue",className:"btn btn-primary fill-xs tooltip-interactive alignIconWithText pointer relative p-2"},O.createElement(P.FormattedMessage,{id:"Continue"}),O.createElement(ue,{when:s&&(0,N.ValueOf)(t,"productOfferingCount",!1)},O.createElement("span",{className:"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification"},O.createElement("span",null,(0,N.ValueOf)(t,"productOfferingCount",0))))))))))),u&&ae.createPortal(O.createElement(le,{summary:t,isContinueEnabled:r,onContinueClick:o}),u)))},fe=(0,I.connect)(function(e){var t=e.summary;return{summary:t,isContinueEnabled:!!(0,N.ValueOf)(t,"nextAction",!1)}},function(e){return{onSummaryClick:function(t){return e(N.Actions.openLightbox({lightboxId:Q,data:{relativeId:t,lightbox:Q}}))},onCancelClick:function(t){return e(N.Actions.openLightbox({lightboxId:X,data:{relativeId:t}}))},onCategoriesClick:function(){return e(N.Actions.broadcastUpdate(N.Actions.toggleTVCategoriesTray()))},onContinueClick:function(){return e(N.Actions.broadcastUpdate(N.Actions.onContinue()))},handleNav:function(){return e(N.Actions.broadcastUpdate(N.Actions.handleNav(!0)))}}})(Ee),ge=N.Components.RestrictionModal,Te=N.Actions.errorOccured,he=N.Actions.widgetRenderComplete,be=function(e){var t=(0,R.useLocation)(),n=O.useContext(N.WidgetContext).config.environmentVariables;return O.createElement(O.Fragment,null,O.createElement("style",null,"\n                .brf .modal.fade.do-not-center-in .modal-dialog {\n                    transform: none!important;\n                    top: auto;\n                }\n                .brf .modal.fade.do-not-center-in .modal-content {\n                    min-height: 460px;\n                }\n            "),O.createElement(q,b({},e,{location:t})),O.createElement(R.Route,{path:"*",component:function(e){return t=e.history,v=v||t,null;var t}}),O.createElement(R.Switch,null,O.createElement(R.Route,{path:["/Changepackage/Internet/Appointment","/Bundle/Internet/Appointment"]},O.createElement(ee,{title:"Appointment"})),O.createElement(R.Route,{path:["/Changepackage/Internet/Review","/Changepackage/TV/Review","/Add/TV/Review","/Bundle/Review"]},O.createElement(re,{title:"Review"})),O.createElement(R.Route,{path:["/Changepackage/Internet/Confirmation","/Changepackage/TV/Confirmation","/Add/TV/Confirmation","/Bundle/Confirmation"]},O.createElement(te,{title:"Confirmation"})),O.createElement(R.Route,{path:["/Changepackage/Internet","/Bundle/Internet"]},O.createElement(ne,{title:"Internet"})),O.createElement(R.Route,{path:["/Changepackage/TV","/Bundle/TV","/Add/TV"]},O.createElement(ie,{title:"fr"===n.language?"Configurez vos service - TV":"Set up your service - TV"})),O.createElement(R.Route,{path:"*"},O.createElement(R.Redirect,{to:e.defaultRoute}))),O.createElement(fe,b({},e,{location:t})),O.createElement(U,null),O.createElement($,null),O.createElement(ge,{id:"NAVIGATION_RESTRICTION_MODAL"}),O.createElement(J,null),O.createElement(j,null),O.createElement(M,null),O.createElement("div",{className:"spacer20 hidden-xs hidden-m","aria-hidden":"true"}),O.createElement("div",{className:"spacer60 hidden-xs hidden-m","aria-hidden":"true"}))},xe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return m(t,e),t.prototype.componentDidCatch=function(e){this.props.onErrorEncountered(e)},t.prototype.componentDidMount=function(){this.props.widgetRenderComplete("omf-changepackage-navigation")},t.prototype.render=function(){return O.createElement(R.BrowserRouter,{basename:"/Ordering"},O.createElement(be,b({},this.props)))},t}(O.Component),ye=(0,I.connect)(function(e){return{defaultRoute:e.defaultRoute,flowType:e.flowType}},function(e){return{onErrorEncountered:function(t){return e(Te(t))},widgetRenderComplete:function(){return e(he())}}})(xe),Ne=N.Components.ApplicationRoot,Oe=function(){return O.createElement(Ne,null,O.createElement(ye,null))},Ie=y.CommonFeatures.BaseConfig,Re=y.CommonFeatures.configProperty,Ce=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return m(t,e),p([Re(""),E("design:type",String)],t.prototype,"flowType",void 0),p([Re({}),E("design:type",Object)],t.prototype,"environmentVariables",void 0),p([Re({}),E("design:type",Object)],t.prototype,"mockdata",void 0),p([Re({}),E("design:type",Object)],t.prototype,"headers",void 0),p([Re({base:"http://127.0.0.1:8881"}),E("design:type",Object)],t.prototype,"api",void 0),p([Re(""),E("design:type",String)],t.prototype,"defaultRoute",void 0),p([Re({}),E("design:type",Object)],t.prototype,"linkURL",void 0),p([Re(""),E("design:type",String)],t.prototype,"isOneTrustDPEnabled",void 0),p([y.Injectable],t)}(Ie),ve=d(750),Ae=d(541),we=d(769),Le=(0,Ae.createAction)("SET_FLOW_TYPE",function(e){switch(sessionStorage.setItem("omf:Flowtype",e),e){case N.EFlowType.INTERNET:N.Omniture.useOmniture().updateContext({s_oSS2:"Internet"});break;case N.EFlowType.TV:N.Omniture.useOmniture().updateContext({s_oSS2:"Change package"});break;case N.EFlowType.ADDTV:N.Omniture.useOmniture().updateContext({s_oSS2:"Add TV"});break;case N.EFlowType.BUNDLE:N.Omniture.useOmniture().updateContext({s_oSS2:"Bundle"})}return e}),Pe=(0,Ae.createAction)("SET_FLOW_SUMMARY_TOTALS",function(e){var t=(0,N.ValueOf)(e,"priceOvertime",[]);return b(b({},e),{Internet:t.find(function(e){return"Internet"===e.flowType}),TV:t.find(function(e){return"TV"===e.flowType})})}),Ve=(0,Ae.createAction)("CHECK_NAVIGATION_RESTRICTIONS"),_e=function(e){function t(t,n){return e.call(this,t,n)||this}return m(t,e),p([y.Injectable,E("design:paramtypes",[y.AjaxServices,Ce])],t)}(N.BaseClient),Se=d(418),ke=N.Actions.showHideLoader,We=N.Actions.continueFlow,Me=N.Actions.historyBack,Fe=N.Actions.historyForward,Ue=N.Actions.historyGo,Be=N.Actions.openLightbox,Ge=N.Actions.applicationExit,De=N.Actions.applicationLogout,je=N.Actions.setWidgetStatus,ze=N.Actions.applicationReset,qe=N.Actions.closeLightbox,Ke=N.Actions.onContinue,Xe=function(){function e(e,t){this.client=e,this.config=t}return e.prototype.combineEpics=function(){return(0,we.combineEpics)(this.historyGoEpic,this.historyForwardEpic,this.historyBackEpic,this.onContinueEpic,this.applicationExitEpic,this.applicationLogoutEpic,this.checkRestrictionsEpic,this.applicationResetEpic)},Object.defineProperty(e.prototype,"checkRestrictionsEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Se.filter)(function(e){return e.type===Ve.toString()}),(0,Se.filter)(function(e){var t=e.payload;return Boolean(t)}),(0,Se.mergeMap)(function(t){var n=t.payload;return(0,Se.concat)([je(e.widgetState=N.EWidgetStatus.UPDATING)],e.client.action(n).pipe((0,Se.mergeMap)(function(e){return(0,N.FilterRestrictionObservable)(e,[Ue(e.data.redirectURLKey)])})))}),(0,Se.catchError)(N.Models.ErrorHandlerObservable(Ve)))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"historyGoEpic",{get:function(){return function(e,t){return e.pipe((0,Se.filter)(function(e){return e.type===Ue.toString()}),(0,Se.filter)(function(e){return"string"==typeof e.payload}),(0,Se.mergeMap)(function(e){var n,r,i=e.payload,a=t.value.flowType,o=i,c="";switch(i){case"APPOINTMENT":case N.EWidgetRoute.APPOINTMENT:o=N.Utils.constructPageRoute(N.EWidgetRoute.APPOINTMENT),c=N.EWidgetRoute.APPOINTMENT;break;case"INTERNET_REVIEW":case"TV_REVIEW":case"BUNDLE_REVIEW":case"REVIEW":case N.EWidgetRoute.REVIEW:o=N.Utils.constructPageRoute(N.EWidgetRoute.REVIEW),c=N.EWidgetRoute.REVIEW;break;case"INTERNET_CONFIRMATION":case"TV_CONFIRMATION":case"BUNDLE_CONFIRMATION":case"CONFIRMATION":case N.EWidgetRoute.CONFIRMATION:o=N.Utils.constructPageRoute(N.EWidgetRoute.CONFIRMATION),c=N.EWidgetRoute.CONFIRMATION;break;case"ADD_TV_REVIEW":o=N.Utils.constructPageRoute(N.EWidgetRoute.REVIEW,N.EFlowType.ADDTV),c=N.EWidgetRoute.REVIEW;break;case"INTERNET_CP":case N.EWidgetRoute.INTERNET:o=N.Utils.constructPageRoute(N.EWidgetRoute.INTERNET),c=N.EWidgetRoute.INTERNET;break;case"TV_CP":case N.EWidgetRoute.TV:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV),c=N.EWidgetRoute.TV;break;case"ADD_TV":o=N.Utils.constructPageRoute(N.EWidgetRoute.TV,N.EFlowType.ADDTV),c=N.EWidgetRoute.TV,a=N.EFlowType.ADDTV;break;case"BUNDLE_TV":o=N.Utils.constructPageRoute(N.EWidgetRoute.TV,N.EFlowType.BUNDLE),c=N.EWidgetRoute.TV,a=N.EFlowType.BUNDLE;break;case N.Volt.EDIsplayGroupKey.BASE_PROGRAMMING:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_Packages,c=N.EWidgetRoute.TV;break;case N.Volt.EDIsplayGroupKey.ADD_ON:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_Addons,c=N.EWidgetRoute.TV;break;case N.Volt.EDIsplayGroupKey.ALACARTE:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_Alacarte,c=N.EWidgetRoute.TV;break;case N.Volt.EDIsplayGroupKey.MOVIE:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_MoviesSeries,c=N.EWidgetRoute.TV;break;case N.Volt.EDIsplayGroupKey.INTERNATIONAL:case N.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_InternationalCombos,c=N.EWidgetRoute.TV;break;case N.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_InternationalAlacarte,c=N.EWidgetRoute.TV;break;case N.Volt.EDIsplayGroupKey.TV_BROWSE_ALL:o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_Browse,c=N.EWidgetRoute.TV;break;case"TV_SEARCH":o=N.Utils.constructPageRoute(N.EWidgetRoute.TV)+N.EWidgetRoute.TV_Search,c=N.EWidgetRoute.TV;break;case"INTERNET_OVERVIEW":case"TV_OVERVIEW":return[Ge()]}return N.Utils.getPageRoute()===N.EWidgetRoute.TV&&c===N.EWidgetRoute.TV?(n=A,window.requestAnimationFrame(function(){return n.push(o.replace(/\/Ordering|\/Changepackage|\/TV|\/Add\b|\/Bundle/gi,""))}),[ke(null)]):N.Utils.getPageRoute()===N.EWidgetRoute.INTERNET&&c===N.EWidgetRoute.INTERNET?[ke(null)]:(r=f(),window.requestAnimationFrame(function(){return r.push(o)}),[Le(a)])}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"historyForwardEpic",{get:function(){return function(e,t){return e.pipe((0,Se.filter)(function(e){return e.type===Fe.toString()||e.type===We.toString()}),(0,Se.mergeMap)(function(){var e,n,r=f(),i=t.value,a=i.routes,o=(0,N.ValueOf)(i,"summary.nextAction",{});switch(!0){case Boolean(o.href):return[Ve(o)];case Boolean(o.redirectURLKey):return[Ue(o.redirectURLKey)];default:return e=r.location.pathname,(n=a.findIndex(function(t){return e.indexOf(t)>-1}))===a.length-1||(e=a[n+=1],r.push(e)),[]}}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onContinueEpic",{get:function(){return function(e,t){return e.pipe((0,Se.filter)(function(e){return e.type===Ke.toString()}),(0,Se.mergeMap)(function(){var e,n,r,i=f(),a=t.value,o=a.routes,c=(0,N.ValueOf)(a,"summary.nextAction",{});return c.redirectURLKey?(0,Se.of)(Ue(c.redirectURLKey)):c.href?(0,Se.of)(Ve(c)):(e=i.location.pathname,(n=o.findIndex(function(t){return e.indexOf(t)>-1}))>=0&&n<o.length-1?(r=o[n+1],i.push(r),(0,Se.of)()):N.Utils.getPageRoute()===N.EWidgetRoute.REVIEW?(0,Se.of)(Ue(N.EWidgetRoute.CONFIRMATION)):(0,Se.of)(Ue(N.EWidgetRoute.REVIEW)))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"historyBackEpic",{get:function(){return function(e,t){return e.pipe((0,Se.filter)(function(e){return e.type===Me.toString()}),(0,Se.mergeMap)(function(e){var n,r,i=e.payload,a=f(),o=t.value,c=o.routes,s=(0,N.ValueOf)(o,"summary.backAction",{});switch(!0){case Boolean(s.href):return[Ve(s)];case Boolean(s.redirectURLKey):return"INTERNET_OVERVIEW"===s.redirectURLKey||"TV_OVERVIEW"===s.redirectURLKey?[ke(!1),Be({lightboxId:G,data:{relativeId:i}})]:[Ue(s.redirectURLKey)];default:return n=(f().location.pathname||"").replace(/\/Ordering|\/Packages|\/Movies|\/Addons|\/Alacarte|\/International|\/Combos|\/Browse|\/Search/i,""),0===(r=c.findIndex(function(e){return n===e}))?[ke(!1),Be({lightboxId:G,data:{relativeId:i}})]:(r-=1,n&&((n=c[r]).indexOf("Appointment")>-1&&!sessionStorage.getItem("omf:hasAppointmentRoute")&&(n=c[r-=1]),a.push(n)),[])}}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"applicationExitEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Se.filter)(function(e){return e.type===Ge.toString()}),(0,Se.mergeMap)(function(){return sessionStorage.clear(),window.location=(0,N.ValueOf)(e.config,"linkURL.exitURL",""),[ke(!0)]}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"applicationResetEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Se.filter)(function(e){return e.type===ze.toString()}),(0,Se.mergeMap)(function(){var t=n.value,r=(0,N.ValueOf)(t,"summary.resetAction",{});return(0,Se.concat)([ke(!0)],e.client.post(r.href,r.messageBody).pipe((0,Se.mergeMap)(function(e){return[qe("APPLICATION_RESET"),window.location.reload()]})))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"applicationLogoutEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Se.filter)(function(e){return e.type===De.toString()}),(0,Se.mergeMap)(function(){return sessionStorage.clear(),window.location=(0,N.ValueOf)(e.config,"linkURL.logoutURL",""),[ke(!0)]}))}},enumerable:!1,configurable:!0}),p([y.Injectable,E("design:paramtypes",[_e,Ce])],e)}(),He=N.Actions.setWidgetStatus,Je=function(){function e(e){this.navigationEpics=e}return e.prototype.combineEpics=function(){return(0,we.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.pipe((0,Se.filter)(function(e){return e.type===He.toString()}),(0,Se.filter)(function(e){return e.payload===N.EWidgetStatus.INIT}),(0,Se.mergeMap)(function(){return[Le(N.Utils.getFlowType()),He(N.EWidgetStatus.RENDERED)]}))}},enumerable:!1,configurable:!0}),p([y.Injectable,E("design:paramtypes",[Xe])],e)}(),Ye=y.CommonFeatures.BaseStore,Qe=y.CommonFeatures.actionsToComputedPropertyName,Ze=Qe(N.Actions).setWidgetProps,$e=Qe(T),et=$e.setFlowType,tt=$e.setSummaryTotals,nt=function(e){function t(t,n,r,i){var a=e.call(this,n)||this;return a.client=t,a.epics=r,a.localization=i,a}return m(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,r;return(0,ve.combineReducers)(b(b(b(b({},N.Reducers.WidgetBaseLifecycle(this.localization)),N.Reducers.WidgetLightboxes()),N.Reducers.WidgetRestrictions()),{defaultRoute:(0,Ae.handleActions)((e={},e[Ze]=function(e,t){var n=t.payload;return n&&n.defaultRoute||e},e),"/"),flowType:(0,Ae.handleActions)((t={},t[Ze]=function(e,t){var n=t.payload;return n&&n.flowType||e},t[et]=function(e,t){var n=t.payload;return n&&n||e},t),""),routes:(0,Ae.handleActions)((n={},n[Ze]=function(e,t){var n=t.payload;return n&&n.flowType&&L[n.flowType]||e},n[et]=function(e,t){var n=t.payload;return n&&L[n]||e},n),[]),summary:(0,Ae.handleActions)((r={},r[tt]=function(e,t){var n=t.payload;return n&&n||e},r),{})}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,we.combineEpics)(this.epics.navigationEpics.combineEpics(),this.epics.combineEpics(),(new N.ModalEpics).combineEpics(),new N.RestricitonsEpics(this.client,"NAVIGATION_RESTRICTION_MODAL").combineEpics(),(new N.LifecycleEpics).combineEpics())},enumerable:!1,configurable:!0}),p([y.Injectable,E("design:paramtypes",[_e,y.Store,Je,_])],t)}(Ye),rt=y.CommonFeatures.BasePipe,it=function(e){function t(n){var r=e.call(this,n)||this;return t.instance=r,r}return m(t,e),t.Subscriptions=function(e){var t;return(t={})[N.Actions.historyGo.toString()]=function(t){var n=t.payload;t.meta,e.dispatch(N.Actions.historyGo(n))},t[N.Actions.historyBack.toString()]=function(){e.dispatch(N.Actions.historyBack())},t[N.Actions.historyForward.toString()]=function(){e.dispatch(N.Actions.historyForward())},t[N.Actions.onContinue.toString()]=function(){e.dispatch(N.Actions.onContinue())},t[N.Actions.acceptRestriction.toString()]=function(t){var n,r,i=t.payload;i&&i.redirectURLKey?e.dispatch(N.Actions.historyGo(i.redirectURLKey)):(n=document.querySelector("#PREVIEW_MODAL"),r=document.querySelector("#NAVIGATION_RESTRICTION_MODAL"),n&&n.classList.contains("show")||r&&r.classList.contains("show")?(e.dispatch(N.Actions.closeLightbox("PREVIEW_MODAL")),e.dispatch(N.Actions.closeLightbox("NAVIGATION_RESTRICTION_MODAL")),e.dispatch(N.Actions.historyGo("REVIEW"))):e.dispatch(N.Actions.historyGo("REVIEW")))},t[N.Actions.applicationExit.toString()]=function(){e.dispatch(N.Actions.applicationExit())},t[N.Actions.applicationLogout.toString()]=function(){e.dispatch(N.Actions.applicationLogout())},t[N.Actions.refreshTotals.toString()]=function(){e.dispatch(N.Actions.refreshTotals())},t[N.Actions.setProductConfigurationTotal.toString()]=function(t){var n=t.payload;e.dispatch(Pe(n))},t[N.Actions.closeLightbox.toString()]=function(t){var n=t.payload;e.dispatch(N.Actions.closeLightbox(n))},t[N.Actions.setHistoryProvider.toString()]=function(e){var t=e.payload;A=t||A},t[N.Actions.setAppointmentVisited.toString()]=function(){w=!0},t},t}(rt),at=N.Actions.setWidgetProps,ot=N.Actions.setWidgetStatus,ct=function(e){function t(t,n,r,i){var a=e.call(this)||this;return a.store=t,a.params=n,a.config=r,a.pipe=i,a}return m(t,e),t.prototype.init=function(){this.pipe.subscribe(it.Subscriptions(this.store)),this.store.dispatch(at(this.config)),this.store.dispatch(at(this.params.props)),this.store.dispatch(ot(N.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store,n=window.location.pathname.split("/");e.render(O.createElement(N.ContextProvider,{value:{config:this.config,mode:"/".concat(n[n.length-1])}},O.createElement(I.Provider,{store:t},O.createElement(Oe,null))))},p([(0,y.Widget)({namespace:"Ordering"}),E("design:paramtypes",[nt,y.ParamsProvider,Ce,it])],t)}(y.ViewWidget),st=ct,g}()});
//# sourceMappingURL=widget.js.map