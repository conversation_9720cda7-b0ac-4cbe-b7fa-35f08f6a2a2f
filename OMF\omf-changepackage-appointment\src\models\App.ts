import { Models } from "omf-changepackage-components";
import { EPreferredContactMethod, EDuration } from "./Enums";

export interface IAppProps extends Models.IBaseAppProps { }

export interface IAppointmentAPIResponse {
  appointment: IAppointment;
}

export interface IAppointment {
  availableDates?: Array<IAvailableDates>;
  preferredDate?: IAvailableDates;
  duration?: EDuration;
  installationAddress?: IInstallationAddress;
  contactInformation?: IContactInformation;
  additionalDetails?: IAdditionalDetails;
  isInstallationRequired?: boolean;
}

export interface IAvailableDates {
  date: string;
  timeSlots: Array<ITimeSlots>;
  isPreferredDate?: boolean;
}

export interface IInstallationAddress {
  address1?: string;
  address2?: string;
  city: string;
  province: string;
  postalCode: string;
  apartmentNumber: string;
  apartmentType: string;
}

export interface IContactInformation {
  preferredContactMethod?: EPreferredContactMethod;
  primaryPhone?: IPrimaryPhone;
  additionalPhone?: IAdditionalPhone;
  textMessage?: string;
  email?: string;
}

export interface IAdditionalPhone {
  phoneNumber: string;
  phoneExtension: string;
}

export interface ITimeSlots {
  intervalType: EDuration;
  timeInterval: ITimeInterval;
  isAvailable: boolean;
  isSelected: boolean;
  duration: string;
}

export interface ITimeInterval {
  startTime: number;
  endTime: number;
}

export interface IFormData {
  dateAndTime: string;
  PREFERED_METHOD_OF_CONTACT: string;
  EMAIL: string;
  TEXT_MESSAGE: string;
  PHONE: string;
  ADDITIONAL_PHONE_NUMBER: string;
  ADDITIONAL_PHONE_EXT: string;
  APPARTMENT: string;
  ENTRY_CODE: string;
  SUPERINTENDANT_NAME: string;
  SUPERINTENDANT_PHONE: string;
  INFORMED_SUPERINTENDANT: string;
  SPECIAL_INSTRUCTIONS: string;
}

export interface IAdditionalDetails {
  apartment: string;
  entryCode: string;
  specialInstructions: string;
  superintendantName: string;
  superintendantPhone: string;
  informedSuperintendant: boolean;
}

export interface IPrimaryPhone {
  phoneNumber: string;
  phoneExtension?: string;
}
