{"name": "omf-changepackage-tv", "version": "0.1.0", "description": "Virgin ordering flow tv selection and changeflow widget", "main": "dist/widget.js", "private": true, "scripts": {"dev": "webpack -w", "build": "webpack", "build:prod": "webpack --env -p", "build:dev": "webpack --env -d", "clean": "rm -rf ./node_modules & rm -rf ./dist & rm -rf ./package-lock.json & rm -rf ./yarn.lock", "lint": "tslint -p src/tsconfig.json -c tslint.json -t stylish --fix && tsc -p src --pretty --noEmit"}, "keywords": [], "author": "BELL", "license": "MIT", "devDependencies": {"@types/js-search": "^1.4.0", "@types/react-redux": "^7.1.5", "@types/react-router": "^5.1.2", "@types/react-router-dom": "^5.1.2", "bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "omf-changepackage-components": "file:../omf-changepackage-components", "husky": "4.3.8", "ajv": "^7.1.1"}, "peerDependencies": {"bwtk": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "rxjs": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "omf-changepackage-components": "*", "react-router-dom": "^5.1.2"}, "dependencies": {"query-string": "^6.12.1"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}