@import "mixins";
.bell-slider {
    .slick-prev {
        &:hover:before,
        &:focus:before {
            border-color: none;
        }
    }
    .slick-next {
        &:hover:before,
        &:focus:before {
            border-color: none;
        }
    }
    .slick-prev:before,
    .slick-next:before {
        background: none;
        border: none;
        border-radius: 0
    }
    .slick-prev {
        left: -45px;
    }
    [dir='rtl'] .slick-prev {
        right: -45px;
    }
    .slick-next {
        right: -45px;
    }
    [dir='rtl'] .slick-next {
        left: -45px;
    }
    .slick-dots {
        li {
            width: 10px;
            height: 10px;
            button {
                background: #cccccc;
                border: 2px solid #979797;
                &:hover,
                &:focus {
                    border: 2px solid #00549a;
                }
            }
            &.slick-active button {
                background: #979797;
            }
        }
    }
}