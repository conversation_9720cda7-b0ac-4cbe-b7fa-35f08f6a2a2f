{"version": 3, "file": "widget.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAAjD,IAMMC,EACIC,EANT,GAAsB,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUH,EAAQK,QAAQ,QAASA,QAAQ,gCAAiCA,QAAQ,SAAUA,QAAQ,eAAgBA,QAAQ,oBAAqBA,QAAQ,cAAeA,QAAQ,SAAUA,QAAQ,iBAAkBA,QAAQ,oBAAqBA,QAAQ,aAAcA,QAAQ,cACpR,GAAqB,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,OAAQ,+BAAgC,QAAS,cAAe,mBAAoB,aAAc,QAAS,gBAAiB,mBAAoB,YAAa,QAASN,QAG9K,IAAQE,KADJD,EAAuB,iBAAZE,QAAuBH,EAAQK,QAAQ,QAASA,QAAQ,gCAAiCA,QAAQ,SAAUA,QAAQ,eAAgBA,QAAQ,oBAAqBA,QAAQ,cAAeA,QAAQ,SAAUA,QAAQ,iBAAkBA,QAAQ,oBAAqBA,QAAQ,aAAcA,QAAQ,SAAWL,EAAQD,EAAW,KAAGA,EAAiC,2BAAGA,EAAY,MAAGA,EAAiB,WAAGA,EAAqB,eAAGA,EAAgB,UAAGA,EAAY,MAAGA,EAAmB,aAAGA,EAAsB,gBAAGA,EAAe,SAAGA,EAAW,OACvf,iBAAZI,QAAuBA,QAAUJ,GAAMG,GAAKD,EAAEC,EAEvE,CATD,CASGM,KAAM,SAASC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAiCC,GACrW,O,wBCNA,SAASC,EAAoBC,GAA7B,IAOKjB,EALAkB,EAAeC,GAAyBF,GAC5C,YAAqBG,IAAjBF,EACIA,EAAanB,SAGjBC,EAASmB,GAAyBF,GAAY,CAGjDlB,QAAS,CAAC,GAIXsB,GAAoBJ,GAAUjB,EAAQA,EAAOD,QAASiB,GAG/ChB,EAAOD,QACf,CCCO,SAASuB,EAAUC,EAAGC,GAI3B,SAASC,IAAOC,KAAKC,YAAcJ,CAAG,CAHtC,GAAiB,mBAANC,GAA0B,OAANA,EAC3B,MAAM,IAAII,UAAU,uBAAyBC,OAAOL,GAAK,iCAC7DM,EAAcP,EAAGC,GAEjBD,EAAEQ,UAAkB,OAANP,EAAaQ,OAAOC,OAAOT,IAAMC,EAAGM,UAAYP,EAAEO,UAAW,IAAIN,EACjF,CAyBO,SAASS,EAAWC,EAAYC,EAAQC,EAAKC,GAA7C,IACsHf,EAE7GzB,EAFVyC,EAAIC,UAAUC,OAAQC,EAAIH,EAAI,EAAIH,EAAkB,OAATE,EAAgBA,EAAON,OAAOW,yBAAyBP,EAAQC,GAAOC,EACrH,GAAuB,iBAAZM,SAAoD,mBAArBA,QAAQC,SAAyBH,EAAIE,QAAQC,SAASV,EAAYC,EAAQC,EAAKC,QACpH,IAASxC,EAAIqC,EAAWM,OAAS,EAAG3C,GAAK,EAAGA,KAASyB,EAAIY,EAAWrC,MAAI4C,GAAKH,EAAI,EAAIhB,EAAEmB,GAAKH,EAAI,EAAIhB,EAAEa,EAAQC,EAAKK,GAAKnB,EAAEa,EAAQC,KAASK,GAChJ,OAAOH,EAAI,GAAKG,GAAKV,OAAOc,eAAeV,EAAQC,EAAKK,GAAIA,CAC9D,CAmDO,SAASK,EAAWC,EAAaC,GACtC,GAAuB,iBAAZL,SAAoD,mBAArBA,QAAQM,SAAyB,OAAON,QAAQM,SAASF,EAAaC,EAClH,CAoEO,SAASE,EAAOC,EAAGC,GAAnB,IAGDvD,EAAe4C,EAAGY,EAASC,EAF3BC,EAAsB,mBAAXC,QAAyBL,EAAEK,OAAOC,UACjD,IAAKF,EAAG,OAAOJ,EACXtD,EAAI0D,EAAEG,KAAKP,GAAOE,EAAK,GAC3B,IACI,WAAc,IAAND,GAAgBA,KAAM,MAAQX,EAAI5C,EAAE8D,QAAQC,MAAMP,EAAGQ,KAAKpB,EAAEqB,MACxE,CACA,MAAOC,GAAST,EAAI,CAAES,MAAOA,EAAS,CACtC,QACI,IACQtB,IAAMA,EAAEmB,OAASL,EAAI1D,EAAU,SAAI0D,EAAEG,KAAK7D,EAClD,CACA,QAAU,GAAIyD,EAAG,MAAMA,EAAES,KAAO,CACpC,CACA,OAAOV,CACT,CAkBO,SAASW,EAAcC,EAAIC,EAAMC,GACtC,GAAIA,GAA6B,IAArB5B,UAAUC,OAAc,IAAK,IAA4Ba,EAAxBxD,EAAI,EAAGuE,EAAIF,EAAK1B,OAAY3C,EAAIuE,EAAGvE,KACxEwD,GAAQxD,KAAKqE,IACRb,IAAIA,EAAKgB,MAAMvC,UAAUwC,MAAMZ,KAAKQ,EAAM,EAAGrE,IAClDwD,EAAGxD,GAAKqE,EAAKrE,IAGrB,OAAOoE,EAAGM,OAAOlB,GAAMgB,MAAMvC,UAAUwC,MAAMZ,KAAKQ,GACpD,CCrNO,SAASM,EAAWC,GACzB,IAAMC,EAAiBD,EAASE,IAAI,SAAAC,GAClC,IAAMC,EAASD,EAAQE,gBAAgBC,KAAK,SAAAC,GAAQ,MAAc,aAAdA,EAAKC,IAAL,GACpD,OAAO,OACFL,GAAO,CACVM,SAAUL,EAASA,EAAOf,MAAQ,IAEtC,IACAqB,EAAS,IAAI,SAAgB,OACtBC,SAAS,QAChBD,EAAOC,SAAS,iBAChBD,EAAOC,SAAS,YAGhBD,EAAOE,aAAaX,EACtB,CCuCA,SAASY,EAAgCC,GAEvC,SAASC,EAA0BZ,GAC7BA,EAAQa,sBAAwB,EAAAC,KAAKC,qBAAqBC,SAC5DC,EAAgBC,QAAQlB,EAAQmB,IAAM,IACtCC,EAAWnC,KAAKe,GAChBiB,EAAgBhC,KAAKe,EAAQmB,IAEjC,CAPA,IAAMF,EAAiC,GAAIG,EAAgC,GAe3E,OAPAT,EAAiBU,QAAQ,SAAAC,GACnB7B,MAAM8B,QAAQD,EAASE,gBACzBF,EAASE,eAAeH,QAAQT,GAEhCA,EAA0BU,EAE9B,GACOF,CACT,CAEO,SAASK,EAAiBC,GAA1B,IACCC,GAAmD,IAAAC,SACvDF,EACA,8CACA,IACAvB,KAAK,SAAC0B,GAAsC,MAAyB,OAAzBA,EAAMC,cAAN,GACxCC,GAAoD,IAAAH,SACxDD,EACA,mBACA,IAEIK,EAAYD,EACfE,OACC,SAAAX,GACE,OAAAA,EAASY,iBACTZ,EAASY,kBAAoB,EAAApB,KAAKqB,iBAAiBC,IADnD,GAGHC,OAAO,SAACjB,EAAiBE,GACxB,IAAM9D,EAAM8D,EAASY,gBAGrB,OAFAd,EAAW5D,GAAO4D,EAAW5D,IAAQ,GACrC4D,EAAW5D,GAAKyB,KAAKqC,GACdF,CACT,EAAG,CAAC,GACAkB,EAAsB,CAC1BC,MAAOR,EACPC,UAAS,EACTnC,SAAUa,EAAgCqB,IAW5C,OAJCO,EAAyB,QAAoB,IAAhBE,KAAKC,SAGnC7C,EAAW0C,EAAQzC,UACZyC,CACT,CCpHO,SAASI,EAAsBC,GACpC,OAAO,IAAAf,SAAqCe,OAAiBpG,EAAW,IAAI8F,OAC1E,SAACO,EAAMC,GAIL,OAHIA,EAAexC,OACjBuC,EAAKC,EAAexC,OAASwC,EAAe3D,OAAS,IAAI4D,QAEpDF,CACT,EAAG,CAAC,EAER,CAYO,SAASG,EAAqBf,GACnC,OAAO,IAAAJ,SAAsCI,OAAWzF,EAAW,IAAI8F,OACrE,SAACW,EAAK1B,GACI,IAAA2B,EAAaP,EAAsBpB,EAASpB,iBAAgB,SASpE,OARIgD,QAAQD,IACVA,EAASE,MAAM,KACZpD,IAAI,SAACP,GAAc,OAAAA,EAAEsD,MAAF,GACnBb,OAAOiB,SACP7B,QAAQ,SAAC7B,GACJwD,EAAI9B,QAAQ1B,GAAK,GAAGwD,EAAI/D,KAAKO,EACnC,GAEGwD,EAAII,MACb,EAAG,IACHnB,OAAOiB,SAASE,MACpB,CAmBO,SAASC,EAAerB,EAAyCiB,GACtE,OAAOjB,EAAUC,OACf,SAAAX,GAAY,OAAEoB,EAAsBpB,EAASpB,iBAAkB+C,UAAY,IAAI/B,QAAQ+B,IAAa,CAAxF,EAEhB,CAEO,SAASK,EAActB,EAAyCuB,GACrE,YADqE,IAAAA,IAAAA,EAAA,QAC9D,IAAA3B,SAAsCI,OAAWzF,EAAW,IAAI6G,KACrE,SAACpI,EAAG2B,GAAM,QACP,IAAAiF,SAAgBc,EAAsB1H,EAAEkF,iBAAkB,eAAgB,IACjE,IAAA0B,SAAgBc,EAAsB/F,EAAEuD,iBAAkB,eAAgB,KAC/D,QAAdqD,EAAsB,GAAK,EAH1B,EAMd,CAEO,SAASC,EAAoBC,GAClC,OAAOP,QAAQO,GAAQA,EAAKN,MAAM,KAAKpD,IAAI,SAAA2D,GACzC,IAAMC,EAAWD,EAAEZ,OACnB,OAAOc,GAAaC,mBAAmBF,EACzC,GAAGG,KAAK,MAAQL,CAClB,CCrDO,SAASM,IACd,IAAK,IAAMvG,KAAOwG,GACZA,GAAaxG,IAAOwG,GAAaxG,GAAqByG,UAG5DxE,MAAMH,KAAK4E,SAASC,iBAAiB,qBAAqB9C,QAAQ,SAAA+C,GAAM,OAAAA,EAAGC,QAAH,GACxEL,GAAe,CAAC,CAClB,CCjCO,SAASM,EAAcC,EAA2BC,GAAlD,IACCC,EAAMF,EAAIG,QACVC,EAAUF,EAAIG,cACZC,EAAiBJ,EAAG,aACtBK,EAAOH,EAAQI,wBACfC,EAAWF,EAAKG,KAAOJ,EAC7BL,EAAS,CAAEU,WAAYF,EAAUG,QAASH,EAAWF,EAAKM,MAAQ,GAAK,QACzE,CCPO,SAASC,EAAcC,GAC5B,OAAOC,KAAKC,MAAMD,KAAKE,UAAUH,GACnC,CC2DA,SAASI,IAGP,OAAOL,EAAcM,GACvB,CAEA,SAASC,EAAgBC,EAAoBzF,GAC3C,IAAMmC,EAAQsD,EAAI3E,QAAQd,GAG1B,OAFImC,GAAS,EAAGsD,EAAIC,OAAOvD,EAAO,GAC7BsD,EAAI5G,KAAKmB,GACPyF,CACT,CAEO,SAASE,EAAoBC,GAA7B,IACC,IAAsB,WAAeA,GAAiBN,KAAsB,GAA3EzD,EAAM,KAAEgE,EAAS,KAClBC,EAAgC,CACpCC,YAAa,SAACC,GAAkB,OAAAH,EAAU,OAAKhE,GAAM,CAAEmE,MAAOR,EAAgB3D,EAAOmE,MAAOA,KAA5D,EAChCC,SAAU,SAACD,GAAmB,OAAAH,EAAU,OAAKhE,GAAM,CAAEmE,MAAOA,EAAQ,CAACA,GAAS,KAAhD,EAC9BE,eAAgB,SAACrD,GAAqB,OAAAgD,EAAU,OAAKhE,GAAM,CAAEgB,SAAU2C,EAAgB3D,EAAOgB,SAAUA,KAAlE,EACtCsD,YAAa,SAACtD,GAAqB,OAAAgD,EAAU,OAAKhE,GAAM,CAAEgB,SAAU,CAACA,KAAlC,EAEnCuD,eAAgB,WAAM,OAAAP,EAAU,OAAKhE,GAAM,CAAEwE,YAAaxE,EAAOwE,aAA3C,EACtBC,WAAY,WAAM,OAAAT,EAAU,OAAKhE,GAAM,CAAE0E,QAAS1E,EAAO0E,SAAvC,EAClBC,MAAO,WAAM,OAAAX,EAAUP,IAAV,EAEbmB,QAAS,SAACC,GAAiB,OAAAb,EAAU,OAChChE,GAAM,CAAE8E,OAAQD,EAAME,UACf/E,EAAO8E,SAAWD,EACK,SAArB7E,EAAO+E,UAAuB,MAAQ,OACpC,QAJW,EAO3BC,SAAU,SAACb,GAAmB,OAAAA,EAAQnE,EAAOmE,MAAMlF,QAAQkF,IAAU,EAA4B,IAAxBnE,EAAOmE,MAAMxI,MAAxD,EAC9BsJ,UAAW,SAACd,GAAkB,OAAAnE,EAAOmE,MAAMlF,QAAQkF,IAAU,GAA6B,IAAxBnE,EAAOmE,MAAMxI,MAAjD,EAC9BuJ,YAAa,SAAClE,GAAqB,OAAAhB,EAAOgB,SAAS/B,QAAQ+B,IAAa,CAArC,EACnCmE,YAAa,WAAM,OAAAnF,EAAOwE,UAAP,EACnBY,QAAS,WAAM,OAAApF,EAAO0E,MAAP,EACfW,UAAW,WAAM,OAAArF,EAAO8E,OAAS9E,EAAO+E,SAAvB,EACjBO,cAAe,WAAM,OAAAtF,EAAOmE,MAAM,IAAM,KAAnB,EAErBoB,SAAU,WAAM,OAACnC,EAAcpD,EAAf,EAChBwF,SAAU,SAACC,GAAU,OAAAzB,EAAUZ,EAAcqC,GAAxB,GAEvB,MAAO,CAACzF,EAAQiE,EAClB,CAEO,SAASyB,EAAYzI,GAC1B,IAAMqF,EAAM,SAAa,MAIzB,OAHA,YAAgB,WACdA,EAAIG,QAAUxF,CAChB,EAAG,CAACA,IACGqF,EAAIG,OACb,C,QPjGIzH,EAeO2K,EA0OPC,E,oBCnQAtH,EOASuH,EACAC,EACAC,EACAC,EACAC,EAEAC,GACAC,G,GCVLC,GAAYC,GA4BpB,GClBA,GCIEC,GACAC,GAIF,GCNE,GACAC,GACAC,GAIF,GCJA,GCVEC,GACAC,GAQF,GCPQ,GAKR,GCPQC,GAGR,GCGQC,GAAWC,GACb,GACJ,GACA,GACA,GACA,GAIAC,GAIF,GCnBEC,GAUWC,GAEPC,GAqDN,GCjEQC,GAASC,GAOXC,GAmDN,GCxDE,GACA,GAcW,GAOPC,GAwBAC,GAKA,GAoHN,GhBzJIxF,GAUJ,GA6FayF,GiB/GL,GAYK,GAyKb,GC1LE,GAaW,GAmGPC,GAON,GCxHaC,GCAAC,GCQP,GA2BN,GlBMMjE,GA0HOkE,GAQAC,GmB3KAC,GCWL,GAwBFC,GAqBAC,GA8KO,GAwOb,GC5cQ,GAcK,GAEPC,GAIA,GAsGAC,GAQN,GCzHa,GAiEb,GCtEa,GAqCb,GClCMC,GAKO,GCXAC,GCLA,GCmBA,GA+Cb,GCxDa,GAyBb,GC1Ba,GAiHb,GCzHE,GAeIC,GA0BO,GA6Bb,GCjEQ,GAaK,GAkJb,GC3Ja,GAuBb,GChCQ,GAEJC,GA+EJ,GCnFEC,GACA,GAOIC,GAqCA,GA8DOC,GC/FXC,GAIA,GACAC,GACA,GAaIC,GAoEN,GAuBaC,GC5HXC,GAGWC,GCFLC,GAUR,GCJEC,GACA,GAEIC,GAGN,G,sBClBAhQ,EAAOD,QAAUe,C,kBCAjBd,EAAOD,QAAUM,C,kBCAjBL,EAAOD,QAAUgB,C,kBCAjBf,EAAOD,QAAUW,C,kBCAjBV,EAAOD,QAAUQ,C,kBCAjBP,EAAOD,QAAUO,C,kBCAjBN,EAAOD,QAAUa,C,kBCAjBZ,EAAOD,QAAUU,C,kBCAjBT,EAAOD,QAAUY,C,kBCAjBX,EAAOD,QAAUc,C,kBCAjBb,EAAOD,QAAUS,C,GzDCbW,GAA2B,CAAC,E,O0DAhCH,EAAoBO,EAAI,SAASxB,EAASkQ,GACzC,IAAI,IAAI5N,KAAO4N,EACXjP,EAAoBoC,EAAE6M,EAAY5N,KAASrB,EAAoBoC,EAAErD,EAASsC,IAC5EL,OAAOc,eAAe/C,EAASsC,EAAK,CAAE6N,YAAY,EAAMC,IAAKF,EAAW5N,IAG3E,ECPArB,EAAoBoC,EAAI,SAASgN,EAAKzE,GAAQ,OAAO3J,OAAOD,UAAUsO,eAAe1M,KAAKyM,EAAKzE,EAAO,ECCtG3K,EAAoB0B,EAAI,SAAS3C,GACX,oBAAX0D,QAA0BA,OAAO6M,aAC1CtO,OAAOc,eAAe/C,EAAS0D,OAAO6M,YAAa,CAAEvM,MAAO,WAE7D/B,OAAOc,eAAe/C,EAAS,aAAc,CAAEgE,OAAO,GACvD,E,mU3DUIjC,EAAgB,SAASP,EAAGC,GAI9B,OAHAM,EAAgBE,OAAOuO,gBAClB,CAAEC,UAAW,cAAgBlM,OAAS,SAAU/C,EAAGC,GAAKD,EAAEiP,UAAYhP,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAIiP,KAAKjP,EAAOQ,OAAOD,UAAUsO,eAAe1M,KAAKnC,EAAGiP,KAAIlP,EAAEkP,GAAKjP,EAAEiP,GAAI,EAC7F3O,EAAcP,EAAGC,EAC1B,EAUWiL,EAAW,WAQpB,OAPAA,EAAWzK,OAAO0O,QAAU,SAAkBnI,GAAlB,IACfoI,EAAG7Q,EAAOuD,EAENoN,EAFb,IAAY3Q,EAAI,EAAGuD,EAAIb,UAAUC,OAAQ3C,EAAIuD,EAAGvD,IAE5C,IAAS2Q,KADTE,EAAInO,UAAU1C,GACOkC,OAAOD,UAAUsO,eAAe1M,KAAKgN,EAAGF,KAAIlI,EAAEkI,GAAKE,EAAEF,IAE9E,OAAOlI,CACX,EACOkE,EAASmE,MAAMlP,KAAMc,UAC9B,EAgH6BR,OAAOC,OA2GXD,OAAOC,OAM5ByK,EAAU,SAAStJ,GAMrB,OALAsJ,EAAU1K,OAAO6O,qBAAuB,SAAUzN,GAAV,IAE7B0N,EADLxN,EAAK,GACT,IAASwN,KAAK1N,EAAOpB,OAAOD,UAAUsO,eAAe1M,KAAKP,EAAG0N,KAAIxN,EAAGA,EAAGb,QAAUqO,GACjF,OAAOxN,CACT,EACOoJ,EAAQtJ,EACjB,EAuDkD,mBAApB2N,iBAAiCA,gB,yFQjUlDpE,GAAoB,IAAAqE,cAAa,uBACjCpE,GAAoB,IAAAoE,cAAsC,sBNIhE,SACLzK,GADK,IAGCM,GAAY,IAAAJ,SAChBF,EACA,mBACA,IAEI0K,EAAwC,GAC5CC,EAAsB,GAClBC,EAAkB,SAAChL,GACvB,IAAMiL,GAAW,IAAA3K,SAAQN,EAAU,kBAAmB,IAAInB,KACxD,SAACC,GAAc,MAAc,iBAAdA,EAAKoM,IAAL,GAEjB,OAAuC,GAAhC,IAAA5K,SAAQ2K,EAAU,QAAS,EACpC,EAeA,OAdAvK,EAAUX,QAAQ,SAAAC,GAEdA,EAASmL,iBACTJ,EAAKnL,QAAQI,EAASmL,iBAAmB,IAEzCJ,EAAKpN,KAAKqC,EAASmL,iBACnBL,EAAenN,KAAK,CAClBiD,gBAAiBZ,EAASmL,gBAC1BzK,UAAWA,EACRC,OAAO,SAAA7B,GAAQ,OAAAA,EAAKqM,kBAAoBnL,EAASmL,eAAlC,GACfrJ,KAAK,SAACpI,EAAG2B,GAAM,OAAA2P,EAAgBtR,GAAKsR,EAAgB3P,EAArC,KAGxB,GACOyP,EAAehJ,KAAK,SAACpI,EAAQ2B,GAClC,QAAQ,GACN,IAA2B,qBAAtB3B,EAAEkH,gBAAwC,OAAQ,EACvD,IAA2B,cAAtBlH,EAAEkH,gBAAiC,OAAO,EAC/C,QAAS,OAAO,EAEpB,EACF,GMxCa8F,GAAa,IAAAmE,cAAa,kBAC1BlE,GAAa,IAAAkE,cAAyB,iBAAkB1K,GACxDyG,GAAgB,IAAAiE,cAAqC,oBN0K3D,SACLzK,GADK,IAmCCgL,EAhCAC,EAAa,SAAC3R,EAAoB2B,GACtC,OAAA3B,EAAE4R,aAAejQ,EAAEiQ,YAAnB,EACIC,GAAgB,IAAAjL,SACpBF,EACA,qCACA,CAAC,GAEGoL,GAAe,IAAAlL,SACnBiL,EACA,eACA,MAEIE,GAAsB,IAAAnL,SAAQiL,EAAe,sBAAuB,IACvE5K,OACC,SAACX,GACC,OAAAA,EAAS9D,KAAO8D,EAAS9D,MAAQ,EAAAsD,KAAKqB,iBAAiBC,IAAvD,GAEHrC,IAAI,SAACuB,GAA8B,OAAC,OAChCA,GAAQ,CACX0L,YAAa1L,EAAS9D,KAFY,GAIhCyP,EAAeF,EAAoB9K,OACvC,SAACX,GAA8B,OAAAA,EAAS4L,MAAT,GAE3BC,EAAuBJ,EAAoB9K,OAC/C,SAACX,GAA8B,OAACA,EAAS4L,MAAV,GA+DjC,OA3DIJ,IACDA,EAAqBM,WAAQ7Q,IAE1BmQ,EAAqCI,EACvC,GAACA,GAAY,EAAKG,IAAY,GAC9BA,GACOhO,KAAK,CACd+N,YAAa,EAAAlM,KAAKqB,iBAAiBkL,cACnCT,aAAc,KAEhBF,EAAWrL,QAAQ,SAACC,GAoBlB,OAnBAA,EAAS0L,YAAc1L,EAAS0L,aAAe1L,EAAS9D,IACxD8D,EAASgM,SAAWH,EACjBlL,OAAO,SAACsL,GAA2B,OAAAA,EAAMC,YAAclM,EAAS9D,GAA7B,GACnC4F,KAAKuJ,GACL5M,IAAI,SAACwN,GAEJ,OAAQA,EAAMP,aACZ,KAAK,EAAAlM,KAAKqB,iBAAiBsL,qBACzBF,EAAMG,MAAQ,EAAAC,aAAaC,uBAC3B,MACF,KAAK,EAAA9M,KAAKqB,iBAAiB0L,uBACzBN,EAAMG,MAAQ,EAAAC,aAAaG,yBAM/B,OAAOP,CACT,GACMjM,EAAS0L,aACf,KAAK,EAAAlM,KAAKqB,iBAAiB4L,iBAC3B,KAAK,EAAAjN,KAAKqB,iBAAiB6L,gBACzB1M,EAASoM,MAAQ,EAAAC,aAAaM,YAC9B,MACF,KAAK,EAAAnN,KAAKqB,iBAAiB+L,SACzB5M,EAASoM,MAAQ,EAAAC,aAAaQ,YAC9B,MACF,KAAK,EAAArN,KAAKqB,iBAAiBiM,MACzB9M,EAASoM,MAAQ,EAAAC,aAAaU,gBAC9B,MACF,KAAK,EAAAvN,KAAKqB,iBAAiBmM,OACzBhN,EAASoM,MAAQ,EAAAC,aAAaY,UAC9B,MACF,KAAK,EAAAzN,KAAKqB,iBAAiBqM,cACzBlN,EAASoM,MAAQ,EAAAC,aAAac,iBAC9B,MACF,KAAK,EAAA3N,KAAKqB,iBAAiBkL,cACzB/L,EAASoM,MAAQ,EAAAC,aAAae,UAMjCpN,EAA0B,QAAoB,IAAhBkB,KAAKC,QACtC,GACAiK,EAAWzK,OACT,SAACX,GAA8B,OAAC4B,QAAQ5B,EAASqN,mBAAlB,GAE1BjC,EAAWtJ,KAAKuJ,EACzB,GMpQaxE,IAAkB,IAAAgE,cAAqC,uBACvD/D,IAAgB,IAAA+D,cAAyB,oBN4G/C,SACLzK,EACAY,GAFK,IAsCGsM,EAlCFC,EAAY,SAACpL,GAAuC,gBAACnC,GACzD,IAAMgE,EAAM7B,EAAKtD,KAAK,SAAAC,GAAQ,OAAAA,EAAKe,KAAOG,EAASH,EAArB,GAC1BmE,IACFA,EAAIwJ,UAAYxN,EAASwN,UACzBxJ,EAAIyJ,WAAazN,EAASyN,WAC1BzJ,EAAI0J,aAAe1N,EAAS0N,aAC5B1J,EAAI2J,WAAa3N,EAAS2N,WAC1B3J,EAAI4J,oBAAsB5N,EAAS4N,oBAEnC5J,EAAI6J,eAAiB7N,EAAS6N,eAC9B7J,EAAI8J,iBAAmB9N,EAAS8N,iBAC5B9J,EAAI9D,gBAAkBF,EAASE,gBAAgBF,EAASE,eAAeH,QAAQwN,EAAUvJ,EAAI9D,iBAKrG,CAhB0D,EAiBpDG,GAAmD,IAAAC,SACvDF,EACA,8CACA,IACAvB,KAAK,SAAC0B,GAAsC,MAAyB,OAAzBA,EAAMC,cAAN,GACxCC,GAAoD,IAAAH,SACxDD,EACA,mBACA,IAEF,MAAsE,WAAlE,IAAAC,SAAQD,EAAsB,2BAA4B,KAOtDiN,EAAanN,EAAiBC,GAC7B,OAAKkN,GAAU,CAAES,QAAyB,IAAhB7M,KAAKC,aAPtCV,EAAoBV,QAAQwN,EAAUvM,EAAQC,QAC7CD,EAAQN,UAA2B,QAAoB,IAAhBQ,KAAKC,SAC7CH,EAAQzC,SAAW,OAAIyC,EAAQzC,WAAQ,GAEvCD,EAAW0C,EAAQzC,UAMd,OAAKyC,GAAO,CAAE+M,QAAyB,IAAhB7M,KAAKC,WACrC,G,UOjKQ4F,GAA+B,EAAAiH,eAAc,WAAjChH,GAAmB,EAAAgH,eAAc,eA4BrD,4B,8CAYA,QAZ4B,OAMvB,GALFhH,GAAe,CACdiH,UAAW,CAAC,UAAW,UACvBC,OAAQ,CAAC,SAAU,SAAU,OAAQ,UACrCzI,OAAQ,OACRC,UAAW,S,uDAEO,GAAnBsB,GAAe,I,wDACI,GAAnBA,GAAe,CAAC,G,oEACG,GAAnBA,GAAe,CAAC,G,wDACG,GAAnBA,GAAe,CAAC,G,uDACiC,GAAjDA,GAAe,CAAEmH,KAAM,0B,mDAXP,GADlB,EAAAC,YACYC,E,CAAb,CAA4BtH,IClB5B,eACE,WAAYuH,EAA0BC,GACpC,SAAK,UAACD,EAAYC,IAAO,IAC3B,CACF,OAJ4B,OAAT,GADlB,EAAAH,W,uBAEyB,EAAAI,aAAsBH,MADnCI,E,CAAb,CAA4B,EAAAC,YCI1BzH,GAEE,EAAA0H,QAAO,aADTzH,GACE,EAAAyH,QAAO,gBAGX,cAGE,WAAoBC,EAAwBL,GAAxB,KAAAK,OAAAA,EAAwB,KAAAL,OAAAA,EAF5C,KAAAM,YAA6B,EAAAC,cAAcC,IAEmB,CAuChE,OArCE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLzT,KAAK0T,mBAET,EAEA,sBAAY,iCAAkB,C,IAA9B,sBACE,OAAO,SAACC,GACN,OAAAA,EAAQC,MACN,IAAAC,QAAO1I,EAAW2I,aAClB,KAAA1O,QAAO,WAAM,SAAKkO,cAAgB,EAAAC,cAAcQ,QAAnC,IACb,KAAAC,UAAS,W,MAAM,YAAAlR,SACb,KAAAmR,IAAGtI,GAAgB,EAAK2H,YAAc,EAAAC,cAAcQ,WACpD,EAAKV,OAAO5E,IAAqC,EAAAyF,MAAMC,kBACrD,EAAAD,MAAME,kBAAgB,KACpB,EAAC,EAAAC,UAAUC,IAAK,EAAKtB,OAAOuB,IAAIC,WAChC,EAAC,EAAAH,UAAUI,OAAQ,EAAKzB,OAAOuB,IAAIG,cACnC,EAAC,EAAAL,UAAUM,QAAS,EAAK3B,OAAOuB,IAAIK,iB,MAErChB,MACD,KAAAI,UAAS,SAACnP,GAAa,WAAAgQ,6BAA4BhQ,EAAU,CAC3DuG,EAAWvG,EAASiQ,MACpBzJ,EAAcxG,EAASiQ,MACvB,EAAA1B,QAAQtH,iBACRH,GAAgB,EAAK2H,YAAc,EAAAC,cAAcwB,WAJ5B,IATZ,IAiBf,KAAAC,YAAW,SAAC1S,GAAoB,YAAA2R,IAE9BtI,GAAgB,EAAK2H,YAAc,EAAAC,cAAcwB,UAEjDrJ,GAAa,IAAI,EAAAuJ,OAAOC,aAAa,aAAc5S,IAJrB,GApBlC,CA2BJ,E,gCAxCuB,GADxB,EAAAuQ,W,uBAI6BK,GAAwBJ,MAHzCqC,E,CAAb,GCNE,GAGE,EAAA/B,QAAO,gBAFTxH,GAEE,EAAAwH,QAAO,oBADTvH,GACE,EAAAuH,QAAO,iBAGX,cAGE,WAAoBC,GAAA,KAAAA,OAAAA,EAFpB,KAAAC,YAA6B,EAAAC,cAAcC,IAEL,CAgDxC,OA9CE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLzT,KAAKoV,oBACLpV,KAAKqV,wBAET,EAEA,sBAAY,kCAAmB,C,IAA/B,sBACE,OAAO,SAAC1B,EAAc2B,GACpB,OAAA3B,EAAQC,MACN,IAAAC,QAAOvI,GAAgBwI,aACvB,KAAA1O,QAAO,SAAC,G,IAAEmQ,EAAO,UAAY,OAAAlP,QAAQkP,IAAY,EAAKjC,cAAgB,EAAAC,cAAcQ,QAAvD,IAC7B,KAAAC,UAAS,SAAC,G,IAAEuB,EAAO,UACjB,YAAAzS,SACE,KAAAmR,IAAG,GAAgB,EAAKX,YAAc,EAAAC,cAAcQ,WACpD,EAAKV,OAAOmC,OAAwCD,GAAS3B,MAC3D,KAAAI,UAAS,SAACnP,GAAa,WAAAgQ,6BAA4BhQ,EAAU,CAC3D0G,GAAc1G,EAASiQ,KAAMQ,EAAOjT,MAAMoD,SAC1C4F,EAAcxG,EAASiQ,MACvBjJ,GAAiB,CAAC,EAAA4J,YAAYC,UAC9B,GAAgB,EAAKpC,YAAc,EAAAC,cAAcwB,WAJ5B,IAH3B,IAYF,KAAAC,YAAW,EAAAC,OAAOU,uBAAuBrK,KAhB3C,CAkBJ,E,gCAEA,sBAAY,sCAAuB,C,IAAnC,sBACE,OAAO,SAACqI,EAAc2B,GACpB,OAAA3B,EAAQC,MACN,IAAAC,QAAOjI,GAAoBkI,aAC3B,KAAA1O,QAAO,SAAC,G,IAAEmQ,EAAO,UACf,OAAAlP,QAAQkP,IAAYlP,QAAQkP,EAAQK,wBAA0B,EAAKtC,cAAgB,EAAAC,cAAcQ,QAAjG,IACF,KAAAC,UAAS,SAAC,G,IAAEuB,EAAO,UACjB,YAAAtB,IACE,EAAAb,QAAQyC,gBAAgB,EAAAzC,QAAQ0C,8BAA6B,IAAA/Q,SAAQwQ,EAAS,qDAC9EhK,GAAcgK,EAASD,EAAOjT,MAAMoD,SACpC4F,EAAckK,GACd1J,GAAiB,CAAC,EAAA4J,YAAYC,UAC9B,GAAgB,EAAKpC,YAAc,EAAAC,cAAcwB,UALnD,GALJ,CAcJ,E,gCAlDwB,GADzB,EAAAlC,W,uBAI6BK,MAHjB6C,E,CAAb,GCJA,cAGE,WAAoB1C,EAAwBL,GAAxB,KAAAK,OAAAA,EAAwB,KAAAL,OAAAA,EAF5C,KAAAM,YAA6B,EAAAC,cAAcC,IAEmB,CAsBhE,OApBE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLzT,KAAKgW,gBAET,EAEA,sBAAY,8BAAe,C,IAA3B,sBACE,OAAO,SAACrC,EAA+C2B,GACrD,OAAA3B,EAAQC,MACN,KAAAxO,QAAO,SAACoQ,GAAqC,OAAAA,EAAOS,OAAShL,EAAkB6I,UAAlC,IAC7C,KAAA1O,QAAO,WAAM,SAAKkO,cAAgB,EAAAC,cAAcQ,QAAnC,IACb,KAAAC,UAAS,WAAM,SAAKX,OAAO5E,IAAsC,EAAKuE,OAAOuB,IAAI2B,mBAAmBtC,MAClG,KAAAI,UAAS,SAAC,G,IAAEc,EAAI,OAAyC,OACvD5J,EAAkB4J,GAClB3J,IAFuD,IAIzD,KAAA6J,YAAW,WAAM,YAAAf,IAAG9I,IAAH,GALJ,GAHjB,CAWJ,E,gCAxB2B,GAD5B,EAAA0H,W,uBAI6BK,GAAwBJ,MAHzCqD,E,CAAb,GCVErK,GAEE,EAAAsH,QAAO,eADTrH,GACE,EAAAqH,QAAO,eAOX,2BACE,KAAAE,YAA6B,EAAAC,cAAcC,IA2F7C,QAzFE,YAAAC,aAAA,WACE,OAAO,IAAAA,cACLzT,KAAKoW,eACLpW,KAAKqW,eAGT,EAYA,sBAAY,6BAAc,C,IAA1B,WACE,OAAO,SAAC1C,EAAc2C,GACpB,OAAA3C,EAAQC,MACN,IAAAC,QAAO/H,GAAegI,aACtB,KAAA1O,QAAO,SAAC,G,IAAEmQ,EAAO,UAAiC,OAAAlP,QAAQkP,EAAR,IAClD,KAAAvB,UAAS,SAAC,GAAD,IAAG,IAAAuB,QAAW/R,EAAI,OAAE,IAAAsR,KAAAA,OAAI,IAAG,GAAC,EAAC,EAE9ByB,EAAW,EAAAC,SAASC,cACpBC,EAAW,GACfpS,GAAI,UAAGd,EAAI,YACXmT,OAAQ,IACRC,OAAQ,IACRC,OAAQ,IACRC,OAAQ,sBAAwBtT,EAChCuT,OAAQ,KACLjC,GAkBL,OAVI,EAAAZ,MAAM8C,gBAAkB,EAAA3C,UAAUC,IAAOoC,EAAYK,SACvDL,EAAYK,OAAS,CACnBE,SAAU,IACVC,aAAc,IAId,EAAAhD,MAAM8C,gBAAkB,EAAA3C,UAAUC,IACpCiC,EAASY,cAAcT,IAElB,KAAAzC,KACT,IACA,KAAAe,YAAW,SAAC1S,GAAoB,YAAA2R,KAAA,GAjClC,CAmCJ,E,gCAGA,sBAAY,6BAAc,C,IAA1B,WACE,OAAO,SAACN,EAAc2C,GACpB,OAAA3C,EAAQC,MACN,IAAAC,QAAO9H,GAAe+H,aACtB,KAAAE,UAAS,eACCvO,EAAY6Q,EAAMjU,MAAoB,QAqB9C,OApBiB,EAAAmU,SAASC,cACjBW,YAAY,CACnB9S,GAAI,eACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,WACRC,OAAQ7R,EAAQC,MACbN,OAAO,SAAAmS,GAAO,OAAAA,EAAInF,UAAJ,GACdlP,IACC,SAAAqU,GAAO,OACLC,SAAUD,EAAIlS,gBACd7B,KAAM+T,EAAI/T,KACViU,IAAK,GACLC,SAAU,IACVC,OAAO,IAAA5S,SAAgBwS,EAAK,qBAAsB,KAClDK,OAAO,IAAA7S,SAAgBwS,EAAK,0CAA2C,IANlE,MAUN,KAAAtD,KACT,IACA,KAAAe,YAAW,SAAC1S,GAAoB,YAAA2R,KAAA,GA1BlC,CA4BJ,E,gCA3FwB,GADzB,EAAApB,YACYgF,E,CAAb,GCPQ,GAAoB,EAAAzE,QAAO,gBAKnC,cACE,WACS0E,EACAC,EACAC,EACAC,GAHA,KAAAH,cAAAA,EACA,KAAAC,gBAAAA,EACA,KAAAC,aAAAA,EACA,KAAAC,cAAAA,CACN,CAgEL,OA9DE,YAAAxE,aAAA,WACE,OAAO,IAAAA,cAAazT,KAAKkY,mBAC3B,EAEA,sBAAY,iCAAkB,C,IAA9B,WACE,OAAO,SAACvE,GACN,OAAAA,EAAQC,MACN,IAAAC,QAAO,GAAgBC,aACvB,KAAA1O,QAAO,SAAC,GAAoD,OAA3C,YAAuD,EAAAmO,cAAcC,IAA1B,IAC5D,KAAAQ,UAAS,WACP,IAAI4C,EAAS,IACb,OAAQ,EAAA1C,MAAM8C,eACZ,KAAK,EAAA3C,UAAUC,GAGf,KAAK,EAAAD,UAAUI,MACbmC,EAAS,KACT,MACF,KAAK,EAAAvC,UAAUM,OACbiC,EAAS,SAMb,OAAQ,EAAA1C,MAAM8C,eACZ,KAAK,EAAA3C,UAAUC,GACb,EAAAkC,SAASC,cAAc0B,cAAc,CACnCvB,OAAM,EACNC,OAAQ,mBAEV,MACF,KAAK,EAAAxC,UAAUI,MACb,EAAA+B,SAASC,cAAc0B,cAAc,CACnCvB,OAAM,EACNC,OAAQ,SACRE,OAAQ,CACNE,SAAU,IACVC,aAAc,EACdkB,iBAAkB,KAGtB,MACF,KAAK,EAAA/D,UAAUM,OACb,EAAA6B,SAASC,cAAc0B,cAAc,CACnCvB,OAAM,EACNC,OAAQ,SACRE,OAAQ,CACNE,SAAU,IACVC,aAAc,EACdkB,iBAAkB,KAQ1B,OAAO,KAAAnE,IAAGhJ,IACZ,GArDF,CAuDJ,E,gCArEgB,GADjB,EAAA4H,W,uBAGyBgF,GACE1B,GACHhB,GACCY,MALbsC,E,CAAb,GCPQrM,GAAqB,EAAAyG,eAAc,iBAG3C,4B,8CAYA,C,MAAA,OAZkC,O,EAArB1L,EAEJ,EAAAC,mBAAP,SAA0B1C,GAA1B,IAIQgU,EACAlV,EAGN,OAPA,EAAamV,SACX,EAAaA,UACb,EAAAC,eAAeF,SAASG,WAAW,EAAAC,eAAe3R,cAE9C3D,GADAkV,EAAgB,EAAaC,UAE/BD,EAAStR,mBAAmB,EAAAyO,YAAYnB,GAAIhQ,EAAIgU,EAASK,QACzDrU,EACG+B,QAAQjD,GAAUA,EAASkB,CACpC,EAVO,EAAAiU,SAAW,KADK,KADxB,EAAA1F,YACY9L,E,CAAb,CAAkCiF,ICG1BC,GAA6C,EAAAwG,eAAc,UAC7D,IADavG,GAAkC,EAAAuG,eAAc,+BAMjC,GAJhC,GAAa,iBACb,GAAU,cACV,GAAa,iBACb,GAAiB,qBAIjBtG,GACED,GAA8B,EAAAkH,SAAQ,UAG1C,eACE,WAAoBC,EAAgBiD,EAA0BsC,EAAsBC,GAClF,QAAK,UAACvC,IAAM,K,OADM,EAAAjD,OAAAA,EAA0C,EAAAuF,MAAAA,EAAsB,EAAAC,aAAAA,E,CAEpF,CAuCF,OA1C2B,OAKzB,sBAAI,sBAAO,C,IAAX,W,YACE,OAAO,IAAAC,iBAAgB,WAElB,EAAAC,SAASC,oBAAoBhZ,KAAK6Y,eAClC,EAAAE,SAASE,oBACT,EAAAF,SAASG,sBAA2B,CAEvCrJ,YAAY,IAAAsJ,gBAAa,KACvB,EAAC,IAAgB,SAACtO,EAAO,GAAgD,OAAvC,WAAkDA,CAAX,E,GACxE,IACH0E,gBAAgB,IAAA4J,gBAAa,KAC3B,EAAC,IAAoB,SAACtO,EAAO,GAA+C,OAAtC,WAAiDA,CAAX,E,GAC3E,IACHpF,SAAS,IAAA0T,gBAAa,KACpB,EAAC,IAAa,SAACtO,EAAO,GAAoC,OAA3B,WAAsCA,CAAX,EAC1D,EAAC,IAAgB,SAACA,EAAO,GAAoC,OAA3B,WAAsCA,CAAX,E,GAC5D,CAAC,GACJuO,WAAW,IAAAD,gBAAa,KACtB,EAAChN,IAAY,SAACtB,EAAO,GAAiC,OAAxB,SAAwB,E,IACrD,KAEP,E,gCASA,sBAAI,0BAAW,C,IAAf,WACE,OAAO,IAAA4I,cAAazT,KAAK4Y,MAAMd,cAAcrE,eAAgBzT,KAAK4Y,MAAMb,gBAAgBtE,eACtFzT,KAAK4Y,MAAMZ,aAAavE,eAAgBzT,KAAK4Y,MAAMX,cAAcxE,eACjEzT,KAAK4Y,MAAMnF,gBAAgB,IAAI,EAAA4F,YAAa5F,eAC5C,IAAI,EAAA6F,kBAAkBtZ,KAAKqT,OAAQ,wBAAwBI,gBAAgB,IAAI,EAAA8F,gBAAiB9F,eAEpG,E,gCAzCgB,GADjB,EAAAZ,W,uBAE6BK,GAAe,QAA0BmF,GAA6BtR,MADvFyS,E,CAAb,CAA2BvN,ICnBzBG,GACE,EAAAqN,WAAU,MASDpN,GAAkB,kBAEzBC,GAA8D,SAAC,GAAD,IAClE9I,EAAI,OACJkW,EAAS,YACTrW,EAAe,kBACfsW,EAAgB,mBAChBC,EAAe,kBACfC,EAAa,gBAGP,GAFO,gBAEwBhU,EAAsBxC,IAAnD+C,EAAQ,WAAEmD,EAAK,QAAEuQ,EAAO,UAmBhC,OAAO,gBAAC1N,GAAK,CACX2N,QAAS1N,GACT2N,UAXkB,WAClB,EAAAxD,SAASC,cAAcW,YAAY,CACjC9S,GAAI,yBACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,SAEZ,EAIE4C,QArBc,WACd,EAAAzD,SAASC,cAAcU,cAAc,CACnC7S,GAAI,yBACJyS,OAAQ,CACNE,SAAU,KAEZiD,OAAQ,mBAEZ,EAcEC,MAAO,gBAAC,EAAAC,iBAAgB,CAAC9V,GAAG,wBAAwB+V,OAAQ,CAAE7W,KAAI,MAClE,uBAAK8W,UAAU,+BACb,uBAAKA,UAAU,kCACb,uBAAKA,UAAU,+BACb,uBAAKC,MAAO,CAAEC,MAAO,OAAQC,OAAQ,QAAUH,UAAU,uEACvD,uBAAK7R,IAAKiR,EAAWgB,IAAKlX,EAAM8W,UAAU,iBAG9C,uBAAKA,UAAU,eACb,wBAAMA,UAAU,WAAU,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,oBAC/C,qBAAGgW,UAAU,oDAAoDT,GACjE,uBAAKS,UAAU,UAAS,cAAa,SACrC,qBAAGA,UAAU,4DAA4D,CAAC3T,EAAoB4C,GAAQ5C,EAAoBmT,GAAUnT,EAAoBP,IAAWhB,OAAO,SAAAhH,GAAK,OAAAiI,QAAQjI,EAAR,GAAY6I,KAAK,QAChM,uBAAKqT,UAAU,WAAU,cAAa,SACtC,qBAAGA,UAAU,mDAAmDK,wBAAyB,CAAEC,OAAQhB,GAAmBD,QAKhI,EAEA,IAAe,IAAAkB,SACb,SAAC,G,IAAEC,EAAY,eACb,WAAA/V,SAAoB+V,OAAcpb,EAAW,CAAC,EAA9C,EACF,SAACqb,GAAa,OACZC,cAAe,WAAM,OAAAD,EAAS,EAAA3H,QAAQ4H,cAAc3O,IAA/B,EADT,EAHhB,CAMEC,ICvEMC,GAAsB,EAAAkN,WAAU,QAAvBjN,GAAa,EAAAiN,WAAU,SAOlChN,GAA+B,SAAC,G,IACpCwO,EAAY,eACZ1I,EAAgB,mBACZ,uCACJ,uBAAK+H,UAAU,YACf,gBAAC/N,GAAO,CAAC2O,MAAM,IAAAnW,SAAQwN,EAAkB,eAAe,IACtD,wBAAM+H,UAAU,gIACd,gBAAC/N,GAAO,CAAC2O,MAAM,IAAAnW,SAAQwN,EAAkB,oBAAoB,I,IAC3D,gBAAC,EAAA6H,iBAAgB,CAAC9V,GAAG,iBAAiB+V,OAAQ,CAAE1C,MAAOhS,KAAKwV,KAAI,IAAApW,SAAQwN,EAAkB,sBAAuB,IAAK6I,kBAAkB,IAAArW,SAAQwN,EAAkB,mBAAoB,QAExL,gBAAChG,GAAO,CAAC2O,MAAM,IAAAnW,SAAQwN,EAAkB,cAAc,I,IACrD,gBAAC,EAAA8I,cAAa,CAAChZ,OAAO,IAAA0C,SAAQwN,EAAkB,aAAc,IAAK+I,OAAO,SAASC,SAAS,OAExF,SAACC,GAAe,uBAAC,EAAApB,iBAAgB,CAAC9V,GAAG,mBAAmB+V,OAAQ,CAAEmB,WAAU,IAA5D,MAM1B,uBAAKlB,UAAU,yDACb,gBAAC/N,GAAO,CAAC2O,MAAM,IAAAnW,SAAQwN,OAAkB7S,GAAW,IAClD,gBAAC,EAAA0a,iBAAgB,CAAC9V,GAAG,Q,KAEvB,gBAACkI,GAAQ,CACPnK,MACEoZ,OAAM,IAAA1W,SAAQwN,EAAkB,mBAAoB,CAAC,GAAGoF,QACpD,IAAA5S,SAAQkW,EAAc,QAAS,IAC/B,IAAAlW,SAAQwN,EAAkB,yBAA0B,GAE1DmJ,SAAS,IAEX,gBAACnP,GAAO,CAAC2O,MAAM,IAAAnW,SAAQwN,OAAkB7S,GAAW,IAClD,qBAAG4a,UAAU,0CACX,gBAAC,EAAAF,iBAAgB,CACf9V,GAAG,gBACH+V,QAAQ,IAAAtV,SAAQkW,OAAcvb,EAAW,CAAC,QAKlD,gBAAC6M,GAAO,CAAC2O,MAAM,IAAAnW,SAAQwN,EAAkB,gBAAgB,IACvD,qBAAG+H,UAAU,sBACV,IAAAvV,SACCwN,EACA,eACA,gBAAC,EAAA6H,iBAAgB,CAAC9V,GAAG,iCA1CvB,EAgDN,MCxDE,GAEE,EAAAmV,WAAU,MADZ,GACE,EAAAA,WAAU,QAaD,GAAkB,uBAOzB/M,GAAiC,SAACiP,GAAD,IAEnCrX,EAOEqX,EAAK,GANPnY,EAMEmY,EAAK,KALPV,EAKEU,EAAK,aAJPpJ,EAIEoJ,EAAK,iBAHPhX,EAGEgX,EAAK,eAFPvJ,EAEEuJ,EAAK,WADPC,EACED,EAAK,SACHE,GAAqB,IAAA9W,SAAQJ,EAAgB,SAAU,GAAK,EAClE,OAAO,uBAAK2V,UAAW,gEAAyDlI,EAAa,cAAgB,KAC3G,yBAAO9N,GAAI,UAAGA,EAAE,aAAagW,UAAU,iDAAiDwB,QAAS,WAAM,OAAAF,EAASD,EAAT,GACrG,yBAAO1F,KAAK,QAAQzS,KAAK,WAAWuY,QAAS3J,IAC7C,wBAAMkI,UAAU,iBAChB,wBAAMA,UAAU,cAAc9W,GAC9B,gBAAC,GAAO,CAAC0X,KAAMW,EAAqB,GAClC,qBAAGvB,UAAU,aAAY,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,0BAA0B+V,OAAQ,CAAEwB,mBAAkB,OAEtG,gBAAC,GAAK,CAACZ,aAAcA,EAAc1I,iBAAkBA,KAG3D,EAEM5F,GAAgB,CACpBrI,GAAI,KACJN,oBAAqB,EAAAC,KAAKC,qBAAqBqB,MAG3C,GAA8D,SAAC,GAWnE,SAASyW,EAAgBC,GACvBC,EAAaD,EACf,CAbkE,IA0ChEzY,EACAkW,EACAG,EACAxW,EACAsW,EACAC,EAEI,EAAExT,EAAUmD,EAAOuQ,EAhDzB3W,EAAO,UACPgZ,EAAO,UACPC,EAAgB,mBAChBpB,EAAa,gBACbqB,EAAe,kBAET,IAA4B,WAAsC1P,IAAc,GAA/E2P,EAAS,KAAEJ,EAAY,KA2C9B,OAzCA,YAAgB,WAAQA,EAAaE,EAAmB,EAAG,CAACA,IAiC1D5Y,EAMEL,EAAO,KALTuW,EAKEvW,EAAO,UAJT0W,EAIE1W,EAAO,cAHTE,EAGEF,EAAO,gBAFTwW,EAEExW,EAAO,iBADTyW,EACEzW,EAAO,gBACHiD,GAAF,EAA+BP,EAAsBxC,IAA3C,SAAEkG,EAAK,QAAEuQ,EAAO,UACzB,gBAAC,GAAK,CACXC,QAAS,GACTO,UAAU,mBACVL,QAAS,WACP,EAAAzD,SAASC,cAAcU,cAAc,CACnC7S,GAAI,sBACJyS,OAAQ,CACNE,SAAU,KAEZiD,OAAQ,0BAEZ,EACAF,UAAW,WACT,EAAAxD,SAASC,cAAcW,YAAY,CACjC9S,GAAI,sBACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,SAEZ,EACA8C,MAAO,gBAAC,EAAAC,iBAAgB,CAAC9V,GAAG,6BAA6B+V,OAAQ,CAAE7W,KAAI,MACvE,uBAAK8W,UAAU,IACb,uBAAKA,UAAU,+BACb,uBAAKA,UAAU,iBAAgB,cAAa,SAC5C,uBAAKA,UAAU,kCACb,uBAAKA,UAAU,+BACb,uBAAKC,MAAO,CAAEC,MAAO,OAAQC,OAAQ,QAAUH,UAAU,uEACvD,uBAAK7R,IAAKiR,EAAWgB,IAAKlX,EAAM8W,UAAU,iBAG9C,uBAAKA,UAAU,eACb,wBAAMA,UAAU,WAAU,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,oBAC/C,qBAAGgW,UAAU,oDAAoDT,GACjE,uBAAKS,UAAU,UAAS,cAAa,SACrC,qBAAGA,UAAU,4DAA4D,CAAC3T,EAAoB4C,GAAQ5C,EAAoBmT,GAAUnT,EAAoBP,IAAWhB,OAAO,SAAAhH,GAAK,OAAAiI,QAAQjI,EAAR,GAAY6I,KAAK,QAChM,uBAAKqT,UAAU,WAAU,cAAa,SACtC,qBAAGA,UAAU,mDAAmDK,wBAAyB,CAAEC,OAAQhB,GAAmBD,QAI5H,wBAAMW,UAAU,+BACd,uBAAKA,UAAU,uBAAsB,cAAa,SAClD,uBAAKA,UAAU,iBAAgB,cAAa,SAC5C,qBAAGA,UAAU,aAAY,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,8BAE5C6X,EAAQjZ,IACN,SAAA+Y,GAAS,uBAACvP,GAAM,KAAKuP,EAAK,CAAE7J,WAAY6J,EAAM3X,KAAOgY,EAAUhY,GAAIsX,SAAUI,IAApE,GAGb,gBAAC,EAAA5B,iBAAgB,CAAC9V,GAAG,YAAY+V,OAAQ,CAAE7W,KAAI,IAE3C,SAAC+Y,GAAa,uBAAC7P,GAAM,KAAKC,GAAa,CAAEnJ,KAAM+Y,EAAenK,WAAYzF,GAAcrI,KAAOgY,EAAUhY,GAAIsX,SAAUI,IAAzG,KAMtB,uBAAK1B,UAAU,qGACb,0BAAQhW,GAAG,yBAAyBgW,UAAU,0BAA0BwB,QA9F5E,WACE,GAAIQ,EAAUhY,KAAO8X,EAAiB9X,GAAI,CACxC,IAAM2X,EAAyB,OAAjBK,EAAUhY,GAAc8X,EAAmBE,EACzDD,EAAgBJ,EAAM3J,gBACtB,EAAAkE,SAASC,cAAcW,YAAY,CACjC9S,GAAI,sBACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,CACN3P,IAAK,0BAEP4P,OAAQ,CACNE,SAAUyE,EAAM5W,gBAChB7B,KAAMyY,EAAMzY,KACZiU,IAAK,GACLC,SAAU,IACVC,OAAO,IAAA5S,SAAgBkX,EAAO,qBAAsB,IACpDrE,OAAO,IAAA7S,SAAgBkX,EAAO,+BAAgC,MAGpE,MACEjB,GAEJ,GAsEqG,gBAAC,EAAAZ,iBAAgB,CAAC9V,GAAG,mCACtH,uBAAKgW,UAAU,YAAW,cAAa,SACvC,0BAAQhW,GAAG,sBAAsBgW,UAAU,0BAA0BwB,QAASd,GAAe,gBAAC,EAAAZ,iBAAgB,CAAC9V,GAAG,iCAGxH,EAEA,IAAe,IAAAuW,SACb,SAAC,GAAD,IAAGpV,EAAO,UAAEqV,EAAY,eAChB3X,GAAU,IAAA4B,SAAoB+V,OAAcpb,EAAW,CAAC,GACxDyc,GAAU,IAAApX,SAAuB5B,EAAS,oBAAqB,IAAID,IACvE,SAAAsZ,GAAW,OAAA/W,EAAQC,MAAMpC,KAAK,SAAA2Y,GAAS,OAAAA,EAAM3X,KAAOkY,CAAb,EAA5B,GACXpX,OAAOiB,SACH+V,EAAmBD,EAAQ7Y,KAAK,SAAA2Y,GAAS,OAAAA,EAAM7J,UAAN,GAC/C,MAAO,CACLjP,QAAO,EACPgZ,QAAO,EACPC,iBAAkBA,GAAoBzP,GAE1C,EACA,SAACoO,GAAa,OACZsB,gBAAiB,SAAC7G,GACZA,GAAQuF,EAASzP,GAAgBkK,IACrCuF,EAAS,EAAA3H,QAAQ4H,cAAc,IACjC,EACAA,cAAe,WACb,EAAAxE,SAASC,cAAcW,YAAY,CACjC9S,GAAI,sBACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,CACN3P,IAAK,yBAGTqT,EAAS,EAAA3H,QAAQ4H,cAAc,IACjC,EAhBY,EAbhB,CA+BE,IhBxLE7T,GAAsD,CAAC,EAU3D,cAYE,WAAYsV,GAVZ,KAAAC,SAAmB,EAWbvV,GAAasV,IAAQtV,GAAasV,GAAcrV,UACpDpH,KAAKyc,KAAOA,EACZzc,KAAK2c,WAAaC,EAAE,IAAMH,GAC1Bzc,KAAK6c,KAAO7c,KAAK6c,KAAKC,KAAK9c,MAC3BA,KAAK+c,MAAQ/c,KAAK+c,MAAMD,KAAK9c,MAC7BA,KAAKgd,KAAOhd,KAAKgd,KAAKF,KAAK9c,MAC3BA,KAAKoH,QAAUpH,KAAKoH,QAAQ0V,KAAK9c,MACjCA,KAAKid,gBAAkBjd,KAAKid,gBAAgBH,KAAK9c,MACjDA,KAAK2c,WAAWO,QAAQ,CACtBC,QAAS,WAEXnd,KAAKod,UACFC,iBAAiB,aAAcrd,KAAK6c,MAGvC1V,GAAasV,GAAQzc,IACvB,CAsDF,OA7EE,sBAAI,wBAAS,C,IAAb,WACE,OAAQqH,SAASiW,eAAetd,KAAKyc,KACvC,E,gCACA,sBAAI,wBAAS,C,IAAb,WACE,OAAQpV,SAASkW,cAAc,IAAMvd,KAAKyc,KAAO,QACnD,E,gCAmBA,YAAAI,KAAA,sBACExV,SAASmW,KAAKH,iBAAiB,YAAard,KAAK+c,OAC5C/c,KAAK0c,SAAY1c,KAAKyd,WACzBC,OAAOL,iBAAiB,SAAUrd,KAAK+c,OACvC/c,KAAKyd,SAAWE,WACd,WACE,EAAKF,SAAW,KAChB,EAAKf,SAAU,EACf,EAAKC,WAAWO,QAAQ,QACxBU,sBACE,WAAM,SAAKC,UAAUR,iBAAiB,QAAS,EAAKJ,gBAA9C,EAEV,EArEM,KAwEZ,EACA,YAAAF,MAAA,SAAMlb,GACJ,IAAMnB,EAASmB,EAAEnB,QA7DrB,SAAsBod,EAA0Bjc,GAC9C,OAAOic,EAAM1Y,OAAO,SAAAvD,GAAK,OAAAwE,QAAQxE,EAAR,GAAYqB,IAAI,SAAA+E,GAAQ,OANnD,SAAsBA,EAAkBpG,GACtC,OAAQoG,EAAKG,IAAM,GAAKvG,EAAEkc,GAAK9V,EAAK+V,OAAS,GAAKnc,EAAEkc,GACjD9V,EAAKgW,KAAOpc,EAAEqc,GAAKjW,EAAKM,MAAQ1G,EAAEqc,CACvC,CAGmDC,CAAalW,EAAMpG,EAAnB,GAAuByB,KAAK,SAAAzB,GAAK,OAAAA,CAAA,KAAM,CAC1F,EA4DSuc,CAAa,CAChBpe,KAAKod,WAAapd,KAAKod,UAAUlV,wBACjClI,KAAK6d,WAAa7d,KAAK6d,UAAU3V,yBAChCrG,KACEnB,EAAO2d,UAAUC,SAAS,yBAC7Bte,KAAKgd,OACLuB,aAAave,KAAKyd,UAClBzd,KAAKyd,SAAW,KAEpB,EAKA,YAAAR,gBAAA,SAAgBpb,GACd7B,KAAKwe,eAAe3c,EACtB,EACA,YAAAmb,KAAA,WACMhd,KAAK0c,UACP1c,KAAK0c,SAAU,EACf1c,KAAK6d,UAAUY,oBAAoB,QAASze,KAAKid,iBACjD5V,SAASmW,KAAKiB,oBAAoB,YAAaze,KAAK+c,OACpDW,OAAOe,oBAAoB,SAAUze,KAAK+c,OAC1C/c,KAAK2c,WAAWO,QAAQ,QAE5B,EACA,YAAA9V,QAAA,WACEC,SAASmW,KAAKiB,oBAAoB,YAAaze,KAAK+c,OACpDW,OAAOe,oBAAoB,SAAUze,KAAK+c,OAC1C/c,KAAKod,UACFqB,oBAAoB,aAAcze,KAAK6c,MAG1C1V,GAAanH,KAAKyc,MAAQ,IAC5B,EACF,EAnFA,GA6Fa7P,GAAoC,SAAC+O,GAAD,IAuBzC,EAAE7B,EAASvQ,EAAOnD,EAClBsY,EA9ImBnC,EAwHvBjY,EASEqX,EAAK,GARPnY,EAQEmY,EAAK,KAPP9B,EAOE8B,EAAK,cANPjC,EAMEiC,EAAK,UALPtY,EAKEsY,EAAK,gBAJPhC,EAIEgC,EAAK,iBAHPlL,EAGEkL,EAAK,SAFPrB,EAEEqB,EAAK,UADPgD,EACEhD,EAAK,YACHiD,EAAY,UAAc,WAAM,uBAAUta,GAAE,OAAGqB,KAAKkZ,MAAsB,IAAhBlZ,KAAKC,UAA/B,EAAkD,CAACtB,IAwBzF,OAtBA,YAAgB,WACd,IAAMwa,EAAO,IAAIC,GAAYH,GAE7B,OADAD,EAAYG,GACL,WAAM,OAAAA,EAAK1X,SAAL,CACf,EAAG,IAEHD,GAAayX,IACXD,EAAYxX,GAAayX,IAEnB9E,GAAF,EAA+BjU,EAAsBxC,IAA5C,QAAEkG,EAAK,QAAEnD,EAAQ,WAC1BsY,EAAc,sLAE+ChF,EAAS,kBAAUlW,EAAI,kGAGpCqW,EAAa,oEAClB,CAAClT,EAAoB4C,GAAQ5C,EAAoBmT,GAAUnT,EAAoBP,IAAWhB,OAAO,SAAAhH,GAAK,OAAAiI,QAAQjI,EAAR,GAAY6I,KAAK,OAAM,0FApJrJsV,EAqJ+D5C,EApJpF4C,EAAIxb,OAJqB,GAKpBwb,EAAIyC,OAAO,EALS,IAKoB,MACrCzC,GAkJ6F,wFAE1EjY,EAAE,yFAAiFyC,GAAaC,mBAAmB,gBAAe,iCAI/J,uBAAKsT,UAAWA,GACd,uBAAKA,UAAU,gBACb,uBAAKhW,GAAIsa,EACPtE,UAAU,sDACV2E,SAAU,EACVC,KAAK,UAAS,aACH,MAAK,YACN,OAAM,iBACD,MAAK,iBACL,OAAM,gBACN,qDAA8CN,EAAS,0FAAwF,aAClJF,GACXjO,IAKX,EiBrKQ,GAAY,EAAAgJ,WAAU,QAYjB,GAEa,SAAAkC,GAAA,IAsBpBwD,EApBF7a,EAgBEqX,EAAK,GAfPnY,EAeEmY,EAAK,KAdPjC,EAcEiC,EAAK,UAbPV,EAaEU,EAAK,aAZPpJ,EAYEoJ,EAAK,iBAXPrJ,EAWEqJ,EAAK,eAVPtY,EAUEsY,EAAK,gBATPtJ,EASEsJ,EAAK,oBARPxJ,EAQEwJ,EAAK,aAPPvJ,EAOEuJ,EAAK,WANPzJ,EAMEyJ,EAAK,WALPyD,EAKEzD,EAAK,kBAJP0D,EAIE1D,EAAK,cAHP2D,EAGE3D,EAAK,YAFP4D,EAEE5D,EAAK,oBADP9B,EACE8B,EAAK,cACDlY,EAAaoC,EAAsBxC,GAAgB,SACrDmc,EACFrN,IAAgB,IAAApN,SAAQqa,EAAmB,SAAU,GAAK,EAExDK,EAAkB,SAAC5d,GACnBA,IACFA,EAAE6d,iBACF7d,EAAE8d,mBAEAR,GACFA,EAAYnC,OAEdsC,EAAY3D,EAAO,kBAAWrX,GAChC,EACA,OACE,gBAACsI,GAAO,GACNjM,IAAK2D,EACLqa,YAAa,SAACG,GACZK,EAAcL,EACdA,EAAKN,eAAiBiB,CACxB,GACI9D,EAAK,CACTrB,UAAW,yCAEX,uBAAKA,UAAU,GAAGhW,GAAIA,EAAE,UAAWb,GACjC,uBACE6W,UAAW,4DACLnI,GAAgBC,EAAa,YAAc,GAAE,2BAC5CD,EAAkD,GAAnC,iCAAqC,2BACrDA,GAAgBD,EAAa,YAAc,KAEjD,uBACEoI,UAAU,sDAAqD,cACnD,QAEZ,uBAAK7R,IAAKiR,EAAWgB,IAAKlX,KAE5B,uBAAK8W,UAAU,yDACb,uBAAKA,UAAU,4BAA2B,cAAa,SAErDjU,QAAQ7C,IACN,0BACEc,GAAI,kBAAWA,GACfgW,UAAU,8GAEVwB,QAAS2D,GAERjc,GAGP,qBAAG8W,UAAU,mCAAmCT,GAChD,gBAAC,GAAO,CACNqB,KAAM/I,IAAgB,IAAApN,SAAQkW,EAAc,QAAS,GAAK,GAE1D,qBAAGX,UAAU,0CACX,gBAAC,EAAAsF,gBAAe,CACdvd,OACE,IAAA0C,SACEwN,EACA,0BACA,KACG,IAAAxN,SAAQkW,EAAc,QAAS,GAEtCK,OAAO,QAET,gBAAC,GAAO,CACNJ,MAAM,IAAAnW,SACJwN,EACA,0BACA,I,IAIF,2BACE,gBAAC,EAAAqN,gBAAe,CACdvd,OAAO,IAAA0C,SAAQkW,EAAc,QAAS,GACtCK,OAAO,aAqBnB,gBAAC,GAAO,CAACJ,MAAOhJ,GAAcsN,GAC5B,uBAAKlF,UAAU,8CACb,2BACE,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,2BAI3B,gBAAC,GAAO,CAAC4W,KAAM/I,GAAgBD,IAAe7L,QAAQgM,IACpD,uBAAKiI,UAAU,8CACb,2BACE,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,wBAI3B,gBAAC,GAAO,CAAC4W,KAAM7U,QAAQgM,IACrB,uBAAKiI,UAAU,8CACb,2BACE,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,sBAAsB+V,OAAQ,CAAE7W,KAAM6O,QAKjE,gBAAC,GAAO,CAAC6I,KAAM/I,GACb,yBACE0N,QAAS,uBAAgBvb,GACzBgW,UAAU,2EACVwB,QAAS,SAAAja,GACPA,EAAE6d,iBACF7d,EAAE8d,kBACGzN,IAAYsN,EACbD,EAAoB5D,EAAO,kBAAWrX,IACtC+a,EAAc/M,GACpB,GAEA,yBACEhO,GAAI,uBAAgBA,GACpB2R,KAAK,WACLzS,KAAK,WACLuY,QAAS3J,EACT0N,SAAU5N,IAEZ,wBAAMoI,UAAU,4BAChB,wBAAMA,UAAU,WAAW9W,OAOzC,EAEA,IAAe,IAAAqX,SAKb,SAAC,GAAqB,OAAG,CAAH,EACtB,SAAAE,GAAY,OACVuE,YAAa,SAACxK,EAAuBxQ,GACnC,OAAAyW,EACE,EAAA3H,QAAQ2M,aAAa,CACnBC,WAAY,GACZlL,KAAM,OAAKA,GAAI,CAAEmL,WAAY3b,MAHjC,EAMFib,oBAAqB,SAACzK,EAAuBxQ,GAC3C,OAAAyW,EACE,EAAA3H,QAAQ2M,aAAa,CACnBC,WAAY,GACZlL,KAAM,OAAKA,GAAI,CAAEmL,WAAY3b,MAHjC,EAMF+a,cAAe,SAAC7J,GACd,OAAAuF,EAASzP,GAAgBkK,GAAzB,EAhBQ,EANd,CAwBE,IClNA,GACE,EAAAiE,WAAU,QAYD,GAAyF,SAAC,GAAD,IA6B9FyG,EA5BN5b,EAAE,KACFd,EAAI,OACJkW,EAAS,YACTrH,EAAmB,sBACnBF,EAAY,eACZC,EAAU,aAEVF,GADS,YACC,cACV+I,EAAY,eACZtB,EAAgB,mBAChBC,EAAe,kBACfrH,EAAgB,mBAChBD,EAAc,iBACd3N,EAAc,iBACd0a,EAAa,gBAEP,IAAqB,YAAe,GAAM,GAAzCc,EAAQ,KAAEC,EAAM,KACjBC,GAAO,IAAAC,WAYb,OAXA,YAAgB,WACdH,GACQ,EAAA3J,SAASC,cAAcW,YAAY,CACjC9S,GAAI,oBACJyS,OAAQ,CACNE,SAAU,KAEZsJ,OAAQ,gBAEpB,EAAG,CAACJ,IACED,GAAe,IAAAnb,SAAQJ,EAAgB,SAAU,GAAK,EACrD,uBAAK2V,UAAW,sDAA+ClI,EAAa,WAAa,GAAE,YAAIF,EAAa,WAAa,IAAM5N,GAAIA,GACxI,uBAAKgW,UAAU,gCACb,uBAAKA,UAAU,yCACb,yBAAOhW,GAAI,gBAASA,GAAMwX,QAAS,SAACja,GAClCA,EAAE6d,iBACF7d,EAAE8d,kBACGxN,IAAgBD,GAChBmN,EAAc/M,EACrB,EAAGgI,UAAU,mFACX,yBAAOrE,KAAK,WAAWzS,KAAK,WAAWuY,QAAS3J,EAAY0N,SAAU5N,IAAeC,IACrF,wBAAMmI,UAAU,uCAAuC9W,GACvD,gBAAC,GAAO,CAAC0X,KAAM/I,GAAgBD,IAAe7L,QAAQgM,IACpD,wBAAMiI,UAAU,oDAAmD,2BAAK,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,wBAE/F,gBAAC,GAAO,CAAC4W,KAAM7U,QAAQgM,IACrB,wBAAMiI,UAAU,oDAAmD,2BAAK,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,sBAAsB+V,OAAQ,CAAE7W,KAAM6O,QAErI,gBAAC,GAAK,CAAC4I,aAAcA,EAAc1I,iBAAkBA,IACrD,wBAAM+H,UAAU,8CAElB,sBAAIA,UAAU,sEAEV,IAAAvV,SAA2BJ,OAAgBjF,EAAW,IACnDmD,MAAM,EAAG,GACTK,IACC,SAAAC,GAAW,iCACT,uBAAKsF,KAAK,IAAA1D,SAAQ5B,EAAS,YAAa,IAAKuX,KAAK,IAAA3V,SAAQ5B,EAAS,OAAQ,IAAKgX,OAAO,IAAApV,SAAQ5B,EAAS,OAAQ,MADvG,IAMnB,uBAAKmX,UAAU,WAAU,cAAa,SACtC,uBAAKA,UAAU,WAAU,cAAa,SACtC,gBAAC,GAAO,CAACY,KAAMgF,GACb,uBAAK5F,UAAU,sBACb,0BAAQhW,GAAI,4BAAqBA,GAAMwX,QAAS,WAAM,OAAAsE,GAAQD,EAAR,EAAmB7F,UAAU,8EAA6E,gBAAgB,4BAAqBhW,GAAI,gBAAiB6b,EAAQ,aAAc,UAAGE,EAAKG,cAAc,CAAClc,GAAI,kBAAiB,YAAI+b,EAAKG,cAAc,CAAClc,GAAI,aAAY,YAAId,IACtU,wBAAM8W,UAAW,wDAAiD6F,EAAW,gBAAkB,gBAC7F,wBAAM7F,UAAU,kCAAuC,wBAAMA,UAAU,mCAEzE,wBAAMA,UAAU,6BAA4B,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,uBAKzE,uBAAKgW,UAAU,wCAAuC,cAAa,SACnE,uBAAKA,UAAU,6CACb,gBAAC,GAAO,CAACY,KAAM7U,QAAQqT,IACrB,uBAAKjR,IAAKiR,EAAWgB,IAAKlX,OAIhC,gBAAC,GAAO,CAAC0X,KAAMiF,GACb,uBAAK7F,UAAU,2EAA2E4E,KAAK,SAAQ,eAAeiB,GACpH,uBAAK7F,UAAU,sCACb,gBAAC,GAAO,CAACY,KAAM7U,QAAQuT,GAAmBD,IACxC,qBAAGgB,wBAAyB,CAAEC,OAAQhB,GAAmBD,KACzD,4BAEF,uBAAKW,UAAU,mDAEX7T,GAAc,IAAA1B,SAAQJ,OAAgBjF,EAAW,KAAKwD,IAAI,SAACC,GAAwB,uBAACsd,GAAO,GAAC9f,IAAKwC,EAAQmB,IAAQnB,EAAO,CAAEgP,cAAc,IAArD,OAOjG,EAEMtF,IAAQ,IAAAgO,SACZ,SAAC,GAAqB,OAAG,CAAH,EACtB,SAACE,GAAa,OACZsE,cAAe,SAAC7J,GAAmC,OAAAuF,EAASzP,GAAgBkK,GAAzB,EADvC,EAFF,CAKZ,IAEF,MCxHa1I,GAAoC,SAAC,GACxC,WADuC,IAE/C4T,EAAK,QACLC,EAAO,UAED,IAA0B,YAAe,GAAM,GAA9CR,EAAQ,KAAES,EAAW,KAW5B,OAVA,YAAgB,WACdT,GACQ,EAAA3J,SAASC,cAAcW,YAAY,CACjC9S,GAAI,kBACJyS,OAAQ,CACNE,SAAU,KAEZsJ,OAAQ,eAEpB,EAAG,CAACJ,IACG,uBAAK7F,UAAU,2EAA2EhW,GAAG,YAClG,0BAAQA,GAAG,QAAQgW,UAAU,iEAAiEwB,QAAS,WAAM,OAAA8E,GAAaT,EAAb,EAAsB,gBAAiBA,GAClJ,wBAAM7F,UAAW,oBAAa6F,EAAW,kBAAoB,iBAAiB,cAAc,S,KAC5F,gBAAC,EAAA/F,iBAAgB,CAAC9V,GAAIoc,KAExB,uBAAKpG,UAAU,WAAU,cAAa,SACtC,gBAAC,EAAAb,WAAWlN,QAAO,CAAC2O,KAAMiF,GACxB,uBAAK7F,UAAU,eACb,0BAAQhW,GAAG,cAAc2R,KAAK,SAAS6F,QAAS,WAAM,OAAA8E,GAAY,EAAZ,EAAoBtG,UAAU,mDAAkD,aAAY,SAChJ,wBAAMA,UAAU,yBAAwB,cAAa,UAEvD,gBAAC,EAAAuG,qBAAoB,CAACvc,GAAIqc,MAIlC,EC/Ba5T,GAAmD,SAAC,GAAD,IAC9DvJ,EAAI,OACJsR,EAAI,OACJrE,EAAQ,WAEFsK,GAAW,IAAA+F,eAKjB,OAHA,YAAgB,WACd/F,EAAS,EAAA3H,QAAQtH,eAAetI,EAAMsR,GACxC,EAAG,IACI,gCAAGrE,EACZ,ECHM,GAAuE,SAAC,G,IAC5EsQ,EAAQ,WACJ,uBAAChU,GAAY,CAACvJ,KAAK,UACvB,uBAAK8W,UAAU,sCACb,uBAAKA,UAAU,aACb,sBAAIA,UAAU,2EACZ,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,iBAEvB,gBAAC,EAAAuc,qBAAoB,CAACvc,GAAG,2BAErB,SAACsW,GAAgB,uBAAC,EAAAnB,WAAWlN,QAAO,CAAC2O,KAAM7U,QAAQuU,IACjD,uBAAKN,UAAU,YACf,qBAAGA,UAAU,qBAAqBM,GAFnB,KAQzB,uBAAKN,UAAU,WAAU,cAAa,SAEpC7T,EAAcsa,GAAU7d,IAAI,SAAA8d,GAAS,uBAAC,GAAK,GAACrgB,IAAKqgB,EAAM1c,IAAQ0c,GAA1B,GAEvC,gBAAClU,GAAM,CAACmU,SAAU,EAAAhd,KAAKqB,iBAAiBmM,OACtCiP,MAAO,sBAAe,EAAAzc,KAAKqB,iBAAiB4L,kBAC5CyP,QAAS,qBAAc,EAAA1c,KAAKqB,iBAAiBmM,UAtB3C,EAyBN,IAAe,IAAAoJ,SACb,SAAC,G,IAAEpV,EAAO,UAAoB,OAC5Bsb,UAAU,IAAAhc,SAAsCU,EAAS,aAAe,EAAAxB,KAAKqB,iBAAiBmM,OAAQ,IACnGrM,OAAO,SAAA1C,GAAQ,OAAAA,EAAKsB,sBAAwB,EAAAC,KAAKC,qBAAqBgd,KAAvD,GAFU,EADhC,CAKE,IlBCIpY,GAA6B,CACjCS,MAAO,GACPnD,SAAU,GAEVwD,YAAY,EACZE,QAAQ,EACRI,OAAQ,OACRC,UAAW,OAmHA6C,GAAmB,SAACnL,EAAQuD,EAAauC,GAClC,KAAd9F,EAAEsf,SAA6B,YAAXtf,EAAEoU,OACxBpU,EAAE6d,iBACF7d,EAAE8d,kBACFhY,EAASvC,GAEb,EAEa6H,GAAmB,SAACpL,EAAQ8F,GACrB,KAAd9F,EAAEsf,SAA6B,YAAXtf,EAAEoU,OACxBpU,EAAE6d,iBACF7d,EAAE8d,kBACFhY,IAEJ,EmBjLauF,GAA+C,SAAC,GAAD,IAC1DtG,EAAI,OACJwa,EAAS,YAEH,IAA0B,WAAexa,EAAK7F,OAXpC,GAWyD,EAAI6F,EAAK7F,QAAO,GAAlFsgB,EAAQ,KAAEC,EAAW,KACtBC,EAAQzW,EAAYlE,GAU1B,OATA,YAAgB,WACT2a,GAASA,EAAMxgB,SAAW6F,EAAK7F,QAClCugB,EAAY1a,EAAK7F,OAfL,GAe0B,EAAI6F,EAAK7F,OACnD,EAAG,CAAC6F,IACJ,YAAgB,WACVya,EAAWza,EAAK7F,QAClB6c,sBAAsB,WAAM,OAAA0D,EAAYD,EAnB5B,GAmBgB,EAEhC,EAAG,CAACA,IACG,gCAEHza,EAAK/D,MAAM,EAAGwe,GAAUne,IAAIke,GAGlC,ECTQ,GAAY,EAAA3H,WAAU,QAwBxBtM,GAAe,WAAM,OACzB,uBAAKmN,UAAU,UACb,uBAAKA,UAAU,qBACb,uBAAKA,UAAU,IACb,wBAAMA,UAAU,0CACd,wBAAMA,UAAU,sBAChB,wBAAMA,UAAU,wBAGpB,uBAAKA,UAAU,eACb,sBAAIA,UAAU,oCACZ,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,+BAEvB,qBAAGgW,UAAU,YACX,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,qCAdJ,EAqBrB8I,GAAmD,SAAC,GAAD,IACvDoU,EAAM,SACNC,EAAO,UACPC,EAAU,aAEJC,EAAUzY,EAAoBsY,EAAO7W,YAAY,GAkBvD,OACE,uBACE2P,UAAU,+BACVC,MAAO,CAAEqH,UAAW,WAEpB,uBAAKtH,UAAU,iBAAgB,cAAa,SAC5C,wBACEA,UAAU,uCACVuH,SAzBN,SAAsBhgB,GACpBA,EAAE6d,iBACF,EAAAlJ,SAASC,cAAcW,YAAY,CACjC9S,GAAI,qBACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,oBAEVmK,EAAO5W,SAAS+W,EAAQhX,YACxBiT,sBAAsB,WAAM,OAAA8D,GAAW,EAAX,EAC9B,EAeMI,QAdN,SAAqBjgB,GACnB2f,EAAOzX,QACP6T,sBAAsB,WAAM,OAAA8D,GAAW,EAAX,EAC9B,GAaM,uBAAKpH,UAAU,mDACb,qBAAGA,UAAU,qBACX,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,gBAGzB,uBAAKgW,UAAU,iDACb,uBAAKA,UAAU,UAAU4E,KAAK,QAAO,kBAAiB,qBACpD,qBAAG5E,UAAU,YAAYhW,GAAG,qBAC1B,gBAAC,EAAA8V,iBAAgB,CAAC9V,GAAG,YAEvB,sBAAIgW,UAAU,uBACX,IAAAvV,SAAuB0c,EAAS,SAAU,IACxClb,OACArD,IAAI,SAAAkC,GAAU,OACb,sBAAIzE,IAAKyE,GACP,yBACEd,GAAI,wBAAiBc,GACrBkV,UAAU,iDACVwB,QAAS,SAAAja,GAAK,OAAAmL,GAAiBnL,EAAGuD,EAAQuc,EAAQrY,YAApC,EACdyY,UAAW,SAAAlgB,GAAK,OAAAmL,GAAiBnL,EAAGuD,EAAQuc,EAAQrY,YAApC,GAEhB,yBACEhF,GAAI,mBAAYc,GAChB6Q,KAAK,WACLzS,KAAK,SACLnB,MAAO+C,EACP2W,QAAS4F,EAAQvX,SAAShF,KAE5B,gBAAC,EAAAgV,iBAAgB,CAAC9V,GAAIc,IACtB,wBAAMkV,UAAU,6BAElB,uBAAKA,UAAU,WAAU,cAAa,SAlB3B,KAuBrB,uBAAKA,UAAU,UAAU4E,KAAK,QAAO,kBAAiB,uBACpD,qBAAG5E,UAAU,YAAYhW,GAAG,uBAC1B,gBAAC,EAAA8V,iBAAgB,CAAC9V,GAAG,cAEvB,sBAAIgW,UAAU,uBACX,IAAAvV,SAAuB0c,EAAS,YAAa,IAC3Clb,OACArD,IAAI,SAAAkC,GAAU,OACb,sBAAIzE,IAAKyE,GACP,yBACEd,GAAI,qBAAcc,GAClBkV,UAAU,iDACVwB,QAAS,SAAAja,GAAK,OAAAmL,GAAiBnL,EAAGuD,EAAQuc,EAAQlY,eAApC,EACdsY,UAAW,SAAAlgB,GAAK,OAAAmL,GAAiBnL,EAAGuD,EAAQuc,EAAQlY,eAApC,GAEhB,yBACEnF,GAAI,wBAAiBc,GACrB6Q,KAAK,WACLzS,KAAK,YACLnB,MAAO+C,EACP2W,QAAS4F,EAAQrX,YAAYlF,KAE/B,gBAAC,EAAAgV,iBAAgB,CAAC9V,GAAIc,IACtB,wBAAMkV,UAAU,6BAElB,uBAAKA,UAAU,WAAU,cAAa,SAlB3B,KAuBrB,uBAAKA,UAAU,qCAAqC4E,KAAK,QAAO,kBAAiB,oBAC/E,2BACE,qBAAG5E,UAAU,YAAYhW,GAAG,oBAC1B,gBAAC,EAAA8V,iBAAgB,CAAC9V,GAAG,WAEvB,sBAAIgW,UAAU,sBACZ,0BACE,yBACEhW,GAAG,kBACHgW,UAAU,iDACVwB,QAAS,SAAAja,GAAK,OAAAoL,GAAiBpL,EAAG8f,EAAQhY,eAA5B,EACdoY,UAAW,SAAClgB,GAAM,OAAAoL,GAAiBpL,EAAG8f,EAAQhY,eAA5B,GAElB,yBACErF,GAAG,qBACH2R,KAAK,WACLzS,KAAK,WACLnB,MAAM,MACN0Z,QAAS4F,EAAQpX,gBAEnB,4BACE,gBAAC,EAAA6P,iBAAgB,CAAC9V,GAAG,0BAEvB,wBAAMgW,UAAU,6BAElB,uBAAKA,UAAU,WAAU,cAAa,UAExC,0BACE,yBACEhW,GAAG,sBACHgW,UAAU,iDACVwB,QAAS,SAAAja,GAAK,OAAAoL,GAAiBpL,EAAG8f,EAAQ9X,WAA5B,EACdkY,UAAW,SAAClgB,GAAM,OAAAoL,GAAiBpL,EAAG8f,EAAQ9X,WAA5B,GAElB,yBACEvF,GAAG,yBACH2R,KAAK,WACLzS,KAAK,OACLnB,MAAM,MACN0Z,QAAS4F,EAAQnX,YAEnB,4BACE,gBAAC,EAAA4P,iBAAgB,CAAC9V,GAAG,qBAEvB,wBAAMgW,UAAU,6BAElB,uBAAKA,UAAU,WAAU,cAAa,YAI5C,uBAAKA,UAAU,aACb,0BACEhW,GAAG,eACH2R,KAAK,QACLqE,UAAU,qDAEV,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,mBAEvB,wBAAMgW,UAAU,YAAW,cAAa,SACxC,0BACEhW,GAAG,YACH2R,KAAK,SACLqE,UAAU,gCAEV,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,eAQrC,EAEa,GAEa,SAAC,GAAD,IACxB0d,EAAsB,yBACtBC,EAAc,iBACdC,EAAe,kBACflf,EAAQ,WACR0d,EAAK,QAELyB,GADS,YACC,cACVC,EAAW,cAEL,IAAwB,YAAe,GAAM,GAA5CC,EAAO,KAAEX,EAAU,KACpB,IpB1ID,SAA2B1e,GAA3B,IAIC,IAAwB,WAAiC,CAAEye,QAAS,CAAC,EAA8Bze,SAAU,KAAK,GAAjH2d,EAAO,KAAE2B,EAAU,KACpB,IAAuBpZ,EAAqCL,KAAsB,GAAjFzD,EAAM,KAAEiE,EAAU,KACnBkZ,EAAYzX,EAAY9H,GAiD9B,OAhDA,YAAgB,eAuBRwf,EJxGwBrd,EIkF1Bsc,EAAmCd,EAAQc,QAE5CA,EAAQ9O,QACF8O,EAAQ/O,WACT1P,EAASjC,SAAWwhB,EAAUxhB,SAEpC0gB,EAAU,CACR9O,QJzF0BxN,EIyFAnC,GJxFzB,IAAA+B,SAAsCI,OAAWzF,EAAW,IAAI8F,OACrE,SAACW,EAAK1B,GACI,IAAA8E,EAAU1D,EAAsBpB,EAASpB,iBAAgB,MASjE,OARIgD,QAAQkD,IACVA,EAAMjD,MAAM,KACTpD,IAAI,SAACP,GAAc,OAAAA,EAAEsD,MAAF,GACnBb,OAAOiB,SACP7B,QAAQ,SAAC7B,GACJwD,EAAI9B,QAAQ1B,GAAK,GAAGwD,EAAI/D,KAAKO,EACnC,GAEGwD,EAAII,MACb,EAAG,IACHnB,OAAOiB,SAASE,QI4EZmM,UAAW,CAAC,UAAW,SAAU,WAc/B8P,EAAWxf,EAASoC,OACxB,SAAAjC,GAAA,IACUiP,EAA0BjP,EAAO,WAArB8O,EAAc9O,EAAO,UACnC,EAAsB0C,EAAsB1C,EAAQE,iBAAlD+C,EAAQ,WAAEmD,EAAK,QACnBkZ,GAAgB,EAQpB,OAPIrd,EAAOwE,WAAY6Y,IAASrQ,GAAcH,GACrC7M,EAAO0E,SAAQ2Y,EAAQrQ,GAAcH,GAC1CwQ,GAAQrd,EAAOmE,MAAMxI,OAAS,IAAG0hB,IAASrd,EAAOmE,MAAMjG,KAAK,SAAAof,GAAK,OAACnZ,GAAS,IAAIlF,QAAQqe,IAAM,CAA5B,IACjED,GAAQrd,EAAOgB,SAASrF,OAAS,IACQ0hB,EAAvCrd,EAAOgB,SAAS/B,QAAQ,UAAY,GAAW,oBAAoBoe,KAAKrc,GAAY,MAC1EhB,EAAOgB,SAAS9C,KAAK,SAAAX,GAAK,OAACyD,GAAY,IAAI/B,QAAQ1B,IAAM,CAA/B,IAEnC8f,CACT,GACAlc,KACA,SAACpI,EAAQ2B,GAAT,IACQ6iB,EAAgBxkB,EAAEiH,EAAO8E,SAAWrE,EAAsB1H,EAAEkF,iBAAiB+B,EAAO8E,SAAW,GAC/F0Y,EAAgB9iB,EAAEsF,EAAO8E,SAAWrE,EAAsB/F,EAAEuD,iBAAiB+B,EAAO8E,SAAW,GACrG,OAAOyY,EAAME,cAAcD,OAAOljB,EAAW,CAAEojB,SAAS,EAAMC,YAAa,UAAkC,SAArB3d,EAAO+E,WAAwB,EAAI,EAE7H,GAEFmY,EAAW,CAAEb,QAAO,EAAEze,SAAUwf,GAClC,EAAG,CAACpd,EAAQpC,IAEL,CAAC2d,EAAStX,EACnB,CoBkF6B2Z,CAAkBhgB,GAAS,GAA/Cwf,EAAQ,KAAEhB,EAAM,KAEXC,EACH,aAAiB,EAAAwB,eAAc,eAClCC,GAAmB,IAAAne,SAA2Byd,EAAU,WAAY,IACpE1C,IAAa9c,GAAYA,EAASjC,OAAS,GAEjD,OACE,uBAAKuZ,UAAU,sBACb,gBAAC,GAAO,CAACY,KAAMiH,GACb,uBAAK7H,UAAU,2EAA2E4E,KAAK,WAC7F,sBAAI5E,UAAU,qEAAqE4E,KAAK,gBACtF,sBAAID,SAAUuC,EAAOpX,WAAa,GAAK,EAAGkQ,UAAW,qBAAckH,EAAOpX,WAAa,SAAW,IAAM8U,KAAK,MAAMpD,QAAS,WAAM,OAAA0F,EAAOhY,UAAP,EAAmB2Z,QAAS,WAAM,OAAA3B,EAAOhY,UAAP,EAAiB,gBAAkBgY,EAAOpX,WAAa,OAAS,SAClO,yBAAO9F,GAAG,aAAagW,UAAU,WAC/B,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,uBAGxB,IAAAS,SAAuByd,EAAU,iBAAkB,IACjDpd,OAAO,SAAAA,GAAU,OAAAqc,EAAQ9O,OAAOtO,QAAQe,IAAW,CAAlC,GACjBlC,IAAI,SAAAkC,GAAU,OACb,sBACE6Z,SAAUuC,EAAOnX,UAAUjF,GAAU,GAAK,EAC1C8Z,KAAK,MACL5a,GAAIc,EACJzE,IAAKyE,EACL+d,QAAS,SAACthB,QACUnC,IAAdmC,EAAEsf,SAAuC,KAAdtf,EAAEsf,UAC/BK,EAAOhY,SAASpE,GAChBsc,GAAW,GAEf,EACA5F,QAAS,WACP0F,EAAOhY,SAASpE,GAChBsc,GAAW,EACb,EACApH,UAAW,qBACTkH,EAAOnX,UAAUjF,GAAU,SAAW,IACtC,gBACcoc,EAAOnX,UAAUjF,GAAU,OAAS,SAEpD,yBAAOkV,UAAU,UAAUhW,GAAI,iBAAUc,IACvC,gBAAC,EAAAgV,iBAAgB,CAAC9V,GAAI,sBAAec,MAtB5B,IA2BnB,uBACEkV,UAAU,sEAAqE,cACnE,SAEd,uBAAKA,UAAU,+BAA8B,cAAa,SAC1D,uBAAKA,UAAU,WAAU,cAAa,SACtC,sBAAIA,UAAU,iDAAiD4E,KAAK,gBAClE,0BACE,0BACE5a,GAAG,mBACHwX,QAAS,WAAM,OAAA4F,GAAYW,EAAZ,EAAoB,gBACpBA,EACfvC,SAAUA,EACVxF,UAAU,8CAEV,wBAAMA,UAAU,4EACd,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,qB,KAErB,wBACEgW,UAAW,6CACT+H,EAAU,WAAa,WAGzB,wBACE/H,UAAW,+BACT+H,EAAU,WAAa,YAG3B,wBACE/H,UAAW,+BACT+H,EAAU,WAAa,mBAUzC,gBAAC,GAAO,CAACnH,KAAMmH,GACb,gBAACjV,GAAe,CACdqU,QAASe,EAASf,QAClBD,OAAQA,EACRE,WAAYA,KAGhB,uBAAKpH,UAAU,iDACb,uBAAKA,UAAU,qBAAoB,cAAa,SAChD,uBAAKA,UAAU,sBACb,gBAAC,GAAO,CACNY,KAAM7U,QAAQqa,GACd0C,YACE,4BACE,gBAAC,EAAAhJ,iBAAgB,CAAC9V,GAAG,uB,MACpB,IAAAS,SAAQyd,EAAU,kBAAmB,G,MAIzC9B,GAEH,gBAAC,GAAO,CAACxF,KAAMkH,GACb,uBAAK9H,UAAU,eAGnB,gBAAC,GAAO,CAACY,KAAMkH,GACb,uBAAK9H,UAAU,+BACb,uBAAKA,UAAU,aAAa4E,KAAK,QAAO,kBAAiB,iBACvD,wBAAM5E,UAAU,mBAAmBhW,GAAG,iBACpC,gBAAC,EAAA8V,iBAAgB,CAAC9V,GAAG,cAEvB,wBAAMgW,UAAU,YAAW,cAAa,UACvC,IAAAvV,SAAuByd,EAAU,oBAAqB,IACpDpd,OAAO,SAAAA,GAAU,OAAAqc,EAAQ/O,UAAUrO,QAAQe,IAAW,CAArC,GACjBlC,IAAI,SAACkC,EAAQhH,GAAM,OAClB,gBAAC,WAAc,CAACuC,IAAKvC,GACnB,yBACEkG,GAAI,eAAQc,GACZ0W,QAAS,SAAAja,GACPmL,GAAiBnL,EAAGuD,EAAQoc,EAAO/X,gBACnCiY,GAAW,EACb,EACAK,UAAW,SAAAlgB,GACTmL,GAAiBnL,EAAGuD,EAAQoc,EAAO/X,gBACnCiY,GAAW,EACb,EACApH,UAAU,kDAEV,yBACEhW,GAAI,gBAASc,GACb6Q,KAAK,WACLzS,KAAK,WACLsc,SAAUA,EACV/D,QAASyF,EAAOlX,YAAYlF,KAE9B,gBAAC,EAAAgV,iBAAgB,CAAC9V,GAAIc,IACtB,wBAAMkV,UAAU,6BAElB,wBAAMA,UAAU,YAAW,cAAa,SAxBxB,IA4BxB,uBAAKA,UAAU,oBAAmB,cAAa,SAC/C,uBAAKA,UAAU,cACb,wBAAMA,UAAU,oBACd,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,aAGvB,0BACEA,GAAG,aACHwX,QAAS,WAAM,OAAA0F,EAAOxX,QAAQ,gBAAf,EACf8V,SAAUA,EACVxF,UAAU,4GAEV,wBAAMA,UAAU,WACd,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,aAEvB,gBAAC,GAAO,CACN4W,KAA6B,sBAAvBsG,EAAO/W,YACb2Y,YAAa,gBAAC,EAAAhJ,iBAAgB,CAAC9V,GAAG,SAElC,gBAAC,EAAA8V,iBAAgB,CAAC9V,GAAG,UAGzB,0BACEA,GAAG,eACHwX,QAAS,WAAM,OAAA0F,EAAOxX,QAAQ,OAAf,EACf8V,SAAUA,EACVxF,UAAU,4GAEV,wBAAMA,UAAU,WACd,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,aAEvB,gBAAC,GAAO,CACN4W,KAA6B,aAAvBsG,EAAO/W,YACb2Y,YAAa,gBAAC,EAAAhJ,iBAAgB,CAAC9V,GAAG,SAElC,gBAAC,EAAA8V,iBAAgB,CAAC9V,GAAG,cAOjC,uBACE4a,KAAK,QAAO,kBACK,iBAAUsC,EAAO9W,iBAClC4P,UAAU,uCAEV,gBAAC,GAAO,CAACY,MAAO4E,EAAUsD,YAAa,gBAACjW,GAAY,OAClD,gBAAC,GAAO,CACN+N,MAAM,IAAAnW,SAAQme,EAAkB,SAAU,GAAK,EAC/CE,YAAa,gBAACjW,GAAY,OAE1B,uBAAKmN,UAAU,mDACb,gBAACpN,GAAiB,CAACtG,KAAMsc,EAAkB9B,UAAW,SAAAje,GAAW,OAC/D,gBAACsd,GAAO,GACN9f,IAAKwC,EAAQmB,IACTnB,EAAO,CACXgP,aAAc+P,GAAoB/e,EAAQgP,gBAAkB8P,EAC5D7C,kBACE4C,EAAyB7e,EAAQic,kBAAoB,KANM,QAgB/E,EAEA,MC5cQ,GAAU,EAAA3F,WAAU,MAcf,GAAkB,mBAEzBpM,GAAsB,SAC1BrK,GACsB,OAAAA,EAASoC,OAAO,SAAAjC,GAAW,OAAAA,EAAQiP,UAAR,EAA3B,EAElB,GAEe,SAAC,GAAD,IACnBpP,EAAQ,WACRqgB,EAAU,aACVC,EAAe,kBAETjD,GAAO,IAAAC,WACb,OAAO,gBAAC,GAAK,CACXvG,QAAS,GACTO,UAAWtX,EAASjC,OAAS,EAAI,mBAAqB,GACtDkZ,QAAS,WACP,EAAAzD,SAASC,cAAcU,cAAc,CACnC7S,GAAI,2BACJyS,OAAQ,CACNE,SAAU,KAEZiD,OAAQ,qBAEZ,EACAF,UAAW,WACT,EAAAxD,SAASC,cAAcW,YAAY,CACjC9S,GAAI,2BACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,SAEZ,EACAkM,aAAa,EACbpJ,MAAO,gBAAC,EAAAC,iBAAgB,CAAC9V,GAAG,4BAE5B,uBAAKgW,UAAU,+BACb,qBAAGA,UAAU,4DACVtX,EACC,gBAAC,EAAAoX,iBAAgB,CACf9V,GAAG,yBACH+V,OAAQ,CACNmJ,MAAOnW,GAAoBrK,GAAUjC,OACrC4W,MAAO0L,KAGT,MAEN,uBAAK/I,UAAU,iBAAgB,cAAa,SAC5C,uBAAKA,UAAU,gCACZtX,GACKqK,GAAoBrK,GAAUE,IAC5B,SAAC,GAAD,IAAGoB,EAAE,KAAEd,EAAI,OAAEkW,EAAS,YAAEpH,EAAc,iBAC5BmR,EAAa5d,EADgC,mBACM,SAC3D,OACE,uBAAKyU,UAAU,+EACb,0BACEhW,GAAI,UAAGA,EAAE,iBACTwX,QAAS,WAAM,OAAAwH,EAAgBhR,EAAhB,EACf2D,KAAK,SACLqE,UAAU,yBAAwB,aACtB,UAAG+F,EAAKG,cAAc,CAAClc,GAAI,gBAAe,YAAId,IAE1D,wBAAM8W,UAAU,kDAElB,uBAAKA,UAAU,oFACb,uBAAK7R,IAAKiR,EAAWgB,IAAKlX,KAE5B,uBAAK8W,UAAU,cACb,qBAAGA,UAAU,6EACV9W,GAEH,uBAAK8W,UAAU,WAAU,cAAa,SACtC,qBAAGA,UAAU,oBAAoBmJ,IAIzC,GAER,uBAAKnJ,UAAU,iBAAgB,cAAa,WAGhD,uBAAKA,UAAU,kHAEb,uBAAKA,UAAU,gBACb,0BACEhW,GAAG,oBACHgW,UAAU,kBACVwB,QAAS,WACP,EAAAtF,SAASC,cAAcW,YAAY,CACjC9S,GAAI,2BACJyS,OAAQ,CACNE,SAAU,KAEZI,OAAQ,SAEZ,EAAC,eACY,SAEb,gBAAC,EAAA+C,iBAAgB,CAAC9V,GAAG,8BAIpB,EAGLgJ,IAAmB,IAAAuN,SACvB,SAAC,GAAqB,OAAW,CAAX,EACtB,SAAAE,GAAY,OACVuI,gBAAiB,SAAC9N,GAChB,OAAAuF,EAASzP,GAAgBkK,GAAzB,EAFQ,EAFW,CAMvB,IAEF,MCzHa,GAAuE,SAAC,GAAD,IAClFxS,EAAQ,WACR6M,EAAU,aACVkQ,EAAY,eAEN2D,EAAiC,SAAa,MAC9C,IAA2B,WAAe,CAAErb,YAAY,EAAOC,QAAS,SAAS,GAAhFqb,EAAO,KAAEC,EAAa,KACvBC,GAAe,IAAA9e,SAAQ8K,EAAY,QAAS,GAC5CwT,GAAa,IAAAte,SAAQ8K,EAAY,sBAAuB,GACxDiU,EAAY,WAYlB,OAVA,YAAgB,WACd,IAAMC,EAAY,WAChBtc,EAAcic,EAAUE,EAC1B,EAEA,OADAlG,OAAOL,iBAAiB,SAAU0G,GAC3B,WACLrG,OAAOe,oBAAoB,SAAUsF,EACvC,CACF,EAAG,IAEI,gBAAChX,GAAY,CAACvJ,KAAK,YACxB,uBAAK8W,UAAU,sCACb,uBAAKA,UAAU,aACb,sBAAIhW,GAAI,gBAASwf,GAAaxJ,UAAU,2EACtC,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,qBAEvB,gBAAC,EAAAuc,qBAAoB,CAACvc,GAAG,+BAErB,SAACsW,GAAgB,uBAAC,EAAAnB,WAAWlN,QAAO,CAAC2O,KAAM7U,QAAQuU,IACjD,uBAAKN,UAAU,YACf,qBAAGA,UAAU,qBAAqBM,GAFnB,IAOvB,uBACEtW,GAAG,OACHoD,IAAKgc,EACLzE,SAAU,EACVC,KAAK,SACLpD,QAAS,SAACja,GAAM,OAAAgiB,EAAe,GAAK9D,EAAa,OAAjC,EAChBgC,UAAW,SAAClgB,GAAM,OAAW,UAAVA,EAAElB,KAAiC,KAAdkB,EAAEsf,SAA6B,YAAXtf,EAAEoU,OAAuB4N,EAAe,GAAK9D,EAAa,OAApG,EAClBzF,UAAW,qFAA8EqJ,EAAQtb,WAAa,4BAA8B,IAC5IkS,MAAO,CAAE0D,KAAM0F,EAAQrb,UACvB,uBAAKgS,UAAU,wCAAwCuJ,GACvD,wBAAMvJ,UAAU,IAAG,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,uBACxC,uBAAKgW,UAAU,WAAU,gBAAC,EAAAb,WAAW9L,aAAY,CAACtL,MAAOghB,OAG7D,uBAAK/I,UAAU,WAAU,cAAa,SACtC,gBAACkH,GAAM,CACLsC,UAAWA,EACX9gB,SAAUA,EACVif,gBAAgB,IAClB,gBAACnV,GAAM,CAACmU,SAAU,EAAAhd,KAAKqB,iBAAiB+L,SACtCqP,MAAO,sBAAe,EAAAzc,KAAKqB,iBAAiB4L,kBAC5CyP,QAAS,qBAAc,EAAA1c,KAAKqB,iBAAiB+L,YAC/C,gBAAC,GAAgB,CACfrO,SAAUA,EAASoC,OAAO,SAAAjC,GAAW,OAAAA,EAAQiP,UAAR,GACrCiR,WAAYA,IAGlB,EAEA,IAAe,IAAAxI,SACb,SAAC,G,IAAEpV,EAAO,UAAEoK,EAAU,aAAoB,OACxC7M,UAAU,IAAA+B,SAAQU,EAAS,aAAe,EAAAxB,KAAKqB,iBAAiB+L,SAAU,IAC1ExB,YAAY,IAAA9K,SAAgC8K,OAAYnQ,EAAW,IAAI4D,KAAK,SAAAwR,GAAQ,OAAAA,EAAKnU,MAAQ,EAAAsD,KAAKqB,iBAAiB+L,QAAnC,GAF5C,EAI1C,SAAC0J,GAAa,OACZgF,aAAc,SAACzb,GAAY,OAAAyW,EAAS,EAAA3H,QAAQ2M,aAAa,CAAEC,WAAY,GAAuBlL,KAAM,CAAEmL,WAAY3b,KAAvF,EADf,EALhB,CAQE,IC9EW,GACa,SAAC,G,IAAEtB,EAAQ,WAAgB,OAAP,UAC5C,gBAAC+J,GAAY,CAACvJ,KAAK,uBACjB,uBAAK8W,UAAU,sCACb,uBAAKA,UAAU,aACb,sBACEhW,GAAI,kBACJgW,UAAU,2EAEV,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,uBAEvB,gBAAC,EAAAuc,qBAAoB,CAACvc,GAAG,iCACtB,SAACsW,GAAgB,OAChB,gBAAC,EAAAnB,WAAWlN,QAAO,CAAC2O,KAAM7U,QAAQuU,IAChC,uBAAKN,UAAU,YACf,qBAAGA,UAAU,qBAAqBM,GAHpB,KASxB,uBAAKN,UAAU,WAAU,cAAa,SACtC,gBAACkH,GAAM,CACLsC,UAAW,kBACX9gB,SAAUA,EACVif,gBAAgB,EAChBC,iBAAiB,EACjBF,wBAAwB,IAE1B,gBAAClV,GAAM,CACLmU,SAAU,EAAAhd,KAAKqB,iBAAiBkL,cAChCkQ,MAAO,sBAAe,EAAAzc,KAAKqB,iBAAiB4L,kBAC5CyP,QAAS,qBAAc,EAAA1c,KAAKqB,iBAAiBkL,iBA/BE,EAoCrD,IAAe,IAAAqK,SACb,SAAC,G,IAAEpV,EAAO,UAAoB,OAC5BzC,UAAU,IAAA+B,SAAQU,EAAS,WAAY,IACvC+M,QAAyB,IAAhB7M,KAAKC,SAFc,EADhC,CAKE,ICvCI2H,GAAiB,SAACsC,GACtB,IAAMmU,EAAgBnU,EAAWvM,KAAK,SAAAwR,GAAQ,OAAAA,EAAKnU,MAAQ,EAAAsD,KAAKqB,iBAAiBqM,aAAnC,GAC9C,OAAO,IAAA5M,SAA2Cif,EAAe,WAAY,IAAI1gB,KAAK,SAAAwR,GAAQ,OAAAA,EAAKnU,MAAQ,EAAAsD,KAAKqB,iBAAiB0L,sBAAnC,EAChG,EAEa,GAA+C,SAAC,GAAD,IAC1D0B,EAAS,YACT1P,EAAQ,WACR6M,EAAU,aACVkQ,EAAY,eAGNkE,GAFC,UAEa1W,GAAesC,IAC7B6T,EAAiC,SAAa,MAC9C,IAA2B,WAAe,CAAErb,YAAY,EAAOC,QAAS,SAAS,GAAhFqb,EAAO,KAAEC,EAAa,KACvBP,GAAa,IAAAte,SAAQkf,EAAa,sBAAuB,GAY/D,OAVA,YAAgB,WACd,IAAMF,EAAY,WAChBtc,EAAcic,EAAUE,EAC1B,EAEA,OADAlG,OAAOL,iBAAiB,SAAU0G,GAC3B,WACLrG,OAAOe,oBAAoB,SAAUsF,EACvC,CACF,EAAG,IAEI,gBAAChX,GAAY,CAACvJ,KAAK,0BACxB,uBAAK8W,UAAU,0EACb,uBAAKA,UAAU,YACf,uBAAKA,UAAU,sCACb,uBAAKA,UAAU,aACb,sBAAIhW,GAAI,EAAAL,KAAKqB,iBAAiB0L,uBAAwBsJ,UAAU,2EAC9D,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,mCAEvB,gBAAC,EAAAuc,qBAAoB,CAACvc,GAAG,6CAErB,SAACsW,GAAgB,uBAAC,EAAAnB,WAAWlN,QAAO,CAAC2O,KAAM7U,QAAQuU,IACjD,uBAAKN,UAAU,YACf,qBAAGA,UAAU,qBAAqBM,GAFnB,IAOvB,uBACEtW,GAAG,OACHoD,IAAKgc,EACL5H,QAAS,WAAM,OAAAiE,EAAa,OAAb,EACfd,SAAU,EACVC,KAAK,SACL5E,UAAW,qFAA8EqJ,EAAQtb,WAAa,4BAA8B,MAC5IkS,MAAO,CAAE0D,KAAM0F,EAAQrb,UACvB,uBAAKgS,UAAU,yCAAwC,IAAAvV,SAAQkf,EAAa,QAAS,IACrF,wBAAM3J,UAAU,IAAG,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,uBACxC,uBAAKgW,UAAU,WAAU,gBAAC,EAAAb,WAAW9L,aAAY,CAACtL,OAAO,IAAA0C,SAAQkf,EAAa,sBAAuB,QAGzG,uBAAK3J,UAAU,aACf,uBAAKA,UAAU,kDAEX5H,EAAUxP,IAAI,SAAAghB,GACZ,IAAM3B,EAAY/b,EAAexD,EAAUkhB,GAC3C,OAAO,gBAAC,EAAAzK,WAAWlN,QAAO,CAAC5L,IAAKujB,EAAUhJ,KAAMqH,EAAUxhB,OAAS,GACjE,gCACE,0BAAQuZ,UAAU,8BAA6B,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAI4f,KACrE,uBAAK5J,UAAU,mDACZ7T,EAAc8b,GAAWrf,IAAI,SAACC,GAAwB,uBAACsd,GAAO,GAAC9f,IAAKwC,EAAQmB,IAAQnB,EAAO,CAAEic,kBAAmB,KAA1D,KAG3D,uBAAK9E,UAAU,aAEnB,KAIN,gBAAC,GAAgB,CACftX,SAAUA,EAASoC,OAAO,SAAAjC,GAAW,OAAAA,EAAQiP,UAAR,GACrCiR,WAAYA,IAGlB,ECtFa7V,GAA6C,SAAC,G,IACzDkF,EAAS,YACTyR,EAAM,SACF,uBAACpX,GAAY,CAACvJ,KAAK,wBACvB,uBAAK8W,UAAU,gDACb,uBAAKA,UAAU,aACf,sBAAIhW,GAAI,EAAAL,KAAKqB,iBAAiBsL,qBAAsB0J,UAAU,6DAA4D,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,+BAC/I,uBAAKgW,UAAU,aAEb5H,EAAUxP,IAAI,SAAAghB,GACZ,IAAME,EAAU5d,EAAe2d,EAAQD,GACvC,OAAO,gBAAC,EAAAzK,WAAWlN,QAAO,CAAC5L,IAAKujB,EAAUhJ,KAAMkJ,EAAQrjB,OAAS,GAC/D,sBAAIuZ,UAAU,8BAA6B,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAI4f,KAChEzd,EAAc2d,GAASlhB,IAAI,SAAA8d,GAAS,uBAAC,GAAK,GAACrgB,IAAKqgB,EAAM1c,IAAQ0c,GAA1B,GAEzC,IAZA,ECRO,GAAiC,SAAC,G,IAC7C5b,EAAM,SACNsN,EAAS,YACT2R,EAAS,YACL,8BAAK/f,GAAG,mBAAmBgW,UAAU,2FACzC,uBAAKhW,GAAG,wBAAwBgW,UAAU,4EACxC,uBAAKA,UAAU,sEACb,uBAAKA,UAAU,sBACb,yBAAOuF,QAAQ,kBAAkBvF,UAAU,iEAAgE,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,8BAChI,wBAAMgW,UAAU,mCAElB,uBAAKA,UAAU,sBACb,uBAAKA,UAAU,6EACb,uBAAKA,UAAU,6EACb,0BAAQhW,GAAG,kBAAkBjC,MAAO+C,EAAQkf,SAAU,SAACziB,GAAM,OAAAwiB,EAAUxiB,EAAEnB,OAAO2B,MAAnB,EAA2BiY,UAAU,8FAChG,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,iBAEjB,SAAAiY,GAAO,iCAAQjY,GAAG,MAAMjC,MAAM,OAAOka,EAA9B,GAIT7J,EAAUxP,IAAI,SAACqhB,EAAQnmB,GAAM,OAAC,gBAAC,EAAAgc,iBAAgB,CAAC9V,GAAIigB,GAEhD,SAAAhI,GAAO,iCAAQjY,GAAI,iBAAUlG,GAAKiE,MAAOkiB,GAAShI,EAA3C,EAFkB,IAOjC,sCAAkB,OAChBhC,MAAO,CACLiK,gBAAiB,sPACjBC,mBAAoB,cACpBC,iBAAkB,YAClBlK,MAAO,OACPmK,eAAgB,aAM5B,uBAAKrK,UAAU,yCApCb,ECeO,GAAuE,SAAC,GAAD,IAClFsK,EAAc,iBACdC,EAAgB,mBAChBV,EAAM,SACNnhB,EAAQ,WACR6M,EAAU,aACVkQ,EAAY,eACZvN,EAAO,UAED,IAAsB,WAAe,OAAM,GAA1CpN,EAAM,KAAEif,EAAS,KAGlBS,GAAW,IAAAC,eASjB,OARA,YAAgB,WACdV,EAAU,MAKZ,EAAG,CAACS,IAEG,gCACL,sBAAIxK,UAAU,6DAA4D,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,wBAC/F,uBAAKgW,UAAU,aACf,gBAAC,EAAA0K,OAAM,KACL,gBAAC,EAAAC,MAAK,CAACC,KAAM,EAAApU,aAAaC,wBACxB,gBAAC,GAAM,CAAC3L,OAAQA,EAAQif,UAAWA,EAAW3R,UAAWkS,IACzD,gBAACpX,GAAM,CAAC2W,OAAQA,EAAQzR,UAAWkS,EAAexf,OAAO,SAAA+f,GAAM,MAAW,QAAX/f,GAAoB+f,EAAG9gB,QAAQe,IAAW,CAA1C,MAEjE,gBAAC,EAAA6f,MAAK,CAACC,KAAM,EAAApU,aAAaG,0BACxB,gBAAC,GAAM,CAAC7L,OAAQA,EAAQif,UAAWA,EAAW3R,UAAWmS,IACzD,gBAAC,GAAQ,CACP9E,aAAcA,EACd/c,SAAUA,EACV6M,WAAYA,EACZ6C,UAAWmS,EAAiBzf,OAAO,SAAA+f,GAAM,MAAW,QAAX/f,GAAoB+f,EAAG9gB,QAAQe,IAAW,CAA1C,GACzCoN,QAASA,KAGb,gBAAC,EAAA4S,SAAQ,CAAC5iB,GAAI,EAAAsO,aAAaC,0BAE7B,gBAACjE,GAAM,CAACmU,SAAU,EAAAhd,KAAKqB,iBAAiBqM,cACtC+O,MAAO,sBAAe,EAAAzc,KAAKqB,iBAAiB4L,kBAC5CyP,QAAS,qBAAc,EAAA1c,KAAKqB,iBAAiBqM,iBAEnD,EAEA,IAAe,IAAAkJ,SACb,SAAC,GAAD,IAAGpV,EAAO,UAAEoK,EAAU,aACdsU,GAAS,IAAApf,SAAsCU,EAAS,aAAe,EAAAxB,KAAKqB,iBAAiBsL,qBAAsB,IACnH5N,GAAW,IAAA+B,SAAsCU,EAAS,aAAe,EAAAxB,KAAKqB,iBAAiB0L,uBAAwB,IAC7H,MAAO,CACL4T,eAAgB1e,EAAqBie,GACrCU,iBAAkB3e,EAAqBlD,GACvCmhB,OAAM,EAAEnhB,SAAQ,EAAE6M,WAAU,EAC5B2C,QAAyB,IAAhB7M,KAAKC,SAElB,EACA,SAACmV,GAAa,OACZgF,aAAc,SAACzb,GAAY,OAAAyW,EAAS,EAAA3H,QAAQ2M,aAAa,CAAEC,WAAY,GAAuBlL,KAAM,CAAEmL,WAAY3b,KAAvF,EADf,EAXhB,CAcE,ICtEW,GAAuE,SAAC,G,IACnFyc,EAAQ,WACJ,uBAAChU,GAAY,CAACvJ,KAAK,qBACvB,sBAAI8W,UAAU,2EACZ,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,4BAEvB,gBAAC,EAAAuc,qBAAoB,CAACvc,GAAG,sCAErB,SAACsW,GAAgB,uBAAC,EAAAnB,WAAWlN,QAAO,CAAC2O,KAAM7U,QAAQuU,IACjD,uBAAKN,UAAU,YACf,qBAAGA,UAAU,qBAAqBM,GAFnB,GAMrB,uBAAKN,UAAU,aACf,uBAAKA,UAAU,kEAEX7T,EAAcsa,GAAU7d,IAAI,SAAA8d,GAAS,uBAAC,GAAK,GAACrgB,IAAKqgB,EAAM1c,IAAQ0c,GAA1B,IAGzC,gBAAClU,GAAM,CAACmU,SAAU,EAAAhd,KAAKqB,iBAAiBiM,MACtCmP,MAAO,sBAAe,EAAAzc,KAAKqB,iBAAiB4L,kBAC5CyP,QAAS,qBAAc,EAAA1c,KAAKqB,iBAAiBiM,SApB3C,EAuBN,IAAe,IAAAsJ,SACb,SAAC,G,IAAEpV,EAAO,UAAoB,OAC5B+M,QAAyB,IAAhB7M,KAAKC,SACdmb,UAAU,IAAAhc,SAAsCU,EAAS,aAAe,EAAAxB,KAAKqB,iBAAiBiM,MAAO,IAFzE,EADhC,CAKE,IC/BW,GAAuE,SAAC,GAsBnF,SAAS8T,EAASxjB,GAChBA,GAAKA,EAAE6d,gBAAkB7d,EAAE6d,iBAC3B,IAAM4F,IAAuB,iBAANzjB,EAAiBA,EAAI0jB,IAAe,IAAItf,OAC1DI,QAAQif,KACb,EAAA9O,SAASC,cAAcW,YAAY,CACjC9S,GAAI,eACJyS,OAAQ,CACNE,SAAU,IACVC,aAAc,EACdkB,iBAAkB,GAEpBoN,OAAQF,IAEVG,EAAQrjB,KAAK,CACXsjB,SAAU,EAAA5U,aAAa6U,UACvBjiB,OAAQ,UAAYkiB,mBAAmBN,KAEzCO,EAAcP,GACdQ,GAAc,GACdC,EAAe,IACjB,CAzCO,UAD2E,IAsD5EC,EAnDAP,GAAU,IAAAQ,cACVnB,GAAW,IAAAC,eACX,IAAgC,WAAkC,IAAG,GAApEmB,EAAW,KAAEH,EAAc,KAC5B,IAAgC,YAAe,GAAK,GAAnDI,EAAY,KAAEL,EAAa,KAC5B,IAA8B,WAAe,IAAG,GAA/CP,EAAU,KAAEM,EAAa,KAoDhC,OAlDA,YAAgB,WACY,YAAtBf,EAASY,UACXG,EAAc,GAClB,EAAG,CAACf,IAgCJ,YAAgB,WACd,SAASsB,EAAYvkB,GAC4B,IAA/C+a,EAAE/a,EAAEnB,QAAQ2lB,QAAQ,gBAAgBtlB,QAAgBglB,EAAe,GACrE,CAEA,OADA1e,SAASgW,iBAAiB,QAAS+I,GAC5B,WACL/e,SAASoX,oBAAoB,QAAS2H,EACxC,CACF,EAAG,IAEGJ,EAAgB,SAACnkB,GACrBkkB,EAAe,IACfD,GAAc,EAChB,EAEO,uBAAKxhB,GAAG,eACb,wBAAMA,GAAG,aAAagW,UAAU,qBAAqBuH,SAAUwD,GAC7D,uBAAK/K,UAAU,mEACb,gBAAC,EAAAuG,qBAAoB,CAACvc,GAAG,mBAErB,SAACiY,GAAgB,6CAAkB,SACjC+H,SAAU,SAACziB,GAAM,OAnD7B,SAAqBA,GACX,IAAAQ,EAAUR,EAAEnB,OAAM,MAC1BmlB,EAAcxjB,GACVgE,QAAQhE,IACVyjB,GAAc,GAChBC,EnCTG,SAA8B1jB,GACnC,OAAKqB,GACUrB,EAAMtB,OAJE,EAI0B2C,EAAOA,OAAOrB,GAD3C,EAGtB,CmCKmBikB,CAAqBjkB,GACtC,CA6C6BkkB,CAAY1kB,EAAZ,EACjBQ,MAAOkjB,EACPtP,KAAK,OACLqE,UAAU,uCACV8I,YAAa7G,EAAG,oBACE,OAClBiK,QAAS,WAAM,OAAAV,GAAc,EAAd,GAPA,GAWrB,uBAAKxL,UAAU,qCACb,0BAAQhW,GAAG,cAAc2R,KAAK,SAASqE,UAAU,iCAAiCkM,QAAS,SAAC3kB,GAAW,OAAAmkB,GAAA,EAAgB,aAAa,UAClI,wBAAM1L,UAAU,0CAKxB,uBAAK4E,KAAK,SAAQ,YAAW,YAAW,gBAAe,YAAY5E,UAAU,YAExE6L,GAAgB9f,QAAQkf,IACvB,IAAAxgB,SAAQmhB,EAAa,SAAU,GAAK,EAChC,gBAAC,EAAA9L,iBAAgB,CAAC9V,GAAG,2BAA2B+V,OAAQ,CAAEhY,MAAO6jB,EAAYnlB,UAC7E,gBAAC,EAAAqZ,iBAAgB,CAAC9V,GAAG,sBACvB,MAGR,gBAAC,EAAAmV,WAAWlN,QAAO,CAAC2O,MAAM,IAAAnW,SAAQmhB,EAAa,SAAU,GAAK,GAC5D,uBAAKhH,KAAK,UAAS,aAAY,qBAAqB5E,UAAU,oEAC5D,uBAAKA,UAAU,QAAQC,MAAO,CAAE0D,KAAM,OAAO,cAAc,SAEzDiI,GAAe7f,QAAQ6f,EAAYnlB,SACzB,uBAAKuZ,UAAU,iBACb,sBAAIA,UAAU,YAAY4E,KAAK,WAE3BgH,EAAYhjB,IAAI,SAACujB,GAA2B,iCAC1C,0BAAQniB,GAAI,iBAAUmiB,EAAWjjB,MAAQ0b,KAAK,SAASpD,QAAS,WAC9DuJ,EAASoB,EAAWjjB,KACtB,EAAG8W,UAAU,+BAA+BmM,EAAWjjB,MAHb,OAYtE,EAEA,IAAe,IAAAqX,SACb,SAAC,GAA6B,OAAGpV,QAAvB,UAAoB,EAC9B,SAACsV,GAAa,OACb,CADa,EAFhB,CAIE,IC7HA,GACE,EAAAtB,WAAU,QAcRhM,GAAe,SAAChJ,GAAiC,uBAAC,EAAAiiB,KAAI,CAACpiB,GAAI,eAAQG,EAASkiB,YAAcnkB,GAAIiC,EAASoM,MAAOqO,KAAK,OAAO5E,UAAW,4CAAqC7V,EAASmiB,SAAW,SAAW,KAC7M,uBAAKtM,UAAU,wCACb,wBAAMA,UAAW,UAAG7V,EAASoiB,QAAU,uBAAyB,0CAAyC,kCACvG,gBAAC,EAAAzM,iBAAgB,CAAC9V,GAAIG,EAAS0L,aAAe,SAC9C,gBAAC,GAAO,CAAC+K,UAAyBxb,IAAnB+E,EAAS8L,O,KACN9L,EAAS8L,M,MAG7B,gBAAC,GAAO,CAAC2K,KAAM7U,QAAQ5B,EAASjB,OAC9B,wBAAM8W,UAAU,kEACb7V,EAASjB,K,QAGd,gBAAC,GAAO,CAAC0X,KAAM7U,QAAQ5B,EAASqiB,gBAC9B,wBAAMxM,UAAU,mDACd,gBAAC,EAAAb,WAAW9L,aAAY,CAACtL,OAAO,IAAA0C,SAAQN,EAAU,sBAAuB,KACzE,0CAAkB,gBAAC,EAAA2V,iBAAgB,CAAC9V,GAAG,YACvC,wBAAMgW,UAAU,WAAU,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,aAAa,SAACiY,GAAQ,uCAAGA,EAAH,OAI3E,uBAAKjC,UAAU,6CACb,wBAAMA,UAAU,mDAAkD,eAAc,KAtB7B,EA0B1C,GAAuE,SAAC,G,IACnFzK,EAAU,aACN,8BAAKyK,UAAU,2BAA2B4E,KAAK,WACnD,uBAAK5E,UAAU,uFACb,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,wBAEvB,gBAACyiB,GAAM,MACP,uBAAKzM,UAAU,uBACf,sBAAIA,UAAU,sFAAsF4E,KAAK,gBAErGrP,EAAW3M,IAAI,SAAAuB,GACb,IAAMuiB,EAAS3gB,SAAQ,IAAA4gB,eAAcxiB,EAASoM,QAC9C,OAAO,sBAAIlQ,IAAK8D,EAAS9D,IAAK2Z,UAAW,gCAAyB0M,EAAS,kBAAoB,IAAO9H,KAAK,MAAK,gBAAgB8H,EAAS,OAAS,SAChJ,gBAACvZ,GAAY,KAAKhJ,IAClB,gBAAC,GAAO,CAACyW,KAAMtY,MAAM8B,QAAQD,EAASgM,WACpC,uBAAK6J,UAAW,qCAA8B0M,EAAS,SAAW,GAAE,YAAIA,EAAS,UAAY,UAAU,gBAAiBlC,SAASY,WAAajhB,EAASoM,MAAOqO,KAAK,QAE/J,IAAAna,SAAQN,EAAU,WAAY,IAAIvB,IAAI,SAACuB,GAA8B,uBAACgJ,GAAY,GAACoZ,SAAS,EAAMD,SAAUvgB,SAAQ,IAAA4gB,eAAcxiB,EAASoM,SAAapM,GAAnF,KAK/E,IAIJ,uBAAKH,GAAG,4BAA4BgW,UAAU,iDAxB1C,EA2BN,IAAe,IAAAO,SACb,SAAC,GAAgC,OAAGhL,WAAvB,aAAoB,EADnC,CAEE,ICnEM,GAAY,EAAA4J,WAAU,QAajB,GAEa,SAAC,GAAD,IA6BlByG,EA5BN5b,EAAE,KACFd,EAAI,OACJmW,EAAgB,mBAChBC,EAAe,kBACfqB,EAAY,eACZ1I,EAAgB,mBAEhB5N,EAAc,iBACd2N,EAAc,iBACd4U,EAAQ,WAER9U,GADY,eACF,cACVF,EAAU,aACVD,EAAS,YACToN,EAAa,gBAEP,IAAqB,YAAe,GAAM,GAAzCc,EAAQ,KAAEgH,EAAM,KACjB9G,GAAO,IAAAC,WAYb,OAXA,YAAgB,WACdH,GACI,EAAA3J,SAASC,cAAcW,YAAY,CACjC9S,GAAI,oBACJyS,OAAQ,CACNE,SAAU,KAEZsJ,OAAQ,gBAEhB,EAAG,CAACJ,IACED,GAAe,IAAAnb,SAAQJ,EAAgB,SAAU,GAAK,EAE1D,uBACEL,GAAIA,EACJgW,UAAW,kFACTpI,EAAa,WAAa,GAAE,YAC1BgV,GAAY9U,EAAa,WAAa,KAE1C,uBAAKkI,UAAU,iDACb,uBAAKA,UAAU,6DACb,gBAAC,GAAO,CAACY,KAAMjJ,GACb,uBAAKqI,UAAU,2FACb,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,qBAEvB,uBAAKgW,UAAU,cAEjB,yBACEhW,GAAI,gBAASA,GACbgW,UAAW,+CACTpI,EAAa,WAAa,IAE5B4J,QAAS,WAAM,OAAC5J,IAAeE,IAAe8U,GAAY7H,EAAc/M,EAAzD,GAEf,yBACEhO,GAAI,gBAASA,GACb2R,KAAK,QACL8F,QAASmL,GAAY9U,EACrB0N,SAAU5N,IAEZ,wBAAMoI,UAAU,iBAChB,wBAAMA,UAAU,kEACb9W,GAEH,gBAAC,GAAK,CAACyX,aAAcA,EAAc1I,iBAAkBA,MAGzD,uBAAK+H,UAAU,yCACf,uBAAKA,UAAU,2DACb,uBAAKA,UAAU,kEACb,qBAAGA,UAAU,sBAEVV,GAAmBD,IAGxB,uBAAKW,UAAU,2CAAyC,KACxD,uBAAKA,UAAU,gBACb,uBAAKA,UAAU,wBACf,uBACEA,UAAU,oDAAmD,cACjD,SAEX,IAAAvV,SAA2BJ,OAAgBjF,EAAW,IACpDmD,MAAM,EAAG,IACTK,IAAI,SAAAC,GAAW,OACd,uBAAKmX,UAAU,gBACb,uBACE7R,KAAK,IAAA1D,SAAQ5B,EAAS,YAAa,IACnCuX,KAAK,IAAA3V,SAAQ5B,EAAS,OAAQ,MAJpB,IASpB,uBAAKmX,UAAU,8BACf,gBAAC,GAAO,CAACY,KAAMgF,GACb,uBAAK5F,UAAU,mCACb,0BACEhW,GAAI,yBAAkBA,GACtBwX,QAAS,WAAM,OAAAqL,GAAQhH,EAAR,EAAiB,gBAClB,kBAAiB,cACnB,WACZ7F,UAAU,qIAAoI,gBAC/H6F,EAAQ,aACX,UAAGE,EAAKG,cAAc,CAAClc,GAAI,kBAAiB,YAAI+b,EAAKG,cAAc,CAAClc,GAAI,aAAY,YAAId,IAGpG,wBACE8W,UAAW,6CACT6F,EAAW,WAAa,WAG1B,wBACE7F,UAAW,+BACT6F,EAAW,WAAa,YAG5B,wBACE7F,UAAW,+BACT6F,EAAW,WAAa,aAI9B,wBAAM7F,UAAU,+CACd,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,uBAM/B,uBAAKgW,UAAU,YAGnB,gBAAC,GAAO,CAACY,KAAMiF,GACb,uBAAK7F,UAAU,2CAA2C4E,KAAK,UAC7D,uBAAK5E,UAAU,mBACf,gBAACkH,GAAM,CACLsC,UAAW,gBAASxf,GACpBtB,UAAU,IAAA+B,SAAQJ,OAAgBjF,EAAW,IAC7CghB,MAAO,gBAAC,EAAAtG,iBAAgB,CAAC9V,GAAG,mBAAmB+V,OAAQ,CAAE7W,KAAI,SAMzE,EAEA,IAAe,IAAAqX,SAKb,SAAC,GAAqB,OAAG,CAAH,EACtB,SAAAE,GAAY,OACVsE,cAAe,SAAC7J,GACd,OAAAuF,EAASzP,GAAgBkK,GAAzB,EAFQ,EANd,CAUE,ICrKW,GAAuE,SAAC,G,IACnFuL,EAAQ,WACJ,uBAAChU,GAAY,CAACvJ,KAAK,mBACvB,sBAAI8W,UAAU,2EACZ,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,kCAEvB,gBAAC,EAAAuc,qBAAoB,CAACvc,GAAG,4CAErB,SAACsW,GAAgB,uBAAC,EAAAnB,WAAWlN,QAAO,CAAC2O,KAAM7U,QAAQuU,IACjD,uBAAKN,UAAU,aACf,qBAAGA,UAAU,qBAAqBM,GAFnB,GAMrB,uBAAKN,UAAU,qBAAoB,eAAc,IAE/C7T,EAAcsa,GAAU7d,IAAI,SAAAR,GAAQ,uBAAC0kB,GAAO,GAACzmB,IAAK+B,EAAK2C,gBAAiB6hB,SAAUnG,EAAShgB,OAAS,GAAO2B,EAAI,CAAEwP,aAAa,IAAAnN,SAAQrC,EAAM,uBAAuB,KAA/H,GAEtC,gBAACoK,GAAM,CAACmU,SAAU,EAAAhd,KAAKqB,iBAAiB4L,iBACtCwP,MAAO,sBAAe,EAAAzc,KAAKqB,iBAAiB4L,kBAC5CyP,QAAS,qBAAc,EAAA1c,KAAKqB,iBAAiB4L,oBAlB3C,EAqBN,IAAe,IAAA2J,SACb,SAAC,G,IAAEpV,EAAO,UAAoB,OAC5Bsb,UAAU,IAAAhc,SAAsCU,EAAS,aAAe,EAAAxB,KAAKqB,iBAAiB4L,iBAAkB,IAC7G9L,OAAO,SAAA1C,GAAQ,OAAAA,EAAKsB,sBAAwB,EAAAC,KAAKC,qBAAqBmjB,OAAvD,GAFU,EADhC,CAKE,ICrCM,GAAY,EAAA5N,WAAU,QAE1B/L,GAAS,GA+Eb,GA7De,SAACiO,GAAD,IACPZ,GAAW,IAAA+F,eACXgE,GAAW,IAAAC,eACXO,EAAQ,UAAsB,WAAM,WAAAvgB,SAnB5C,SAA0BugB,GACxB,OAAKjf,QAAQif,GACCA,EAAMgC,QAAQ,IAAK,IAAIhhB,MAAM,KAC9Bd,OACX,SAACW,EAAUohB,GACT,IAAMC,EAAOD,EAAKjhB,MAAM,KAExB,OADAH,EAAIqhB,EAAK,IAAMC,mBAAmBD,EAAK,IAAM,IACtCrhB,CACT,EAAG,CAAC,GAPsB,CAAC,CAS/B,CAS4DuhB,CAAiB5C,EAASphB,QAAS,QAAS,GAA5D,EAAiE,CAACohB,IACtG6C,EvCMD,SAAmBrC,GAAnB,IACC,IAAwB,WAAyC,MAAK,GAArEqC,EAAO,KAAEC,EAAU,KACpB5kB,GAAW,IAAA6kB,aAAY,SAAAhd,GAAS,WAAA9F,SAAQ8F,EAAO,mBAAoB,GAAnC,GAItC,OAHA,YAAgB,WATX,IAAyBxI,EAU5BulB,GAV4BvlB,EAUDijB,EATxB5hB,GACUrB,EAAMtB,OAVE,EAU0B2C,EAAOA,OAAOrB,GAD3C,IAUpB,EAAG,CAACijB,EAAOtiB,IACJ2kB,CACT,CuCbkBG,CAAUxC,GAgB1B,OAdA,YAAgB,WACd5X,KAAW4X,GACNqC,GACA5M,EAAS,EAAA3H,QAAQtH,eAAe,SAAU,CAC3CxH,GAAI,gBACJyS,OAAQ,CACNE,SAAU,IACVC,aAAc,EACdkB,iBAAmBuP,EAAQ5mB,OAAS,EAAK,EAAI,GAE/CykB,OAAS9X,GAAS4X,IAExB,EAAG,CAACqC,IAEGA,EAAW,gCAChB,uBAAKrN,UAAU,sCACb,uBAAKA,UAAU,aACb,sBACEhW,GAAG,SACHgW,UAAU,2EAEV,gBAAC,GAAO,CACNY,KAAMyM,EAAQ5mB,OAAS,EACvBqiB,YACE,gBAAC,EAAAhJ,iBAAgB,CACf9V,GAAG,uBACH+V,OAAQ,CAAEhY,MAAOijB,IAEhB,SAACyC,GAAc,2CAAgB,SAAS7I,KAAK,SAAS6I,EAAvC,IAIpB,gBAAC,EAAA3N,iBAAgB,CACf9V,GAAG,oBACH+V,OAAQ,CAAEhY,MAAOijB,IAEhB,SAAC0C,GAAkB,2CAAgB,SAAS9I,KAAK,SAAS8I,EAAvC,OAM9B,uBAAK1N,UAAU,WAAU,cAAa,SACtC,gBAACkH,GAAM,CACLsC,UAAU,SACV9gB,SAAU2kB,EACV1F,gBAAgB,EAChBC,iBAAiB,EACjBF,wBAAwB,EACxBI,aAAa,EACbD,YAAY,KAET,IACT,ECjFExU,GAEE,EAAA8L,WAAU,aADZ,GACE,EAAAA,WAAU,QAMR7L,GAAwF,SAAC,G,IAC7F+B,EAAI,OACJsY,EAAY,eACZC,EAAgB,mBAChBC,EAAY,eACZ9iB,EAAe,kBACX,kCACJ,uBAAKiV,UAAU,WACb,uBAAKA,UAAU,YAAY3K,EAAK,gBAAC,GAAO,CAACuL,KAAM7V,IAAoB,EAAApB,KAAKqB,iBAAiB+L,UAAY8W,EAAe,GAAG,gBAAC,EAAA/N,iBAAgB,CAAC9V,GAAG,oBAAoB+V,OAAQ,CAAE9J,MAAO4X,OACjL,2BACE,gBAACxa,GAAY,CAACtL,OAAO,IAAA0C,SAAQkjB,EAAc,QAAS,KACpD,0CAAkB,gBAAC,EAAA7N,iBAAgB,CAAC9V,GAAG,YACvC,wBAAMgW,UAAU,WAAU,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,aAAa,SAACiY,GAAQ,uCAAGA,EAAH,MAGzE,gBAAC,GAAO,CAACrB,OAAQgN,GACf,uBAAK5N,UAAU,UAAS,cAAa,SACrC,gBAAC,GAAO,CAACY,MAAM,IAAAnW,SAAQmjB,EAAkB,eAAe,IACtD,uBAAK5N,UAAU,WACb,uBAAKA,UAAU,aAAY,IAAAvV,SAAQmjB,EAAkB,cAAe,KACpE,2BACE,gBAACva,GAAY,CAACtL,OAAO,IAAA0C,SAAQmjB,EAAkB,yBAA0B,KACzE,0CAAkB,gBAAC,EAAA9N,iBAAgB,CAAC9V,GAAG,YACvC,wBAAMgW,UAAU,WAAU,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,aAAa,SAACiY,GAAQ,uCAAGA,EAAH,OAI3E,gBAAC,GAAO,CAACrB,MAAM,IAAAnW,SAAQmjB,EAAkB,cAAc,IACrD,2BACE,gBAAC,EAAA7M,cAAa,CAAChZ,OAAO,IAAA0C,SAAQmjB,EAAkB,aAAc,IAAK5M,OAAO,SAASC,SAAS,OACzF,SAACC,GAAuB,uBAAC,EAAApB,iBAAgB,CAAC9V,GAAG,mBAAmB+V,OAAQ,CAAEmB,WAAU,IAA5D,MAxB7B,EA+BA,GAAuC,SAAC,GAAD,IAC3CjM,EAAc,iBAER,IAA0B,YAAe,GAAM,GAA9C4Q,EAAQ,KAAES,EAAW,KACtBwH,EAAejI,EAAW,gBAAkB,cAelD,OAbA,YAAgB,WAEVA,GACF,EAAA3J,SAASC,cAAcW,YAAY,CACjC9S,GAAI,sBACJyS,OAAQ,CACNE,SAAU,KAEZsJ,OAAQ,yBAGd,EAAG,CAACJ,IAEG,gBAAC,GAAO,CAACjF,KAAMtY,MAAM8B,QAAQ6K,IAAmBA,EAAexO,OAAS,GAC7E,2BAASuZ,UAAU,oBACjB,uBAAKA,UAAU,yCACb,uBAAKA,UAAU,0FACb,uBAAKA,UAAU,kFACb,qBAAG4E,KAAK,SAAS5a,GAAG,iBAAiB+jB,KAAK,qBAAqBvM,QAAS,WAAM,OAAA8E,GAAaT,EAAb,EAAsB,gBAAgB,kBAClH7F,UAAU,2HAA0H,gBACrH6F,GACf,wBAAM7F,UAAU,0BAAyB,YAAW,SAAQ,cAAa,OAAM,cACjE,QAAO,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAI6b,EAAW,WAAa,YACnE,wBAAM7F,UAAW,UAAG8N,EAAY,wCAAsC,cACxD,QACZ,wBAAM9N,UAAW,4BAAqB8N,KACtC,wBAAM9N,UAAW,4BAAqB8N,MAExC,uBAAK9N,UAAU,0BACb,wBAAMA,UAAU,8BAA6B,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAG,2BAClE,wBAAMgW,UAAU,0CAA0CC,MAAO,CAAE+N,QAASnI,EAAW,YAASzgB,IAAa,gBAAC,EAAA0a,iBAAgB,CAAC9V,GAAG,+BAIxI,uBAAKA,GAAG,kBACNgW,UAAU,qGACVC,MAAO,CAAE+N,QAASnI,EAAW,QAAU,SACvC,uBAAK7F,UAAU,qDAEX/K,EAAerM,IAAI,SAAC,G,IAClBmC,EAAe,kBACfF,EAAS,YACL,8BAAKmV,UAAU,6BACnB,8BAAQ,gBAAC,EAAAF,iBAAgB,CAAC9V,GAAI,iBAAUe,MACvCF,EAAUjC,IAAI,SAAAuB,GAAY,uBAACmJ,GAAY,KAAKnJ,EAAQ,CAAEY,gBAAiBA,IAA7C,GAFvB,QAWtB,EAGawI,IAAS,IAAAgN,SACpB,SAAC,GAAoC,OAAGtL,eAAvB,kBAAyD,GAArC,EADjB,CAEpB,ICjGAzB,GACE,EAAA2L,WAAU,iBAGZ,GAGE,EAAArG,QAAO,aAFTrF,GAEE,EAAAqF,QAAO,qBADT,GACE,EAAAA,QAAO,UAYLpF,GAA8D,SAAC2N,GAAD,IAO5DmJ,EANA/J,GAAW,IAAA+F,eACf2E,GAAU,IAAAQ,cAWZ,OAVA,YAAgB,WACdlL,EAAS,EAAA3H,QAAQyC,gBAAgB,EAAAzC,QAAQmV,mBAAmB9C,IAC9D,EAAG,IAEGX,GAAW,IAAAC,eACjB,YAAgB,WACdpJ,EAAM6M,WACNthB,IACAwW,OAAO+K,SAAS,EAAG,EACrB,EAAG,CAAC3D,IACG,wBAAMxgB,GAAG,eACd,yBAAOqW,wBAAyB,CAC9BC,OAAQ,0PAUV,gBAAC/M,GAAM,MACP,uBAAKyM,UAAU,aACf,uBAAKA,UAAU,sCACb,uBAAKA,UAAW,+EAAwEqB,EAAMvC,UAAY,kBAAoB,KAC5H,gBAACsP,GAAU,OAEb,uBAAKpO,UAAU,0FACb,gBAAC,EAAA0K,OAAM,KACL,gBAAC,EAAAC,MAAK,CAAC0D,OAAK,EAACzD,KAAM,EAAApU,aAAaM,aAC9B,gBAACwX,GAAQ,OAEX,gBAAC,EAAA3D,MAAK,CAACC,KAAM,EAAApU,aAAaU,iBACxB,gBAACqX,GAAY,OAEf,gBAAC,EAAA5D,MAAK,CAACC,KAAM,EAAApU,aAAaQ,aACxB,gBAACwX,GAAQ,OAEX,gBAAC,EAAA7D,MAAK,CAACC,KAAM,EAAApU,aAAac,kBACxB,gBAACmX,GAAa,OAEhB,gBAAC,EAAA9D,MAAK,CAACC,KAAM,EAAApU,aAAaY,WACxB,gBAACsX,GAAM,OAET,gBAAC,EAAA/D,MAAK,CAACC,KAAM,EAAApU,aAAae,WACxB,gBAACoX,GAAO,OAEV,gBAAC,EAAAhE,MAAK,CAACC,KAAM,EAAApU,aAAa6U,WACxB,gBAAC,GAAM,OAGT,gBAAC,EAAAV,MAAK,CAACC,KAAK,KACV,gBAAC,EAAAE,SAAQ,CAAC5iB,GAAI,EAAAsO,aAAaM,kBAKnC,gBAAC,GAAmB,MACpB,gBAAC8X,GAAsB,MACvB,gBAACpb,GAAgB,CAACxJ,GAAG,yBACrB,uBAAKA,GAAG,eAAewX,QAASH,EAAM6M,SAAUlO,UAAW,uBAAgBqB,EAAMvC,UAAY,OAAS,QAAQ,eAAe,IAEjI,EAEA,4B,8CAqBA,QArBwB,OAEtB,YAAA+P,kBAAA,SAAkBC,GAChBppB,KAAK2b,MAAM0N,mBAAmBD,EAChC,EAEA,YAAAE,mBAAA,WACEtpB,KAAKupB,UAAY,mBAAY,EAAArV,MAAMsV,mBAAmB,EAAA1Y,aAAawD,IACrE,EAEA,YAAAmV,kBAAA,WACEzpB,KAAK2b,MAAM5N,qBAAqB,uBAClC,EAEA,YAAA2b,OAAA,WACE,OAAQ,gBAAC,EAAAC,cAAa,CAACC,SAAU5pB,KAAKupB,WACpC,gBAACvb,GAAS,KAAKhO,KAAK2b,QACpB,uBAAKrB,UAAU,aACf,uBAAKA,UAAU,aAEnB,EACF,EArBA,CAAwB,aAuBXrM,IAAc,IAAA4M,SACzB,SAAC,GAA+B,OAAGzB,UAAvB,YAAoB,EAChC,SAAC2B,GAAa,OACZsO,mBAAoB,SAAC/mB,GAAe,OAAAyY,EAAS,GAAazY,GAAtB,EACpCyL,qBAAsB,WAAM,OAAAgN,EAAShN,KAAT,EAC5Bya,SAAU,WAAM,OAAAzN,EAAS,IAAU,GAAnB,EAHJ,EAFW,CAOzB,ICnIA7M,GACE,EAAAuL,WAAU,gBAEDtL,GAAM,WAAM,uBAACD,GAAe,KACvC,gBAACD,GAAW,MADW,ECFjBG,GAAa,EAAAqE,eAAc,SAUnC,eAuBE,WAAYoX,GACV,QAAK,UAACA,IAAI,K,OACVC,EAAKxR,SAAW,E,CAClB,CACF,OA3B0B,OACjB,EAAAyR,cAAP,SAAqBzT,G,MACnB,OAAO,EAAP,IAIG,EAAAlD,QAAQjH,UAAU2H,YAAa,SAAC,G,IAAEyB,EAAO,UACxCrO,IACAoP,EAAMyE,SAAS,EAAA3H,QAAQjH,UAAUoJ,GACnC,EACA,EAAC,EAAAnC,QAAQ4W,WAAWlW,YAAa,WAC/B5M,IACAoP,EAAMyE,SAAS,EAAA3H,QAAQrH,kBACvB,EAAAqH,QAAQyC,gBAAgB,EAAAzC,QAAQ6W,iBAClC,E,CAEJ,EAWF,EA3BA,CAA0B7b,ICJxBC,GAEE,EAAA+E,QAAO,eADT,GACE,EAAAA,QAAO,gBACL9E,GAAgB,WAGtB,eACE,WAAoBgI,EAAsB4T,EAAmDlX,EAAwBY,GACnH,QAAK,YAAE,K,OADW,EAAA0C,MAAAA,EAAsB,EAAA4T,OAAAA,EAAmD,EAAAlX,OAAAA,EAAwB,EAAAY,KAAAA,E,CAErH,CAyCF,OA5C6C,OAW3C,YAAAuW,KAAA,WACEnqB,KAAK4T,KAAKwW,UAAUN,GAAKC,cAAc/pB,KAAKsW,QAC5CtW,KAAKsW,MAAMyE,SAAS1M,GAAerO,KAAKgT,SACxChT,KAAKsW,MAAMyE,SAAS1M,GAAerO,KAAKkqB,OAAOvO,QAC/C3b,KAAKsW,MAAMyE,SAAS,GAAgB,EAAAxH,cAAcC,MACpD,EAOA,YAAApM,QAAA,WACEpH,KAAK4T,KAAKyW,cACVrqB,KAAKsW,MAAMlP,SACb,EAUA,YAAAsiB,OAAA,SAAOzrB,GACG,IAAAqY,EAAUtW,KAAI,MACtB/B,EAAKyrB,OACH,gBAAC,EAAAY,gBAAe,CAACjoB,MAAO,CAAE2Q,OAAQhT,KAAKgT,SACrC,gBAAC1E,GAAa,CAAOgI,MAAK,GAAI,gBAACnI,GAAG,QAGxC,EA3CkC,IADnC,IAAAoc,QAAO,CAAEC,UAAW,a,uBAEQhR,GAAuB,EAAAiR,eAAmD3X,GAAsBgX,MADxGY,E,CAArB,CAA6C,EAAAC,Y", "sources": ["omf-changepackage-tv:///webpack/universalModuleDefinition?", "omf-changepackage-tv:///webpack/bootstrap?", "omf-changepackage-tv:///./tslib/tslib.es6.mjs?", "omf-changepackage-tv:///../src/utils/Search.ts?", "omf-changepackage-tv:///../src/mutators/index.ts?", "omf-changepackage-tv:///../src/utils/Characteristics.ts?", "omf-changepackage-tv:///../src/views/Components/Tooltip.tsx?", "omf-changepackage-tv:///../src/utils/Floater.ts?", "omf-changepackage-tv:///../src/utils/deepCopy.ts?", "omf-changepackage-tv:///../src/utils/Filter.ts?", "omf-changepackage-tv:///../src/store/Actions.ts?", "omf-changepackage-tv:///../src/Config.ts?", "omf-changepackage-tv:///../src/Client.ts?", "omf-changepackage-tv:///../src/store/Epics/Catalog.ts?", "omf-changepackage-tv:///../src/store/Epics/Order.ts?", "omf-changepackage-tv:///../src/store/Epics/UserAccount.ts?", "omf-changepackage-tv:///../src/store/Epics/Omniture.ts?", "omf-changepackage-tv:///../src/store/Epics.ts?", "omf-changepackage-tv:///../src/Localization.ts?", "omf-changepackage-tv:///../src/store/Store.ts?", "omf-changepackage-tv:///../src/views/Modals/Details.tsx?", "omf-changepackage-tv:///../src/views/Components/Price.tsx?", "omf-changepackage-tv:///../src/views/Modals/MultipleWays.tsx?", "omf-changepackage-tv:///../src/views/Components/Channel.tsx?", "omf-changepackage-tv:///../src/views/Components/Combo.tsx?", "omf-changepackage-tv:///../src/views/Components/Legal.tsx?", "omf-changepackage-tv:///../src/views/Components/Omniture.tsx?", "omf-changepackage-tv:///../src/views/Addons/index.tsx?", "omf-changepackage-tv:///../src/views/Components/ProgressiveLoad.tsx?", "omf-changepackage-tv:///../src/views/Components/Filter.tsx?", "omf-changepackage-tv:///../src/views/Modals/SelectedChannels.tsx?", "omf-changepackage-tv:///../src/views/Alacarte/index.tsx?", "omf-changepackage-tv:///../src/views/Browser/index.tsx?", "omf-changepackage-tv:///../src/views/International/Alacarte.tsx?", "omf-changepackage-tv:///../src/views/International/Combos.tsx?", "omf-changepackage-tv:///../src/views/International/Filter.tsx?", "omf-changepackage-tv:///../src/views/International/index.tsx?", "omf-changepackage-tv:///../src/views/MoviesSeries/index.tsx?", "omf-changepackage-tv:///../src/views/Navigation/Search.tsx?", "omf-changepackage-tv:///../src/views/Navigation/index.tsx?", "omf-changepackage-tv:///../src/views/Packages/Package.tsx?", "omf-changepackage-tv:///../src/views/Packages/index.tsx?", "omf-changepackage-tv:///../src/views/Search/index.tsx?", "omf-changepackage-tv:///../src/views/header/index.tsx?", "omf-changepackage-tv:///../src/views/index.tsx?", "omf-changepackage-tv:///../src/App.tsx?", "omf-changepackage-tv:///../src/Pipe.ts?", "omf-changepackage-tv:///../src/Widget.tsx?", "omf-changepackage-tv:///external umd {\"root\":\"JSSearch\",\"commonjs2\":\"js-search\",\"commonjs\":\"js-search\",\"amd\":\"js-search\"}?", "omf-changepackage-tv:///external umd {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}?", "omf-changepackage-tv:///external umd {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}?", "omf-changepackage-tv:///external umd {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}?", "omf-changepackage-tv:///external umd {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}?", "omf-changepackage-tv:///external umd {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}?", "omf-changepackage-tv:///external umd {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}?", "omf-changepackage-tv:///external umd {\"root\":\"ReactRouterDOM\",\"commonjs2\":\"react-router-dom\",\"commonjs\":\"react-router-dom\",\"amd\":\"react-router-dom\"}?", "omf-changepackage-tv:///external umd {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}?", "omf-changepackage-tv:///external umd {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}?", "omf-changepackage-tv:///external umd {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}?", "omf-changepackage-tv:///webpack/runtime/define property getters?", "omf-changepackage-tv:///webpack/runtime/hasOwnProperty shorthand?", "omf-changepackage-tv:///webpack/runtime/make namespace object?"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"bwtk\"), require(\"omf-changepackage-components\"), require(\"react\"), require(\"react-redux\"), require(\"react-router-dom\"), require(\"react-intl\"), require(\"redux\"), require(\"redux-actions\"), require(\"redux-observable\"), require(\"js-search\"), require(\"rxjs\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"bwtk\", \"omf-changepackage-components\", \"react\", \"react-redux\", \"react-router-dom\", \"react-intl\", \"redux\", \"redux-actions\", \"redux-observable\", \"js-search\", \"rxjs\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"bwtk\"), require(\"omf-changepackage-components\"), require(\"react\"), require(\"react-redux\"), require(\"react-router-dom\"), require(\"react-intl\"), require(\"redux\"), require(\"redux-actions\"), require(\"redux-observable\"), require(\"js-search\"), require(\"rxjs\")) : factory(root[\"bwtk\"], root[\"OMFChangepackageComponents\"], root[\"React\"], root[\"ReactRedux\"], root[\"ReactRouterDOM\"], root[\"ReactIntl\"], root[\"Redux\"], root[\"ReduxActions\"], root[\"ReduxObservable\"], root[\"JSSearch\"], root[\"rxjs\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function(__WEBPACK_EXTERNAL_MODULE__102__, __WEBPACK_EXTERNAL_MODULE__446__, __WEBPACK_EXTERNAL_MODULE__442__, __WEBPACK_EXTERNAL_MODULE__999__, __WEBPACK_EXTERNAL_MODULE__634__, __WEBPACK_EXTERNAL_MODULE__419__, __WEBPACK_EXTERNAL_MODULE__750__, __WEBPACK_EXTERNAL_MODULE__541__, __WEBPACK_EXTERNAL_MODULE__769__, __WEBPACK_EXTERNAL_MODULE__81__, __WEBPACK_EXTERNAL_MODULE__418__) {\nreturn ", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import * as React from \"react\";\r\nimport * as JsSearch from \"js-search\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ValueOf } from \"omf-changepackage-components\";\r\nimport { ITVChannel } from \"../models\";\r\n\r\nlet search: JsSearch.Search;\r\n\r\nexport function initSearch(channels: Array<ITVChannel>) {\r\n  const filterChannels = channels.map(channel => {\r\n    const result = channel.characteristics.find(item => item.name === \"callSign\");\r\n    return {\r\n      ...channel,\r\n      callSign: result ? result.value : \"\"\r\n    };\r\n  });\r\n  search = new JsSearch.Search(\"id\");\r\n  search.addIndex(\"name\");\r\n  search.addIndex(\"channelNumber\");\r\n  search.addIndex(\"callSign\");\r\n  // search.indexStrategy = new JsSearch.AllSubstringsIndexStrategy();\r\n  // search.tokenizer = new JsSearch.StopWordsTokenizer(search.tokenizer);\r\n  search.addDocuments(filterChannels);\r\n}\r\n\r\nconst MAX_QUERY_LENGTH = 0;\r\n\r\nexport function getSearchSuggestions(value: string): Array<ITVChannel> {\r\n  if (!search) return [];\r\n  const result = value.length > MAX_QUERY_LENGTH ? search.search(value) : [];\r\n  return result as Array<ITVChannel>;\r\n}\r\n\r\nexport function getSearchResult(value: string): Array<ITVChannel> {\r\n  if (!search) return [];\r\n  const result = value.length > MAX_QUERY_LENGTH ? search.search(value) : [];\r\n  return result as Array<ITVChannel>;\r\n}\r\n\r\nexport function useSearch(query: string): Array<ITVChannel> | null {\r\n  const [results, setResults] = React.useState<Array<ITVChannel> | null>(null);\r\n  const channels = useSelector(state => ValueOf(state, \"catalog.channels\", []));\r\n  React.useEffect(() => {\r\n    setResults(getSearchResult(query));\r\n  }, [query, channels]);\r\n  return results;\r\n}\r\n", "import { EWidgetRoute, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport {\r\n  IAccountDetail,\r\n  INavigationItem,\r\n  IProductOffering,\r\n  IServiceAccountAPI,\r\n  ITVCatalog,\r\n  ITVChannel\r\n} from \"../models\";\r\nimport { initSearch } from \"../utils/Search\";\r\n\r\nexport function serviceAccountMutatorFn(\r\n  response: IServiceAccountAPI\r\n): Array<IAccountDetail> {\r\n  const offerings = ValueOf<Array<IProductOffering>>(\r\n    response,\r\n    \"ProductOfferings\",\r\n    []\r\n  );\r\n  const accountDetails: Array<IAccountDetail> = [],\r\n    keys: Array<string> = [];\r\n  const getSortPriority = (offering: any): number => {\r\n    const priority = ValueOf(offering, \"Characteristics\", []).find(\r\n      (item: any) => item.Name === \"sortPriority\"\r\n    );\r\n    return ValueOf(priority, \"Value\", 0) * 1;\r\n  };\r\n  offerings.forEach(offering => {\r\n    if (\r\n      offering.DisplayGroupKey &&\r\n      keys.indexOf(offering.DisplayGroupKey) < 0\r\n    ) {\r\n      keys.push(offering.DisplayGroupKey);\r\n      accountDetails.push({\r\n        displayGroupKey: offering.DisplayGroupKey,\r\n        offerings: offerings\r\n          .filter(item => item.DisplayGroupKey === offering.DisplayGroupKey)\r\n          .sort((a, b) => getSortPriority(a) - getSortPriority(b))\r\n      });\r\n    }\r\n  });\r\n  return accountDetails.sort((a: any, b: any) => {\r\n    switch (true) {\r\n      case a.displayGroupKey === \"BASE_PROGRAMMING\": return -1;\r\n      case a.displayGroupKey === \"PROMOTION\": return 1;\r\n      default: return 0;\r\n    }\r\n  });\r\n}\r\n\r\n// function channelReducer(channels: Array<ITVChannel>, offering: Volt.IProductOffering): Array<ITVChannel> {\r\n//     if (\r\n//         ValueOf(offering, \"productOfferingType\", false) === Volt.EProductOfferingType.CHANNEL\r\n//     ) {\r\n//         channels.push(offering as ITVChannel);\r\n//     } else {\r\n//         const childOfferings = ValueOf(offering, \"childOfferings\", []);\r\n//         channels = channels.concat(childOfferings.reduce(channelReducer, []));\r\n//     }\r\n//     return channels;\r\n// }\r\n\r\nfunction collectChannlesFromAllOfferings(productOfferings: Array<Volt.IProductOffering>): Array<ITVChannel> {\r\n  const duplicateFilter: Array<string> = [], collection: Array<ITVChannel> = [];\r\n  function appendChannelToCollection(channel: any) {\r\n    if (channel.productOfferingType === Volt.EProductOfferingType.CHANNEL &&\r\n      duplicateFilter.indexOf(channel.id) < 0) {\r\n      collection.push(channel);\r\n      duplicateFilter.push(channel.id);\r\n    }\r\n  }\r\n  productOfferings.forEach(offering => {\r\n    if (Array.isArray(offering.childOfferings)) {\r\n      offering.childOfferings.forEach(appendChannelToCollection);\r\n    } else {\r\n      appendChannelToCollection(offering);\r\n    }\r\n  });\r\n  return collection;\r\n}\r\n\r\nexport function catalogMutatorFn(response: Volt.IAPIResponse): ITVCatalog {\r\n  const productOfferingGroup: Volt.IProductOfferingGroup = ValueOf(\r\n    response,\r\n    \"productOfferingDetail.productOfferingGroups\",\r\n    []\r\n  ).find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === \"TV\");\r\n  const productOfferingList: Array<Volt.IProductOffering> = ValueOf(\r\n    productOfferingGroup,\r\n    \"productOfferings\",\r\n    []\r\n  );\r\n  const offerings = productOfferingList\r\n    .filter(\r\n      offering =>\r\n        offering.displayGroupKey &&\r\n        offering.displayGroupKey !== Volt.EDIsplayGroupKey.NONE\r\n    )\r\n    .reduce((collection: any, offering) => {\r\n      const key = offering.displayGroupKey;\r\n      collection[key] = collection[key] || [];\r\n      collection[key].push(offering);\r\n      return collection;\r\n    }, {} as Array<{ [key: string]: Array<Volt.IProductOffering> }>);\r\n  const catalog: ITVCatalog = {\r\n    index: productOfferingList,\r\n    offerings,\r\n    channels: collectChannlesFromAllOfferings(productOfferingList)\r\n    // productOfferingList.filter(\r\n    //   offering =>\r\n    //     offering.productOfferingType === Volt.EProductOfferingType.CHANNEL\r\n    // ) as Array<ITVChannel> // .reduce(channelReducer, [])\r\n  };\r\n\r\n  (catalog as any)[\"refresh\"] = Math.random() * 1000;\r\n\r\n  // Init search indexing\r\n  initSearch(catalog.channels);\r\n  return catalog;\r\n}\r\n\r\nexport function orderMutatorFn(\r\n  response: Volt.IAPIResponse,\r\n  catalog: ITVCatalog\r\n): ITVCatalog {\r\n  const applyDiff = (list: Array<Volt.IProductOffering>) => (offering: Volt.IProductOffering) => {\r\n    const src = list.find(item => item.id === offering.id);\r\n    if (src) {\r\n      src.isCurrent = offering.isCurrent;\r\n      src.isDisabled = offering.isDisabled;\r\n      src.isSelectable = offering.isSelectable;\r\n      src.isSelected = offering.isSelected;\r\n      src.isAlreadyIncludedIn = offering.isAlreadyIncludedIn;\r\n      // src.multipleWaysToAdd = offering.multipleWaysToAdd;\r\n      src.offeringAction = offering.offeringAction;\r\n      src.promotionDetails = offering.promotionDetails;\r\n      if (src.childOfferings && offering.childOfferings) offering.childOfferings.forEach(applyDiff(src.childOfferings));\r\n    } else {\r\n      // Do something if new product\r\n\r\n    }\r\n  };\r\n  const productOfferingGroup: Volt.IProductOfferingGroup = ValueOf(\r\n    response,\r\n    \"productOfferingDetail.productOfferingGroups\",\r\n    []\r\n  ).find((group: Volt.IProductOfferingGroup) => group.lineOfBusiness === \"TV\");\r\n  const productOfferingList: Array<Volt.IProductOffering> = ValueOf(\r\n    productOfferingGroup,\r\n    \"productOfferings\",\r\n    []\r\n  );\r\n  if (ValueOf(productOfferingGroup, \"productOfferingGroupType\", \"\") === \"Delta\") {\r\n    productOfferingList.forEach(applyDiff(catalog.index));\r\n    (catalog.offerings as any)[\"refresh\"] = Math.random() * 1000;\r\n    catalog.channels = [...catalog.channels];\r\n    // udpate search indexing\r\n    initSearch(catalog.channels);\r\n  } else {\r\n    const newCatalog = catalogMutatorFn(response);\r\n    return { ...newCatalog, refresh: Math.random() * 1000 } as ITVCatalog;\r\n  }\r\n\r\n  return { ...catalog, refresh: Math.random() * 1000 } as ITVCatalog;\r\n}\r\n\r\n/*\r\n    TV_BASE_PRODUCT,\r\n    ALACARTE,\r\n    MOVIE,\r\n    TV,\r\n    SPECIALITY_SPORTS,\r\n    ADD_ON,\r\n    INTERNATIONAL,\r\n    BASE_PROGRAMMING,\r\n    SPECIALITY_CHANNELS,\r\n    OFFERS,\r\n    NONE\r\n*/\r\n\r\nexport function navigationMutatorFn(\r\n  response: Volt.IAPIResponse\r\n): Array<INavigationItem> {\r\n  const itemSorter = (a: INavigationItem, b: INavigationItem): number =>\r\n    a.sortPriority - b.sortPriority;\r\n  const displayGroups = ValueOf(\r\n    response,\r\n    \"productOfferingDetail.displayGroup\",\r\n    {}\r\n  );\r\n  const baseOffering = ValueOf<INavigationItem>(\r\n    displayGroups,\r\n    \"baseOffering\",\r\n    null\r\n  );\r\n  const additionalOfferings = ValueOf(displayGroups, \"additionalOfferings\", [])\r\n    .filter(\r\n      (offering: INavigationItem) =>\r\n        offering.key && offering.key !== Volt.EDIsplayGroupKey.NONE\r\n    )\r\n    .map((offering: INavigationItem) => ({\r\n      ...offering,\r\n      offeringKey: offering.key\r\n    }));\r\n  const rootOffering = additionalOfferings.filter(\r\n    (offering: INavigationItem) => offering.isRoot\r\n  );\r\n  const suplimentaryOffering = additionalOfferings.filter(\r\n    (offering: INavigationItem) => !offering.isRoot\r\n  );\r\n  // Remove count prop from base package\r\n  // so it does not show on the sidebar\r\n  if (baseOffering) {\r\n    (baseOffering as any).count = undefined;\r\n  }\r\n  const navigation: Array<INavigationItem> = baseOffering\r\n    ? [baseOffering, ...rootOffering]\r\n    : rootOffering;\r\n  navigation.push({\r\n    offeringKey: Volt.EDIsplayGroupKey.TV_BROWSE_ALL,\r\n    sortPriority: 99\r\n  } as INavigationItem);\r\n  navigation.forEach((offering: INavigationItem) => {\r\n    offering.offeringKey = offering.offeringKey || offering.key;\r\n    offering.children = suplimentaryOffering\r\n      .filter((child: INavigationItem) => child.parentKey === offering.key)\r\n      .sort(itemSorter)\r\n      .map((child: INavigationItem) => {\r\n        // child.route = `#${child.key}`;\r\n        switch (child.offeringKey) {\r\n          case Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:\r\n            child.route = EWidgetRoute.TV_InternationalCombos;\r\n            break;\r\n          case Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:\r\n            child.route = EWidgetRoute.TV_InternationalAlacarte;\r\n            break;\r\n          default:\r\n            // No specific route for other offering keys\r\n            break;\r\n        }\r\n        return child;\r\n      });\r\n    switch (offering.offeringKey) {\r\n      case Volt.EDIsplayGroupKey.BASE_PROGRAMMING:\r\n      case Volt.EDIsplayGroupKey.TV_BASE_PRODUCT:\r\n        offering.route = EWidgetRoute.TV_Packages;\r\n        break;\r\n      case Volt.EDIsplayGroupKey.ALACARTE:\r\n        offering.route = EWidgetRoute.TV_Alacarte;\r\n        break;\r\n      case Volt.EDIsplayGroupKey.MOVIE:\r\n        offering.route = EWidgetRoute.TV_MoviesSeries;\r\n        break;\r\n      case Volt.EDIsplayGroupKey.ADD_ON:\r\n        offering.route = EWidgetRoute.TV_Addons;\r\n        break;\r\n      case Volt.EDIsplayGroupKey.INTERNATIONAL:\r\n        offering.route = EWidgetRoute.TV_International;\r\n        break;\r\n      case Volt.EDIsplayGroupKey.TV_BROWSE_ALL:\r\n        offering.route = EWidgetRoute.TV_Browse;\r\n        break;\r\n      default:\r\n        // No specific route for other offering keys\r\n        break;\r\n    }\r\n    (offering as any)[\"refresh\"] = Math.random() * 1000;\r\n  });\r\n  navigation.filter(\r\n    (offering: INavigationItem) => !Boolean(offering.parentDisplayGroup)\r\n  );\r\n  return navigation.sort(itemSorter);\r\n}\r\n", "import { Volt, ValueOf } from \"omf-changepackage-components\";\r\nimport { Localization } from \"../Localization\";\r\n\r\nexport function toCharacteristicsJSON(charactgerstics: Array<Volt.ICharacteristic>): { [key: string]: any } {\r\n  return ValueOf<Array<Volt.ICharacteristic>>(charactgerstics, undefined, []).reduce(\r\n    (json, charactgerstic) => {\r\n      if (charactgerstic.name) {\r\n        json[charactgerstic.name] = (charactgerstic.value || \"\").trim();\r\n      }\r\n      return json;\r\n    }, {} as any\r\n  );\r\n}\r\n\r\nexport function toCharacteristicsJSONArray(charactgerstics: Array<Volt.ICharacteristic>): { [key: string]: Array<any> } {\r\n  return ValueOf<Array<Volt.ICharacteristic>>(charactgerstics, undefined, []).reduce(\r\n    (json, charactgerstic) => {\r\n      if (charactgerstic.name) {\r\n        json[charactgerstic.name] = (charactgerstic.value || \"\").split(\",\").map(s => s.trim());\r\n      }\r\n      return json;\r\n    }, {} as any\r\n  );\r\n}\r\nexport function getSupportdLanguages(offerings: Array<Volt.IProductOffering>): Array<string> {\r\n  return ValueOf<Array<Volt.IProductOffering>>(offerings, undefined, []).reduce(\r\n    (acc, offering) => {\r\n      const { language } = toCharacteristicsJSON(offering.characteristics);\r\n      if (Boolean(language)) {\r\n        language.split(\",\")\r\n          .map((l: string) => l.trim())\r\n          .filter(Boolean)\r\n          .forEach((l: string) => {\r\n            if (acc.indexOf(l) < 0) acc.push(l);\r\n          });\r\n      }\r\n      return acc.sort();\r\n    }, [] as Array<string>\r\n  ).filter(Boolean).sort();\r\n}\r\n\r\nexport function getSupportdGenres(offerings: Array<Volt.IProductOffering>): Array<string> {\r\n  return ValueOf<Array<Volt.IProductOffering>>(offerings, undefined, []).reduce(\r\n    (acc, offering) => {\r\n      const { genre } = toCharacteristicsJSON(offering.characteristics);\r\n      if (Boolean(genre)) {\r\n        genre.split(\",\")\r\n          .map((l: string) => l.trim())\r\n          .filter(Boolean)\r\n          .forEach((l: string) => {\r\n            if (acc.indexOf(l) < 0) acc.push(l);\r\n          });\r\n      }\r\n      return acc.sort();\r\n    }, [] as Array<string>\r\n  ).filter(Boolean).sort();\r\n}\r\n\r\nexport function filterLanguage(offerings: Array<Volt.IProductOffering>, language: string): Array<Volt.IProductOffering> {\r\n  return offerings.filter(\r\n    offering => ((toCharacteristicsJSON(offering.characteristics)).language || \"\").indexOf(language) > -1\r\n  );\r\n}\r\n\r\nexport function sortOfferings(offerings: Array<Volt.IProductOffering>, direction: \"asc\" | \"desc\" = \"asc\"): Array<Volt.IProductOffering> {\r\n  return ValueOf<Array<Volt.IProductOffering>>(offerings, undefined, []).sort(\r\n    (a, b) => (\r\n      (ValueOf<number>(toCharacteristicsJSON(a.characteristics), \"sortPriority\", 0) -\r\n                ValueOf<number>(toCharacteristicsJSON(b.characteristics), \"sortPriority\", 0)) *\r\n            (direction === \"asc\" ? 1 : -1)\r\n    )\r\n  );\r\n}\r\n\r\nexport function translateStringList(list: string): string {\r\n  return Boolean(list) ? list.split(\",\").map(t => {\r\n    const trimmedT = t.trim();\r\n    return Localization.getLocalizedString(trimmedT);\r\n  }).join(\", \") : list;\r\n}\r\n", "// import { Utils } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { ITVChannel } from \"../../models\";\r\nimport { toCharacteristicsJSON, translateStringList } from \"../../utils/Characteristics\";\r\nimport { Localization } from \"../../Localization\";\r\n\r\nconst MAX_DESCRIPTION_LENGTH = 80;\r\nconst DELAY = 500;\r\n\r\nfunction formatDescription(txt: string) {\r\n  if (txt.length > MAX_DESCRIPTION_LENGTH)\r\n    return txt.substr(0, MAX_DESCRIPTION_LENGTH) + \"...\";\r\n  else return txt;\r\n}\r\n\r\nfunction rectContains(rect: ClientRect, e: MouseEvent) {\r\n  return (rect.top - 10 < e.y && rect.bottom + 10 > e.y) &&\r\n    (rect.left < e.x && rect.right > e.x);\r\n}\r\n\r\nfunction rectsContain(rects: Array<ClientRect>, e: MouseEvent): boolean {\r\n  return rects.filter(e => <PERSON>ole<PERSON>(e)).map(rect => rectContains(rect, e)).find(e => e) || false;\r\n}\r\n\r\nlet _popoverinst: { [key: string]: PopoverCtrl | null } = {};\r\n\r\nexport function CleanupPopoverStack() {\r\n  for (const key in _popoverinst) {\r\n    if (_popoverinst[key]) (_popoverinst[key] as PopoverCtrl).destroy();\r\n  }\r\n  // Destroy all that's left on screen\r\n  Array.from(document.querySelectorAll(\".channel-tooltip\")).forEach(el => el.remove());\r\n  _popoverinst = {};\r\n}\r\nclass PopoverCtrl {\r\n  elId: string;\r\n  visible: boolean = false;\r\n  $triggerEl: any;\r\n  _delayed: any;\r\n  onTooltipClick: (e: any) => void;\r\n  get triggerEl(): HTMLDivElement {\r\n    return (document.getElementById(this.elId)) as HTMLDivElement;\r\n  }\r\n  get tooltipEl(): HTMLDivElement {\r\n    return (document.querySelector(\".\" + this.elId + \"_inst\")) as HTMLDivElement;\r\n  }\r\n  constructor(elId: string) {\r\n    if (_popoverinst[elId]) (_popoverinst[elId] as any).destroy();\r\n    this.elId = elId;\r\n    this.$triggerEl = $(\"#\" + elId);\r\n    this.show = this.show.bind(this);\r\n    this._hide = this._hide.bind(this);\r\n    this.hide = this.hide.bind(this);\r\n    this.destroy = this.destroy.bind(this);\r\n    this._onTooltipClick = this._onTooltipClick.bind(this);\r\n    this.$triggerEl.tooltip({\r\n      trigger: \"manual\"\r\n    });\r\n    this.triggerEl\r\n      .addEventListener(\"mouseenter\", this.show);\r\n    // this.triggerEl\r\n    //   .addEventListener(\"mouseleave\", this._cleanup);\r\n    _popoverinst[elId] = this;\r\n  }\r\n  show() {\r\n    document.body.addEventListener(\"mousemove\", this._hide);\r\n    if (!this.visible && !this._delayed) {\r\n      window.addEventListener(\"scroll\", this._hide);\r\n      this._delayed = setTimeout(\r\n        () => {\r\n          this._delayed = null;\r\n          this.visible = true;\r\n          this.$triggerEl.tooltip(\"show\");\r\n          requestAnimationFrame(\r\n            () => this.tooltipEl.addEventListener(\"click\", this._onTooltipClick)\r\n          );\r\n        }, DELAY\r\n      );\r\n    }\r\n  }\r\n  _hide(e: MouseEvent) {\r\n    const target = e.target as HTMLDivElement;\r\n    if (!rectsContain([\r\n      this.triggerEl && this.triggerEl.getBoundingClientRect(),\r\n      this.tooltipEl && this.tooltipEl.getBoundingClientRect()\r\n    ], e\r\n    ) || target.classList.contains(\"tooltip-interactive\")) {\r\n      this.hide();\r\n      clearTimeout(this._delayed);\r\n      this._delayed = null;\r\n    }\r\n  }\r\n  // _cleanup() {\r\n  //   clearTimeout(this._delayed);\r\n  //   this._delayed = null;\r\n  // }\r\n  _onTooltipClick(e: any) {\r\n    this.onTooltipClick(e);\r\n  }\r\n  hide() {\r\n    if (this.visible) {\r\n      this.visible = false;\r\n      this.tooltipEl.removeEventListener(\"click\", this._onTooltipClick);\r\n      document.body.removeEventListener(\"mousemove\", this._hide);\r\n      window.removeEventListener(\"scroll\", this._hide);\r\n      this.$triggerEl.tooltip(\"hide\");\r\n    }\r\n  }\r\n  destroy() {\r\n    document.body.removeEventListener(\"mousemove\", this._hide);\r\n    window.removeEventListener(\"scroll\", this._hide);\r\n    this.triggerEl\r\n      .removeEventListener(\"mouseenter\", this.show);\r\n    // this.triggerEl\r\n    //   .removeEventListener(\"mouseleave\", this._cleanup);\r\n    _popoverinst[this.elId] = null;\r\n  }\r\n}\r\n\r\ninterface ComponentProps extends ITVChannel {\r\n  children: any;\r\n  className: string;\r\n  connectCtrl: (ctr: PopoverCtrl) => void;\r\n}\r\n\r\ndeclare const $: any;\r\n\r\nexport const Tooltip: React.FC<ComponentProps> = (props: ComponentProps) => {\r\n  const {\r\n    id,\r\n    name,\r\n    channelNumber,\r\n    imagePath,\r\n    characteristics,\r\n    shortDescription,\r\n    children,\r\n    className,\r\n    connectCtrl\r\n  } = props;\r\n  const tooltipId = React.useMemo(() => `tooltip${id}${Math.floor(Math.random() * 100)}`, [id]);\r\n\r\n  React.useEffect(() => {\r\n    const ctrl = new PopoverCtrl(tooltipId);\r\n    connectCtrl(ctrl);\r\n    return () => ctrl.destroy();\r\n  }, []);\r\n\r\n  _popoverinst[tooltipId] &&\r\n    connectCtrl(_popoverinst[tooltipId] as any);\r\n\r\n  const { culture, genre, language } = toCharacteristicsJSON(characteristics);\r\n  const TooltipBody = `<div style=\"display:flex; flex-direction: row;\">\r\n      <div style=\"flex-shrink:0; padding-right:20px\">\r\n        <img width=\"75\" class=\"img-responsive channel-border\" src=\"${imagePath}\" alt=\"${name}\" />\r\n      </div>\r\n      <div>\r\n        <div class=\"txtVirginBlue txtSize18 noMargin\">${channelNumber}</div>\r\n        <div class=\"txtBlack txtSize18 noMargin\">${[translateStringList(genre), translateStringList(culture), translateStringList(language)].filter(i => Boolean(i)).join(\" / \")}</div>\r\n        <div class=\"tooltip-description txtSize14\" style=\"color:#333\">${formatDescription(shortDescription)}</div>\r\n        <div class=\"spacer15\"></div>\r\n        <button id=\"viewDetails${id}\" class=\"txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal txtVirginBlue\">${Localization.getLocalizedString(\"View details\")}</button>\r\n      </div></div>`;\r\n\r\n  return (\r\n    <div className={className}>\r\n      <div className=\"floatL w-100\">\r\n        <div id={tooltipId}\r\n          className=\"tooltip-interactive w-100 alignIconWithText pointer\"\r\n          tabIndex={0}\r\n          role=\"tooltip\"\r\n          data-delay=\"100\"\r\n          data-html=\"true\"\r\n          data-placement=\"top\"\r\n          data-container=\"body\"\r\n          data-template={`<div class=\"tooltip channel-tooltip top in ${tooltipId}_inst\" role=\"tooltip\"><div class=\"arrow\"></div><div class=\"tooltip-inner\"></div></div>`}\r\n          data-title={TooltipBody}>\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "export function manageFloater(ref: React.RefObject<any>, callback: Function) {\r\n  const $el = ref.current;\r\n  const $parent = $el.parentElement;\r\n  const { clientHeight } = $el;\r\n  const rect = $parent.getBoundingClientRect();\r\n  const floatVal = rect.top < -clientHeight;\r\n  callback({ isFloating: floatVal, leftPos: floatVal ? rect.right - 85 : \"auto\" });\r\n}\r\n", "export function iterationCopy(src: any): any {\r\n  return JSON.parse(JSON.stringify(src));\r\n}\r\n", "import * as React from \"react\";\r\nimport { ITVChannel } from \"../models\";\r\n// import { useHistory } from \"./History\";\r\nimport { toCharacteristicsJSON, getSupportdGenres } from \"./Characteristics\";\r\nimport { iterationCopy } from \"./deepCopy\";\r\n\r\nexport interface IFilterState {\r\n  genre: Array<string>;\r\n  language: Array<string>;\r\n  // is4K: boolean;\r\n  isDontHave: boolean;\r\n  isHave: boolean;\r\n  // sort\r\n  sortBy: string;\r\n  sortOrder: \"asc\" | \"desc\";\r\n}\r\n\r\nexport interface IFilteredContentFilters {\r\n  genres: Array<string>;\r\n  languages: Array<string>;\r\n}\r\n\r\nexport interface IFilteredContent {\r\n  filters: IFilteredContentFilters;\r\n  channels: Array<ITVChannel>;\r\n}\r\n\r\nexport interface IFilterDispatcher {\r\n  toggleGenre: (genre: string) => void;\r\n  setGenre: (genre?: string) => void;\r\n  toggleLanguage: (language: string) => void;\r\n  setLanguage: (language: string) => void;\r\n  // toggleIs4K: () => void;\r\n  toggleDontHave: () => void;\r\n  toggleHave: () => void;\r\n  reset: () => void;\r\n  // sort\r\n  setSort: (prop: string) => void;\r\n  // static\r\n  hasGenre: (genre?: string) => boolean;\r\n  onlyGenre: (genre: string) => boolean;\r\n  hasLanguage: (genre: string) => boolean;\r\n  hasDontHave: () => boolean;\r\n  hasHave: () => boolean;\r\n  whichSort: () => string;\r\n  selectedGenre: () => string;\r\n  // utility\r\n  getState: () => IFilterState;\r\n  setState: (state: IFilterState) => void;\r\n}\r\n\r\nconst defaultState: IFilterState = {\r\n  genre: [],\r\n  language: [],\r\n  // is4K: false,\r\n  isDontHave: false,\r\n  isHave: false,\r\n  sortBy: \"name\",\r\n  sortOrder: \"asc\"\r\n};\r\n\r\nfunction buildDefaultFilters(): IFilterState {\r\n  // const history = useHistory();\r\n  // debugger;\r\n  return iterationCopy(defaultState);\r\n}\r\n\r\nfunction toggleArrayItem(arr: Array<string>, item: string): Array<string> {\r\n  const index = arr.indexOf(item);\r\n  if (index > -1) arr.splice(index, 1);\r\n  else arr.push(item);\r\n  return arr;\r\n}\r\n\r\nexport function useFilterDispatcher(defaultFilter?: IFilterState): [IFilterState, IFilterDispatcher] {\r\n  const [filter, setFilter] = React.useState(defaultFilter || buildDefaultFilters());\r\n  const dispatcher: IFilterDispatcher = {\r\n    toggleGenre: (genre: string) => setFilter({ ...filter, genre: toggleArrayItem(filter.genre, genre) }),\r\n    setGenre: (genre?: string) => setFilter({ ...filter, genre: genre ? [genre] : [] }),\r\n    toggleLanguage: (language: string) => setFilter({ ...filter, language: toggleArrayItem(filter.language, language) }),\r\n    setLanguage: (language: string) => setFilter({ ...filter, language: [language] }),\r\n    // toggleIs4K: () => setFilter({ ...filter, is4K: !filter.is4K }),\r\n    toggleDontHave: () => setFilter({ ...filter, isDontHave: !filter.isDontHave }),\r\n    toggleHave: () => setFilter({ ...filter, isHave: !filter.isHave }),\r\n    reset: () => setFilter(buildDefaultFilters()),\r\n    // Sort\r\n    setSort: (prop: string) => setFilter({\r\n      ...filter, sortBy: prop, sortOrder:\r\n                filter.sortBy === prop ? (\r\n                  filter.sortOrder === \"desc\" ? \"asc\" : \"desc\"\r\n                ) : \"asc\"\r\n    }),\r\n    // Static\r\n    hasGenre: (genre?: string) => genre ? filter.genre.indexOf(genre) > -1 : filter.genre.length === 0,\r\n    onlyGenre: (genre: string) => filter.genre.indexOf(genre) > -1 && filter.genre.length === 1,\r\n    hasLanguage: (language: string) => filter.language.indexOf(language) > -1,\r\n    hasDontHave: () => filter.isDontHave,\r\n    hasHave: () => filter.isHave,\r\n    whichSort: () => filter.sortBy + filter.sortOrder,\r\n    selectedGenre: () => filter.genre[0] || \"All\",\r\n    // utility\r\n    getState: () => (iterationCopy(filter)),\r\n    setState: (state) => setFilter(iterationCopy(state))\r\n  };\r\n  return [filter, dispatcher];\r\n}\r\n\r\nexport function usePrevious(value: any): any {\r\n  const ref = React.useRef(null);\r\n  React.useEffect(() => {\r\n    ref.current = value;\r\n  }, [value]);\r\n  return ref.current;\r\n}\r\n\r\nexport function useChannelsFilter(channels: Array<ITVChannel>, defaultFilter?: IFilterState): [\r\n  IFilteredContent,\r\n  IFilterDispatcher\r\n] {\r\n  const [content, setContent] = React.useState<IFilteredContent>({ filters: {} as IFilteredContentFilters, channels: [] });\r\n  const [filter, dispatcher] = useFilterDispatcher(defaultFilter || buildDefaultFilters());\r\n  const _channels = usePrevious(channels);\r\n  React.useEffect(() => {\r\n    let filters: IFilteredContentFilters = content.filters;\r\n    if (\r\n      !filters.genres ||\r\n            !filters.languages ||\r\n            channels.length !== _channels.length\r\n    ) {\r\n      filters = {\r\n        genres: getSupportdGenres(channels),\r\n        languages: [\"English\", \"French\", \"Other\"] // getSupportdLanguages(channels)\r\n      };\r\n      // channels.reduce(\r\n      //     (acc, channel) => {\r\n      //         const { language, genre } = toCharacteristicsJSON(channel.characteristics);\r\n      //         if (Boolean(genre) && acc.genres.indexOf(genre) < 0) acc.genres.push(genre);\r\n      //         if (Boolean(language) && acc.languages.indexOf(language) < 0) acc.languages.push(language);\r\n      //         return acc;\r\n      //     }, {\r\n      //         genres: [],\r\n      //         languages: []\r\n      //     } as IFilteredContentFilters\r\n      // );\r\n    }\r\n    const filtered = channels.filter(\r\n      channel => {\r\n        const { isSelected, isCurrent } = channel;\r\n        const { language, genre } = toCharacteristicsJSON(channel.characteristics);\r\n        let test: boolean = true;\r\n        if (filter.isDontHave) test = !(isSelected || isCurrent);\r\n        else if (filter.isHave) test = (isSelected || isCurrent);\r\n        if (test && filter.genre.length > 0) test = !!filter.genre.find(g => (genre || \"\").indexOf(g) > -1);\r\n        if (test && filter.language.length > 0) {\r\n          if (filter.language.indexOf(\"Other\") > -1) test = !/(english|french)/i.test(language || \"\");\r\n          else test = !!filter.language.find(l => (language || \"\").indexOf(l) > -1);\r\n        }\r\n        return test;\r\n      }\r\n    ).sort(\r\n      (a: any, b: any) => {\r\n        const testA: string = a[filter.sortBy] || toCharacteristicsJSON(a.characteristics)[filter.sortBy] || \"\";\r\n        const testB: string = b[filter.sortBy] || toCharacteristicsJSON(b.characteristics)[filter.sortBy] || \"\";\r\n        return testA.localeCompare(testB, undefined, { numeric: true, sensitivity: \"base\" }) * (filter.sortOrder === \"desc\" ? -1 : 1);\r\n        //  return testA.localeCompare(testB) * (filter.sortOrder === \"desc\" ? -1 : 1);\r\n      }\r\n    );\r\n    setContent({ filters, channels: filtered });\r\n  }, [filter, channels]);\r\n\r\n  return [content, dispatcher];\r\n}\r\n\r\nexport const handlePropFilter = (e: any, filter: any, callback: Function) => {\r\n  if (e.keyCode === 32 || e.type !== \"keydown\") {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    callback(filter);\r\n  }\r\n};\r\n\r\nexport const handleVoidFilter = (e: any, callback: Function) => {\r\n  if (e.keyCode === 32 || e.type !== \"keydown\") {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    callback();\r\n  }\r\n};\r\n", "import { Volt } from \"omf-changepackage-components\";\r\nimport { Action, createAction } from \"redux-actions\";\r\nimport { INavigationItem, IProductOffering, IServiceAccountAPI, ITVCatalog } from \"../models\";\r\nimport { catalogMutatorFn, navigationMutatorFn, orderMutatorFn, serviceAccountMutatorFn } from \"../mutators\";\r\n\r\n// Widget actions\r\nexport const getAccountDetails = createAction(\"GET_ACCOUNT_DETAILS\");\r\nexport const setAccountDetails = createAction<Array<IProductOffering>>(\"SET_ACCOUNT_DETAILS\", serviceAccountMutatorFn as any) as (response: IServiceAccountAPI) => Action<Array<IProductOffering>>;\r\nexport const getCatalog = createAction(\"GET_TV_CATALOG\");\r\nexport const setCatalog = createAction<ITVCatalog>(\"SET_TV_CATALOG\", catalogMutatorFn as any) as (response: Volt.IAPIResponse) => Action<ITVCatalog>;\r\nexport const setNavigation = createAction<Array<INavigationItem>>(\"SET_TV_NAVIGATION\", navigationMutatorFn as any) as (response: Volt.IAPIResponse) => Action<Array<INavigationItem>>;\r\n\r\nexport const toggleSelection = createAction<Volt.IHypermediaAction>(\"TOGGLE_TV_SELECTION\");\r\nexport const updateCatalog = createAction<ITVCatalog>(\"UPDATE_TV_CATALOG\", orderMutatorFn as any) as (response: Volt.IAPIResponse, catalog: ITVCatalog) => Action<ITVCatalog>;\r\n\r\n// Piped actions\r\n", "import { Injectable, CommonFeatures } from \"bwtk\";\r\nimport { Models } from \"omf-changepackage-components\";\r\n\r\nconst { BaseConfig, configProperty } = CommonFeatures;\r\n\r\ninterface IAppConfig extends Models.IBaseConfig {\r\n  flowType: string;\r\n  filters: {\r\n    languages: Array<string>;\r\n    genres: Array<string>;\r\n    sortBy: string;\r\n    sortOrder: \"asc\" | \"desc\";\r\n  };\r\n}\r\n\r\ninterface IAppAPI extends Models.IBaseWidgetAPI {\r\n  catalogAPI: string;\r\n  addCatalogAPI: string;\r\n  bundleCatalogAPI: string;\r\n  serviceAccountAPI: string;\r\n}\r\n\r\n/**\r\n * Widget configuration provider\r\n * Allows the external immutable\r\n * config setting\r\n * @export\r\n * @class Config\r\n * @extends {BaseConfig<IAppConfig>}\r\n */\r\n@Injectable\r\nexport class Config extends BaseConfig<IAppConfig> {\r\n  @configProperty({\r\n    languages: [\"English\", \"French\"],\r\n    genres: [\"Family\", \"Movies\", \"News\", \"Sports\"],\r\n    sortBy: \"name\",\r\n    sortOrder: \"desc\"\r\n  }) filters: string;\r\n  @configProperty(\"\") flowType: string;\r\n  @configProperty({}) environmentVariables: any;\r\n  @configProperty({}) mockdata: any;\r\n  @configProperty({}) headers: any;\r\n  @configProperty({ base: \"http://127.0.0.1:8881\"}) api: IAppAPI;\r\n}\r\n", "import { Injectable, AjaxServices } from \"bwtk\";\r\nimport { BaseClient } from \"omf-changepackage-components\";\r\n\r\nimport { Config } from \"./Config\";\r\n\r\n/**\r\n * Base client implementation\r\n * for AJAX calls\r\n * @export\r\n * @class Client\r\n * @extends {BaseClient}\r\n */\r\n@Injectable\r\nexport class Client extends BaseClient {\r\n  constructor(ajaxClient: AjaxServices, config: Config) {\r\n    super(ajaxClient, config);\r\n  }\r\n}\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { mergeMap, filter, catchError , of, concat } from \"rxjs\";\r\n\r\nimport { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, Utils, EFlowType } from \"omf-changepackage-components\";\r\nimport { Client } from \"../../Client\";\r\nimport {\r\n  IStoreState\r\n} from \"../../models\";\r\nimport {\r\n  getCatalog,\r\n  setCatalog,\r\n  setNavigation\r\n} from \"../Actions\";\r\nimport { Config } from \"../../Config\";\r\n\r\nconst {\r\n  errorOccured,\r\n  setWidgetStatus\r\n} = Actions;\r\n\r\n@Injectable\r\nexport class CatalogEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  constructor(private client: Client, private config: Config) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.requestCatalogEpic,\r\n    );\r\n  }\r\n\r\n  private get requestCatalogEpic(): CatalogEpic {\r\n    return (action$: any) =>\r\n      action$.pipe(\r\n        ofType(getCatalog.toString()),\r\n        filter(() => this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(() => concat(\r\n          of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),\r\n          this.client.get<AjaxResponse<Volt.IAPIResponse>>(Utils.appendRefreshOnce(\r\n            Utils.getURLByFlowType({\r\n              [EFlowType.TV]: this.config.api.catalogAPI,\r\n              [EFlowType.ADDTV]: this.config.api.addCatalogAPI,\r\n              [EFlowType.BUNDLE]: this.config.api.bundleCatalogAPI\r\n            })\r\n          )).pipe(\r\n            mergeMap((response) => FilterRestrictionObservable(response, [\r\n              setCatalog(response.data),\r\n              setNavigation(response.data),\r\n              Actions.omniPageLoaded(),\r\n              setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)\r\n            ]))\r\n          )\r\n        )),\r\n        catchError((error: Response) => of(\r\n          // Set widget to rendered state even on error so UI remains functional\r\n          setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED),\r\n          // Dispatch error action to show error message\r\n          errorOccured(new Models.ErrorHandler(\"getCatalog\", error))\r\n        ))\r\n      );\r\n  }\r\n\r\n}\r\n\r\ntype CatalogEpic = Epic<any, any, IStoreState, any>;\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { of, concat , filter, mergeMap, catchError } from \"rxjs\";\r\n\r\nimport { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, ValueOf, EWidgetName } from \"omf-changepackage-components\";\r\nimport { Client } from \"../../Client\";\r\nimport {\r\n  IStoreState\r\n} from \"../../models\";\r\nimport {\r\n  toggleSelection,\r\n  updateCatalog,\r\n  setNavigation\r\n} from \"../Actions\";\r\n\r\nconst {\r\n  setWidgetStatus,\r\n  finalizeRestriction,\r\n  clearCachedState\r\n} = Actions;\r\n\r\n@Injectable\r\nexport class OrderingEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  constructor(private client: Client) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.toggleSelectionEpic,\r\n      this.finalizeRestrictionEpic\r\n    );\r\n  }\r\n\r\n  private get toggleSelectionEpic(): CatalogEpic {\r\n    return (action$: any, state$) =>\r\n      action$.pipe(\r\n        ofType(toggleSelection.toString()),\r\n        filter(({ payload }: any) => Boolean(payload) && this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(({ payload }: ReduxActions.Action<Volt.IHypermediaAction>) =>\r\n          concat(\r\n            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),\r\n            this.client.action<AjaxResponse<Volt.IAPIResponse>>(payload).pipe(\r\n              mergeMap((response) => FilterRestrictionObservable(response, [\r\n                updateCatalog(response.data, state$.value.catalog),\r\n                setNavigation(response.data),\r\n                clearCachedState([EWidgetName.PREVIEW]),\r\n                setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)\r\n              ]))\r\n            )\r\n          )\r\n        ),\r\n        catchError(Models.ErrorHandlerObservable(toggleSelection))\r\n      );\r\n  }\r\n\r\n  private get finalizeRestrictionEpic(): CatalogEpic {\r\n    return (action$: any, state$) =>\r\n      action$.pipe(\r\n        ofType(finalizeRestriction.toString()),\r\n        filter(({ payload }: ReduxActions.Action<Volt.IAPIResponse>) => \r\n          Boolean(payload) && Boolean(payload.productOfferingDetail) && this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(({ payload }: ReduxActions.Action<Volt.IAPIResponse>) => \r\n          of(\r\n            Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(payload, \"productOfferingDetail.productConfigurationTotal\"))),\r\n            updateCatalog(payload, state$.value.catalog),\r\n            setNavigation(payload),\r\n            clearCachedState([EWidgetName.PREVIEW]),\r\n            setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)\r\n          )\r\n        )\r\n      );\r\n  }\r\n}\r\n\r\ntype CatalogEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, IStoreState>;\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics , StateObservable } from \"redux-observable\";\r\nimport { EWidgetStatus, AjaxResponse } from \"omf-changepackage-components\";\r\nimport { filter, mergeMap, catchError , of, Observable } from \"rxjs\";\r\n\r\n\r\nimport { Client } from \"../../Client\";\r\nimport {\r\n  IStoreState, IServiceAccountAPI,\r\n} from \"../../models\";\r\nimport {\r\n  getAccountDetails,\r\n  setAccountDetails,\r\n  getCatalog\r\n} from \"../Actions\";\r\nimport { Config } from \"../../Config\";\r\n\r\n@Injectable\r\nexport class UserAccountEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  constructor(private client: Client, private config: Config) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.requestDataEpic,\r\n    );\r\n  }\r\n\r\n  private get requestDataEpic(): UserAccountEpic {\r\n    return (action$: Observable<ReduxActions.Action<any>>, state$: StateObservable<IStoreState>) =>\r\n      action$.pipe(\r\n        filter((action: ReduxActions.Action<any>) => action.type === getAccountDetails.toString()),\r\n        filter(() => this.widgetState !== EWidgetStatus.UPDATING),\r\n        mergeMap(() => this.client.get<AjaxResponse<IServiceAccountAPI>>(this.config.api.serviceAccountAPI).pipe(\r\n          mergeMap(({ data }: AjaxResponse<IServiceAccountAPI>) => [\r\n            setAccountDetails(data),\r\n            getCatalog()\r\n          ]),\r\n          catchError(() => of(getCatalog()))\r\n        ))\r\n      );\r\n  }\r\n}\r\n\r\ntype UserAccountEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, any>;\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Actions, EWidgetStatus, Omniture, ValueOf, Utils, EFlowType } from \"omf-changepackage-components\";\r\nimport { combineEpics, Epic, ofType } from \"redux-observable\";\r\nimport { mergeMap, filter, catchError , of } from \"rxjs\";\r\n\r\nimport { IStoreState } from \"../../models\";\r\n\r\nconst {\r\n  omniPageLoaded,\r\n  omniPageSubmit\r\n} = Actions;\r\n\r\n// function catalofToPRD(catalog: ): Array<Omniture.IProduct> {\r\n\r\n// }\r\n\r\n@Injectable\r\nexport class OmnitureEpics {\r\n  widgetState: EWidgetStatus = EWidgetStatus.INIT;\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.pageLoadedEpic,\r\n      this.pageSubmitEpic\r\n      // this.internetPageSubmitEpic\r\n    );\r\n  }\r\n\r\n  /**\r\n     * The only Omniture dependecy for pageload\r\n     * on Internet changepackage page is account details\r\n     * so we wait for those to come from API and then\r\n     * fire the beakon\r\n     * @readonly\r\n     * @private\r\n     * @type {UserAccountEpic}\r\n     * @memberof OmnitureEpics\r\n     */\r\n  private get pageLoadedEpic(): UserAccountEpic {\r\n    return (action$: any, store) =>\r\n      action$.pipe(\r\n        ofType(omniPageLoaded.toString()),\r\n        filter(({ payload }: ReduxActions.Action<any>) => Boolean(payload)),\r\n        mergeMap(({ payload: { name, data = {} } }: ReduxActions.Action<any>) => {\r\n          // const { accountDetails } = store.getState();\r\n          const omniture = Omniture.useOmniture();\r\n          const callPayload: Omniture.IProps = {\r\n            id: `${name}Pageload`,\r\n            s_oSS1: \"~\",\r\n            s_oSS2: \"~\",\r\n            s_oSS3: \"~\",\r\n            s_oPGN: \"Setup your service:\" + name,\r\n            s_oAPT: \"~\",\r\n            ...data\r\n          };\r\n          // if (accountDetails.length > 0 && !callPayload.s_oPLE) {\r\n          //     callPayload.s_oPLE = {\r\n          //         type: Omniture.EMessageType.Information,\r\n          //         content: \"\"\r\n          //     };\r\n          // }\r\n          if (Utils.getFlowType() === EFlowType.TV && !callPayload.s_oAPT) {\r\n            callPayload.s_oAPT = {\r\n              actionId: 394,\r\n              actionresult: 1\r\n            };\r\n          }\r\n          // omniture.trackFragment(callPayload);\r\n          if (Utils.getFlowType() !== EFlowType.TV) {\r\n            omniture.trackFragment(callPayload);\r\n          }\r\n          return of();\r\n        }),\r\n        catchError((error: Response) => of())\r\n      );\r\n  }\r\n\r\n\r\n  private get pageSubmitEpic(): UserAccountEpic {\r\n    return (action$: any, store) =>\r\n      action$.pipe(\r\n        ofType(omniPageSubmit.toString()),\r\n        mergeMap(() => {\r\n          const { catalog } = store.value as IStoreState;\r\n          const omniture = Omniture.useOmniture();\r\n          omniture.trackAction({\r\n            id: \"tvPageSubmit\",\r\n            s_oAPT: {\r\n              actionId: 647\r\n            },\r\n            s_oBTN: \"Continue\",\r\n            s_oPRD: catalog.index\r\n              .filter(prd => prd.isSelected)\r\n              .map(\r\n                prd => ({\r\n                  category: prd.displayGroupKey,\r\n                  name: prd.name,\r\n                  sku: \"\",\r\n                  quantity: \"1\",\r\n                  price: ValueOf<string>(prd, \"regularPrice.price\", \"0\"),\r\n                  promo: ValueOf<string>(prd, \"promotionDetails.promotionalPrice.price\", \"\")\r\n                })\r\n              )\r\n          });\r\n          return of();\r\n        }),\r\n        catchError((error: Response) => of())\r\n      );\r\n  }\r\n}\r\n\r\ntype UserAccountEpic = Epic<any, any, IStoreState, any>;\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics, ofType } from \"redux-observable\";\r\nimport { filter, mergeMap, of } from \"rxjs\";\r\nimport { EWidgetStatus, Actions, Omniture, Utils, EFlowType } from \"omf-changepackage-components\";\r\nimport { CatalogEpics } from \"./Epics/Catalog\";\r\nimport { OrderingEpics } from \"./Epics/Order\";\r\nimport { getAccountDetails } from \"./Actions\";\r\nimport { UserAccountEpics } from \"./Epics/UserAccount\";\r\nimport { OmnitureEpics } from \"./Epics/Omniture\";\r\n\r\nconst { setWidgetStatus } = Actions;\r\n\r\n// const { concat } = ActionsObservable;\r\n\r\n@Injectable\r\nexport class Epics {\r\n  constructor(\r\n    public omnitureEpics: OmnitureEpics,\r\n    public userAccountEpic: UserAccountEpics,\r\n    public catalogEpics: CatalogEpics,\r\n    public orderingEpics: OrderingEpics\r\n  ) {}\r\n\r\n  combineEpics() {\r\n    return combineEpics(this.onWidgetStatusEpic);\r\n  }\r\n\r\n  private get onWidgetStatusEpic(): GeneralEpic {\r\n    return (action$: any) =>\r\n      action$.pipe(\r\n        ofType(setWidgetStatus.toString()),\r\n        filter(({ payload }: ReduxActions.Action<EWidgetStatus>) => payload === EWidgetStatus.INIT),\r\n        mergeMap(() => {\r\n          let s_oSS2 = \"~\";\r\n          switch (Utils.getFlowType()) {\r\n            case EFlowType.TV:\r\n              s_oSS2 = \"TV\";\r\n              break;\r\n            case EFlowType.ADDTV:\r\n              s_oSS2 = \"TV\";\r\n              break;\r\n            case EFlowType.BUNDLE:\r\n              s_oSS2 = \"Bundle\";\r\n              break;\r\n            default:\r\n              // Use default s_oSS2 value for other flow types\r\n              break;\r\n          }\r\n          switch (Utils.getFlowType()) {\r\n            case EFlowType.TV:\r\n              Omniture.useOmniture().updateContext({\r\n                s_oSS2,\r\n                s_oSS3: \"Change package\",\r\n              });\r\n              break;\r\n            case EFlowType.ADDTV:\r\n              Omniture.useOmniture().updateContext({\r\n                s_oSS2,\r\n                s_oSS3: \"Add Tv\",\r\n                s_oAPT: {\r\n                  actionId: 507,\r\n                  actionresult: 1,\r\n                  applicationState: 0,\r\n                },\r\n              });\r\n              break;\r\n            case EFlowType.BUNDLE:\r\n              Omniture.useOmniture().updateContext({\r\n                s_oSS2,\r\n                s_oSS3: \"Add Tv\",\r\n                s_oAPT: {\r\n                  actionId: 508,\r\n                  actionresult: 1,\r\n                  applicationState: 0,\r\n                },\r\n              });\r\n              break;\r\n            default:\r\n              // No specific Omniture context update for other flow types\r\n              break;\r\n          }\r\n          return of(getAccountDetails());\r\n        })\r\n      );\r\n  }\r\n}\r\n\r\ntype GeneralEpic = Epic<ReduxActions.Action<any>, any>;\r\n", "import {\r\n  CommonFeatures,\r\n  CommonServices,\r\n  Injectable,\r\n  ServiceLocator\r\n} from \"bwtk\";\r\nimport { EWidgetName } from \"omf-changepackage-components\";\r\n\r\nconst { BaseLocalization } = CommonFeatures;\r\n\r\n@Injectable\r\nexport class Localization extends BaseLocalization {\r\n  static Instance = null;\r\n  static getLocalizedString(id: string): string {\r\n    Localization.Instance =\r\n      Localization.Instance ||\r\n      ServiceLocator.instance.getService(CommonServices.Localization);\r\n    const instance: any = Localization.Instance;\r\n    const result = instance\r\n      ? instance.getLocalizedString(EWidgetName.TV, id, instance.locale)\r\n      : id;\r\n    return Boolean(result) ? result : id;\r\n  }\r\n}\r\n", "import { combineReducers } from \"redux\";\r\nimport { Action, handleActions } from \"redux-actions\";\r\nimport { combineEpics } from \"redux-observable\";\r\nimport { Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics, Actions } from \"omf-changepackage-components\";\r\n\r\nimport { Store as BwtkStore, Injectable, CommonFeatures } from \"bwtk\";\r\n\r\nimport * as actions from \"./Actions\";\r\n\r\nimport { IStoreState, INavigationItem, ITVCatalog, IAccountDetail } from \"../models\";\r\nimport { Epics } from \"./Epics\";\r\nimport { Localization } from \"../Localization\";\r\nimport { Client } from \"../Client\";\r\n\r\nconst { BaseStore, actionsToComputedPropertyName } = CommonFeatures;\r\nconst {\r\n  setNavigation,\r\n  setCatalog,\r\n  updateCatalog,\r\n  setAccountDetails\r\n} = actionsToComputedPropertyName(actions);\r\n\r\nconst {\r\n  handleNav\r\n} = actionsToComputedPropertyName(Actions);\r\n\r\n@Injectable\r\nexport class Store extends BaseStore<IStoreState> {\r\n  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {\r\n    super(store);\r\n  }\r\n\r\n  get reducer() {\r\n    return combineReducers({\r\n      // =========== Widget lifecycle methods =============\r\n      ...Reducers.WidgetBaseLifecycle(this.localization) as any,\r\n      ...Reducers.WidgetLightboxes() as any,\r\n      ...Reducers.WidgetRestrictions() as any,\r\n      // =========== Widget data ===============\r\n      navigation: handleActions<Array<INavigationItem> | null>({\r\n        [setNavigation]: (state, { payload }: Action<Array<INavigationItem>>) => payload || state,\r\n      }, []),\r\n      accountDetails: handleActions<Array<IAccountDetail>>({\r\n        [setAccountDetails]: (state, { payload }: Action<Array<IAccountDetail>>) => payload || state,\r\n      }, []),\r\n      catalog: handleActions<ITVCatalog | null>({\r\n        [setCatalog]: (state, { payload }: Action<ITVCatalog>) => payload || state,\r\n        [updateCatalog]: (state, { payload }: Action<ITVCatalog>) => payload || state,\r\n      }, {} as ITVCatalog),\r\n      navStatus: handleActions<boolean>({\r\n        [handleNav]: (state, { payload }: Action<boolean>) => payload\r\n      }, false)\r\n    }) as any;\r\n  }\r\n\r\n  /**\r\n   * Middlewares are collected bottom-to-top\r\n   * so, the bottom-most epic will receive the\r\n   * action first, while the top-most -- last\r\n   * @readonly\r\n   * @memberof Store\r\n   */\r\n  get middlewares(): any {\r\n    return combineEpics(this.epics.omnitureEpics.combineEpics(), this.epics.userAccountEpic.combineEpics(), \r\n      this.epics.catalogEpics.combineEpics(), this.epics.orderingEpics.combineEpics(),\r\n      this.epics.combineEpics(), new ModalEpics().combineEpics(), \r\n      new RestricitonsEpics(this.client, \"TV_RESTRICTION_MODAL\").combineEpics(), new LifecycleEpics().combineEpics()\r\n    );\r\n  }\r\n}\r\n", "import { Actions, Components, ValueOf, Omniture } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { toCharacteristicsJSON, translateStringList } from \"../../utils/Characteristics\";\r\n\r\nconst {\r\n  Modal\r\n} = Components;\r\n\r\ninterface IComponentProps extends ITVChannel {\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  closeLightbox: () => void;\r\n}\r\n\r\nexport const ModalId: string = \"CHANNEL_DETIALS\";\r\n\r\nconst Component: React.FC<IComponentProps & IComponentDispatches> = ({\r\n  name,\r\n  imagePath,\r\n  characteristics,\r\n  shortDescription,\r\n  longDescription,\r\n  channelNumber,\r\n  closeLightbox\r\n}) => {\r\n  const { language, genre, culture } = toCharacteristicsJSON(characteristics);\r\n  const onShown = () => {\r\n    Omniture.useOmniture().trackFragment({\r\n      id: \"channelDetailsLightbox\",\r\n      s_oAPT: {\r\n        actionId: 104\r\n      },\r\n      s_oPRM: \"Channel details\"\r\n    });\r\n  };\r\n  const onDismissed = () => {\r\n    Omniture.useOmniture().trackAction({\r\n      id: \"channelDetailsLightbox\",\r\n      s_oAPT: {\r\n        actionId: 647\r\n      },\r\n      s_oBTN: \"Close\"\r\n    });\r\n  };\r\n  return <Modal\r\n    modalId={ModalId}\r\n    onDismiss={onDismissed}\r\n    onShown={onShown}\r\n    title={<FormattedMessage id=\"CHANNEL_DETIALS_TITLE\" values={{ name }} />}>\r\n    <div className=\"pad-30 pad-15-left-right-xs\">\r\n      <div className=\"d-flex flex-column flex-sm-row\">\r\n        <div className=\"heightFitContent flexStatic\">\r\n          <div style={{ width: \"94px\", height: \"94px\" }} className=\"margin-15-bottom margin-30-right borderGrayLight6 flex align-center\">\r\n            <img src={imagePath} alt={name} className=\"fill pad-5\" />\r\n          </div>\r\n        </div> \r\n        <div className=\"pad-30-left\">\r\n          <span className=\"sr-only\"><FormattedMessage id=\"Channel number\" /></span>\r\n          <p className=\"no-margin txtSize16 txtVirginBlue line-height-18\">{channelNumber}</p>\r\n          <div className=\"spacer5\" aria-hidden=\"true\"></div>\r\n          <p className=\"no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold\">{[translateStringList(genre), translateStringList(culture), translateStringList(language)].filter(i => Boolean(i)).join(\" / \")}</p>\r\n          <div className=\"spacer15\" aria-hidden=\"true\"></div>\r\n          <p className=\"no-margin txtSize14 vm-dark-grey2 line-height-18\" dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </Modal>;\r\n};\r\n\r\nexport default connect<IComponentProps, IComponentDispatches>(\r\n  ({ lightboxData }: IStoreState) =>\r\n    ValueOf<ITVChannel>(lightboxData, undefined, {}),\r\n  (dispatch) => ({\r\n    closeLightbox: () => dispatch(Actions.closeLightbox(ModalId)),\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { FormattedMessage, FormattedDate } from \"react-intl\";\r\nimport {\r\n  Volt,\r\n  Components,\r\n  ValueOf\r\n} from \"omf-changepackage-components\";\r\n\r\nconst { Visible, Currency } = Components;\r\n\r\ninterface IPriceProps {\r\n  regularPrice: Volt.IPriceDetail;\r\n  promotionDetails: Volt.IPromotionDetail;\r\n}\r\n\r\nconst Price: React.FC<IPriceProps> = ({\r\n  regularPrice,\r\n  promotionDetails\r\n}) => <>\r\n  <div className=\"spacer5\" />\r\n  <Visible when={ValueOf(promotionDetails, \"description\", false)}>\r\n    <span className=\"package-name pad-5 fill-xs txtSize12 txtGray border-radius-3 bgGrey sans-serif txtBold pad-10-left pad-10-right inline-block\">\r\n      <Visible when={ValueOf(promotionDetails, \"discountDuration\", false)}>&nbsp;\r\n        <FormattedMessage id=\"PromotionValid\" values={{ price: Math.abs(ValueOf(promotionDetails, \"discountPrice.price\", 0)), discountDuration: ValueOf(promotionDetails, \"discountDuration\", \"\") }} />\r\n      </Visible>\r\n      <Visible when={ValueOf(promotionDetails, \"expiryDate\", false)}>&nbsp;\r\n        <FormattedDate value={ValueOf(promotionDetails, \"expiryDate\", \"\")} format=\"yMMMMd\" timeZone=\"UTC\">\r\n          {\r\n            (expiryDate) => <FormattedMessage id=\"PromotionExpires\" values={{ expiryDate }} />\r\n          }\r\n        </FormattedDate>\r\n      </Visible>\r\n    </span>\r\n  </Visible>\r\n  <div className=\"txtCurrency virginUltraReg txtBlack txtSize40 fill-xs\">\r\n    <Visible when={ValueOf(promotionDetails, undefined, false)}>\r\n      <FormattedMessage id=\"Now\" />&nbsp;\r\n    </Visible>\r\n    <Currency\r\n      value={\r\n        isNaN(ValueOf(promotionDetails, \"promotionalPrice\", {}).price)\r\n          ? ValueOf(regularPrice, \"price\", 0)\r\n          : ValueOf(promotionDetails, \"promotionalPrice.price\", 0)\r\n      }\r\n      monthly={true}\r\n    />\r\n    <Visible when={ValueOf(promotionDetails, undefined, false)}>\r\n      <p className=\"txtSize12 txtGray txtBold fill-xs flex\">\r\n        <FormattedMessage\r\n          id=\"Current Price\"\r\n          values={ValueOf(regularPrice, undefined, {}) as any}\r\n        />\r\n      </p>\r\n    </Visible>\r\n  </div>\r\n  <Visible when={ValueOf(promotionDetails, \"legalMessage\", false)}>\r\n    <p className=\"txtSize12 txtGray\">\r\n      {ValueOf(\r\n        promotionDetails,\r\n        \"legalMessage\",\r\n        <FormattedMessage id=\"Prices may increase legal\" />\r\n      )}\r\n    </p>\r\n  </Visible>\r\n</>;\r\n\r\nexport default Price;\r\n", "import { Actions, Components, ValueOf, Volt, Omniture } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { toggleSelection } from \"../../store/Actions\";\r\nimport { toCharacteristicsJSON, translateStringList } from \"../../utils/Characteristics\";\r\nimport Price from \"../Components/Price\";\r\n\r\nconst {\r\n  Modal,\r\n  Visible\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n  channel: ITVChannel;\r\n  parents: Array<Volt.IProductOffering>;\r\n  defaultSelection: Volt.IProductOffering;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onContinueClick: (action: Volt.IHypermediaAction | undefined) => void;\r\n  closeLightbox: () => void;\r\n}\r\n\r\nexport const ModalId: string = \"MULTIPLE_WAYS_TO_ADD\";\r\n\r\ninterface IOptionProps extends Volt.IProductOffering {\r\n  isSelected: boolean;\r\n  onSelect: Function;\r\n}\r\n\r\nconst Option: React.FC<IOptionProps> = (props) => {\r\n  const {\r\n    id,\r\n    name,\r\n    regularPrice,\r\n    promotionDetails,\r\n    childOfferings,\r\n    isSelected,\r\n    onSelect\r\n  } = props;\r\n  const additionalCahnnels = ValueOf(childOfferings, \"length\", 0) - 1;\r\n  return <div className={`boxContainer borderGrayLight6 pad-15 margin-20-bottom ${isSelected ? \"borderBlack\" : \"\"}`}>\r\n    <label id={`${id}selectCTA`} className=\"graphical_ctrl pointer ctrl_radioBtn txtSize15\" onClick={() => onSelect(props)}>\r\n      <input type=\"radio\" name=\"offering\" checked={isSelected} />\r\n      <span className=\"ctrl_element\"></span>\r\n      <span className=\"radio-text\">{name}</span>\r\n      <Visible when={additionalCahnnels > 0}>\r\n        <p className=\"no-margin\"><FormattedMessage id=\"Get additional channels\" values={{ additionalCahnnels }} /></p>\r\n      </Visible>\r\n      <Price regularPrice={regularPrice} promotionDetails={promotionDetails} />\r\n    </label>\r\n  </div>;\r\n};\r\n\r\nconst NoThanksOffer = {\r\n  id: \"NO\",\r\n  productOfferingType: Volt.EProductOfferingType.NONE\r\n} as Volt.IProductOffering;\r\n\r\nconst Component: React.FC<IComponentProps & IComponentDispatches> = ({\r\n  channel,\r\n  parents,\r\n  defaultSelection,\r\n  closeLightbox,\r\n  onContinueClick\r\n}) => {\r\n  const [selection, setSelection] = React.useState<Volt.IProductOffering>(NoThanksOffer);\r\n\r\n  React.useEffect(() => { setSelection(defaultSelection); }, [defaultSelection]);\r\n\r\n  function handleSelection(offer: Volt.IProductOffering) {\r\n    setSelection(offer);\r\n  }\r\n\r\n  function handleContinue() {\r\n    if (selection.id !== defaultSelection.id) {\r\n      const offer = selection.id === \"NO\" ? defaultSelection : selection;\r\n      onContinueClick(offer.offeringAction);\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"mutipleWaysLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: \"MULTIPLE_WAYS_CONTINUE\"\r\n        },\r\n        s_oPRD: {\r\n          category: offer.displayGroupKey,\r\n          name: offer.name,\r\n          sku: \"\",\r\n          quantity: \"1\",\r\n          price: ValueOf<string>(offer, \"regularPrice.price\", \"\"),\r\n          promo: ValueOf<string>(offer, \"promotionDetails.description\", \"\")\r\n        }\r\n      });\r\n    } else {\r\n      closeLightbox();\r\n    }\r\n  }\r\n\r\n  const {\r\n    name,\r\n    imagePath,\r\n    channelNumber,\r\n    characteristics,\r\n    shortDescription,\r\n    longDescription,\r\n  } = channel;\r\n  const { language, genre, culture } = toCharacteristicsJSON(characteristics);\r\n  return <Modal\r\n    modalId={ModalId}\r\n    className=\"do-not-center-in\"\r\n    onShown={() => {\r\n      Omniture.useOmniture().trackFragment({\r\n        id: \"mutipleWaysLightbox\",\r\n        s_oAPT: {\r\n          actionId: 104\r\n        },\r\n        s_oPRM: \"Multiple ways to order\"\r\n      });\r\n    }}\r\n    onDismiss={() => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"mutipleWaysLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: \"Close\"\r\n      });\r\n    }}\r\n    title={<FormattedMessage id=\"MULTIPLE_WAYS_TO_ADD_TITLE\" values={{ name }} />}>\r\n    <div className=\"\">\r\n      <div className=\"pad-30 pad-15-left-right-xs\">\r\n        <div className=\"spacer15 clear\" aria-hidden=\"true\"></div>\r\n        <div className=\"d-flex flex-column flex-sm-row\">\r\n          <div className=\"heightFitContent flexStatic\">\r\n            <div style={{ width: \"94px\", height: \"94px\" }} className=\"margin-15-bottom margin-30-right borderGrayLight6 flex align-center\">\r\n              <img src={imagePath} alt={name} className=\"fill pad-5\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"pad-30-left\">\r\n            <span className=\"sr-only\"><FormattedMessage id=\"Channel number\" /></span>\r\n            <p className=\"no-margin txtSize16 txtVirginBlue line-height-18\">{channelNumber}</p>\r\n            <div className=\"spacer5\" aria-hidden=\"true\"></div>\r\n            <p className=\"no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold\">{[translateStringList(genre), translateStringList(culture), translateStringList(language)].filter(i => Boolean(i)).join(\" / \")}</p>\r\n            <div className=\"spacer15\" aria-hidden=\"true\"></div>\r\n            <p className=\"no-margin txtSize14 vm-dark-grey2 line-height-18\" dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <form className=\"pad-30 pad-15-left-right-xs\">\r\n        <div className=\"spacer1 bgGrayLight3\" aria-hidden=\"true\"></div>\r\n        <div className=\"spacer25 clear\" aria-hidden=\"true\"></div>\r\n        <p className=\"txtSize16\"><FormattedMessage id=\"Ways to add this channel\" /></p>\r\n        {\r\n          parents.map(\r\n            offer => <Option {...offer} isSelected={offer.id === selection.id} onSelect={handleSelection} />\r\n          )\r\n        }\r\n        <FormattedMessage id=\"No thanks\" values={{ name }}>\r\n          {\r\n            (txt: any) => <Option {...NoThanksOffer} name={txt as string} isSelected={NoThanksOffer.id === selection.id} onSelect={handleSelection} />\r\n          }\r\n        </FormattedMessage>\r\n      </form>\r\n    </div>\r\n\r\n    <div className=\"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\">\r\n      <button id=\"MULTIPLE_WAYS_CONTINUE\" className=\"btn btn-primary fill-xs\" onClick={handleContinue}><FormattedMessage id=\"MULTIPLE_WAYS_TO_ADD_CONTINUE\" /></button>\r\n      <div className=\"vSpacer15\" aria-hidden=\"true\"></div>\r\n      <button id=\"MULTIPLE_WAYS_CLOSE\" className=\"btn btn-default fill-xs\" onClick={closeLightbox}><FormattedMessage id=\"MULTIPLE_WAYS_TO_ADD_CLOSE\" /></button>\r\n    </div>\r\n  </Modal>;\r\n};\r\n\r\nexport default connect<IComponentProps, IComponentDispatches>(\r\n  ({ catalog, lightboxData }: IStoreState) => {\r\n    const channel = ValueOf<ITVChannel>(lightboxData, undefined, {});\r\n    const parents = ValueOf<Array<string>>(channel, \"multipleWaysToAdd\", []).map(\r\n      offerId => catalog.index.find(offer => offer.id === offerId)\r\n    ).filter(Boolean) as Array<Volt.IProductOffering>;\r\n    const defaultSelection = parents.find(offer => offer.isSelected);\r\n    return ({\r\n      channel,\r\n      parents,\r\n      defaultSelection: defaultSelection || NoThanksOffer\r\n    });\r\n  },\r\n  (dispatch) => ({\r\n    onContinueClick: (action: Volt.IHypermediaAction | undefined) => {\r\n      if (action) dispatch(toggleSelection(action));\r\n      dispatch(Actions.closeLightbox(ModalId));\r\n    },\r\n    closeLightbox: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"mutipleWaysLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: \"MULTIPLE_WAYS_CLOSE\"\r\n        }\r\n      });\r\n      dispatch(Actions.closeLightbox(ModalId));\r\n    },\r\n  })\r\n)(Component);\r\n", "import {\r\n  Actions,\r\n  Components,\r\n  ValueOf,\r\n  Volt\r\n} from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage, FormattedNumber } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { toggleSelection } from \"../../store\";\r\nimport { toCharacteristicsJSON } from \"../../utils/Characteristics\";\r\nimport { ModalId as ChannelDetailsModal } from \"../Modals/Details\";\r\nimport { ModalId as MultipleWaysToAdd } from \"../Modals/MultipleWays\";\r\nimport { Tooltip } from \"./Tooltip\";\r\n\r\nconst { Visible } = Components;\r\n\r\ninterface IComponentProps extends ITVChannel { }\r\n\r\ninterface IComponentConnectedProps { }\r\n\r\ninterface IComponentDispatches {\r\n  onInfoClick: (props: IComponentProps, id: string) => void;\r\n  onActionClick: (action: Volt.IHypermediaAction) => void;\r\n  onMultipleWaysToAdd: (props: IComponentProps, id: string) => void;\r\n}\r\n\r\nexport const Component: React.FC<IComponentProps &\r\n  IComponentConnectedProps &\r\n  IComponentDispatches> = props => {\r\n  const {\r\n    id,\r\n    name,\r\n    imagePath,\r\n    regularPrice,\r\n    promotionDetails,\r\n    offeringAction,\r\n    characteristics,\r\n    isAlreadyIncludedIn,\r\n    isSelectable,\r\n    isSelected,\r\n    isDisabled,\r\n    multipleWaysToAdd,\r\n    onActionClick,\r\n    onInfoClick,\r\n    onMultipleWaysToAdd,\r\n    channelNumber\r\n  } = props;\r\n  const { callSign } = toCharacteristicsJSON(characteristics);\r\n  const isMultipleWaysToAdd =\r\n      isSelectable && ValueOf(multipleWaysToAdd, \"length\", 0) > 0;\r\n  let popoverCtrl: any;\r\n  const handleInfoClick = (e?: any) => {\r\n    if (e) {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n    }\r\n    if (popoverCtrl) {\r\n      popoverCtrl.hide();\r\n    }\r\n    onInfoClick(props, `channel_${id}`);\r\n  };\r\n  return (\r\n    <Tooltip\r\n      key={id}\r\n      connectCtrl={(ctrl) => {\r\n        popoverCtrl = ctrl;\r\n        ctrl.onTooltipClick = handleInfoClick;\r\n      }}\r\n      {...props}\r\n      className={\"col-12 col-sm-3 col-md-3 pad-15-left\"}\r\n    >\r\n      <div className=\"\" id={id} data-cs={callSign}>\r\n        <div\r\n          className={`bell-tv-channel flexCol flexRow-xs\r\n              ${isSelectable && isSelected ? \" selected\" : \"\"}\r\n              ${!isSelectable ? \" bell-tv-channel-nonselectable\" : \"\"}\r\n              ${isSelectable && isDisabled ? \" disabled\" : \"\"}`}\r\n        >\r\n          <div\r\n            className=\"bell-tv-channel-icon flexBlock flexCenter floatL-xs\"\r\n            aria-hidden=\"true\"\r\n          >\r\n            <img src={imagePath} alt={name} />\r\n          </div>\r\n          <div className=\"bell-tv-channel-description flexGrow flex flex-column\">\r\n            <div className=\"spacer5 d-none d-sm-block\" aria-hidden=\"true\"></div>\r\n            {\r\n              Boolean(name) &&\r\n                <button\r\n                  id={`channel_${id}`}\r\n                  className=\"bell-tv-channel-title txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal flexGrow links-blue-on-bg-white\"\r\n                  // href={`#${callSign}`}\r\n                  onClick={handleInfoClick}\r\n                >\r\n                  {name}\r\n                </button>\r\n            }\r\n            <p className=\"bell-tv-channel-number noMargin\">{channelNumber}</p>\r\n            <Visible\r\n              when={isSelectable && ValueOf(regularPrice, \"price\", 0) > 0}\r\n            >\r\n              <p className=\"bell-tv-channel-price txtBlue noMargin\">\r\n                <FormattedNumber\r\n                  value={\r\n                    ValueOf(\r\n                      promotionDetails,\r\n                      \"promotionalPrice.price\",\r\n                      false\r\n                    ) || ValueOf(regularPrice, \"price\", 0)\r\n                  }\r\n                  format=\"CAD\"\r\n                />\r\n                <Visible\r\n                  when={ValueOf(\r\n                    promotionDetails,\r\n                    \"promotionalPrice.price\",\r\n                    false\r\n                  )}\r\n                >\r\n                    &nbsp;\r\n                  <del>\r\n                    <FormattedNumber\r\n                      value={ValueOf(regularPrice, \"price\", 0)}\r\n                      format=\"CAD\"\r\n                    />\r\n                  </del>\r\n                </Visible>\r\n              </p>\r\n            </Visible>\r\n          </div>\r\n\r\n          {/* <Visible\r\n              when={isSelectable && ValueOf(promotionDetails, undefined, false)}\r\n            >\r\n              <div className=\"bell-tv-channel-tile-description txtSize12\">\r\n                <dfn>\r\n                  {ValueOf(\r\n                    promotionDetails,\r\n                    \"legalMessage\",\r\n                    <FormattedMessage id=\"Prices may increase legal\" />\r\n                  )}\r\n                </dfn>\r\n              </div>\r\n            </Visible> */}\r\n          <Visible when={!isDisabled && isMultipleWaysToAdd}>\r\n            <div className=\"bell-tv-channel-tile-description txtSize12\">\r\n              <dfn>\r\n                <FormattedMessage id=\"Multipleways to add\" />\r\n              </dfn>\r\n            </div>\r\n          </Visible>\r\n          <Visible when={isSelectable && isDisabled && !Boolean(isAlreadyIncludedIn)}>\r\n            <div className=\"bell-tv-channel-tile-description txtSize12\">\r\n              <dfn>\r\n                <FormattedMessage id=\"Already selected\" />\r\n              </dfn>\r\n            </div>\r\n          </Visible>\r\n          <Visible when={Boolean(isAlreadyIncludedIn)}>\r\n            <div className=\"bell-tv-channel-tile-description txtSize12\">\r\n              <dfn>\r\n                <FormattedMessage id=\"Already included in\" values={{ name: isAlreadyIncludedIn }} />\r\n              </dfn>\r\n            </div>\r\n          </Visible>\r\n\r\n          <Visible when={isSelectable}>\r\n            <label\r\n              htmlFor={`offeringWays_${id}`}\r\n              className=\"bell-tv-channel-checkbox graphical_ctrl graphical_ctrl_checkbox absolute\"\r\n              onClick={e => {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n                if (!isDisabled) isMultipleWaysToAdd\r\n                  ? onMultipleWaysToAdd(props, `channel_${id}`)\r\n                  : onActionClick(offeringAction);\r\n              }}\r\n            >\r\n              <input\r\n                id={`offeringWays_${id}`}\r\n                type=\"checkbox\"\r\n                name=\"packages\"\r\n                checked={isSelected}\r\n                disabled={isDisabled}\r\n              />\r\n              <span className=\"ctrl_element chk_radius\"></span>\r\n              <span className=\"sr-only\">{name}</span>\r\n            </label>\r\n          </Visible>\r\n        </div>\r\n      </div>\r\n    </Tooltip>\r\n  );\r\n};\r\n\r\nexport default connect<\r\n  IComponentConnectedProps,\r\n  IComponentDispatches,\r\n  IComponentProps\r\n>(\r\n  ({ }: IStoreState) => ({}),\r\n  dispatch => ({\r\n    onInfoClick: (data: IComponentProps, id: string) =>\r\n      dispatch(\r\n        Actions.openLightbox({\r\n          lightboxId: ChannelDetailsModal,\r\n          data: { ...data, relativeId: id }\r\n        })\r\n      ),\r\n    onMultipleWaysToAdd: (data: IComponentProps, id: string) =>\r\n      dispatch(\r\n        Actions.openLightbox({\r\n          lightboxId: MultipleWaysToAdd,\r\n          data: { ...data, relativeId: id }\r\n        })\r\n      ),\r\n    onActionClick: (action: Volt.IHypermediaAction) =>\r\n      dispatch(toggleSelection(action))\r\n  })\r\n)(Component);\r\n", "import { Components, Omniture, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage, useIntl } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { toggleSelection } from \"../../store\";\r\nimport { sortOfferings } from \"../../utils/Characteristics\";\r\nimport Channel from \"./Channel\";\r\nimport Price from \"./Price\";\r\n\r\nconst {\r\n  Visible\r\n} = Components;\r\n\r\ninterface IComponentProps extends Volt.IProductOffering {\r\n}\r\n\r\ninterface IComponentConnectedProps {\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onActionClick: (action: Volt.IHypermediaAction) => void;\r\n}\r\n\r\nexport const Component: React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches> = ({\r\n  id,\r\n  name,\r\n  imagePath,\r\n  isAlreadyIncludedIn,\r\n  isSelectable,\r\n  isSelected,\r\n  isCurrent,\r\n  isDisabled,\r\n  regularPrice,\r\n  shortDescription,\r\n  longDescription,\r\n  promotionDetails,\r\n  offeringAction,\r\n  childOfferings,\r\n  onActionClick\r\n}) => {\r\n  const [expanded, Expand] = React.useState(false);\r\n  const intl = useIntl();\r\n  React.useEffect(() => {\r\n    expanded &&\r\n            Omniture.useOmniture().trackAction({\r\n              id: \"showChannelsClick\",\r\n              s_oAPT: {\r\n                actionId: 648\r\n              },\r\n              s_oEPN: \"Show Channel\"\r\n            });\r\n  }, [expanded]);\r\n  const haveChildren = ValueOf(childOfferings, \"length\", 0) > 0;\r\n  return <div className={`bell-tv-package bell-tv-movie-pack noBorder ${isSelected ? \"selected\" : \"\"} ${isDisabled ? \"disabled\" : \"\"}`} id={id}>\r\n    <div className=\"bell-tv-package-body flexRow\">\r\n      <div className=\"bell-tv-package-left flexGrow flexCol\">\r\n        <label id={`combo_${id}`} onClick={(e) => {\r\n          e.preventDefault();\r\n          e.stopPropagation();\r\n          if (!isSelectable || isDisabled) return;\r\n          else onActionClick(offeringAction);\r\n        }} className=\"bell-tv-package-checkbox graphical_ctrl graphical_ctrl_checkbox txtSize15 block\">\r\n          <input type=\"checkbox\" name=\"packages\" checked={isSelected} disabled={isDisabled || !isSelectable} />\r\n          <span className=\"block txtSize16 pad-5-left txtBlack\">{name}</span>\r\n          <Visible when={isSelectable && isDisabled && !Boolean(isAlreadyIncludedIn)}>\r\n            <span className=\"block bell-tv-channel-tile-description txtSize12\"><dfn><FormattedMessage id=\"Already selected\" /></dfn></span>\r\n          </Visible>\r\n          <Visible when={Boolean(isAlreadyIncludedIn)}>\r\n            <span className=\"block bell-tv-channel-tile-description txtSize12\"><dfn><FormattedMessage id=\"Already included in\" values={{ name: isAlreadyIncludedIn }} /></dfn></span>\r\n          </Visible>\r\n          <Price regularPrice={regularPrice} promotionDetails={promotionDetails} />\r\n          <span className=\"ctrl_element chk_radius borderGrayLight7\"></span>\r\n        </label>\r\n        <ul className=\"flexRow flexWrap bell-tv-individual-channels virgin-channel-block\">\r\n          {\r\n            ValueOf<Array<ITVChannel>>(childOfferings, undefined, [])\r\n              .slice(0, 3)\r\n              .map(\r\n                channel => <li>\r\n                  <img src={ValueOf(channel, \"imagePath\", \"\")} alt={ValueOf(channel, \"name\", \"\")} title={ValueOf(channel, \"name\", \"\")} />\r\n                </li>\r\n              )\r\n          }\r\n        </ul>\r\n        <div className=\"flexGrow\" aria-hidden=\"true\" />\r\n        <div className=\"spacer15\" aria-hidden=\"true\" />\r\n        <Visible when={haveChildren}>\r\n          <div className=\" flexBlock flexRow\">\r\n            <button id={`View_all_channels_${id}`} onClick={() => Expand(!expanded)} className=\"btn btn-link no-pad links-blue-on-bg-white txtDecorationNoneHover txtSize14\" aria-controls={`View_all_channels_${id}`} aria-expanded={expanded} aria-label={`${intl.formatMessage({id: \"Show channels\"})} ${intl.formatMessage({id: \"FOR_TEXT\"})} ${name}`}>\r\n              <span className={`volt-icon links-blue-on-bg-white margin-5-top ${expanded ? \"icon-Collapse\" : \"icon-Expand\"}`}>\r\n                <span className=\"volt-icon path1 icon-Collapse\"></span><span className=\"volt-icon path2 icon-Collapse\"></span>\r\n              </span>\r\n              <span className=\"sans-serif margin-10-left\"><FormattedMessage id=\"Show channels\" /></span>\r\n            </button>\r\n          </div>\r\n        </Visible>\r\n      </div>\r\n      <div className=\"spacer10 flexStatic d-block d-sm-none\" aria-hidden=\"true\" />\r\n      <div className=\"bell-tv-package-right flexStatic block-xs\">\r\n        <Visible when={Boolean(imagePath)}>\r\n          <img src={imagePath} alt={name} />\r\n        </Visible>\r\n      </div>\r\n    </div>\r\n    <Visible when={expanded}>\r\n      <div className=\"bell-tv-package-footer bgGrayLight4 pad-30 pad-15-left-right-xs expanded\" role=\"region\" aria-hidden={!expanded}>\r\n        <div className=\"bell-tv-package-filters-row no-pad\">\r\n          <Visible when={Boolean(longDescription || shortDescription)}>\r\n            <p dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} />\r\n            <hr />\r\n          </Visible>\r\n          <div className=\"bell-tv-channels bell-tv-channel-picker flexRow\">\r\n            {\r\n              sortOfferings(ValueOf(childOfferings, undefined, [])).map((channel: ITVChannel) => <Channel key={channel.id} {...channel} isSelectable={false} />)\r\n            }\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Visible>\r\n  </div>;\r\n};\r\n\r\nconst Combo = connect<IComponentConnectedProps, IComponentDispatches, IComponentProps>(\r\n  ({ }: IStoreState) => ({}),\r\n  (dispatch) => ({\r\n    onActionClick: (action: Volt.IHypermediaAction) => dispatch(toggleSelection(action))\r\n  })\r\n)(Component);\r\n\r\nexport default Combo;\r\n", "import { Components, FormattedHTMLMessage, Omniture } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\n\r\ninterface IComponentProps {\r\n  pageName: string;\r\n  label: string;\r\n  content: string;\r\n}\r\n\r\nexport const Footer: React.FC<IComponentProps> = ({\r\n  pageName,\r\n  label,\r\n  content\r\n}) => {\r\n  const [expanded, toggleState] = React.useState(false);\r\n  React.useEffect(() => {\r\n    expanded &&\r\n            Omniture.useOmniture().trackAction({\r\n              id: \"ligalStuffClick\",\r\n              s_oAPT: {\r\n                actionId: 648\r\n              },\r\n              s_oEPN: \"Legal Stuff\"\r\n            });\r\n  }, [expanded]);\r\n  return <div className=\"virginUltraReg more-info pad-15-top accss-focus-outline-override-grey-bg\" id=\"moreInfo\">\r\n    <button id=\"Legal\" className=\"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray\" onClick={() => toggleState(!expanded)} aria-expanded={expanded}>\r\n      <span className={`volt-icon ${expanded ? \"icon-collapse_m\" : \"icon-expand_m\"}`} aria-hidden=\"true\" />&nbsp;&nbsp;\r\n      <FormattedMessage id={label} />\r\n    </button>\r\n    <div className=\"spacer30\" aria-hidden=\"true\" />\r\n    <Components.Visible when={expanded}>\r\n      <div className=\"moreInfoBox\">\r\n        <button id=\"legal_close\" type=\"button\" onClick={() => toggleState(false)} className=\"close moreInfoLink x-inner txtDarkGrey txtSize18\" aria-label=\"close\">\r\n          <span className=\"virgin-icon icon-big_X\" aria-hidden=\"true\" />\r\n        </button>\r\n        <FormattedHTMLMessage id={content} />\r\n      </div>\r\n    </Components.Visible>\r\n  </div>;\r\n};\r\n", "import * as React from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Actions } from \"omf-changepackage-components\";\r\n\r\ninterface IComponentConnectedProps {\r\n  name: string;\r\n  data?: any;\r\n  children?: any;\r\n}\r\n\r\nexport const OmniturePage: React.FC<IComponentConnectedProps> = ({\r\n  name,\r\n  data,\r\n  children\r\n}) => {\r\n  const dispatch = useDispatch();\r\n  // Fire omniture call for a page when view initializes\r\n  React.useEffect(() => {\r\n    dispatch(Actions.omniPageLoaded(name, data));\r\n  }, []);\r\n  return <>{children}</>;\r\n};\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { ValueOf, Volt, Components, FormattedHTMLMessage } from \"omf-changepackage-components\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nimport Combo from \"../Components/Combo\";\r\nimport { Footer } from \"../Components/Legal\";\r\nimport { sortOfferings } from \"../../utils/Characteristics\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\n\r\ninterface IComponentConnectedProps {\r\n  packages: Array<Volt.IProductOffering>;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n}\r\n\r\nconst Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  packages\r\n}) => <OmniturePage name=\"Addons\">\r\n  <div className=\"flexRow flex-justify-space-between\">\r\n    <div className=\"margin-xs\">\r\n      <h2 className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\">\r\n        <FormattedMessage id=\"Addons page\" />\r\n      </h2>\r\n      <FormattedHTMLMessage id=\"Addons page description\">\r\n        {\r\n          (__html: any) => <Components.Visible when={Boolean(__html)}>\r\n            <div className=\"spacer5\"></div>\r\n            <p className=\"noMargintxtSize14\">{__html}</p>\r\n          </Components.Visible>\r\n        }\r\n      </FormattedHTMLMessage>\r\n    </div>\r\n  </div>\r\n  <div className=\"spacer15\" aria-hidden=\"true\" />\r\n  {\r\n    sortOfferings(packages).map(combo => <Combo key={combo.id} {...combo} />)\r\n  }\r\n  <Footer pageName={Volt.EDIsplayGroupKey.ADD_ON}\r\n    label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}\r\n    content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.ADD_ON}`} />\r\n</OmniturePage>;\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog }: IStoreState) => ({\r\n    packages: ValueOf<Array<Volt.IProductOffering>>(catalog, \"offerings.\" + Volt.EDIsplayGroupKey.ADD_ON, [])\r\n      .filter(pack => pack.productOfferingType === Volt.EProductOfferingType.COMBO)\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { usePrevious } from \"../../utils/Filter\";\r\n\r\nconst MIN_BLOCK = 15;\r\n\r\ninterface IComponentProps {\r\n  list: Array<any>;\r\n  formatter: (item: any, index?: number, list?: Array<any>) => any;\r\n}\r\n\r\nexport const ProgressiveLoader: React.FC<IComponentProps> = ({\r\n  list,\r\n  formatter\r\n}) => {\r\n  const [progress, setProgress] = React.useState(list.length > MIN_BLOCK ? 0 : list.length);\r\n  const _list = usePrevious(list);\r\n  React.useEffect(() => {\r\n    if (!_list || _list.length !== list.length)\r\n      setProgress(list.length > MIN_BLOCK ? 0 : list.length);\r\n  }, [list]);\r\n  React.useEffect(() => {\r\n    if (progress < list.length) {\r\n      requestAnimationFrame(() => setProgress(progress + MIN_BLOCK));\r\n    }\r\n  }, [progress]);\r\n  return <>\r\n    {\r\n      list.slice(0, progress).map(formatter)\r\n    }\r\n  </>;\r\n};\r\n", "import {\r\n  Components,\r\n  ValueOf,\r\n  WidgetContext,\r\n  Omniture\r\n} from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\n// import { connect } from \"react-redux\";\r\nimport { ITVChannel } from \"../../models\";\r\nimport {\r\n  handlePropFilter,\r\n  handleVoidFilter,\r\n  IFilterDispatcher,\r\n  IFilteredContentFilters,\r\n  useChannelsFilter,\r\n  useFilterDispatcher\r\n} from \"../../utils/Filter\";\r\nimport Channel from \"./Channel\";\r\nimport { ProgressiveLoader } from \"./ProgressiveLoad\";\r\n\r\nconst { Visible } = Components;\r\n\r\ninterface IComponentProps {\r\n  // refresh: any;\r\n  channels: Array<ITVChannel>;\r\n  label?: any;\r\n  groupName: string;\r\n  forceSelectable?: boolean;\r\n  allowSelection?: boolean;\r\n  allowMultipleWaysToAdd?: boolean;\r\n  showHeader?: any;\r\n  showFilters?: any;\r\n}\r\n\r\ninterface IComponentConnectedProps { }\r\n\r\ninterface IComponentDispatches { }\r\n\r\ninterface IAdvancedFiltersProps {\r\n  filters: IFilteredContentFilters;\r\n  Filter: IFilterDispatcher;\r\n  toggleTray: any;\r\n}\r\n\r\nconst ErrorMessage = () => (\r\n  <div className=\"pad-30\">\r\n    <div className=\"flexBlock flexRow\">\r\n      <div className=\"\">\r\n        <span className=\"virgin-icon icon-BIG_WARNING txtSize38\">\r\n          <span className=\"virgin-icon path1\"></span>\r\n          <span className=\"virgin-icon path2\"></span>\r\n        </span>\r\n      </div>\r\n      <div className=\"pad-15-left\">\r\n        <h3 className=\"noMargin txtSize20 pad-15-bottom\">\r\n          <FormattedMessage id=\"NO_CHANNELS_FOUND_MESSAGE\" />\r\n        </h3>\r\n        <p className=\"noMargin\">\r\n          <FormattedMessage id=\"NO_CHANNELS_FOUND_DESCRIPTION\" />\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst AdvancedFilters: React.FC<IAdvancedFiltersProps> = ({\r\n  Filter,\r\n  filters,\r\n  toggleTray\r\n}) => {\r\n  const _Filter = useFilterDispatcher(Filter.getState())[1];\r\n  function onFormSubmit(e: React.FormEvent<HTMLFormElement>) {\r\n    e.preventDefault();\r\n    Omniture.useOmniture().trackAction({\r\n      id: \"advanceFilterApply\",\r\n      s_oAPT: {\r\n        actionId: 647\r\n      },\r\n      s_oBTN: \"Advance Filters\"\r\n    });\r\n    Filter.setState(_Filter.getState());\r\n    requestAnimationFrame(() => toggleTray(false));\r\n  }\r\n  function onFormReset(e: React.FormEvent<HTMLFormElement>) {\r\n    Filter.reset();\r\n    requestAnimationFrame(() => toggleTray(false));\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"bell-tv-package-filters-tray\"\r\n      style={{ maxHeight: \"9999em\" }}\r\n    >\r\n      <div className=\"spacer1 bgGray\" aria-hidden=\"true\" />\r\n      <form\r\n        className=\"bell-tv-package-filters-row bgGray19\"\r\n        onSubmit={onFormSubmit}\r\n        onReset={onFormReset}\r\n      >\r\n        <div className=\"flexRow flex-justify-space-between txtSize12-xs\">\r\n          <p className=\"txtBold txtBlack1\">\r\n            <FormattedMessage id=\"Refine by\" />\r\n          </p>\r\n        </div>\r\n        <div className=\"flexRow flex-justify-space-between flexCol-xs\">\r\n          <div className=\"flexCol\" role=\"group\" aria-labelledby=\"refineGenresLabel\">\r\n            <p className=\"txtBlack1\" id=\"refineGenresLabel\">\r\n              <FormattedMessage id=\"Genres\" />\r\n            </p>\r\n            <ul className=\"noMargin noBullets\">\r\n              {ValueOf<Array<string>>(filters, \"genres\", [])\r\n                .sort()\r\n                .map(filter => (\r\n                  <li key={filter}>\r\n                    <label\r\n                      id={`checkboxLabel_${filter}`}\r\n                      className=\"graphical_ctrl graphical_ctrl_checkbox pointer\"\r\n                      onClick={e => handlePropFilter(e, filter, _Filter.toggleGenre)}\r\n                      onKeyDown={e => handlePropFilter(e, filter, _Filter.toggleGenre)}\r\n                    >\r\n                      <input\r\n                        id={`checkbox_${filter}`}\r\n                        type=\"checkbox\"\r\n                        name=\"genres\"\r\n                        value={filter}\r\n                        checked={_Filter.hasGenre(filter)}\r\n                      />\r\n                      <FormattedMessage id={filter} />\r\n                      <span className=\"ctrl_element chk_radius\" />\r\n                    </label>\r\n                    <div className=\"spacer10\" aria-hidden=\"true\" />\r\n                  </li>\r\n                ))}\r\n            </ul>\r\n          </div>\r\n          <div className=\"flexCol\" role=\"group\" aria-labelledby=\"refineLanguageLabel\">\r\n            <p className=\"txtBlack1\" id=\"refineLanguageLabel\">\r\n              <FormattedMessage id=\"Language\" />\r\n            </p>\r\n            <ul className=\"noMargin noBullets\">\r\n              {ValueOf<Array<string>>(filters, \"languages\", [])\r\n                .sort()\r\n                .map(filter => (\r\n                  <li key={filter}>\r\n                    <label\r\n                      id={`label_Lang_${filter}`}\r\n                      className=\"graphical_ctrl graphical_ctrl_checkbox pointer\"\r\n                      onClick={e => handlePropFilter(e, filter, _Filter.toggleLanguage)}\r\n                      onKeyDown={e => handlePropFilter(e, filter, _Filter.toggleLanguage)}\r\n                    >\r\n                      <input\r\n                        id={`checkbox_Lang_${filter}`}\r\n                        type=\"checkbox\"\r\n                        name=\"languages\"\r\n                        value={filter}\r\n                        checked={_Filter.hasLanguage(filter)}\r\n                      />\r\n                      <FormattedMessage id={filter} />\r\n                      <span className=\"ctrl_element chk_radius\" />\r\n                    </label>\r\n                    <div className=\"spacer10\" aria-hidden=\"true\" />\r\n                  </li>\r\n                ))}\r\n            </ul>\r\n          </div>\r\n          <div className=\"flexCol flex-justify-space-between\" role=\"group\" aria-labelledby=\"refineOtherLabel\">\r\n            <div>\r\n              <p className=\"txtBlack1\" id=\"refineOtherLabel\">\r\n                <FormattedMessage id=\"Other\" />\r\n              </p>\r\n              <ul className=\"noMargin noBullets\">\r\n                <li>\r\n                  <label\r\n                    id=\"label_dont_have\"\r\n                    className=\"graphical_ctrl graphical_ctrl_checkbox pointer\"\r\n                    onClick={e => handleVoidFilter(e, _Filter.toggleDontHave)}\r\n                    onKeyDown={(e) => handleVoidFilter(e, _Filter.toggleDontHave)}\r\n                  >\r\n                    <input\r\n                      id=\"checkbox_dont_have\"\r\n                      type=\"checkbox\"\r\n                      name=\"dontHave\"\r\n                      value=\"yes\"\r\n                      checked={_Filter.hasDontHave()}\r\n                    />\r\n                    <span>\r\n                      <FormattedMessage id=\"Channels I dont have\" />\r\n                    </span>\r\n                    <span className=\"ctrl_element chk_radius\" />\r\n                  </label>\r\n                  <div className=\"spacer10\" aria-hidden=\"true\" />\r\n                </li>\r\n                <li>\r\n                  <label\r\n                    id=\"label_have_channels\"\r\n                    className=\"graphical_ctrl graphical_ctrl_checkbox pointer\"\r\n                    onClick={e => handleVoidFilter(e, _Filter.toggleHave)}\r\n                    onKeyDown={(e) => handleVoidFilter(e, _Filter.toggleHave)}\r\n                  >\r\n                    <input\r\n                      id=\"checkbox_have_channels\"\r\n                      type=\"checkbox\"\r\n                      name=\"have\"\r\n                      value=\"yes\"\r\n                      checked={_Filter.hasHave()}\r\n                    />\r\n                    <span>\r\n                      <FormattedMessage id=\"Channels I have\" />\r\n                    </span>\r\n                    <span className=\"ctrl_element chk_radius\" />\r\n                  </label>\r\n                  <div className=\"spacer10\" aria-hidden=\"true\" />\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div className=\" txtRight\">\r\n              <button\r\n                id=\"RESET_FILTER\"\r\n                type=\"reset\"\r\n                className=\"btn btn-link txtVirginBlueFix txtDecoration_hover\"\r\n              >\r\n                <FormattedMessage id=\"Reset filters\" />\r\n              </button>\r\n              <span className=\"vSpacer15\" aria-hidden=\"true\" />\r\n              <button\r\n                id=\"APPLY_BTN\"\r\n                type=\"submit\"\r\n                className=\"btn btn-primary txtSize12-xs\"\r\n              >\r\n                <FormattedMessage id=\"Apply\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const Component: React.FC<IComponentProps &\r\n  IComponentConnectedProps &\r\n  IComponentDispatches> = ({\r\n  allowMultipleWaysToAdd,\r\n  allowSelection,\r\n  forceSelectable,\r\n  channels,\r\n  label,\r\n  groupName,\r\n  showHeader,\r\n  showFilters\r\n}) => {\r\n  const [expaned, toggleTray] = React.useState(false);\r\n  const [filtered, Filter] = useChannelsFilter(channels);\r\n  const {\r\n    config: { filters }\r\n  }: any = React.useContext(WidgetContext);\r\n  const filteredChannels = ValueOf<Array<ITVChannel>>(filtered, \"channels\", []);\r\n  const disabled = !(channels && channels.length > 0);\r\n\r\n  return (\r\n    <div className=\"panel panel-shadow\">\r\n      <Visible when={showHeader}>\r\n        <nav className=\"bell-tv-package-filters-row bgWhite flexRow border-radius-top flexCenter\" role=\"tablist\">\r\n          <ul className=\"noMargin noBullets bell-tv-package-filters-nav margin-10-bottom-xs\" role=\"presentation\">\r\n            <li tabIndex={Filter.hasGenre() ? 0 : -1} className={`table-cell ${Filter.hasGenre() ? \"active\" : \"\"}`} role=\"tab\" onClick={() => Filter.setGenre()} onKeyUp={() => Filter.setGenre()} aria-selected={ Filter.hasGenre() ? \"true\" : \"false\"}>\r\n              <label id=\"filter_All\" className=\"pointer\">\r\n                <FormattedMessage id=\"FILTER_TEXT_All\" />\r\n              </label>\r\n            </li>\r\n            {ValueOf<Array<string>>(filtered, \"filters.genres\", [])\r\n              .filter(filter => filters.genres.indexOf(filter) > -1)\r\n              .map(filter => (\r\n                <li\r\n                  tabIndex={Filter.onlyGenre(filter) ? 0 : -1}\r\n                  role=\"tab\"\r\n                  id={filter}\r\n                  key={filter}\r\n                  onKeyUp={(e) => {\r\n                    if (e.keyCode === undefined || e.keyCode === 13) {\r\n                      Filter.setGenre(filter);\r\n                      toggleTray(false);\r\n                    }\r\n                  }}\r\n                  onClick={() => {\r\n                    Filter.setGenre(filter);\r\n                    toggleTray(false);\r\n                  }}\r\n                  className={`table-cell ${\r\n                    Filter.onlyGenre(filter) ? \"active\" : \"\"\r\n                  }`}\r\n                  aria-selected={ Filter.onlyGenre(filter) ? \"true\" : \"false\"}\r\n                >\r\n                  <label className=\"pointer\" id={`filter_${filter}`}>\r\n                    <FormattedMessage id={`FILTER_TEXT_${filter}`} />\r\n                  </label>\r\n                </li>\r\n              ))}\r\n          </ul>\r\n          <div\r\n            className=\"spacer2 fill-xs border-filter bgVirginCustomGray1 d-block d-sm-none\"\r\n            aria-hidden=\"true\"\r\n          ></div>\r\n          <div className=\"vSpacer10 flexGrow hidden-xs\" aria-hidden=\"true\" />\r\n          <div className=\"spacer10\" aria-hidden=\"true\" />\r\n          <ul className=\"noMargin noBullets bell-tv-package-filters-nav\" role=\"presentation\">\r\n            <li>\r\n              <button\r\n                id=\"Advanced_Filters\"\r\n                onClick={() => toggleTray(!expaned)}\r\n                aria-expanded={expaned}\r\n                disabled={disabled}\r\n                className=\"btn btn-link no-pad txtDecorationNoneHover\"\r\n              >\r\n                <span className=\"sans-serif icon-blue showText txtDecoration_hover links-blue-on-bg-white\">\r\n                  <FormattedMessage id=\"Advanced Filters\" />\r\n                &nbsp;&nbsp;\r\n                  <span\r\n                    className={`volt-icon txtSize16 icon-blue icon-${\r\n                      expaned ? \"Collapse\" : \"Expand\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`volt-icon path1 icon-${\r\n                        expaned ? \"Collapse\" : \"Expand\"\r\n                      }`}\r\n                    />\r\n                    <span\r\n                      className={`volt-icon path2 icon-${\r\n                        expaned ? \"Collapse\" : \"Expand\"\r\n                      }`}\r\n                    />\r\n                  </span>\r\n                </span>\r\n              </button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </Visible>\r\n      <Visible when={expaned}>\r\n        <AdvancedFilters\r\n          filters={filtered.filters}\r\n          Filter={Filter}\r\n          toggleTray={toggleTray}\r\n        />\r\n      </Visible>\r\n      <div className=\"bell-tv-package-filters-row bgGray19 block-xs\">\r\n        <div className=\"spacer6 visible-xs\" aria-hidden=\"true\" />\r\n        <div className=\"txtSize18 flexGrow\">\r\n          <Visible\r\n            when={Boolean(label)}\r\n            placeholder={\r\n              <span>\r\n                <FormattedMessage id=\"Number of channels\" /> (\r\n                {ValueOf(filtered, \"channels.length\", 0)})\r\n              </span>\r\n            }\r\n          >\r\n            {label}\r\n          </Visible>\r\n          <Visible when={showFilters}>\r\n            <div className=\"spacer15\" />\r\n          </Visible>\r\n        </div>\r\n        <Visible when={showFilters}>\r\n          <div className=\"flexRow block-xs flexCenter\">\r\n            <div className=\"flexStatic\" role=\"group\" aria-labelledby=\"languageLabel\">\r\n              <span className=\"txtBold txtBlack\" id=\"languageLabel\">\r\n                <FormattedMessage id=\"Language\" />\r\n              </span>\r\n              <span className=\"vSpacer15\" aria-hidden=\"true\" />\r\n              {ValueOf<Array<string>>(filtered, \"filters.languages\", [])\r\n                .filter(filter => filters.languages.indexOf(filter) > -1)\r\n                .map((filter, i) => (\r\n                  <React.Fragment key={i}>\r\n                    <label\r\n                      id={`lang_${filter}`}\r\n                      onClick={e => {\r\n                        handlePropFilter(e, filter, Filter.toggleLanguage);\r\n                        toggleTray(false);\r\n                      }}\r\n                      onKeyDown={e => {\r\n                        handlePropFilter(e, filter, Filter.toggleLanguage);\r\n                        toggleTray(false);\r\n                      }}\r\n                      className=\"graphical_ctrl graphical_ctrl_checkbox pointer\"\r\n                    >\r\n                      <input\r\n                        id={`input_${filter}`}\r\n                        type=\"checkbox\"\r\n                        name=\"packages\"\r\n                        disabled={disabled}\r\n                        checked={Filter.hasLanguage(filter)}\r\n                      />\r\n                      <FormattedMessage id={filter} />\r\n                      <span className=\"ctrl_element chk_radius\" />\r\n                    </label>\r\n                    <span className=\"vSpacer30\" aria-hidden=\"true\" />\r\n                  </React.Fragment>\r\n                ))}\r\n            </div>\r\n            <div className=\"flexGrow spacer15\" aria-hidden=\"true\" />\r\n            <div className=\"flexStatic\">\r\n              <span className=\"txtBold txtBlack\">\r\n                <FormattedMessage id=\"Sort by\" />\r\n              </span>\r\n              {/* <span className=\"vSpacer15\" aria-hidden=\"true\" /> */}\r\n              <button\r\n                id=\"sortBy_num\"\r\n                onClick={() => Filter.setSort(\"channelNumber\")}\r\n                disabled={disabled}\r\n                className=\"btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray\"\r\n              >\r\n                <span className=\"sr-only\">\r\n                  <FormattedMessage id=\"Sort by\" />\r\n                </span>\r\n                <Visible\r\n                  when={Filter.whichSort() === \"channelNumberdesc\"}\r\n                  placeholder={<FormattedMessage id=\"0-9\" />}\r\n                >\r\n                  <FormattedMessage id=\"9-0\" />\r\n                </Visible>\r\n              </button>\r\n              <button\r\n                id=\"sortBy_alpha\"\r\n                onClick={() => Filter.setSort(\"name\")}\r\n                disabled={disabled}\r\n                className=\"btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray\"\r\n              >\r\n                <span className=\"sr-only\">\r\n                  <FormattedMessage id=\"Sort by\" />\r\n                </span>\r\n                <Visible\r\n                  when={Filter.whichSort() === \"namedesc\"}\r\n                  placeholder={<FormattedMessage id=\"A-Z\" />}\r\n                >\r\n                  <FormattedMessage id=\"Z-A\" />\r\n                </Visible>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </Visible>\r\n      </div>\r\n      <div\r\n        role=\"group\"\r\n        aria-labelledby={`filter_${Filter.selectedGenre()}`}\r\n        className=\"bell-tv-package-filters-row bgWhite\"\r\n      >\r\n        <Visible when={!disabled} placeholder={<ErrorMessage />}>\r\n          <Visible\r\n            when={ValueOf(filteredChannels, \"length\", 0) > 0}\r\n            placeholder={<ErrorMessage />}\r\n          >\r\n            <div className=\"bell-tv-channels bell-tv-channel-picker flexRow\">\r\n              <ProgressiveLoader list={filteredChannels} formatter={channel => (\r\n                <Channel\r\n                  key={channel.id}\r\n                  {...channel}\r\n                  isSelectable={forceSelectable || (channel.isSelectable && !!allowSelection)}\r\n                  multipleWaysToAdd={\r\n                    allowMultipleWaysToAdd ? channel.multipleWaysToAdd : []\r\n                  }\r\n                />\r\n              )} />\r\n            </div>\r\n          </Visible>\r\n        </Visible>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Component;\r\n", "import {\r\n  Components,\r\n  Volt,\r\n  Omniture\r\n} from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage, useIntl } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { toggleSelection } from \"../../store/Actions\";\r\nimport { toCharacteristicsJSON } from \"../../utils/Characteristics\";\r\n\r\nconst { Modal } = Components;\r\n\r\ninterface IComponentProps { }\r\n\r\ninterface IConnectedProps {\r\n  // refresh: any;\r\n  channels: Array<ITVChannel>;\r\n  totalPrice: any;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onRemoveChannel: (action: Volt.IHypermediaAction) => void;\r\n}\r\n\r\nexport const ModalId: string = \"SELECTED_CANNELS\";\r\n\r\nconst getSelectedChannels = (\r\n  channels: Array<ITVChannel>\r\n): Array<ITVChannel> => channels.filter(channel => channel.isSelected);\r\n\r\nconst Component: React.FC<IComponentProps &\r\n  IComponentDispatches &\r\n  IConnectedProps> = ({\r\n  channels,\r\n  totalPrice,\r\n  onRemoveChannel\r\n}) =>   {\r\n  const intl = useIntl();\r\n  return <Modal\r\n    modalId={ModalId}\r\n    className={channels.length > 8 ? \"do-not-center-in\" : \"\"}\r\n    onShown={() => {\r\n      Omniture.useOmniture().trackFragment({\r\n        id: \"selectedChannelsLightbox\",\r\n        s_oAPT: {\r\n          actionId: 104\r\n        },\r\n        s_oPRM: \"Selected channels\"\r\n      });\r\n    }}\r\n    onDismiss={() => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"selectedChannelsLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: \"Close\"\r\n      });\r\n    }}\r\n    flexDisplay={true}\r\n    title={<FormattedMessage id=\"SELECTED_CANNELS_TITLE\" />}\r\n  >\r\n    <div className=\"pad-30 pad-15-left-right-xs\">\r\n      <p className=\"no-margin txtSize18 txtBold vm-dark-grey2 line-height-18\">\r\n        {channels ? (\r\n          <FormattedMessage\r\n            id=\"SELECTED_CANNELS_LABEL\"\r\n            values={{\r\n              total: getSelectedChannels(channels).length,\r\n              price: totalPrice\r\n            }}\r\n          />\r\n        ) : null}\r\n      </p>\r\n      <div className=\"spacer15 clear\" aria-hidden=\"true\" />\r\n      <div className=\"flexRow flexCol-xs flexWrap \">\r\n        {channels &&\r\n              getSelectedChannels(channels).map(\r\n                ({ id, name, imagePath, offeringAction, characteristics }) => {\r\n                  const { callsign } = toCharacteristicsJSON(characteristics);\r\n                  return (\r\n                    <div className=\"selectchannelBlock pad-15 fill-xs txtCenter-xs margin-15-right pad-0-bottom\">\r\n                      <button\r\n                        id={`${id}RemoveChannel`}\r\n                        onClick={() => onRemoveChannel(offeringAction)}\r\n                        type=\"button\"\r\n                        className=\"no-pad close no-margin\"\r\n                        aria-label={`${intl.formatMessage({id: \"REMOVE_TEXT\"})} ${name}`}\r\n                      >\r\n                        <span className=\"volt-icon icon-big_X txtSize16 close-channel\"></span>\r\n                      </button>\r\n                      <div className=\"channelImage margin-15-bottom margin-10-left justify-center no-margin-xs fill-xs\">\r\n                        <img src={imagePath} alt={name} />\r\n                      </div>\r\n                      <div className=\"pad-5-left\">\r\n                        <p className=\"channelName no-margin txtSize14 txtVirginBlue line-height-18 txtUnderline\">\r\n                          {name}\r\n                        </p>\r\n                        <div className=\"spacer10\" aria-hidden=\"true\" />\r\n                        <p className=\"margin-15-bottom\">{callsign}</p>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                }\r\n              )}\r\n        <div className=\"spacer30 clear\" aria-hidden=\"true\" />\r\n      </div>\r\n    </div>\r\n    <div className=\"modal-footer bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\">\r\n      {/* <p className=\"txtBold\"><FormattedMessage id=\"SAVE_SELECTION_NOTE\" /></p> */}\r\n      <div className=\"col1 flexRow\">\r\n        <button\r\n          id=\"SEL_CANNELS_CLOSE\"\r\n          className=\"btn btn-primary\"\r\n          onClick={() => {\r\n            Omniture.useOmniture().trackAction({\r\n              id: \"selectedChannelsLightbox\",\r\n              s_oAPT: {\r\n                actionId: 647\r\n              },\r\n              s_oBTN: \"Close\"\r\n            });\r\n          }}\r\n          data-dismiss=\"modal\"\r\n        >\r\n          <FormattedMessage id=\"SELECTED_CANNELS_CLOSE\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </Modal>;}\r\n;\r\n\r\nconst SelectedChannels = connect<IComponentProps, IComponentDispatches>(\r\n  ({ }: IStoreState) => ({} as any),\r\n  dispatch => ({\r\n    onRemoveChannel: (action: Volt.IHypermediaAction) =>\r\n      dispatch(toggleSelection(action))\r\n  })\r\n)(Component);\r\n\r\nexport default SelectedChannels;\r\n", "import { Actions, Components, FormattedHTMLMessage, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { INavigationItem, IStoreState, ITVChannel } from \"../../models\";\r\nimport { manageFloater } from \"../../utils/Floater\";\r\nimport Filter from \"../Components/Filter\";\r\nimport { Footer } from \"../Components/Legal\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\nimport SelectedChannels, { ModalId as SelectedChannelsModal } from \"../Modals/SelectedChannels\";\r\n\r\n\r\ninterface IComponentConnectedProps {\r\n  channels: Array<ITVChannel>;\r\n  navigation: Volt.IDisplayGroupOffering;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  openLightbox: (data: any) => void;\r\n}\r\n\r\nexport const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  channels,\r\n  navigation,\r\n  openLightbox\r\n}) => {\r\n  const floater$: HTMLDivElement | any = React.useRef(null);\r\n  const [floater, setIsFloating] = React.useState({ isFloating: false, leftPos: \"auto\" });\r\n  const channelCount = ValueOf(navigation, \"count\", 0);\r\n  const totalPrice = ValueOf(navigation, \"subTotalPrice.price\", 0);\r\n  const groupName = \"Alacarte\";\r\n\r\n  React.useEffect(() => {\r\n    const scrollSpy = () => {\r\n      manageFloater(floater$, setIsFloating);\r\n    };\r\n    window.addEventListener(\"scroll\", scrollSpy);\r\n    return () => {\r\n      window.removeEventListener(\"scroll\", scrollSpy);\r\n    };\r\n  }, []);\r\n\r\n  return <OmniturePage name=\"Alacarte\">\r\n    <div className=\"flexRow flex-justify-space-between\">\r\n      <div className=\"margin-xs\">\r\n        <h2 id={`group-${groupName}`} className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\">\r\n          <FormattedMessage id=\"A la carte page\" />\r\n        </h2>\r\n        <FormattedHTMLMessage id=\"A la carte page Description\">\r\n          {\r\n            (__html: any) => <Components.Visible when={Boolean(__html)}>\r\n              <div className=\"spacer5\"></div>\r\n              <p className=\"noMargintxtSize14\">{__html}</p>\r\n            </Components.Visible>\r\n          }\r\n        </FormattedHTMLMessage>\r\n      </div>\r\n      <div\r\n        id=\"wrap\"\r\n        ref={floater$}\r\n        tabIndex={0}\r\n        role=\"button\"\r\n        onClick={(e) => channelCount > 0 && openLightbox(\"wrap\")}\r\n        onKeyDown={(e) => (e.key === \"Enter\" || e.keyCode === 32 || e.type !== \"keydown\") && channelCount > 0 && openLightbox(\"wrap\")}\r\n        className={`floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer ${floater.isFloating ? \"fix-floating-notification\" : \"\"}`}\r\n        style={{ left: floater.leftPos }}>\r\n        <div className=\" txtSize26 virginUltraReg pad-10-top\">{channelCount}</div>\r\n        <span className=\"\"><FormattedMessage id=\"CHANNELS_SELECTED\" /></span>\r\n        <div className=\"txtBold\"><Components.BellCurrency value={totalPrice} /></div>\r\n      </div>\r\n    </div>\r\n    <div className=\"spacer15\" aria-hidden=\"true\" />\r\n    <Filter\r\n      groupName={groupName}\r\n      channels={channels}\r\n      allowSelection={true} />\r\n    <Footer pageName={Volt.EDIsplayGroupKey.ALACARTE}\r\n      label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}\r\n      content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.ALACARTE}`} />\r\n    <SelectedChannels\r\n      channels={channels.filter(channel => channel.isSelected)}\r\n      totalPrice={totalPrice}\r\n    />\r\n  </OmniturePage>;\r\n};\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog, navigation }: IStoreState) => ({\r\n    channels: ValueOf(catalog, \"offerings.\" + Volt.EDIsplayGroupKey.ALACARTE, []),\r\n    navigation: ValueOf<Array<INavigationItem>>(navigation, undefined, []).find(data => data.key === Volt.EDIsplayGroupKey.ALACARTE) as Volt.IDisplayGroupOffering\r\n  }),\r\n  (dispatch) => ({\r\n    openLightbox: (id: any) => dispatch(Actions.openLightbox({ lightboxId: SelectedChannelsModal, data: { relativeId: id } }))\r\n  })\r\n)(Component);\r\n", "import { Components, FormattedHTMLMessage, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport Filter from \"../Components/Filter\";\r\nimport { Footer } from \"../Components/Legal\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\n\r\ninterface IComponentConnectedProps {\r\n  channels: Array<ITVChannel>;\r\n  refresh: any;\r\n}\r\n\r\ninterface IComponentDispatches {}\r\n\r\nexport const Component: React.FC<IComponentConnectedProps &\r\n  IComponentDispatches> = ({ channels, refresh }) => (\r\n  <OmniturePage name=\"Browse all Channels\">\r\n    <div className=\"flexRow flex-justify-space-between\">\r\n      <div className=\"margin-xs\">\r\n        <h2\r\n          id={`group-BrowseAll`}\r\n          className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\"\r\n        >\r\n          <FormattedMessage id=\"All channels page\" />\r\n        </h2>\r\n        <FormattedHTMLMessage id=\"All channels page description\">\r\n          {(__html: any) => (\r\n            <Components.Visible when={Boolean(__html)}>\r\n              <div className=\"spacer5\"></div>\r\n              <p className=\"noMargintxtSize14\">{__html}</p>\r\n            </Components.Visible>\r\n          )}\r\n        </FormattedHTMLMessage>\r\n      </div>\r\n    </div>\r\n    <div className=\"spacer15\" aria-hidden=\"true\" />\r\n    <Filter\r\n      groupName={`group-BrowseAll`}\r\n      channels={channels}\r\n      allowSelection={true}\r\n      forceSelectable={true}\r\n      allowMultipleWaysToAdd={true}\r\n    />\r\n    <Footer\r\n      pageName={Volt.EDIsplayGroupKey.TV_BROWSE_ALL}\r\n      label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}\r\n      content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.TV_BROWSE_ALL}`}\r\n    />\r\n  </OmniturePage>\r\n);\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog }: IStoreState) => ({\r\n    channels: ValueOf(catalog, \"channels\", []),\r\n    refresh: Math.random() * 1000\r\n  })\r\n)(Component);\r\n", "import { Components, FormattedHTMLMessage, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { INavigationItem, ITVChannel } from \"../../models\";\r\nimport { filterLanguage, sortOfferings } from \"../../utils/Characteristics\";\r\nimport { manageFloater } from \"../../utils/Floater\";\r\nimport Channel from \"../Components/Channel\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\nimport SelectedChannels from \"../Modals/SelectedChannels\";\r\n\r\n\r\ninterface IComponentConnectedProps {\r\n  languages: Array<string>;\r\n  channels: Array<ITVChannel>;\r\n  navigation: Array<INavigationItem>;\r\n  openLightbox: Function;\r\n  refresh: any;\r\n}\r\n\r\nconst getAlaCarteNav = (navigation: Array<INavigationItem>) => {\r\n  const international = navigation.find(data => data.key === Volt.EDIsplayGroupKey.INTERNATIONAL);\r\n  return ValueOf<Array<Volt.IDisplayGroupOffering>>(international, \"children\", []).find(data => data.key === Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE);\r\n};\r\n\r\nexport const Alacarte: React.FC<IComponentConnectedProps> = ({\r\n  languages,\r\n  channels,\r\n  navigation,\r\n  openLightbox,\r\n  refresh\r\n}) => {\r\n  const floaterData = getAlaCarteNav(navigation);\r\n  const floater$: HTMLDivElement | any = React.useRef(null);\r\n  const [floater, setIsFloating] = React.useState({ isFloating: false, leftPos: \"auto\" });\r\n  const totalPrice = ValueOf(floaterData, \"subTotalPrice.price\", 0);\r\n\r\n  React.useEffect(() => {\r\n    const scrollSpy = () => {\r\n      manageFloater(floater$, setIsFloating);\r\n    };\r\n    window.addEventListener(\"scroll\", scrollSpy);\r\n    return () => {\r\n      window.removeEventListener(\"scroll\", scrollSpy);\r\n    };\r\n  }, []);\r\n\r\n  return <OmniturePage name=\"International Alacarte\">\r\n    <div className=\"section-bell-tv-international-channels-individual-tv-channels clearfix\">\r\n      <div className=\"spacer5\" />\r\n      <div className=\"flexRow flex-justify-space-between\">\r\n        <div className=\"margin-xs\">\r\n          <h2 id={Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE} className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\">\r\n            <FormattedMessage id=\"International a la carte page\" />\r\n          </h2>\r\n          <FormattedHTMLMessage id=\"International a la carte page description\">\r\n            {\r\n              (__html: any) => <Components.Visible when={Boolean(__html)}>\r\n                <div className=\"spacer5\"></div>\r\n                <p className=\"noMargintxtSize14\">{__html}</p>\r\n              </Components.Visible>\r\n            }\r\n          </FormattedHTMLMessage>\r\n        </div>\r\n        <div\r\n          id=\"wrap\"\r\n          ref={floater$}\r\n          onClick={() => openLightbox(\"wrap\")}\r\n          tabIndex={0}\r\n          role=\"button\"\r\n          className={`floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer ${floater.isFloating ? \"fix-floating-notification\" : null}`}\r\n          style={{ left: floater.leftPos }}>\r\n          <div className=\" txtSize26 virginUltraReg pad-10-top\">{ValueOf(floaterData, \"count\", 0)}</div>\r\n          <span className=\"\"><FormattedMessage id=\"CHANNELS_SELECTED\" /></span>\r\n          <div className=\"txtBold\"><Components.BellCurrency value={ValueOf(floaterData, \"subTotalPrice.price\", 0)} /></div>\r\n        </div>\r\n      </div>\r\n      <div className=\"spacer15\" />\r\n      <div className=\"panel-body bell-tv-package-filters-row bgWhite\">\r\n        {\r\n          languages.map(langauge => {\r\n            const _channels = filterLanguage(channels, langauge);\r\n            return <Components.Visible key={langauge} when={_channels.length > 0}>\r\n              <fieldset>\r\n                <legend className=\"txtSize18 txtBlack txtBold\"><FormattedMessage id={langauge} /></legend>\r\n                <div className=\"bell-tv-channels bell-tv-channel-picker flexRow\">\r\n                  {sortOfferings(_channels).map((channel: ITVChannel) => <Channel key={channel.id} {...channel} multipleWaysToAdd={[]} />)}\r\n                </div>\r\n              </fieldset>\r\n              <div className=\"spacer40\" />\r\n            </Components.Visible>;\r\n          })\r\n        }\r\n      </div>\r\n    </div>\r\n    <SelectedChannels\r\n      channels={channels.filter(channel => channel.isSelected)}\r\n      totalPrice={totalPrice}\r\n    />\r\n  </OmniturePage>;\r\n};\r\n", "import * as React from \"react\";\r\nimport { Volt, Components } from \"omf-changepackage-components\";\r\nimport { FormattedMessage } from \"react-intl\";\r\n\r\nimport Combo from \"../Components/Combo\";\r\nimport { filterLanguage, sortOfferings } from \"../../utils/Characteristics\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\n\r\ninterface IComponentConnectedProps {\r\n  languages: Array<string>;\r\n  combos: Array<Volt.IProductOffering>;\r\n}\r\n\r\nexport const Combos: React.FC<IComponentConnectedProps> = ({\r\n  languages,\r\n  combos\r\n}) => <OmniturePage name=\"International Combos\">\r\n  <div className=\"section-bell-tv-international-channels-combo\">\r\n    <div className=\"spacer10\" />\r\n    <h3 id={Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS} className=\"virginUltraReg noMargin txtBlack text-uppercase txtSize20\"><FormattedMessage id=\"International Combos page\" /></h3>\r\n    <div className=\"spacer15\" />\r\n    {\r\n      languages.map(langauge => {\r\n        const _combos = filterLanguage(combos, langauge);\r\n        return <Components.Visible key={langauge} when={_combos.length > 0}>\r\n          <h4 className=\"txtSize18 txtBlack txtBold\"><FormattedMessage id={langauge} /></h4>\r\n          {sortOfferings(_combos).map(combo => <Combo key={combo.id} {...combo} />)}\r\n        </Components.Visible>;\r\n      })\r\n    }\r\n  </div>\r\n</OmniturePage>;\r\n", "import * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\n\r\ninterface IFilterProps {\r\n  filter: string;\r\n  languages: Array<string>;\r\n  setFitler: (state: string) => void;\r\n}\r\nexport const Filter: React.FC<IFilterProps> = ({\r\n  filter,\r\n  languages,\r\n  setFitler\r\n}) => <div id=\"filtersContainer\" className=\"tvcsfilters-filtersContainer bell-tv-package col-xs-12 bgGray19 container-full-width-xs\">\r\n  <div id=\"filterInputsContainer\" className=\"tvcsfilters-filtersInputsContainer bell-tv-package-body clearfix bgWhite\">\r\n    <div className=\"tvcsfilters-conditionalInlineBlock flexBlock flexCenter flexCol-xs\">\r\n      <div className=\"col-xs-12 col-sm-3\">\r\n        <label htmlFor=\"Select_Language\" className=\"tvcsfilters-conditionalTitleFormatting noMargin txtLightGray3\"><FormattedMessage id=\"Select a language region\" /></label>\r\n        <span className=\"spacer10 col-xs-12 visible-xs\" />\r\n      </div>\r\n      <div className=\"col-xs-12 col-sm-9\">\r\n        <div className=\"tvcsfilters-conditionalFilterPadding15 tvcsfilters-conditionalInlineBlock\">\r\n          <div className=\"form-control-select-box tvcsfilters-xs-select-dropdown col-xs-12 col-sm-8\">\r\n            <select id=\"Select_Language\" value={filter} onChange={(e) => setFitler(e.target.value)} className=\"form-control form-control-select tvcsfilters-select-dropdown-filter txtSize14 bgGrayLight1\">\r\n              <FormattedMessage id=\"All languages\" >\r\n                {\r\n                  txt => <option id=\"all\" value=\"all\">{txt}</option>\r\n                }\r\n              </FormattedMessage>\r\n              {\r\n                languages.map((option, i) => (<FormattedMessage id={option}>\r\n                  {\r\n                    txt => <option id={`option_${i}`} value={option}>{txt}</option>\r\n                  }\r\n                </FormattedMessage>))\r\n              }\r\n            </select>\r\n            <span aria-hidden=\"true\"\r\n              style={{\r\n                backgroundImage: \"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAYBAMAAADT3mpnAAAAD1BMVEUAAADhCgrhCgrhCgrhCiGX/cTIAAAAA3RSTlMAv5hn/23fAAAAPklEQVQI12MAAgUGMGByhNDCJmABRmNjRzDX2NhEAMwFCoC4IAAUoCqAmwuxxxAqIABxhyFEBdRWRhAX5m4AQWUIfOEz3hMAAAAASUVORK5CYII=)\",\r\n                backgroundPosition: \"center left\",\r\n                backgroundRepeat: \"no-repeat\",\r\n                width: \"24px\",\r\n                backgroundSize: \"40%\"\r\n              }}></span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div className=\"tvcsfilters-conditionalSpacerShadow\" />\r\n  </div>\r\n</div >;\r\n", "import { Actions, EWidgetRoute, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { Redirect, Route, Switch, useLocation } from \"react-router-dom\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { getSupportdLanguages } from \"../../utils/Characteristics\";\r\nimport { Footer } from \"../Components/Legal\";\r\nimport { ModalId as SelectedChannelsModal } from \"../Modals/SelectedChannels\";\r\nimport { Alacarte } from \"./Alacarte\";\r\nimport { Combos } from \"./Combos\";\r\nimport { Filter } from \"./Filter\";\r\n\r\n\r\ninterface IComponentConnectedProps {\r\n  comboLanguages: Array<string>;\r\n  channelLanguages: Array<string>;\r\n  combos: Array<Volt.IProductOffering>;\r\n  channels: Array<ITVChannel>;\r\n  navigation: any;\r\n  refresh: any;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  openLightbox: Function;\r\n}\r\n\r\nexport const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  comboLanguages,\r\n  channelLanguages,\r\n  combos,\r\n  channels,\r\n  navigation,\r\n  openLightbox,\r\n  refresh\r\n}) => {\r\n  const [filter, setFitler] = React.useState(\"all\");\r\n  // const languages = allLanguages.filter(ln => filter === \"all\" || ln === filter);\r\n  // Focus proper section on sidebar navigation\r\n  const location = useLocation();\r\n  React.useEffect(() => {\r\n    setFitler(\"all\");\r\n    // try {\r\n    //     const target = document.getElementById(location.hash.substr(1));\r\n    //     target && target.scrollIntoView(true);\r\n    // } catch (e) { }\r\n  }, [location]);\r\n  // --\r\n  return <>\r\n    <h2 className=\"virginUltraReg txtSize28 noMargin txtBlack text-uppercase\"><FormattedMessage id=\"International page\" /></h2>\r\n    <div className=\"spacer10\" />\r\n    <Switch>\r\n      <Route path={EWidgetRoute.TV_InternationalCombos}>\r\n        <Filter filter={filter} setFitler={setFitler} languages={comboLanguages} />\r\n        <Combos combos={combos} languages={comboLanguages.filter(ln => filter === \"all\" || ln.indexOf(filter) > -1)} />\r\n      </Route>\r\n      <Route path={EWidgetRoute.TV_InternationalAlacarte}>\r\n        <Filter filter={filter} setFitler={setFitler} languages={channelLanguages} />\r\n        <Alacarte\r\n          openLightbox={openLightbox}\r\n          channels={channels}\r\n          navigation={navigation}\r\n          languages={channelLanguages.filter(ln => filter === \"all\" || ln.indexOf(filter) > -1)}\r\n          refresh={refresh}\r\n        />\r\n      </Route>\r\n      <Redirect to={EWidgetRoute.TV_InternationalCombos} />\r\n    </Switch>\r\n    <Footer pageName={Volt.EDIsplayGroupKey.INTERNATIONAL}\r\n      label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}\r\n      content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.INTERNATIONAL}`} />\r\n  </>;\r\n};\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog, navigation }: IStoreState) => {\r\n    const combos = ValueOf<Array<Volt.IProductOffering>>(catalog, \"offerings.\" + Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS, []);\r\n    const channels = ValueOf<Array<Volt.IProductOffering>>(catalog, \"offerings.\" + Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE, []) as Array<ITVChannel>;\r\n    return {\r\n      comboLanguages: getSupportdLanguages(combos),\r\n      channelLanguages: getSupportdLanguages(channels),\r\n      combos, channels, navigation,\r\n      refresh: Math.random() * 1000\r\n    };\r\n  },\r\n  (dispatch) => ({\r\n    openLightbox: (id: any) => dispatch(Actions.openLightbox({ lightboxId: SelectedChannelsModal, data: { relativeId: id } }))\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Volt, ValueOf, Components, FormattedHTMLMessage } from \"omf-changepackage-components\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nimport Combo from \"../Components/Combo\";\r\nimport { Footer } from \"../Components/Legal\";\r\nimport { sortOfferings } from \"../../utils/Characteristics\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\n\r\ninterface IComponentConnectedProps {\r\n  packages: Array<Volt.IProductOffering>;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n}\r\n\r\nexport const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  packages\r\n}) => <OmniturePage name=\"Movies and Series\">\r\n  <h2 className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\">\r\n    <FormattedMessage id=\"Movies and Series page\" />\r\n  </h2>\r\n  <FormattedHTMLMessage id=\"Movies and Series page Description\">\r\n    {\r\n      (__html: any) => <Components.Visible when={Boolean(__html)}>\r\n        <div className=\"spacer5\"></div>\r\n        <p className=\"noMargintxtSize14\">{__html}</p>\r\n      </Components.Visible>\r\n    }\r\n  </FormattedHTMLMessage>\r\n  <div className=\"spacer20\" />\r\n  <div className=\"section-bell-tv-packages accss-focus-outline-override-white-bg\">\r\n    {\r\n      sortOfferings(packages).map(combo => <Combo key={combo.id} {...combo} />)\r\n    }\r\n  </div>\r\n  <Footer pageName={Volt.EDIsplayGroupKey.MOVIE}\r\n    label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}\r\n    content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.MOVIE}`} />\r\n</OmniturePage>;\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog }: IStoreState) => ({\r\n    refresh: Math.random() * 1000,\r\n    packages: ValueOf<Array<Volt.IProductOffering>>(catalog, \"offerings.\" + Volt.EDIsplayGroupKey.MOVIE, [])\r\n  })\r\n)(Component);\r\n", "import { EWidgetRoute, Components, ValueOf, Omniture, FormattedHTMLMessage } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { useHistory, useLocation } from \"react-router-dom\";\r\nimport { IStoreState, ITVCatalog, ITVChannel } from \"../../models\";\r\nimport { getSearchSuggestions } from \"../../utils/Search\";\r\n\r\ninterface IComponentConnectedProps {\r\n  catalog: ITVCatalog;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n}\r\n\r\ndeclare const $: any;\r\n\r\nexport const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  catalog\r\n}) => {\r\n  const history = useHistory();\r\n  const location = useLocation();\r\n  const [suggestions, setSuggestions] = React.useState<Array<ITVChannel>>([]);\r\n  const [isNavigating, setNavigating] = React.useState(true);\r\n  const [searchTerm, setSearchTerm] = React.useState(\"\");\r\n\r\n  React.useEffect(() => {\r\n    if (location.pathname !== \"/Search\")\r\n      setSearchTerm(\"\");\r\n  }, [location]);\r\n\r\n  function handleInput(e: React.ChangeEvent<HTMLInputElement>) {\r\n    const { value } = e.target;\r\n    setSearchTerm(value);\r\n    if (Boolean(value))\r\n      setNavigating(false);\r\n    setSuggestions(getSearchSuggestions(value));\r\n  }\r\n\r\n  function redirect(e: any) {\r\n    e && e.preventDefault && e.preventDefault();\r\n    const query = ((typeof e === \"string\" ? e : searchTerm) || \"\").trim();\r\n    if (!Boolean(query)) return;\r\n    Omniture.useOmniture().trackAction({\r\n      id: \"searchSubmit\",\r\n      s_oAPT: {\r\n        actionId: 395,\r\n        actionresult: 0,\r\n        applicationState: 0\r\n      },\r\n      s_oSRT: query\r\n    });\r\n    history.push({\r\n      pathname: EWidgetRoute.TV_Search,\r\n      search: \"?query=\" + encodeURIComponent(query)\r\n    });\r\n    setSearchTerm(query);\r\n    setNavigating(true);\r\n    setSuggestions([]);\r\n  }\r\n\r\n  React.useEffect(() => {\r\n    function detectClick(e: any) {\r\n      $(e.target).closest(\"#search_area\").length === 0 && setSuggestions([]);\r\n    }\r\n    document.addEventListener(\"click\", detectClick);\r\n    return () => {\r\n      document.removeEventListener(\"click\", detectClick);\r\n    };\r\n  }, []);\r\n\r\n  const handleNavAway = (e: React.KeyboardEvent<HTMLButtonElement>) => {\r\n    setSuggestions([]);\r\n    setNavigating(true);\r\n  };\r\n\r\n  return <div id=\"search_area\">\r\n    <form id=\"search-bar\" className=\"bell-tv-search-bar\" onSubmit={redirect}>\r\n      <div className=\"bell-search-field relative accss-focus-outline-override-grey-bg\">\r\n        <FormattedHTMLMessage id=\"Search channels\">\r\n          {\r\n            (txt: string) => <input aria-label=\"Search\"\r\n              onChange={(e) => handleInput(e)}\r\n              value={searchTerm}\r\n              type=\"text\"\r\n              className=\"form-control bell-search-field-input\"\r\n              placeholder={txt}\r\n              aria-autocomplete=\"both\"\r\n              onFocus={() => setNavigating(false)}\r\n            />\r\n          }\r\n        </FormattedHTMLMessage>\r\n        <div className=\"absolute bell-search-field-button\">\r\n          <button id=\"SEARCH_ICON\" type=\"submit\" className=\"btn btn-search-submi txtSize20\" onFocus={(e: any) => handleNavAway(e)} aria-label=\"Search\">\r\n            <span className=\"volt-icon icon-search txtDarkGrey\"></span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </form>\r\n    <div role=\"status\" aria-live=\"assertive\" aria-relevant=\"additions\" className=\"sr-only\">\r\n      {\r\n        !isNavigating && Boolean(searchTerm) ?\r\n          ValueOf(suggestions, \"length\", 0) > 0\r\n            ? <FormattedMessage id=\"AVAILABLE_SEARCH_RESULTS\" values={{ value: suggestions.length }} />\r\n            : <FormattedMessage id=\"NO_SEARCH_RESULTS\" />\r\n          : null\r\n      }\r\n    </div>\r\n    <Components.Visible when={ValueOf(suggestions, \"length\", 0) > 0}>\r\n      <div role=\"tooltip\" aria-label=\"Search suggestions\" className=\"bell-search-suggestions tooltip fade bottom in bs-tooltip-bottom\">\r\n        <div className=\"arrow\" style={{ left: \"50%\" }} aria-hidden=\"true\" />\r\n        {\r\n          suggestions && Boolean(suggestions.length) &&\r\n                    <div className=\"tooltip-inner\">\r\n                      <ul className=\"noBullets\" role=\"listbox\">\r\n                        {\r\n                          suggestions.map((suggestion: ITVChannel) => <li>\r\n                            <button id={`SEARCH_${suggestion.name}`} role=\"option\" onClick={() => {\r\n                              redirect(suggestion.name);\r\n                            }} className=\"btn txtLeft pad-0 txtNormal\">{suggestion.name}</button>\r\n                          </li>)\r\n                        }\r\n                      </ul>\r\n                    </div>\r\n        }\r\n      </div>\r\n    </Components.Visible>\r\n  </div>;\r\n};\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog }: IStoreState) => ({ catalog }),\r\n  (dispatch) => ({\r\n  })\r\n)(Component);\r\n", "import { Components, ValueOf } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { Link, useRouteMatch } from \"react-router-dom\";\r\nimport { INavigationItem, IStoreState } from \"../../models\";\r\nimport Search from \"./Search\";\r\n\r\nconst {\r\n  Visible\r\n} = Components;\r\n\r\ninterface IComponentConnectedProps {\r\n  navigation: Array<INavigationItem>;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n}\r\n\r\ninterface IOfferingLinkProps extends INavigationItem {\r\n  isActive?: boolean;\r\n  subMenu?: boolean;\r\n}\r\n\r\nconst OfferingLink = (offering: IOfferingLinkProps) => <Link id={`MENU_${offering.offeringId}`} to={offering.route} role=\"link\" className={`bell-tv-navigator-tab-row flexRow ${offering.isActive ? \"active\" : \"\"}`}>\r\n  <div className=\"bell-tv-navigator-tabs-text flexGrow\">\r\n    <span className={`${offering.subMenu ? \"sans-serif txtSize14\" : \"virginUltraReg txtSize16 text-uppercase\"} noPadding block submenu-name`}>\r\n      <FormattedMessage id={offering.offeringKey || \"NONE\"} />\r\n      <Visible when={offering.count !== undefined}>\r\n                &nbsp;({offering.count})\r\n      </Visible>\r\n    </span>\r\n    <Visible when={Boolean(offering.name)}>\r\n      <span className=\"virginUltraReg txtSize16 noPadding submenu-name text-uppercase\">\r\n        {offering.name}&nbsp;-&nbsp;\r\n      </span>\r\n    </Visible>\r\n    <Visible when={Boolean(offering.subTotalPrice)}>\r\n      <span className=\"noPadding submenu-price submenu-price txtSize14\">\r\n        <Components.BellCurrency value={ValueOf(offering, \"subTotalPrice.price\", 0)} />\r\n        <span aria-hidden><FormattedMessage id=\"PER_MO\" /></span>\r\n        <span className=\"sr-only\"><FormattedMessage id=\"PER_MONTH\">{(txt) => <>{txt}</>}</FormattedMessage></span>\r\n      </span>\r\n    </Visible>\r\n  </div>\r\n  <div className=\"bell-tv-navigator-tabs-pointer flexStatic\">\r\n    <span className=\"volt-icon icon-Right_arrow txtSize15 inlineBlock\" aria-hidden={true}></span>\r\n  </div>\r\n</Link>;\r\n\r\nexport const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  navigation\r\n}) => <nav className=\"bell-tv-navigator sticky\" role=\"tablist\">\r\n  <div className=\"virginUltraReg txtSize22 bgGray mobile-menu-header text-uppercase d-block d-md-none\">\r\n    <FormattedMessage id=\"YOUR_TV_CATEGORIES\" />\r\n  </div>\r\n  <Search />\r\n  <div className=\"spacer15 hidden-xs\"></div>\r\n  <ul className=\"bell-tv-navigator-tabs noBullets virgin-scroll accss-focus-outline-override-grey-bg\" role=\"presentation\">\r\n    {\r\n      navigation.map(offering => {\r\n        const active = Boolean(useRouteMatch(offering.route));\r\n        return <li key={offering.key} className={`bell-tv-navigator-tab ${active ? \"active expanded\" : \"\"}`}  role=\"tab\" aria-selected={active ? \"true\" : \"false\"}>\r\n          <OfferingLink {...offering} />\r\n          <Visible when={Array.isArray(offering.children)}>\r\n            <div className={`bell-tv-navigator-tab-more ${active ? \"active\" : \"\"} ${active ? \"d-block\" : \"d-none\"}`} aria-expanded={location.pathname === offering.route} role=\"tab\">\r\n              {\r\n                ValueOf(offering, \"children\", []).map((offering: INavigationItem) => <OfferingLink subMenu={true} isActive={Boolean(useRouteMatch(offering.route))} {...offering} />)\r\n              }\r\n            </div>\r\n          </Visible>\r\n        </li>;\r\n      })\r\n    }\r\n  </ul>\r\n  {/* This element is popilated from omf-changpackage-navigation/src/summary/TvSummaryPortal.tsx */}\r\n  <div id=\"tv-sedebar-summary-portal\" className=\"dockbar-content d-block d-md-none pad-15-top\" />\r\n</nav>;\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ navigation }: IStoreState) => ({ navigation })\r\n)(Component);\r\n", "import {\r\n  Components,\r\n  Omniture,\r\n  ValueOf,\r\n  Volt\r\n} from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage, useIntl } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ITVChannel } from \"../../models\";\r\nimport { toggleSelection } from \"../../store\";\r\nimport Filter from \"../Components/Filter\";\r\nimport Price from \"../Components/Price\";\r\n\r\nconst { Visible } = Components;\r\n\r\ninterface IComponentProps extends Volt.IProductOffering {\r\n  isSingle: boolean;\r\n  isDisabled: boolean;\r\n}\r\n\r\ninterface IComponentConnectedProps { }\r\n\r\ninterface IComponentDispatches {\r\n  onActionClick: (action: Volt.IHypermediaAction) => void;\r\n}\r\n\r\nexport const Component: React.FC<IComponentProps &\r\n  IComponentConnectedProps &\r\n  IComponentDispatches> = ({\r\n  id,\r\n  name,\r\n  shortDescription,\r\n  longDescription,\r\n  regularPrice,\r\n  promotionDetails,\r\n  // displayGroupKey,\r\n  childOfferings,\r\n  offeringAction,\r\n  isSingle,\r\n  isSelectable,\r\n  isSelected,\r\n  isDisabled,\r\n  isCurrent,\r\n  onActionClick  \r\n}) => {\r\n  const [expanded, Toggle] = React.useState(false);\r\n  const intl = useIntl();\r\n  React.useEffect(() => {\r\n    expanded &&\r\n        Omniture.useOmniture().trackAction({\r\n          id: \"showChannelsClick\",\r\n          s_oAPT: {\r\n            actionId: 648\r\n          },\r\n          s_oEPN: \"Show Channel\"\r\n        });\r\n  }, [expanded]);\r\n  const haveChildren = ValueOf(childOfferings, \"length\", 0) > 0;\r\n  return (\r\n    <div\r\n      id={id}\r\n      className={`bell-tv-package bell-tv-base-pack accss-focus-outline-override-white-bg ${\r\n        isDisabled ? \"disabled\" : \"\"\r\n      } ${isSingle || isSelected ? \"selected\" : \"\"}`}\r\n    >\r\n      <div className=\"bell-tv-package-main flexRow block-xs bgWhite\">\r\n        <div className=\"relative bell-tv-package-controls flexStatic no-margin-xs\">\r\n          <Visible when={isCurrent}>\r\n            <div className=\"absolute pad-5-top pad-5-bottom pad-30-left pad-30-right bgOrange txtWhite current-flag\">\r\n              <FormattedMessage id=\"Current package\" />\r\n            </div>\r\n            <div className=\"spacer30\"></div>\r\n          </Visible>\r\n          <label\r\n            id={`label_${id}`}\r\n            className={`graphical_ctrl ctrl_radioBtn pointer ${\r\n              isDisabled ? \"disabled\" : \"\"\r\n            }`}\r\n            onClick={() => !isDisabled && !isSelected && !isSingle && onActionClick(offeringAction)}\r\n          >\r\n            <input\r\n              id={`radio_${id}`}\r\n              type=\"radio\"\r\n              checked={isSingle || isSelected}\r\n              disabled={isDisabled}\r\n            />\r\n            <span className=\"ctrl_element\" />\r\n            <span className=\"package-name block inlineBlock-xs txtSize18 txtNormal txtBlack\">\r\n              {name}\r\n            </span>\r\n            <Price regularPrice={regularPrice} promotionDetails={promotionDetails} />\r\n          </label>\r\n        </div>\r\n        <div className=\"bell-tv-package-separator flexStatic\"></div>\r\n        <div className=\"bell-tv-package-description relative flexBlock flexWrap\">\r\n          <div className=\"bell-tv-package-channels-detail flexStatic flexBasis100 order1\">\r\n            <p className=\"noMargin txtSize14\">\r\n              {/* <span className=\"aria-visible\">Package description: </span> */}\r\n              {longDescription || shortDescription}\r\n            </p>\r\n          </div>\r\n          <div className=\"spacer30 flexStatic flexBasis100 order2\">&nbsp;</div>\r\n          <div className=\"order4 flex1\">\r\n            <div className=\"spacer10 visible-sm\"></div>\r\n            <div\r\n              className=\"bell-tv-package-icons noMargin flexBlock flexWrap\"\r\n              aria-hidden=\"true\"\r\n            >\r\n              {ValueOf<Array<ITVChannel>>(childOfferings, undefined, [])\r\n                .slice(0, 10)\r\n                .map(channel => (\r\n                  <div className=\"channel-item\">\r\n                    <img\r\n                      src={ValueOf(channel, \"imagePath\", \"\")}\r\n                      alt={ValueOf(channel, \"name\", \"\")}\r\n                    />\r\n                  </div>\r\n                ))}\r\n            </div>\r\n            <div className=\"spacer15 col-xs-12 order5\"></div>\r\n            <Visible when={haveChildren}>\r\n              <div className=\"col-xs-12 order6 flex-container\">\r\n                <button\r\n                  id={`ACCORDION_ICON_${id}`}\r\n                  onClick={() => Toggle(!expanded)}\r\n                  aria-controls=\"div1-accessible\"\r\n                  data-toggle=\"collapse\"\r\n                  className=\"btn btn-link no-pad txtVirginBlue accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center\"\r\n                  aria-expanded={expanded}\r\n                  aria-label={`${intl.formatMessage({id: \"Show channels\"})} ${intl.formatMessage({id: \"FOR_TEXT\"})} ${name}`}\r\n                >\r\n                  {/* <span className=\"sr-only accordion-label\" aria-live=\"polite\" aria-atomic=\"true\" aria-hidden=\"false\">collapsed</span> */}\r\n                  <span\r\n                    className={`volt-icon txtSize22 icon-blue icon-${\r\n                      expanded ? \"Collapse\" : \"Expand\"\r\n                    }`}\r\n                  >\r\n                    <span\r\n                      className={`volt-icon path1 icon-${\r\n                        expanded ? \"Collapse\" : \"Expand\"\r\n                      }`}\r\n                    ></span>\r\n                    <span\r\n                      className={`volt-icon path2 icon-${\r\n                        expanded ? \"Collapse\" : \"Expand\"\r\n                      }`}\r\n                    ></span>\r\n                  </span>\r\n                  <span className=\"txtSize12 sans-serif txtBlue margin-10-left\">\r\n                    <FormattedMessage id=\"Show channels\" />\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </Visible>\r\n          </div>\r\n          <div className=\"clear\"></div>\r\n        </div>\r\n      </div>\r\n      <Visible when={expanded}>\r\n        <div className=\"bell-tv-package-footer bgGray19 expanded\" role=\"region\">\r\n          <div className=\"spacer1 bgGray\" />\r\n          <Filter\r\n            groupName={`radio_${id}`}\r\n            channels={ValueOf(childOfferings, undefined, [])}\r\n            label={<FormattedMessage id=\"Package channels\" values={{ name }} />}\r\n          />\r\n        </div>\r\n      </Visible>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default connect<\r\n  IComponentConnectedProps,\r\n  IComponentDispatches,\r\n  IComponentProps\r\n>(\r\n  ({ }: IStoreState) => ({}),\r\n  dispatch => ({\r\n    onActionClick: (action: Volt.IHypermediaAction) =>\r\n      dispatch(toggleSelection(action))\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { Volt, ValueOf, Components, FormattedHTMLMessage } from \"omf-changepackage-components\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nimport Package from \"./Package\";\r\nimport { Footer } from \"../Components/Legal\";\r\nimport { sortOfferings } from \"../../utils/Characteristics\";\r\nimport { OmniturePage } from \"../Components/Omniture\";\r\n\r\ninterface IComponentConnectedProps {\r\n  packages: Array<Volt.IProductOffering>;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n}\r\n\r\nexport const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({\r\n  packages\r\n}) => <OmniturePage name=\"Your TV package\">\r\n  <h2 className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\">\r\n    <FormattedMessage id=\"Available Core Packages page\" />\r\n  </h2>\r\n  <FormattedHTMLMessage id=\"Available Core Packages page Description\">\r\n    {\r\n      (__html: any) => <Components.Visible when={Boolean(__html)}>\r\n        <div className=\"spacer15\"></div>\r\n        <p className=\"noMargintxtSize14\">{__html}</p>\r\n      </Components.Visible>\r\n    }\r\n  </FormattedHTMLMessage>\r\n  <div className=\"spacer30 hidden-xs\" aria-hidden={true} />\r\n  {\r\n    sortOfferings(packages).map(pack => <Package key={pack.displayGroupKey} isSingle={packages.length < 2} {...pack} isDisabled={!ValueOf(pack, \"offeringAction.href\", false)} />)\r\n  }\r\n  <Footer pageName={Volt.EDIsplayGroupKey.BASE_PROGRAMMING}\r\n    label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}\r\n    content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`} />\r\n</OmniturePage>;\r\n\r\nexport default connect<IComponentConnectedProps, IComponentDispatches>(\r\n  ({ catalog }: IStoreState) => ({\r\n    packages: ValueOf<Array<Volt.IProductOffering>>(catalog, \"offerings.\" + Volt.EDIsplayGroupKey.BASE_PROGRAMMING, [])\r\n      .filter(pack => pack.productOfferingType === Volt.EProductOfferingType.PACKAGE)\r\n  })\r\n)(Component);\r\n", "\r\nimport * as React from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport { Components, ValueOf, Actions } from \"omf-changepackage-components\";\r\nimport { useSearch } from \"../../utils/Search\";\r\nimport Filter from \"../Components/Filter\";\r\n\r\nconst { Visible } = Components;\r\n\r\nlet _query = \"\";\r\n\r\nfunction parseQueryString(query: string): any {\r\n  if (!Boolean(query)) return {};\r\n  const frags = query.replace(\"?\", \"\").split(\"&\");\r\n  return frags.reduce(\r\n    (acc: any, frag: any) => {\r\n      const pair = frag.split(\"=\");\r\n      acc[pair[0]] = decodeURIComponent(pair[1] || \"\");\r\n      return acc;\r\n    }, {}\r\n  );\r\n}\r\n\r\ninterface ComponentProps {\r\n  location?: any;\r\n}\r\n\r\nconst Search = (props: ComponentProps) => {\r\n  const dispatch = useDispatch();\r\n  const location = useLocation();\r\n  const query = React.useMemo<string>(() => ValueOf<string>(parseQueryString(location.search), \"query\", \"\"), [location]);\r\n  const results = useSearch(query);\r\n\r\n  React.useEffect(() => {\r\n    _query !== query\r\n      && results\r\n      && dispatch(Actions.omniPageLoaded(\"search\", {\r\n        id: \"Search result\",\r\n        s_oAPT: {\r\n          actionId: 395,\r\n          actionresult: 2,\r\n          applicationState: (results.length > 0) ? 1 : 2\r\n        },\r\n        s_oSRT: (_query = query)\r\n      }));\r\n  }, [results]);\r\n\r\n  return results ? (<>\r\n    <div className=\"flexRow flex-justify-space-between\">\r\n      <div className=\"margin-xs\">\r\n        <h2\r\n          id=\"Search\"\r\n          className=\"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs\"\r\n        >\r\n          <Visible\r\n            when={results.length > 0}\r\n            placeholder={\r\n              <FormattedMessage\r\n                id=\"NO_SEARCH_RESULT_FOR\"\r\n                values={{ value: query }}\r\n              >\r\n                {(no_search) => <span aria-live=\"polite\" role=\"alert\">{no_search}</span>}\r\n              </FormattedMessage>\r\n            }\r\n          >\r\n            <FormattedMessage\r\n              id=\"SEARCH_RESULT_FOR\"\r\n              values={{ value: query }}\r\n            >\r\n              {(search_search) => <span aria-live=\"polite\" role=\"alert\">{search_search}</span>}\r\n            </FormattedMessage>\r\n          </Visible>\r\n        </h2>\r\n      </div>\r\n    </div>\r\n    <div className=\"spacer15\" aria-hidden=\"true\" />\r\n    <Filter\r\n      groupName=\"Search\"\r\n      channels={results}\r\n      allowSelection={true}\r\n      forceSelectable={true}\r\n      allowMultipleWaysToAdd={true}\r\n      showFilters={false}\r\n      showHeader={false}\r\n    />\r\n  </>) : null;\r\n};\r\n\r\nexport default Search;\r\n", "import * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { FormattedMessage, FormattedDate } from \"react-intl\";\r\nimport { ValueOf, Components, Omniture, Volt } from \"omf-changepackage-components\";\r\nimport { IStoreState, IProductOffering, IAccountDetail } from \"../../models\";\r\n\r\nconst {\r\n  BellCurrency,\r\n  Visible\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n  accountDetails: Array<IAccountDetail>;\r\n}\r\n\r\nconst OfferingView: React.FC<IProductOffering & { displayGroupKey: Volt.EDIsplayGroupKey }> = ({\r\n  Name,\r\n  RegularPrice,\r\n  PromotionDetails,\r\n  ChannelCount,\r\n  displayGroupKey\r\n}) => <div>\r\n  <div className=\"flexRow\">\r\n    <div className=\"flexGrow\">{Name}<Visible when={displayGroupKey === Volt.EDIsplayGroupKey.ALACARTE && ChannelCount > 0}><FormattedMessage id=\"Count of channels\" values={{ count: ChannelCount }} /></Visible></div>\r\n    <div>\r\n      <BellCurrency value={ValueOf(RegularPrice, \"Price\", 0)} />\r\n      <span aria-hidden><FormattedMessage id=\"PER_MO\" /></span>\r\n      <span className=\"sr-only\"><FormattedMessage id=\"PER_MONTH\">{(txt) => <>{txt}</>}</FormattedMessage></span>\r\n    </div>\r\n  </div>\r\n  <Visible when={!!PromotionDetails}>\r\n    <div className=\"spacer5\" aria-hidden=\"true\" />\r\n    <Visible when={ValueOf(PromotionDetails, \"Description\", false)}>\r\n      <div className=\"flexRow\">\r\n        <div className=\"flexGrow\">{ValueOf(PromotionDetails, \"Description\", \"\")}</div>\r\n        <div>\r\n          <BellCurrency value={ValueOf(PromotionDetails, \"PromotionalPrice.Price\", 0)} />\r\n          <span aria-hidden><FormattedMessage id=\"PER_MO\" /></span>\r\n          <span className=\"sr-only\"><FormattedMessage id=\"PER_MONTH\">{(txt) => <>{txt}</>}</FormattedMessage></span>\r\n        </div>\r\n      </div>\r\n    </Visible>\r\n    <Visible when={ValueOf(PromotionDetails, \"ExpiryDate\", false)}>\r\n      <div>\r\n        <FormattedDate value={ValueOf(PromotionDetails, \"ExpiryDate\", \"\")} format=\"yMMMMd\" timeZone=\"UTC\">\r\n          {(expiryDate: string) => <FormattedMessage id=\"PromotionExpires\" values={{ expiryDate }} />}\r\n        </FormattedDate>\r\n      </div>\r\n    </Visible>\r\n  </Visible>\r\n</div>;\r\n\r\nconst Component: React.FC<IComponentProps> = ({\r\n  accountDetails\r\n}) => {\r\n  const [expanded, toggleState] = React.useState(false);\r\n  const collapseIcon = expanded ? \"icon-Collapse\" : \"icon-Expand\";\r\n  // Omniture tracking for expand Interent package\r\n  React.useEffect(() => {\r\n    // we only care about when the menu expands\r\n    if (expanded) {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"myCurrentPackageCTA\",\r\n        s_oAPT: {\r\n          actionId: 648\r\n        },\r\n        s_oEPN: \"My current TV package\"\r\n      });\r\n    }\r\n  }, [expanded]);\r\n  // ---\r\n  return <Visible when={Array.isArray(accountDetails) && accountDetails.length > 0}>\r\n    <section className=\"bgVirginGradiant\">\r\n      <div className=\"container liquid-container sans-serif\">\r\n        <div className=\"accordion-group internet-current-package flexCol accss-focus-outline-override-black-bg\">\r\n          <div className=\"accordion-heading col-xs-12 noPaddingImp accss-focus-outline-override-black-bg\">\r\n            <a role=\"button\" id=\"my_currentPack\" href=\"javascript:void(0)\" onClick={() => toggleState(!expanded)} aria-controls=\"div1-accessible\"\r\n              className=\"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content\"\r\n              aria-expanded={expanded}>\r\n              <span className=\"sr-only accordion-label\" aria-live=\"polite\" aria-atomic=\"true\"\r\n                aria-hidden=\"true\"><FormattedMessage id={expanded ? \"Collapse\" : \"Expand\"} /></span>\r\n              <span className={`${collapseIcon} virgin-icon txtSize24 virginRedIcon`}\r\n                aria-hidden=\"true\">\r\n                <span className={`virgin-icon path1 ${collapseIcon}`} />\r\n                <span className={`virgin-icon path2 ${collapseIcon}`} />\r\n              </span>\r\n              <div className=\"margin-15-left flexCol\">\r\n                <span className=\"txtWhite txtBold txtSize18\"><FormattedMessage id=\"My current TV package\" /></span>\r\n                <span className=\"expand txtWhite txtSize12 no-margin-top\" style={{ display: expanded ? \"none\" : undefined }}><FormattedMessage id=\"Expand to view details\" /></span>\r\n              </div>\r\n            </a>\r\n          </div>\r\n          <div id=\"div1-accessible\"\r\n            className=\"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left\"\r\n            style={{ display: expanded ? \"block\" : \"none\" }}>\r\n            <div className=\"accordion-inner flexWrap flexJustifySpace flexRow\">\r\n              {\r\n                accountDetails.map(({\r\n                  displayGroupKey,\r\n                  offerings\r\n                }) => <div className=\"col-sm-5 margin-15-bottom\">\r\n                  <strong><FormattedMessage id={`HEADER_${displayGroupKey}`} /></strong>\r\n                  {offerings.map(offering => <OfferingView {...offering} displayGroupKey={displayGroupKey} />)}\r\n                </div>)\r\n              }\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </Visible>;\r\n};\r\n\r\n\r\nexport const Header = connect<IComponentProps>(\r\n  ({ accountDetails }: IStoreState) => ({ accountDetails: accountDetails || [] })\r\n)(Component);\r\n", "import { Actions, Components, EWidgetRoute, Utils } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { connect, useDispatch } from \"react-redux\";\r\nimport { BrowserRouter, Redirect, Route, Switch, useLocation, useHistory } from \"react-router-dom\";\r\nimport { IStoreState } from \"../models\";\r\nimport Addons from \"./Addons\";\r\nimport Alacarte from \"./Alacarte\";\r\nimport Browser from \"./Browser\";\r\nimport International from \"./International\";\r\nimport ChannelDetailsModal from \"./Modals/Details\";\r\nimport MultipleWaysToAddModal from \"./Modals/MultipleWays\";\r\nimport MoviesSeries from \"./MoviesSeries\";\r\nimport Navigation from \"./Navigation\";\r\nimport Packages from \"./Packages\";\r\nimport Search from \"./Search\";\r\nimport { CleanupPopoverStack } from \"./Components/Tooltip\";\r\nimport { Header } from \"./header\";\r\n\r\nconst {\r\n  RestrictionModal\r\n} = Components;\r\n\r\nconst {\r\n  errorOccured,\r\n  widgetRenderComplete,\r\n  handleNav\r\n} = Actions;\r\n\r\ninterface IComponentProps {\r\n  navStatus: boolean;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onErrorEncountered: Function;\r\n  widgetRenderComplete: Function;\r\n  closeNav: any;\r\n}\r\n\r\nconst AppRouter: React.FC<IComponentProps & IComponentDispatches> = (props) => {\r\n  const dispatch = useDispatch(),\r\n    history = useHistory();\r\n  React.useEffect(() => {\r\n    dispatch(Actions.broadcastUpdate(Actions.setHistoryProvider(history)));\r\n  }, []);\r\n  // Close mobile sidebar whenever we navigate\r\n  const location = useLocation();\r\n  React.useEffect(() => {\r\n    props.closeNav();\r\n    CleanupPopoverStack();\r\n    window.scrollTo(0, 0);\r\n  }, [location]);\r\n  return <main id=\"mainContent\">\r\n    <style dangerouslySetInnerHTML={{\r\n      __html: `\r\n            html {\r\n                scroll-behavior: smooth;\r\n            }\r\n            @media (max-width: 992px) {\r\n                .channel-tooltip {\r\n                    display: none!important;\r\n                }\r\n            }\r\n        `}} />\r\n    <Header />\r\n    <div className=\"spacer30\" />\r\n    <div className=\"container liquid-container flexRow\">\r\n      <div className={`col-md-3 col-xs-12 bell-tv-navigator-menu side-navigation d-md-block ${props.navStatus ? \"open-nav-slider\" : \"\"}`}>\r\n        <Navigation />\r\n      </div>\r\n      <div className=\"floatR col-md-9 col-xs-12 bell-tv-navigator-page accss-focus-outline-override-white-bg\">\r\n        <Switch>\r\n          <Route exact path={EWidgetRoute.TV_Packages}>\r\n            <Packages />\r\n          </Route>\r\n          <Route path={EWidgetRoute.TV_MoviesSeries}>\r\n            <MoviesSeries />\r\n          </Route>\r\n          <Route path={EWidgetRoute.TV_Alacarte}>\r\n            <Alacarte />\r\n          </Route>\r\n          <Route path={EWidgetRoute.TV_International}>\r\n            <International />\r\n          </Route>\r\n          <Route path={EWidgetRoute.TV_Addons}>\r\n            <Addons />\r\n          </Route>\r\n          <Route path={EWidgetRoute.TV_Browse}>\r\n            <Browser />\r\n          </Route>\r\n          <Route path={EWidgetRoute.TV_Search}>\r\n            <Search />\r\n          </Route>\r\n          {/* Every unknown path should redirect to base route */}\r\n          <Route path=\"*\">\r\n            <Redirect to={EWidgetRoute.TV_Packages} />\r\n          </Route>\r\n        </Switch>\r\n      </div>\r\n    </div>\r\n    <ChannelDetailsModal />\r\n    <MultipleWaysToAddModal />\r\n    <RestrictionModal id=\"TV_RESTRICTION_MODAL\" />\r\n    <div id=\"NAV_BACKDROP\" onClick={props.closeNav} className={`nav-backdrop ${props.navStatus ? \"show\" : \"hide\"}`} aria-hidden={true}></div>\r\n  </main>;\r\n};\r\n\r\nclass Component extends React.Component<IComponentProps & IComponentDispatches> {\r\n  baseRoute: string;\r\n  componentDidCatch(err: any) {\r\n    this.props.onErrorEncountered(err);\r\n  }\r\n\r\n  componentWillMount() {\r\n    this.baseRoute = `/Ordering${Utils.constructPageRoute(EWidgetRoute.TV)}`;\r\n  }\r\n\r\n  componentDidMount() {\r\n    this.props.widgetRenderComplete(\"omf-changepackage-tv\");\r\n  }\r\n\r\n  render() {\r\n    return (<BrowserRouter basename={this.baseRoute}>\r\n      <AppRouter {...this.props} />\r\n      <div className=\"spacer60\" />\r\n      <div className=\"spacer60\" />\r\n    </BrowserRouter>);\r\n  }\r\n}\r\n\r\nexport const Application = connect<IComponentProps, IComponentDispatches>(\r\n  ({ navStatus }: IStoreState) => ({ navStatus }),\r\n  (dispatch) => ({\r\n    onErrorEncountered: (error: any) => dispatch(errorOccured(error)),\r\n    widgetRenderComplete: () => dispatch(widgetRenderComplete()),\r\n    closeNav: () => dispatch(handleNav(false))\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { Components } from \"omf-changepackage-components\";\r\nimport { Application } from \"./views\";\r\n\r\nconst {\r\n  ApplicationRoot\r\n} = Components;\r\n\r\nexport const App = () => <ApplicationRoot>\r\n  <Application />\r\n</ApplicationRoot>;\r\n\r\n", "import { CommonFeatures } from \"bwtk\";\r\nimport { Actions } from \"omf-changepackage-components\";\r\nimport { Action } from \"redux-actions\";\r\nimport { Store } from \"./store\";\r\nimport { CleanupPopoverStack } from \"./views/Components/Tooltip\";\r\n\r\nconst { BasePipe } = CommonFeatures;\r\n\r\n/**\r\n * rxjs pipe provider\r\n * this fascilitates the direct connection\r\n * between widgets through rxjs Observable\r\n * @export\r\n * @class Pipe\r\n * @extends {BasePipe}\r\n */\r\nexport class <PERSON>pe extends BasePipe {\r\n  static Subscriptions(store: Store) {\r\n    return {\r\n      // [Actions.historyGo.toString()]: ({ payload }: Action<string>) => {\r\n      //     debugger;\r\n      // },\r\n      [Actions.handleNav.toString()]: ({ payload }: Action<boolean>) => {\r\n        CleanupPopoverStack();\r\n        store.dispatch(Actions.handleNav(payload));\r\n      },\r\n      [Actions.onContinue.toString()]: () => {\r\n        CleanupPopoverStack();\r\n        store.dispatch(Actions.omniPageSubmit());\r\n        Actions.broadcastUpdate(Actions.historyForward());\r\n      }\r\n    };\r\n  }\r\n  /**\r\n     *Creates a static instance of Pipe.\r\n     * @param {*} arg\r\n     * @memberof Pipe\r\n     */\r\n  static instance: Pipe;\r\n  constructor(arg: any) {\r\n    super(arg);\r\n    Pipe.instance = this;\r\n  }\r\n}\r\n", "import { ParamsProvider, ViewWidget, Widget } from \"bwtk\";\r\nimport { Actions, ContextProvider, EWidgetStatus } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport * as ReactRedux from \"react-redux\";\r\nimport { App } from \"./App\";\r\nimport { Config } from \"./Config\";\r\nimport { IWidgetProps } from \"./models\";\r\nimport { Pipe } from \"./Pipe\";\r\nimport { Store } from \"./store\";\r\nimport { Root } from \"react-dom/client\";\r\n\r\nconst {\r\n  setWidgetProps,\r\n  setWidgetStatus\r\n} = Actions;\r\nconst StoreProvider = ReactRedux.Provider as any;\r\n\r\n@Widget({ namespace: \"Ordering\" })\r\nexport default class WidgetContainer extends ViewWidget {\r\n  constructor(private store: Store, private params: ParamsProvider<IWidgetProps, any>, private config: Config, private pipe: Pipe) {\r\n    super();\r\n  }\r\n\r\n  /**\r\n   * Initialize widget flow\r\n   * please do not place any startup login in here\r\n   * all logic should reside in Epics.onWidgetStatusEpic\r\n   * @memberof WidgetContainer\r\n   */\r\n  init() {\r\n    this.pipe.subscribe(Pipe.Subscriptions(this.store));\r\n    this.store.dispatch(setWidgetProps(this.config));\r\n    this.store.dispatch(setWidgetProps(this.params.props));\r\n    this.store.dispatch(setWidgetStatus(EWidgetStatus.INIT));\r\n  }\r\n\r\n  /**\r\n   * Deinitialize widget flow\r\n   * Destroy all listeneres and connections\r\n   * @memberof WidgetContainer\r\n   */\r\n  destroy() {\r\n    this.pipe.unsubscribe();\r\n    this.store.destroy();\r\n  }\r\n\r\n  /**\r\n   * Render widget\r\n   * Set all contextual providers:\r\n   * * ContextProvider: top-most wrapper used to propagate all *immutable* state params\r\n   * * StoreProvider: redux store wrapper used to propagate all *mutable* state params\r\n   * @param {Element} root\r\n   * @memberof WidgetContainer\r\n   */\r\n  render(root: Root) {\r\n    const { store } = this;\r\n    root.render(\r\n      <ContextProvider value={{ config: this.config }}>\r\n        <StoreProvider {...{ store }}><App /></StoreProvider>\r\n      </ContextProvider>\r\n    );\r\n  }\r\n}\r\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__81__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__102__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__418__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__419__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__442__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__446__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__541__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__634__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__750__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__769__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__999__;", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};"], "names": ["root", "factory", "a", "i", "exports", "module", "require", "define", "amd", "self", "__WEBPACK_EXTERNAL_MODULE__102__", "__WEBPACK_EXTERNAL_MODULE__446__", "__WEBPACK_EXTERNAL_MODULE__442__", "__WEBPACK_EXTERNAL_MODULE__999__", "__WEBPACK_EXTERNAL_MODULE__634__", "__WEBPACK_EXTERNAL_MODULE__419__", "__WEBPACK_EXTERNAL_MODULE__750__", "__WEBPACK_EXTERNAL_MODULE__541__", "__WEBPACK_EXTERNAL_MODULE__769__", "__WEBPACK_EXTERNAL_MODULE__81__", "__WEBPACK_EXTERNAL_MODULE__418__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_module_cache__", "undefined", "__webpack_modules__", "__extends", "d", "b", "__", "this", "constructor", "TypeError", "String", "extendStatics", "prototype", "Object", "create", "__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "__metadata", "metadataKey", "metadataValue", "metadata", "__read", "o", "n", "ar", "e", "m", "Symbol", "iterator", "call", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "Array", "slice", "concat", "initSearch", "channels", "filterChannels", "map", "channel", "result", "characteristics", "find", "item", "name", "callSign", "search", "addIndex", "addDocuments", "collectChannlesFromAllOfferings", "productOfferings", "appendChannelToCollection", "productOfferingType", "Volt", "EProductOfferingType", "CHANNEL", "duplicateFilter", "indexOf", "id", "collection", "for<PERSON>ach", "offering", "isArray", "childOfferings", "catalogMutatorFn", "response", "productOfferingGroup", "ValueOf", "group", "lineOfBusiness", "productOfferingList", "offerings", "filter", "displayGroupKey", "EDIsplayGroupKey", "NONE", "reduce", "catalog", "index", "Math", "random", "toCharacteristicsJSON", "charact<PERSON><PERSON>", "json", "charact<PERSON>tic", "trim", "getSupportdLanguages", "acc", "language", "Boolean", "split", "sort", "filterLanguage", "sortOfferings", "direction", "translateStringList", "list", "t", "trimmedT", "Localization", "getLocalizedString", "join", "CleanupPopoverStack", "_popoverinst", "destroy", "document", "querySelectorAll", "el", "remove", "manageFloater", "ref", "callback", "$el", "current", "$parent", "parentElement", "clientHeight", "rect", "getBoundingClientRect", "floatVal", "top", "isFloating", "leftPos", "right", "iterationCopy", "src", "JSON", "parse", "stringify", "buildDefaultFilters", "defaultState", "toggleArrayItem", "arr", "splice", "useFilterDispatcher", "defaultFilter", "setFilter", "dispatcher", "toggleGenre", "genre", "setGenre", "toggleLanguage", "setLanguage", "toggleDontHave", "isDontHave", "toggleHave", "isHave", "reset", "setSort", "prop", "sortBy", "sortOrder", "hasGenre", "only<PERSON><PERSON><PERSON>", "hasLanguage", "hasDontHave", "hasHave", "whichSort", "selectedG<PERSON>re", "getState", "setState", "state", "usePrevious", "__assign", "ownKeys", "getAccountDetails", "setAccountDetails", "getCatalog", "setCatalog", "setNavigation", "toggleSelection", "updateCatalog", "BaseConfig", "configProperty", "errorOccured", "setWidgetStatus", "finalizeRestriction", "clearCachedState", "omniPageLoaded", "omniPageSubmit", "BaseLocalization", "BaseStore", "actionsToComputedPropertyName", "handleNav", "Modal", "ModalId", "Component", "Visible", "<PERSON><PERSON><PERSON><PERSON>", "Price", "Option", "NoThanksOffer", "<PERSON><PERSON><PERSON>", "Combo", "Footer", "OmniturePage", "handlePropFilter", "handleVoidFilter", "<PERSON><PERSON><PERSON><PERSON>", "ErrorMessage", "AdvancedFilters", "getSelectedChannels", "SelectedChannels", "getAlaCarteNav", "Combos", "OfferingLink", "_query", "BellCurrency", "OfferingView", "Header", "RestrictionModal", "widgetRenderComplete", "AppRouter", "Application", "ApplicationRoot", "App", "BasePipe", "setWidgetProps", "StoreProvider", "definition", "enumerable", "get", "obj", "hasOwnProperty", "toStringTag", "setPrototypeOf", "__proto__", "p", "assign", "s", "apply", "getOwnPropertyNames", "k", "SuppressedError", "createAction", "accountDetails", "keys", "getSortPriority", "priority", "Name", "DisplayGroupKey", "navigation", "itemSorter", "sortPriority", "displayGroups", "baseOffering", "additionalOfferings", "offeringKey", "rootOffering", "isRoot", "suplimentaryOffering", "count", "TV_BROWSE_ALL", "children", "child", "parent<PERSON><PERSON>", "INTERNATIONAL_COMBOS", "route", "EWidgetRoute", "TV_InternationalCombos", "INTERNATIONAL_ALACARTE", "TV_InternationalAlacarte", "BASE_PROGRAMMING", "TV_BASE_PRODUCT", "TV_Packages", "ALACARTE", "TV_Alacarte", "MOVIE", "TV_MoviesSeries", "ADD_ON", "TV_Addons", "INTERNATIONAL", "TV_International", "TV_Browse", "parentDisplayGroup", "newCatalog", "applyDiff", "isCurrent", "isDisabled", "isSelectable", "isSelected", "isAlreadyIncludedIn", "offeringAction", "promotionDetails", "refresh", "CommonFeatures", "languages", "genres", "base", "Injectable", "Config", "ajaxClient", "config", "AjaxServices", "Client", "BaseClient", "Actions", "client", "widgetState", "EWidgetStatus", "INIT", "combineEpics", "requestCatalogEpic", "action$", "pipe", "ofType", "toString", "UPDATING", "mergeMap", "of", "Utils", "appendRefreshOnce", "getURLByFlowType", "EFlowType", "TV", "api", "catalogAPI", "ADDTV", "addCatalogAPI", "BUNDLE", "bundleCatalogAPI", "FilterRestrictionObservable", "data", "RENDERED", "catchError", "Models", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CatalogEpics", "toggleSelectionEpic", "finalizeRestrictionEpic", "state$", "payload", "action", "EWidgetName", "PREVIEW", "ErrorHandlerObservable", "productOfferingDetail", "broadcastUpdate", "setProductConfigurationTotal", "OrderingEpics", "requestDataEpic", "type", "serviceAccountAPI", "UserAccountEpics", "pageLoadedEpic", "pageSubmitEpic", "store", "omniture", "Omniture", "useOmniture", "callPayload", "s_oSS1", "s_oSS2", "s_oSS3", "s_oPGN", "s_oAPT", "getFlowType", "actionId", "actionresult", "trackFragment", "trackAction", "s_oBTN", "s_oPRD", "prd", "category", "sku", "quantity", "price", "promo", "OmnitureEpics", "omnitureEpics", "userAccountEpic", "catalogEpics", "orderingEpics", "onWidgetStatusEpic", "updateContext", "applicationState", "Epics", "instance", "Instance", "ServiceLocator", "getService", "CommonServices", "locale", "epics", "localization", "combineReducers", "Reducers", "WidgetBaseLifecycle", "WidgetLightboxes", "WidgetRestrictions", "handleActions", "navStatus", "ModalEpics", "RestricitonsEpics", "LifecycleEpics", "Store", "Components", "imagePath", "shortDescription", "longDescription", "channelNumber", "culture", "modalId", "on<PERSON><PERSON><PERSON>", "onShown", "s_oPRM", "title", "FormattedMessage", "values", "className", "style", "width", "height", "alt", "dangerouslySetInnerHTML", "__html", "connect", "lightboxData", "dispatch", "closeLightbox", "regularPrice", "when", "abs", "discountDuration", "FormattedDate", "format", "timeZone", "expiryDate", "isNaN", "monthly", "props", "onSelect", "additionalCahnnels", "onClick", "checked", "handleSelection", "offer", "setSelection", "parents", "defaultSelection", "onContinueClick", "selection", "txt", "offerId", "elId", "visible", "$triggerEl", "$", "show", "bind", "_hide", "hide", "_onTooltipClick", "tooltip", "trigger", "triggerEl", "addEventListener", "getElementById", "querySelector", "body", "_delayed", "window", "setTimeout", "requestAnimationFrame", "tooltipEl", "rects", "y", "bottom", "left", "x", "rectContains", "rectsContain", "classList", "contains", "clearTimeout", "onTooltipClick", "removeEventListener", "TooltipBody", "connectCtrl", "tooltipId", "floor", "ctrl", "PopoverCtrl", "substr", "tabIndex", "role", "popoverCtrl", "multipleWaysToAdd", "onActionClick", "onInfoClick", "onMultipleWaysToAdd", "isMultipleWaysToAdd", "handleInfoClick", "preventDefault", "stopPropagation", "FormattedNumber", "htmlFor", "disabled", "openLightbox", "lightboxId", "relativeId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expanded", "Expand", "intl", "useIntl", "s_oEPN", "formatMessage", "Channel", "label", "content", "toggleState", "FormattedHTMLMessage", "useDispatch", "packages", "combo", "pageName", "COMBO", "keyCode", "formatter", "progress", "setProgress", "_list", "Filter", "filters", "toggleTray", "_Filter", "maxHeight", "onSubmit", "onReset", "onKeyDown", "allowMultipleWaysToAdd", "allowSelection", "forceSelectable", "showHeader", "showFilters", "expaned", "<PERSON><PERSON><PERSON><PERSON>", "_channels", "filtered", "test", "g", "testA", "testB", "localeCompare", "numeric", "sensitivity", "useChannelsFilter", "WidgetContext", "filteredChannels", "onKeyUp", "placeholder", "totalPrice", "onRemoveChannel", "flexDisplay", "total", "callsign", "floater$", "floater", "setIsFloating", "channelCount", "groupName", "scrollSpy", "international", "floaterData", "langauge", "combos", "_combos", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "option", "backgroundImage", "backgroundPosition", "backgroundRepeat", "backgroundSize", "comboLanguages", "channelLanguages", "location", "useLocation", "Switch", "Route", "path", "ln", "Redirect", "redirect", "query", "searchTerm", "s_oSRT", "history", "pathname", "TV_Search", "encodeURIComponent", "setSearchTerm", "setNavigating", "setSuggestions", "handleNavAway", "useHistory", "suggestions", "isNavigating", "detectClick", "closest", "getSearchSuggestions", "handleInput", "onFocus", "suggestion", "Link", "offeringId", "isActive", "subMenu", "subTotalPrice", "Search", "active", "useRouteMatch", "isSingle", "Toggle", "Package", "PACKAGE", "replace", "frag", "pair", "decodeURIComponent", "parseQueryString", "results", "setResults", "useSelector", "useSearch", "no_search", "search_search", "RegularPrice", "PromotionDetails", "ChannelCount", "collapseIcon", "href", "display", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "closeNav", "scrollTo", "Navigation", "exact", "Packages", "MoviesSeries", "Alaca<PERSON>", "International", "Addons", "Browser", "MultipleWays", "componentDidCatch", "err", "onErrorEncountered", "componentWillMount", "baseRoute", "constructPageRoute", "componentDidMount", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basename", "arg", "<PERSON><PERSON>", "Subscriptions", "onContinue", "historyForward", "params", "init", "subscribe", "unsubscribe", "ContextProvider", "Widget", "namespace", "ParamsProvider", "WidgetContainer", "ViewWidget"], "sourceRoot": ""}