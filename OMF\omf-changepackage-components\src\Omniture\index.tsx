import * as React from "react";
import { OmnitureTracker } from "./Tracker";

export * from "./Tracker";

export namespace Omniture {

  export interface IOmniture {
    // Omniture specific
    s_oPGN?: string | "~" | IElementReffrence;
    s_oLNG?: string | "~" | IElementReffrence;
    s_oPRV?: string | "~" | IElementReffrence;
    s_oSS1?: string | "~" | IElementReffrence;
    s_oSS2?: string | "~" | IElementReffrence;
    s_oSS3?: string | "~" | IElementReffrence;
    s_oLGS?: boolean;
    s_oSID?: string | "~" | IElementReffrence;
    s_oPTE?: boolean;
    s_oLOB?: string | "~" | IElementReffrence;
    s_oACT?: string | "~" | IElementReffrence;
    s_oMOT?: string | "~" | IElementReffrence;
    s_oBUP?: string | "~" | IElementReffrence;
    s_oMOID?: string | "~" | IElementReffrence;
    s_oIID?: string | "~" | IElementReffrence;
    s_oRID?: string | "~" | IElementReffrence;
    s_oESTD?: string | "~" | IElementReffrence;
    s_oESTT?: string | "~" | IElementReffrence;
    s_oAPT?: "~" | IApplicationState;
    s_oPRM?: string | "~" | IElementReffrence;
    s_oLBC?: string | "~" | IElementReffrence;
    s_oPLE?: "*" | IMessage | IElementReffrence | Array<IMessage | IElementReffrence>;
    s_oARS?: "*" | string | IElementReffrence | Array<string | IElementReffrence>;
    s_oERR_CLASS?: "*" | IErrorClass | IElementReffrence | Array<string | IErrorClass | IElementReffrence>;
    s_oERR_DESC?: "*" | IErrorDescription | IElementReffrence | Array<string | IErrorDescription | IElementReffrence>;
    s_oAJC?: boolean;
    // combination of s_oARS + s_oERR_CLASS + s_oERR_DESC + s_oPLE +? s_oAJC
    // s_oERR?: "*" | IError | Array<IError | IElementReffrence>;
    // --
    s_oBTN?: string | IElementReffrence;
    s_oEPN?: string | IElementReffrence;
    s_oCOSL?: string | IElementReffrence;
    s_oPRD?: "*" | IProduct | Array<IProduct>;
    s_oPID?: string | IElementReffrence;
    s_oSRT?: string | IElementReffrence;
  }
  /**
     * Omniture component props
     * @export
     * @interface IProps
     * @namespace Omniture
     */
  export interface IProps extends IOmniture {
    id: string;
    // Engine flags
    rel?: "page" | "fragment" | "component";
    ready?: boolean;
    enabled?: boolean;
    once?: boolean;
    // collect?: Array<string>;
    // parent?: "";
    timestamp?: string;
  }

  export interface IElementReffrence {
    // DOM element ID
    ref: string;
    // Max characters
    maxlength?: number;
    // Regular expression to apply
    regex?: string;
  }

  export function Ref(elementId: string): IElementReffrence {
    return {
      ref: elementId
    };
  }

  export interface IApplicationState {
    actionId?: string | number;
    actionresult?: string | number;
    applicationState?: string | number;
  }

  export interface IMessage {
    type: EMessageType;
    content: string | IElementReffrence;
    errorCodes?: Array<string>;
  }

  export interface IProduct {
    category: string;
    name: string;
    sku: string;
    quantity: string;
    price: string;
    promo: string;
  }

  export enum EMessageType {
    Confirmation = "C",
    Information = "I",
    Warning = "W",
    Error = "E",
  }

  export enum EErrorType {
    Technical = "T",
    Business = "B",
    Validation = "V"
  }

  export enum EApplicationLayer {
    Browser = "BR",
    Frontend = "FE",
    ESB = "ESB",
    Backend = "BE",
    Servicegrid = "SG",
    Cache = "C"
  }

  export interface IErrorDescription {
    code: string | number;
    description: string | IElementReffrence;
  }

  export interface IErrorClass {
    code: string | number;
    type: EErrorType;
    layer: EApplicationLayer;
  }

  export interface IError extends IErrorClass, IErrorDescription {
    ajax?: boolean;
    lightbox?: {
      title?: string | IElementReffrence,
      content?: string | IElementReffrence
    };
  }

  /**
     * Omniture wrapper component
     * @param {IProps} props
     * @returns React.FC
     * @namespace Omniture
     */
  export const Component: React.FC<React.PropsWithChildren<IOmniture>> = (props) => {
    const {
      children,
      ...omniture
    } = props;
    const _childern = React.Children.toArray(children);
    if (_childern.length > 1) throw "Omniture component may not have more then one child";
    return _childern.length > 0 ? <>
      {
        _childern.map((el: any) => React.cloneElement(el, { ...el.props, "data-omni": btoa(JSON.stringify(omniture)) }))
      }
    </> : <span data-omni={btoa(JSON.stringify(omniture))} />;
  };

  const NOOPE = () => { };
  const _stub = {
    trackPage: NOOPE,
    trackFragment: NOOPE,
    trackAction: NOOPE,
    trackError: NOOPE,
    trackFailure: NOOPE,
    updateContext: NOOPE,
  };

  export function useOmniture(): OmnitureTracker {
    return ((window as any)["OmnitureTracker"] && (window as any)["OmnitureTracker"].getInstance()) || _stub;
  }
}
