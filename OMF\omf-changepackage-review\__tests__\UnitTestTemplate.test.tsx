import * as React from "react";
import { render, unmountComponentAtNode } from "react-dom";
import { act } from "react-dom/test-utils";
import { Provider } from "react-redux";
import configureStore from 'redux-mock-store';
import { Confirmation } from "../src/views/confirmation";
import { Messages } from "../src/views/messages";
import { Legal } from "../src/views/legal";
import { Appointment } from "../src/views/appointment";
import { fireEvent } from "@testing-library/react";
import { EReviewMode, ContextProvider, Actions } from "omf-changepackage-components";

let container: any = null,
  store: any = null,
  push = jest.fn();

const mockStore = configureStore([]);

jest.mock('react-intl', () => ({
  FormattedMessage: () => (<div></div>),
  FormattedHTMLMessage: () => (<div></div>)
}));

jest.mock('react-router-dom', () => ({
  useHistory: () => ({
    push: push,
  }),
}));

beforeEach(() => {
  container = document.createElement("div");
  document.body.appendChild(container);
  store = mockStore({
    confirmation: {
      confirmationNumber: "abcdefg",
      orderDate: "2020-01-01"
    },
    messages: [{
      messageBody: "Eugene is here", messageCode: 1
    }, {
      messageBody: "Avtar is gone", messageCode: 2
    }],
    acceptedTerms: ["TERM_1", "TERM_2"],
    appointment: {
      "appointmentDetails": {
        "availableDates": null,
        "preferredDate": {
          "date": "2019-12-31T00:00:00",
          "timeSlots": [
            {
              "intervalType": "AllDay",
              "timeInterval": {
                "startTime": 800,
                "endTime": 1700
              },
              "isAvailable": true,
              "isSelected": true
            }
          ]
        },
        "duration": null,
        "installationAddress": {
          "address1": "5115 Creekbank Road",
          "address2": "Address2",
          "city": "Mississauga",
          "province": "ON",
          "postalCode": "A1B 2C3"
        },
        "contactInformation": {
          "preferredContactMethod": "Email",
          "primaryPhone": {
            "phoneNumber": null,
            "phoneExtension": null
          },
          "additionalPhone": null,
          "textMessage": null,
          "email": null
        },
        "additionalDetails": {
          "apartment": "10",
          "entryCode": null,
          "specialInstructions": null,
          "superintendantName": null,
          "superintendantPhone": null,
          "informedSuperintendant": false
        },
        "isInstallationRequired": false
      },
      "customerInformation": {
        "name": "Yong Zhao",
        "email": "<EMAIL>",
        "address": {
          "address1": "175",
          "address2": "DEGUIRE",
          "city": "ST LAURENT",
          "province": "QC",
          "postalCode": "H4N1N7"
        }
      }
    }
  });
  store.dispatch = jest.fn();

  jest.spyOn(Actions, "broadcastUpdate").mockImplementation(() => ({
    type: "PIPE_SEND_UPDATE",
    payload: "Route back to appointment widget" as any
  }));



});

afterEach(() => {
  unmountComponentAtNode(container);
  container.remove();
  container = null;
  store = null;
});

test("Renders Messages component correctly", () => {

  act(() => {
    render(
      <Provider store={store} >
        <Messages />
      </Provider>,
      container);
  });

  const p = container.querySelectorAll("p.txtGray4A");

  expect(p.length).toBe(2);

  expect(p[1].textContent).toBe("Avtar is gone");

});

test("Confirmation component rendered with confirmation number and date", () => {
  act(() => {
    render(
      <Provider store={store} >
        <Confirmation />
      </Provider>,
      container);
  });

  expect(container.querySelector(".notification-message").textContent).toContain("Confirmation number:abcdefgOrder date:2020-01-01");
});


test("Terms & Conditions component rendered correctly", () => {
  act(() => {
    render(
      <Provider store={store} >
        <Legal />
      </Provider>,
      container);
  });

  expect(container.querySelector("#terms-conditions").style.height).toBe("150px");

  act(() => {
    fireEvent.click(container.querySelector("button.expand-toggle"));
  });

  expect(container.querySelector("#terms-conditions").style.height).toBe("350px");

  expect(container.querySelector("input[value='TERM_1'").checked).toBe(true);
  expect(container.querySelector("input[value='TERM_1'").checked).toBe(true);

});

test("Appointment component rendered", () => {
  act(() => {
    render(
      <ContextProvider value={{ mode: EReviewMode.Review } as any}>
        <Provider store={store} >
          <Appointment />
        </Provider>
      </ContextProvider>,
      container);
  });

  // two edit buttons rendered
  expect(container.querySelectorAll("button").length).toBe(2);

  // customer name is rendered
  expect(container.querySelectorAll(".flexRow")[0].textContent).toContain("Yong Zhao");

  act(() => {
    fireEvent.click(container.querySelectorAll("button")[0]);
  });
  // simulate to click on the first edit button to dispatch a route change action
  expect(store.dispatch).toHaveBeenCalledWith({ payload: "Route back to appointment widget", type: "PIPE_SEND_UPDATE" });

});