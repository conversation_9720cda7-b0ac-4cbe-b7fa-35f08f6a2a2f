// import { Utils } from "omf-changepackage-components";
import * as React from "react";
import { ITVChannel } from "../../models";
import { toCharacteristicsJSON, translateStringList } from "../../utils/Characteristics";
import { Localization } from "../../Localization";

const MAX_DESCRIPTION_LENGTH = 80;
const DELAY = 500;

function formatDescription(txt: string) {
  if (txt.length > MAX_DESCRIPTION_LENGTH)
    return txt.substr(0, MAX_DESCRIPTION_LENGTH) + "...";
  else return txt;
}

function rectContains(rect: ClientRect, e: MouseEvent) {
  return (rect.top - 10 < e.y && rect.bottom + 10 > e.y) &&
    (rect.left < e.x && rect.right > e.x);
}

function rectsContain(rects: Array<ClientRect>, e: MouseEvent): boolean {
  return rects.filter(e => <PERSON>ole<PERSON>(e)).map(rect => rectContains(rect, e)).find(e => e) || false;
}

let _popoverinst: { [key: string]: PopoverCtrl | null } = {};

export function CleanupPopoverStack() {
  for (const key in _popoverinst) {
    if (_popoverinst[key]) (_popoverinst[key] as PopoverCtrl).destroy();
  }
  // Destroy all that's left on screen
  Array.from(document.querySelectorAll(".channel-tooltip")).forEach(el => el.remove());
  _popoverinst = {};
}
class PopoverCtrl {
  elId: string;
  visible: boolean = false;
  $triggerEl: any;
  _delayed: any;
  onTooltipClick: (e: any) => void;
  get triggerEl(): HTMLDivElement {
    return (document.getElementById(this.elId)) as HTMLDivElement;
  }
  get tooltipEl(): HTMLDivElement {
    return (document.querySelector("." + this.elId + "_inst")) as HTMLDivElement;
  }
  constructor(elId: string) {
    if (_popoverinst[elId]) (_popoverinst[elId] as any).destroy();
    this.elId = elId;
    this.$triggerEl = $("#" + elId);
    this.show = this.show.bind(this);
    this._hide = this._hide.bind(this);
    this.hide = this.hide.bind(this);
    this.destroy = this.destroy.bind(this);
    this._onTooltipClick = this._onTooltipClick.bind(this);
    this.$triggerEl.tooltip({
      trigger: "manual"
    });
    this.triggerEl
      .addEventListener("mouseenter", this.show);
    // this.triggerEl
    //   .addEventListener("mouseleave", this._cleanup);
    _popoverinst[elId] = this;
  }
  show() {
    document.body.addEventListener("mousemove", this._hide);
    if (!this.visible && !this._delayed) {
      window.addEventListener("scroll", this._hide);
      this._delayed = setTimeout(
        () => {
          this._delayed = null;
          this.visible = true;
          this.$triggerEl.tooltip("show");
          requestAnimationFrame(
            () => this.tooltipEl.addEventListener("click", this._onTooltipClick)
          );
        }, DELAY
      );
    }
  }
  _hide(e: MouseEvent) {
    const target = e.target as HTMLDivElement;
    if (!rectsContain([
      this.triggerEl && this.triggerEl.getBoundingClientRect(),
      this.tooltipEl && this.tooltipEl.getBoundingClientRect()
    ], e
    ) || target.classList.contains("tooltip-interactive")) {
      this.hide();
      clearTimeout(this._delayed);
      this._delayed = null;
    }
  }
  // _cleanup() {
  //   clearTimeout(this._delayed);
  //   this._delayed = null;
  // }
  _onTooltipClick(e: any) {
    this.onTooltipClick(e);
  }
  hide() {
    if (this.visible) {
      this.visible = false;
      this.tooltipEl.removeEventListener("click", this._onTooltipClick);
      document.body.removeEventListener("mousemove", this._hide);
      window.removeEventListener("scroll", this._hide);
      this.$triggerEl.tooltip("hide");
    }
  }
  destroy() {
    document.body.removeEventListener("mousemove", this._hide);
    window.removeEventListener("scroll", this._hide);
    this.triggerEl
      .removeEventListener("mouseenter", this.show);
    // this.triggerEl
    //   .removeEventListener("mouseleave", this._cleanup);
    _popoverinst[this.elId] = null;
  }
}

interface ComponentProps extends ITVChannel {
  children: any;
  className: string;
  connectCtrl: (ctr: PopoverCtrl) => void;
}

declare const $: any;

export const Tooltip: React.FC<ComponentProps> = (props: ComponentProps) => {
  const {
    id,
    name,
    channelNumber,
    imagePath,
    characteristics,
    shortDescription,
    children,
    className,
    connectCtrl
  } = props;
  const tooltipId = React.useMemo(() => `tooltip${id}${Math.floor(Math.random() * 100)}`, [id]);

  React.useEffect(() => {
    const ctrl = new PopoverCtrl(tooltipId);
    connectCtrl(ctrl);
    return () => ctrl.destroy();
  }, []);

  _popoverinst[tooltipId] &&
    connectCtrl(_popoverinst[tooltipId] as any);

  const { culture, genre, language } = toCharacteristicsJSON(characteristics);
  const TooltipBody = `<div style="display:flex; flex-direction: row;">
      <div style="flex-shrink:0; padding-right:20px">
        <img width="75" class="img-responsive channel-border" src="${imagePath}" alt="${name}" />
      </div>
      <div>
        <div class="txtVirginBlue txtSize18 noMargin">${channelNumber}</div>
        <div class="txtBlack txtSize18 noMargin">${[translateStringList(genre), translateStringList(culture), translateStringList(language)].filter(i => Boolean(i)).join(" / ")}</div>
        <div class="tooltip-description txtSize14" style="color:#333">${formatDescription(shortDescription)}</div>
        <div class="spacer15"></div>
        <button id="viewDetails${id}" class="txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal txtVirginBlue">${Localization.getLocalizedString("View details")}</button>
      </div></div>`;

  return (
    <div className={className}>
      <div className="floatL w-100">
        <div id={tooltipId}
          className="tooltip-interactive w-100 alignIconWithText pointer"
          tabIndex={0}
          role="tooltip"
          data-delay="100"
          data-html="true"
          data-placement="top"
          data-container="body"
          data-template={`<div class="tooltip channel-tooltip top in ${tooltipId}_inst" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>`}
          data-title={TooltipBody}>
          {children}
        </div>
      </div>
    </div>
  );
};
