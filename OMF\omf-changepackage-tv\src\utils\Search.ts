import * as React from "react";
import * as JsSearch from "js-search";
import { useSelector } from "react-redux";
import { ValueOf } from "omf-changepackage-components";
import { ITVChannel } from "../models";

let search: JsSearch.Search;

export function initSearch(channels: Array<ITVChannel>) {
  const filterChannels = channels.map(channel => {
    const result = channel.characteristics.find(item => item.name === "callSign");
    return {
      ...channel,
      callSign: result ? result.value : ""
    };
  });
  search = new JsSearch.Search("id");
  search.addIndex("name");
  search.addIndex("channelNumber");
  search.addIndex("callSign");
  // search.indexStrategy = new JsSearch.AllSubstringsIndexStrategy();
  // search.tokenizer = new JsSearch.StopWordsTokenizer(search.tokenizer);
  search.addDocuments(filterChannels);
}

const MAX_QUERY_LENGTH = 0;

export function getSearchSuggestions(value: string): Array<ITVChannel> {
  if (!search) return [];
  const result = value.length > MAX_QUERY_LENGTH ? search.search(value) : [];
  return result as Array<ITVChannel>;
}

export function getSearchResult(value: string): Array<ITVChannel> {
  if (!search) return [];
  const result = value.length > MAX_QUERY_LENGTH ? search.search(value) : [];
  return result as Array<ITVChannel>;
}

export function useSearch(query: string): Array<ITVChannel> | null {
  const [results, setResults] = React.useState<Array<ITVChannel> | null>(null);
  const channels = useSelector(state => ValueOf(state, "catalog.channels", []));
  React.useEffect(() => {
    setResults(getSearchResult(query));
  }, [query, channels]);
  return results;
}
