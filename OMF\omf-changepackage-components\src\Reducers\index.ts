import { getWidgetBaseLifecycle } from "./Lifecycle";
import { getWidgetRestrictions } from "./Restrictions";
import { getWidgetLightboxes } from "./Modals";

export namespace Reducers {
  export const WidgetBaseLifecycle: typeof getWidgetBaseLifecycle = getWidgetBaseLifecycle;
  export const WidgetRestrictions: typeof getWidgetRestrictions = getWidgetRestrictions;
  export const WidgetLightboxes: typeof getWidgetLightboxes = getWidgetLightboxes;
}
