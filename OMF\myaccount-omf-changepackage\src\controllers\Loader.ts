import {$} from "../lib/jQuery";

const LoaderID = "#responsiveLoader";

/**
 * Show/hide widget loader
 * depending on current loading status
 * @export
 * @class Loader
 */
export class Loader {
  loader: HTMLDivElement;
  visible: boolean = false;
  sources: Array<string> = [];
  constructor() {
    this.toggle(true);
  }
  /**
     * Causes loader on page to be shown/hidden
     * @param {boolean} [state] Loader visibility status
     * @param {string} [source] Widget name, where the request is originaes from
     * @memberof Loader
     */
  toggle(state?: boolean | null, source?: string) {
    const _visible: boolean = state === null ? false : state === undefined ? (!this.visible) : state;
    const _source = source || "";
    if (state !== null && Boolean(_source)) {
      const sourceAt: number = this.sources.indexOf(_source);
      if (sourceAt > -1 && !_visible) {
        this.sources.splice(sourceAt, 1);
      } else if (sourceAt === -1 && _visible) {
        this.sources.push(_source);
      }
      this.visible = this.sources.length > 0;
    } else {
      this.visible = _visible;
      this.sources = [];
    }
    if (this.visible) {
      $(LoaderID).show();
    } else {
      $(LoaderID).hide();
    }
  }
}
