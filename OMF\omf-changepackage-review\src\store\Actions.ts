import { createAction, Action } from "redux-actions";
import { IOrderSummary, IOrderAPIResponse, IAppointmentDetails, IOrderConfirmation } from "../models";
import { orderDetailsMutatorFn, orderMessagesMutatorFn, appointmentDetailsMutatorFn, acceptedTermsMutatorFn, orderConfirmationMutatorFn } from "../mutators";
import { Volt } from "omf-changepackage-components";

// Widget actions
export const getOrderSummary = createAction("GET_ORDER_SUMMARY");
export const getOrderDetails = createAction("GET_ORDER_DETAILS");
export const setReviewMessages = createAction<Array<Volt.IMessage>>("SET_REVIEW_MESSAGES", orderMessagesMutatorFn as any) as (respones: Volt.IAPIResponse) => Action<Array<Volt.IMessage>>;
export const setAcceptedTerms = createAction<any>("SET_ACCEPTED_TERMS", acceptedTermsMutatorFn as any) as (terms: any, term: string) => Action<any>;
export const setOrderSummary = createAction<IOrderSummary>("SET_ORDER_SUMMARY", orderDetailsMutatorFn as any) as (respones: IOrderAPIResponse) => Action<IOrderSummary>;
export const setAppointmentDetails = createAction<IAppointmentDetails>("SET_ORDER_APPOINTMENT_DETAILS", appointmentDetailsMutatorFn as any) as (respones: Volt.IAPIResponse) => Action<IAppointmentDetails>;
export const setOrderConfirmation = createAction<IOrderConfirmation>("SET_ORDER_CONFIRMATION", orderConfirmationMutatorFn as any) as (respones: Volt.IAPIResponse) => Action<IOrderConfirmation>;
export const submitOrder = createAction("SUBMIT_COMPLETE_ORDER");
export const downloadPDF = createAction("DOWNLOAD_PDF");
export const modifyChannelSelection = createAction("MODIFY_CHANNEL_SELECTION");
// Piped actions
