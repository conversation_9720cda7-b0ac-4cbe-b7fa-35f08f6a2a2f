import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { of , filter, mergeMap } from "rxjs";

import { EWidgetStatus, Actions } from "omf-changepackage-components";
import { ReviewEpics } from "./Epics/Review";
import { OrderEpics } from "./Epics/Order";
import { OmnitureEpics } from "./Epics/Omniture";
import { IStoreState } from "../models";

const {
  setWidgetStatus,
} = Actions;

// const { concat } = ActionsObservable;

@Injectable
export class Epics {
  constructor(
    public reviewEpics: ReviewEpics,
    public orderEpics: OrderEpics,
    public omnitureEpics: OmnitureEpics
  ) { }

  combineEpics() {
    return combineEpics(
      this.onWidgetStatusEpic,
    );
  }

  private get onWidgetStatusEpic(): GeneralEpic {
    return (action$) =>
      action$.pipe(
        ofType(setWidgetStatus.toString()),
        filter((action: ReduxActions.Action<EWidgetStatus>) => action.payload === EWidgetStatus.INIT),
        mergeMap(() => of())
      );
  }

}

type GeneralEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, IStoreState>;
