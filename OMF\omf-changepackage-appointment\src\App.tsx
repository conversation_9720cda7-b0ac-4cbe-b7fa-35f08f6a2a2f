import { Components } from "omf-changepackage-components";
import * as React from "react";
import { FormContext, useForm } from "react-hook-form";
import { Application } from "./views";

const {
  ApplicationRoot
} = Components;

export const App = (props: any) => {
  const methods = useForm();
  return (
    <ApplicationRoot>
      <FormContext {...methods}> { /** Create context for react-hook-form */}
        <Application />
      </FormContext>
    </ApplicationRoot>
  );
};
