import { Actions, Components, E<PERSON>lowType, EWidgetRoute, ValueOf, Utils, Volt } from "omf-changepackage-components";
import * as React from "react";
import * as ReactDOM from "react-dom";
// import { useRouteMatch } from "react-router-dom";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ISummary } from "../../models";
import { ModalId as ResetModalId } from "../modals/ApplicationReset";
import { ModalId as SummaryModalId } from "../modals/Summary";
import TVSummaryPortal from "./TvSummaryPortal";

const {
  Visible,
  Currency,
} = Components;

const DisabledContinue = () => {
  const page = (Utils.getPageRoute() || "").replace("/", "").toUpperCase();
  const messageid = `TOOLTIP_${page}`;
  const [hover, toggleHover] = React.useState(false);
  return <button id="tierContinue"
    onClick={() => toggleHover(!hover)}
    onKeyUp={() => toggleHover(!hover)}
    onMouseOver={() => toggleHover(true)}
    onMouseOut={() => toggleHover(false)}
    className="btn btn-primary fill-xs tooltip-interactive relative alignIconWithText disabled"
    aria-disabled="true">
    <FormattedMessage id="Continue" />
    <Visible when={hover}>
      <FormattedMessage id={messageid}>
        {
          (txt: any) => Boolean(txt) && txt !== messageid ?
            <div className="tooltip fade bs-tooltip-top show" role="tooltip" id="tooltip504192" style={{ position: "absolute", width: "240px", top: "-110px", left: "50%", transform: "translateX(-50%)" }}>
              <div className="arrow" style={{ left: "50%" }} />
              <div className="tooltip-inner" style={{ width: "100%" }}>
                <div className="flexRow bgWhite txtBlack">
                  <div className="olt-icon icon-warning txtSize22 margin-10-right">
                    <span className="volt-icon path1 yellowIcon"></span>
                    <span className="volt-icon path2"></span>
                  </div>
                  <div className="margin-5-top"><FormattedMessage id={messageid} /></div>
                </div>
              </div>
            </div> : <span></span>
        }
      </FormattedMessage>
    </Visible>
  </button>;
};

interface IPriceBlock {
  label: any;
  price: Volt.IPriceDetail;
  regularPrice?: Volt.IPriceDetail;
  className?: string;
}

const PriceBlock: React.FC<IPriceBlock> = ({
  label,
  price,
  regularPrice,
  className = ""
}) => <div className={"virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify " + className}>
  <p className="noMargin txtSize12 txtWhite">{label}</p>
  <div>
    <Visible when={ValueOf(regularPrice, undefined, false)}>
      <p className="noMargin txtSize12 txtWhite"><FormattedMessage id="Price after credit" /></p>
    </Visible>
    <span className="virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite">
      <Currency value={ValueOf(price, "price", 0)} monthly={true} prefixClassName={"txtSize16 txtUppercase"} fractionClassName={"txtSize16"} />
    </span>
    <Visible when={ValueOf(regularPrice, undefined, false)}>
      <p className="noMargin txtSize12 txtWhite">
        <FormattedMessage id="Current price" values={ValueOf(regularPrice, undefined)} />
      </p>
    </Visible>
  </div>
</div>;

interface IComponentConnectedProps {
  summary: ISummary;
  isContinueEnabled: boolean;
}

interface IComponentProps {
  flowType: EFlowType;
  location: any;
}

interface IComponentDispatches {
  onSummaryClick: (data: any) => void;
  onCancelClick: (data: any) => void;
  onCategoriesClick: () => void;
  onContinueClick: () => void;
  handleNav: () => void;
}

const Component: React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches> = ({
  summary,
  flowType,
  location,
  isContinueEnabled,
  onSummaryClick,
  onCancelClick,
  onContinueClick,
  onCategoriesClick,
  handleNav
}) => {
  // const isTVStep = Boolean(useRouteMatch(EWidgetRoute.TV));
  const isTVStep = window.location.href.indexOf(EWidgetRoute.TV) > -1;
  const isVisible = !(location.pathname.indexOf(EWidgetRoute.REVIEW) > 0 ||
    location.pathname.indexOf(EWidgetRoute.CONFIRMATION) > 0);
  const tvSummaryContainer = document.getElementById("tv-sedebar-summary-portal") as HTMLDivElement;

  return <Visible when={isVisible}><nav>
    <div className="virgin-dockbar col1 scrollTop">
      <div className="nopad bgBlack accss-focus-outline-override-black-bg" style={{opacity: "92%"}}>
        <div className="virgin-dockbar-panel flexRow block-xs container container-fluid no-pad-xs">
          <div className="flexRow block-xs">
            <Visible when={ValueOf(summary, "Internet", false)}>
              <Visible when={ValueOf(summary, "?Internet.currentPrice")}>
                <PriceBlock label={<FormattedMessage id="Current" />}
                  price={ValueOf(summary, "?Internet.currentPrice")}
                  regularPrice={ValueOf(summary, "?Internet.regularCurrentPrice")} />
              </Visible>
              <Visible when={ValueOf(summary, "?Internet.newPrice")}>
                <PriceBlock label={<FormattedMessage id="NewInternet" />}
                  className="bgOrange"
                  price={ValueOf(summary, "?Internet.newPrice")}
                  regularPrice={ValueOf(summary, "?Internet.regularNewPrice")} />
              </Visible>
            </Visible>
            <Visible when={ValueOf(summary, "TV", false)}>
              <Visible when={ValueOf(summary, "?TV.currentPrice")}>
                {/* <div className="virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify">
                  <p className="noMargin txtSize12 txtWhite"><FormattedMessage id="CurrentTV" /></p>
                  <div>
                    <Visible when={ValueOf(summary, "?TV.regularCurrentPrice", false)}>
                      <p className="noMargin txtSize12 txtWhite"><FormattedMessage id="Price after credit" /></p>
                    </Visible>
                    <span className="virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite">
                      <Currency value={ValueOf(summary, "?TV.currentPrice")} monthly={true} prefixClassName={"txtSize16 txtUppercase"} fractionClassName={"txtSize16"} />
                    </span>
                    <Visible when={ValueOf(summary, "?TV.regularCurrentPrice")}>
                      <p className="noMargin txtSize12 txtWhite">
                        <FormattedMessage id="Current price" values={ValueOf(summary, "?TV.regularCurrentPrice")} />
                      </p>
                    </Visible>
                  </div>
                </div> */}
                <PriceBlock label={<FormattedMessage id="CurrentTV" />}
                  price={ValueOf(summary, "?TV.currentPrice")}
                  regularPrice={ValueOf(summary, "?TV.regularCurrentPrice")} />
              </Visible>
              <Visible when={ValueOf(summary, "?TV.newPrice")}>
                <PriceBlock label={<FormattedMessage id="NewTV" />}
                  className="bgOrange"
                  price={ValueOf(summary, "?TV.newPrice")}
                  regularPrice={ValueOf(summary, "?TV.regularNewPrice")} />
              </Visible>
            </Visible>
          </div>
          <Visible when={ValueOf(summary, "summaryAction")}>
            <div className="virgin-dockbar-row flexCol-xs preview-btn">
              <button id="orderReview" onClick={() => onSummaryClick("orderReview")} className="btn btn-link txtUnderline txtWhite txtSize12 pad-10-left accss-changeplan-preview"><FormattedMessage id="Preview" /></button>
            </div>
          </Visible>
          <Visible when={ValueOf(summary, "resetAction")}>
            <div className="flexStatic flexCol-xs preview-btn">
              <button id="orderCancel" onClick={() => onCancelClick("orderCancel")} className="btn btn-link txtWhite txtUnderline txtSize12 dockbar-cancel"><FormattedMessage id="Reset" /></button>
            </div>
          </Visible>
          <div className="spacer10 visible-m" aria-hidden="true" />
          <div className="flexGrow" />
          <div className="flex dockbar-buttons continue-button fullWidth-xs align-items-center flexCol-xs bgBlack">
            <Visible when={isTVStep}>
              <div className="virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs d-md-none">
                <button id="TV_CATEGORIES" className="btn btn-secondary-inverted p-2" onClick={handleNav}>
                  <FormattedMessage id="TV_CATEGORIES" />
                </button>
              </div>
            </Visible>
            <div className="virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs">
              <Visible when={isContinueEnabled} placeholder={<DisabledContinue />}>
                <button onClick={onContinueClick}
                  id="tier_Continue" className="btn btn-primary fill-xs tooltip-interactive alignIconWithText pointer relative p-2">
                  <FormattedMessage id="Continue" />
                  <Visible when={isTVStep && ValueOf(summary, "productOfferingCount", false)}>
                    <span className="bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification">
                      <span>{ValueOf(summary, "productOfferingCount", 0)}</span>
                    </span>
                  </Visible>
                </button>
              </Visible>
            </div>
          </div>
        </div>
      </div>
    </div>
    {
      tvSummaryContainer &&
      ReactDOM.createPortal(<TVSummaryPortal summary={summary} isContinueEnabled={isContinueEnabled} onContinueClick={onContinueClick} />, tvSummaryContainer)
    }
  </nav>
  </Visible>;
};
export const SummaryPanel = connect<IComponentConnectedProps, IComponentDispatches, IComponentProps>(
  ({ summary }: IStoreState) => ({
    summary,
    isContinueEnabled: !!ValueOf(summary, "nextAction", false)
  }),
  (dispatch) => ({
    onSummaryClick: (id: any) => dispatch(Actions.openLightbox({ lightboxId: SummaryModalId, data: { relativeId: id, lightbox: SummaryModalId } })),
    onCancelClick: (id: any) => dispatch(Actions.openLightbox({ lightboxId: ResetModalId, data: { relativeId: id } })),
    onCategoriesClick: () => dispatch(Actions.broadcastUpdate(Actions.toggleTVCategoriesTray())),
    onContinueClick: () => dispatch(Actions.broadcastUpdate(Actions.onContinue())),
    handleNav: () => dispatch(Actions.broadcastUpdate(Actions.handleNav(true)))
  })
)(Component);
