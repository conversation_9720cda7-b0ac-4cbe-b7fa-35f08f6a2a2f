import { Injectable } from "bwtk";
import { Epic, combineEpics } from "redux-observable";
import { EWidgetStatus, Actions, Models, AjaxResponse, Volt, FilterRestrictionObservable, EWidgetName, Utils, EFlowType, ValueOf } from "omf-changepackage-components";
import { filter, mergeMap, catchError , concat, of, Observable } from 'rxjs';

import { Client } from "../../Client";
import {
  IStoreState
} from "../../models";
import {
  getInternetCatalog,
  setInternetCatalog,
  togglePackageSelection,
  updateInternetCatalog
} from "../Actions";
import { Config } from "../../Config";

const {
  errorOccured,
  setWidgetStatus,
  clearCachedState,
  finalizeRestriction
} = Actions;

@Injectable
export class CatalogEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.requestCatalogEpic,
      this.togglePlanSelectionEpic,
      this.finalizeRestrictionEpic
    );
  }

  private get requestCatalogEpic(): CatalogEpic {
    return (action$: Observable<InputAction>) =>
      action$.pipe(
        filter((action): action is InputAction => action.type === getInternetCatalog.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => concat(
          of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
          this.client.get<AjaxResponse<Volt.IAPIResponse>>(Utils.appendRefreshOnce(
            Utils.getURLByFlowType({
              [EFlowType.TV]: this.config.api.catalogAPI,
              [EFlowType.INTERNET]: this.config.api.catalogAPI,
              [EFlowType.BUNDLE]: this.config.api.bundleCatalogAPI
            })
          )).pipe(
            mergeMap((response: AjaxResponse<Volt.IAPIResponse>) => 
              FilterRestrictionObservable(response, [
                setInternetCatalog(response.data),
                Actions.omniPageLoaded(),
                setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)
              ])
            )
          )
        )),
        catchError((error: Response) => of(
          errorOccured(new Models.ErrorHandler("getInternetCatalog", error))
        ))
      ) as Observable<OutputAction>;
  }

  private get togglePlanSelectionEpic(): CatalogEpic {
    return (action$: Observable<InputAction>, state$) =>
      action$.pipe(
        filter((action): action is ReduxActions.Action<Volt.IHypermediaAction> => 
          action.type === togglePackageSelection.toString()
        ),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(({ payload }) =>
          concat(
            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
            this.client.action<AjaxResponse<Volt.IAPIResponse>>(payload).pipe(
              mergeMap((response: AjaxResponse<Volt.IAPIResponse>) => 
                FilterRestrictionObservable(response, [
                  updateInternetCatalog(response.data, (state$ as any).value.catalog),
                  clearCachedState([EWidgetName.PREVIEW]),
                  setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)
                ])
              )
            )
          )
        ),
        catchError((error: Response) => of(errorOccured(new Models.ErrorHandler("togglePackageSelection", error))))
      ) as Observable<OutputAction>;
  }

  private get finalizeRestrictionEpic(): CatalogEpic {
    return (action$: Observable<InputAction>, state$) =>
      action$.pipe(
        filter((action): action is ReduxActions.Action<Volt.IAPIResponse> => 
          action.type === finalizeRestriction.toString()
        ),
        filter(({ payload }) => 
          Boolean(payload) && 
          Boolean(payload.productOfferingDetail) && 
          this.widgetState !== EWidgetStatus.UPDATING
        ),
        mergeMap(({ payload }) => of(
          Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(payload, "productOfferingDetail.productConfigurationTotal"))),
          updateInternetCatalog(payload, (state$ as any).value.catalog),
          clearCachedState([EWidgetName.PREVIEW]),
          setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED)
        ))
      ) as Observable<OutputAction>;
  }
}

type InputAction = ReduxActions.Action<any>;
type OutputAction = ReduxActions.Action<any>;
type CatalogEpic = Epic<InputAction, OutputAction, void, IStoreState>;
