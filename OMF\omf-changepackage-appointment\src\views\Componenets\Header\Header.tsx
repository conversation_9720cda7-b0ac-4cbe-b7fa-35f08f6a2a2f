import * as React from "react";
import { Components } from "omf-changepackage-components";
import { Heading, HeadingTags } from "./Heading";
import { Banner, EBannerIcons } from "./Banner";
import { getMessagesList } from "../../../utils/AppointmentUtils";

interface ComponentProps {
  errors: any;
}

interface ComponentState {}

export class Header extends React.PureComponent<ComponentProps, ComponentState> {
  constructor(props: any) {
    super(props);
  }

  private headingProps = {
    tag: HeadingTags.H2,
    classNames: "txtSize28 txtSize24-xs",
    content: "INSTALLATION_PAGE_HEADING",
  };

  render() {
    return (
      <>
        <Components.Container>
          <Components.BRF3Container>
            <span className="spacer5 flex col-12"></span>
            <Heading {...this.headingProps} />
            <span className="spacer25 flex col-12 clear"></span>
          </Components.BRF3Container>
        </Components.Container>
        <Banner iconType={EBannerIcons.INFO} heading={"INSTALLATION_HEADING"} message={"INSTALLATION_MESSAGE"} />
        {Object.keys(this.props.errors).length ? (
          <Banner iconType={EBannerIcons.ERROR} heading={"ERRORS_HEADING"} messages={getMessagesList(this.props.errors)} />
        ) : null}
      </>
    );
  }
}
