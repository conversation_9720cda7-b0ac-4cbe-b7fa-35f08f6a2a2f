import { Actions, Components, ValueOf, Volt, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import { toggleSelection } from "../../store/Actions";
import { toCharacteristicsJSON, translateStringList } from "../../utils/Characteristics";
import Price from "../Components/Price";

const {
  Modal,
  Visible
} = Components;

interface IComponentProps {
  channel: ITVChannel;
  parents: Array<Volt.IProductOffering>;
  defaultSelection: Volt.IProductOffering;
}

interface IComponentDispatches {
  onContinueClick: (action: Volt.IHypermediaAction | undefined) => void;
  closeLightbox: () => void;
}

export const ModalId: string = "MULTIPLE_WAYS_TO_ADD";

interface IOptionProps extends Volt.IProductOffering {
  isSelected: boolean;
  onSelect: Function;
}

const Option: React.FC<IOptionProps> = (props) => {
  const {
    id,
    name,
    regularPrice,
    promotionDetails,
    childOfferings,
    isSelected,
    onSelect
  } = props;
  const additionalCahnnels = ValueOf(childOfferings, "length", 0) - 1;
  return <div className={`boxContainer borderGrayLight6 pad-15 margin-20-bottom ${isSelected ? "borderBlack" : ""}`}>
    <label id={`${id}selectCTA`} className="graphical_ctrl pointer ctrl_radioBtn txtSize15" onClick={() => onSelect(props)}>
      <input type="radio" name="offering" checked={isSelected} />
      <span className="ctrl_element"></span>
      <span className="radio-text">{name}</span>
      <Visible when={additionalCahnnels > 0}>
        <p className="no-margin"><FormattedMessage id="Get additional channels" values={{ additionalCahnnels }} /></p>
      </Visible>
      <Price regularPrice={regularPrice} promotionDetails={promotionDetails} />
    </label>
  </div>;
};

const NoThanksOffer = {
  id: "NO",
  productOfferingType: Volt.EProductOfferingType.NONE
} as Volt.IProductOffering;

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  channel,
  parents,
  defaultSelection,
  closeLightbox,
  onContinueClick
}) => {
  const [selection, setSelection] = React.useState<Volt.IProductOffering>(NoThanksOffer);

  React.useEffect(() => { setSelection(defaultSelection); }, [defaultSelection]);

  function handleSelection(offer: Volt.IProductOffering) {
    setSelection(offer);
  }

  function handleContinue() {
    if (selection.id !== defaultSelection.id) {
      const offer = selection.id === "NO" ? defaultSelection : selection;
      onContinueClick(offer.offeringAction);
      Omniture.useOmniture().trackAction({
        id: "mutipleWaysLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "MULTIPLE_WAYS_CONTINUE"
        },
        s_oPRD: {
          category: offer.displayGroupKey,
          name: offer.name,
          sku: "",
          quantity: "1",
          price: ValueOf<string>(offer, "regularPrice.price", ""),
          promo: ValueOf<string>(offer, "promotionDetails.description", "")
        }
      });
    } else {
      closeLightbox();
    }
  }

  const {
    name,
    imagePath,
    channelNumber,
    characteristics,
    shortDescription,
    longDescription,
  } = channel;
  const { language, genre, culture } = toCharacteristicsJSON(characteristics);
  return <Modal
    modalId={ModalId}
    className="do-not-center-in"
    onShown={() => {
      Omniture.useOmniture().trackFragment({
        id: "mutipleWaysLightbox",
        s_oAPT: {
          actionId: 104
        },
        s_oPRM: "Multiple ways to order"
      });
    }}
    onDismiss={() => {
      Omniture.useOmniture().trackAction({
        id: "mutipleWaysLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: "Close"
      });
    }}
    title={<FormattedMessage id="MULTIPLE_WAYS_TO_ADD_TITLE" values={{ name }} />}>
    <div className="">
      <div className="pad-30 pad-15-left-right-xs">
        <div className="spacer15 clear" aria-hidden="true"></div>
        <div className="d-flex flex-column flex-sm-row">
          <div className="heightFitContent flexStatic">
            <div style={{ width: "94px", height: "94px" }} className="margin-15-bottom margin-30-right borderGrayLight6 flex align-center">
              <img src={imagePath} alt={name} className="fill pad-5" />
            </div>
          </div>
          <div className="pad-30-left">
            <span className="sr-only"><FormattedMessage id="Channel number" /></span>
            <p className="no-margin txtSize16 txtVirginBlue line-height-18">{channelNumber}</p>
            <div className="spacer5" aria-hidden="true"></div>
            <p className="no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold">{[translateStringList(genre), translateStringList(culture), translateStringList(language)].filter(i => Boolean(i)).join(" / ")}</p>
            <div className="spacer15" aria-hidden="true"></div>
            <p className="no-margin txtSize14 vm-dark-grey2 line-height-18" dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} />
          </div>
        </div>
      </div>
      <form className="pad-30 pad-15-left-right-xs">
        <div className="spacer1 bgGrayLight3" aria-hidden="true"></div>
        <div className="spacer25 clear" aria-hidden="true"></div>
        <p className="txtSize16"><FormattedMessage id="Ways to add this channel" /></p>
        {
          parents.map(
            offer => <Option {...offer} isSelected={offer.id === selection.id} onSelect={handleSelection} />
          )
        }
        <FormattedMessage id="No thanks" values={{ name }}>
          {
            (txt: any) => <Option {...NoThanksOffer} name={txt as string} isSelected={NoThanksOffer.id === selection.id} onSelect={handleSelection} />
          }
        </FormattedMessage>
      </form>
    </div>

    <div className="bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg">
      <button id="MULTIPLE_WAYS_CONTINUE" className="btn btn-primary fill-xs" onClick={handleContinue}><FormattedMessage id="MULTIPLE_WAYS_TO_ADD_CONTINUE" /></button>
      <div className="vSpacer15" aria-hidden="true"></div>
      <button id="MULTIPLE_WAYS_CLOSE" className="btn btn-default fill-xs" onClick={closeLightbox}><FormattedMessage id="MULTIPLE_WAYS_TO_ADD_CLOSE" /></button>
    </div>
  </Modal>;
};

export default connect<IComponentProps, IComponentDispatches>(
  ({ catalog, lightboxData }: IStoreState) => {
    const channel = ValueOf<ITVChannel>(lightboxData, undefined, {});
    const parents = ValueOf<Array<string>>(channel, "multipleWaysToAdd", []).map(
      offerId => catalog.index.find(offer => offer.id === offerId)
    ).filter(Boolean) as Array<Volt.IProductOffering>;
    const defaultSelection = parents.find(offer => offer.isSelected);
    return ({
      channel,
      parents,
      defaultSelection: defaultSelection || NoThanksOffer
    });
  },
  (dispatch) => ({
    onContinueClick: (action: Volt.IHypermediaAction | undefined) => {
      if (action) dispatch(toggleSelection(action));
      dispatch(Actions.closeLightbox(ModalId));
    },
    closeLightbox: () => {
      Omniture.useOmniture().trackAction({
        id: "mutipleWaysLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: {
          ref: "MULTIPLE_WAYS_CLOSE"
        }
      });
      dispatch(Actions.closeLightbox(ModalId));
    },
  })
)(Component);
