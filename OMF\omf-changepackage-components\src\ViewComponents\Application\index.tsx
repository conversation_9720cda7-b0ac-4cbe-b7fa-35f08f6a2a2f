import * as React from "react";
import { connect } from "react-redux";
import { IntlProvider } from "react-intl";
import { Models, EWidgetStatus } from "../../Models";
import { ErrorComponent } from "../Error";

export interface IApplicationRootProps {
  propsfilter?: (props: Models.IBaseAppProps) => Models.IBaseAppProps;
  placeholder?: any | null;
  children?: any;
}

/**
 * Main application view router
 * displays one of the 3 top level views
 * * null view: no data yet, there is nothing to show
 * * ErrorHandler: application failed
 * * Application: the app itself
 * @param {*} props
 * @returns
 */
const Component: React.FC<Models.IBaseAppProps & IApplicationRootProps> = (props) => (
  Boolean(props.localization &&
        props.localization.messages &&
        Object.keys(props.localization.messages).length) ?
    <IntlProvider {...props.localization} formats={{ ...props.localization.formats, number: { CAD: { currency: "CAD", currencyDisplay: "symbol", style: "currency", minimumFractionDigits: 2 } } }} locale={props.localization.fullLocale}>
      <React.Fragment>
        {((status: EWidgetStatus) => {
          switch (status) {
            case EWidgetStatus.RENDERED:
            case EWidgetStatus.UPDATING:
              return props.children;
            case EWidgetStatus.ERROR:
              return <ErrorComponent details={props.errorHandlerProps} />;
            default:
              return props.placeholder;
          }
        })(props.widgetStatus)}
      </React.Fragment>
    </IntlProvider> : null);


export const ApplicationRootComponent = connect<Models.IBaseAppProps, {}, IApplicationRootProps>(
  ({ localization, error, widgetStatus }: Models.IBaseStoreState) => ({
    localization,
    widgetStatus,
    errorHandlerProps: error
  }), {},
  function (stateProps, despatchProps, ownProps) {
    return {
      ...(ownProps.propsfilter ? ownProps.propsfilter(stateProps) : stateProps),
      ...despatchProps,
      ...ownProps,
    };
  }
)(Component);
