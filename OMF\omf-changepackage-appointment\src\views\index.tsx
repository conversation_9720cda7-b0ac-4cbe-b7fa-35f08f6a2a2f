import * as React from "react";
import { useDispatch } from "react-redux";
import { Actions, Components, EWidgetName } from "omf-changepackage-components";
import { useFormContext } from "react-hook-form";
import { Header } from "./Componenets";
import Form from "./Form";

const {
  RestrictionModal
} = Components;

const {
  widgetRenderComplete
} = Actions;

interface IComponentProps {
}

/**
 * Header Componenet
 * Container HTML
 * @param props
 */

export const Application = (props: IComponentProps) => {
  const dispatch = useDispatch();
  const { errors } = useFormContext();

  React.useEffect(() => {
    dispatch(widgetRenderComplete(EWidgetName.APPOINTMENT));
  }, []);

  return <main id="mainContent">
    <span className="flex spacer30 col-12" aria-hidden="true"></span>
    <RestrictionModal id="APPOINTMENT_RESTRICTION_MODAL" />
    <Header errors={errors} />
    <Components.Container>
      <Components.Panel className="pad-25-left pad-25-right clearfix">
        <Form />
      </Components.Panel>
    </Components.Container>
  </main>;
};
