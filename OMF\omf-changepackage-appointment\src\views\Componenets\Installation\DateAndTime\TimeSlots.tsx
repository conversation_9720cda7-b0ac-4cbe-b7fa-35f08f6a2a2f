import * as React from "react";
import { FormattedDate } from "react-intl";
import { stripTimeBit } from "../../../../utils/AppointmentUtils";
import { IAvailableDates } from "../../../../models";
import { EDuration } from "../../../../models/Enums";
import { FormattedHTMLMessage } from "omf-changepackage-components";

interface ComponentProps {
  availableDates?: Array<IAvailableDates>;
  initSlickSlider: Function;
  selectDate: Function;
  selectedDateTime: IAvailableDates;
}

export class TimeSlots extends React.Component<ComponentProps, any> {
  static displayName = "TimeSlots";
  constructor(props: ComponentProps) {
    super(props);
  }

  componentDidMount() {
    this.props.initSlickSlider();
  }

  render() {
    const { availableDates, selectDate, selectedDateTime } = this.props;

    return <div className="flexBlock margin-15-bottom sub-option relative timeslot-picker">
      <div className="select-timeslot fill">

        {availableDates && availableDates.map((day, dayIndex) =>
          <div className="">
            <div className={day.timeSlots[0].intervalType === EDuration.AllDay ? "allDayContainer" : "day-container"} >
              <label htmlFor={"dayIndex_" + dayIndex} className="virginUltra sans-serif-xs txtBold-xs txtSize16 txtBlack lineHeight1-3 margin-15-bottom">
                <FormattedDate value={stripTimeBit(day.date as string)} weekday="long" timeZone="UTC" />
                <br className={`hidden-m`} />
                <span className="d-sm-none d-md-none d-lg-none d-xl-none">, </span>
                <FormattedDate value={stripTimeBit(day.date as string)} year="numeric" month="short" day="2-digit" timeZone="UTC" />
              </label>

              <ul className="noMargin list-unstyled timeItem" aria-labelledby="mondayList">
                {
                  day.timeSlots.map(timeSlot => {
                    const selectedInterval = selectedDateTime.timeSlots[0].intervalType === timeSlot.intervalType && selectedDateTime.date === day.date;
                    return <li className={`txtBlue ${selectedInterval ? "selected" : ""}`}>
                      <button id={`slot_${timeSlot.intervalType}`} onClick={(e) => selectDate(e, day.date, timeSlot)} className={`btn btn-link ${timeSlot.intervalType === EDuration.AllDay ? "flexCol flexJustify" : ""} ${timeSlot.isAvailable ? "" : "disabled"} ${timeSlot.isSelected ? "selected" : ""}`} tabIndex={0}>
                        <FormattedHTMLMessage id={timeSlot.intervalType} />
                      </button>
                    </li>;
                  })
                }
              </ul>
            </div>
          </div>
        )}

      </div>
    </div>;
  }
}
