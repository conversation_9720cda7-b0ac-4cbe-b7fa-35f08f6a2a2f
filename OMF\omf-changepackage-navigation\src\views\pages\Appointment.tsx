import { WidgetLoader } from "bwtk";
import { EWidgetName } from "omf-changepackage-components";
import * as React from "react";
import { enableAppointementRoute } from "../../utils/History";

interface IPage {
  title: string;
}

export const Appointment: React.FC<IPage> = ({ title }) => {
  React.useEffect(() => {
    enableAppointementRoute();
    // Set page title
    document.title = `${title} - ${document.title}`;
  }, []);
  return <WidgetLoader widget={EWidgetName.APPOINTMENT} />;
};
