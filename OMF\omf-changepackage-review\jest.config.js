module.exports = {
  // The root of source code
  roots: ["<rootDir>"],

  // Jest transformations -- this adds support for TypeScript
  transform: {
    "^.+\\.tsx?$": "ts-jest"
  },

  // Test spec file resolution pattern
  testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.tsx?$",

  // Module file extensions for importing
  moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],

  setupFiles: ["<rootDir>/jest-setup-file.ts"]
};