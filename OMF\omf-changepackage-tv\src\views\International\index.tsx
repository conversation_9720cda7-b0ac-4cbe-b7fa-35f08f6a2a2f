import { Actions, EWidgetRoute, ValueOf, Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { Redirect, Route, Switch, useLocation } from "react-router-dom";
import { IStoreState, ITVChannel } from "../../models";
import { getSupportdLanguages } from "../../utils/Characteristics";
import { Footer } from "../Components/Legal";
import { ModalId as SelectedChannelsModal } from "../Modals/SelectedChannels";
import { Alacarte } from "./Alacarte";
import { Combos } from "./Combos";
import { Filter } from "./Filter";


interface IComponentConnectedProps {
  comboLanguages: Array<string>;
  channelLanguages: Array<string>;
  combos: Array<Volt.IProductOffering>;
  channels: Array<ITVChannel>;
  navigation: any;
  refresh: any;
}

interface IComponentDispatches {
  openLightbox: Function;
}

export const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  comboLanguages,
  channelLanguages,
  combos,
  channels,
  navigation,
  openLightbox,
  refresh
}) => {
  const [filter, setFitler] = React.useState("all");
  // const languages = allLanguages.filter(ln => filter === "all" || ln === filter);
  // Focus proper section on sidebar navigation
  const location = useLocation();
  React.useEffect(() => {
    setFitler("all");
    // try {
    //     const target = document.getElementById(location.hash.substr(1));
    //     target && target.scrollIntoView(true);
    // } catch (e) { }
  }, [location]);
  // --
  return <>
    <h2 className="virginUltraReg txtSize28 noMargin txtBlack text-uppercase"><FormattedMessage id="International page" /></h2>
    <div className="spacer10" />
    <Switch>
      <Route path={EWidgetRoute.TV_InternationalCombos}>
        <Filter filter={filter} setFitler={setFitler} languages={comboLanguages} />
        <Combos combos={combos} languages={comboLanguages.filter(ln => filter === "all" || ln.indexOf(filter) > -1)} />
      </Route>
      <Route path={EWidgetRoute.TV_InternationalAlacarte}>
        <Filter filter={filter} setFitler={setFitler} languages={channelLanguages} />
        <Alacarte
          openLightbox={openLightbox}
          channels={channels}
          navigation={navigation}
          languages={channelLanguages.filter(ln => filter === "all" || ln.indexOf(filter) > -1)}
          refresh={refresh}
        />
      </Route>
      <Redirect to={EWidgetRoute.TV_InternationalCombos} />
    </Switch>
    <Footer pageName={Volt.EDIsplayGroupKey.INTERNATIONAL}
      label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}
      content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.INTERNATIONAL}`} />
  </>;
};

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog, navigation }: IStoreState) => {
    const combos = ValueOf<Array<Volt.IProductOffering>>(catalog, "offerings." + Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS, []);
    const channels = ValueOf<Array<Volt.IProductOffering>>(catalog, "offerings." + Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE, []) as Array<ITVChannel>;
    return {
      comboLanguages: getSupportdLanguages(combos),
      channelLanguages: getSupportdLanguages(channels),
      combos, channels, navigation,
      refresh: Math.random() * 1000
    };
  },
  (dispatch) => ({
    openLightbox: (id: any) => dispatch(Actions.openLightbox({ lightboxId: SelectedChannelsModal, data: { relativeId: id } }))
  })
)(Component);
