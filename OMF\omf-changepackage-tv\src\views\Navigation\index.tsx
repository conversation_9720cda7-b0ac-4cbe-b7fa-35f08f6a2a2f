import { Components, ValueOf } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { Link, useRouteMatch } from "react-router-dom";
import { INavigationItem, IStoreState } from "../../models";
import Search from "./Search";

const {
  Visible
} = Components;

interface IComponentConnectedProps {
  navigation: Array<INavigationItem>;
}

interface IComponentDispatches {
}

interface IOfferingLinkProps extends INavigationItem {
  isActive?: boolean;
  subMenu?: boolean;
}

const OfferingLink = (offering: IOfferingLinkProps) => <Link id={`MENU_${offering.offeringId}`} to={offering.route} role="link" className={`bell-tv-navigator-tab-row flexRow ${offering.isActive ? "active" : ""}`}>
  <div className="bell-tv-navigator-tabs-text flexGrow">
    <span className={`${offering.subMenu ? "sans-serif txtSize14" : "virginUltraReg txtSize16 text-uppercase"} noPadding block submenu-name`}>
      <FormattedMessage id={offering.offeringKey || "NONE"} />
      <Visible when={offering.count !== undefined}>
                &nbsp;({offering.count})
      </Visible>
    </span>
    <Visible when={Boolean(offering.name)}>
      <span className="virginUltraReg txtSize16 noPadding submenu-name text-uppercase">
        {offering.name}&nbsp;-&nbsp;
      </span>
    </Visible>
    <Visible when={Boolean(offering.subTotalPrice)}>
      <span className="noPadding submenu-price submenu-price txtSize14">
        <Components.BellCurrency value={ValueOf(offering, "subTotalPrice.price", 0)} />
        <span aria-hidden><FormattedMessage id="PER_MO" /></span>
        <span className="sr-only"><FormattedMessage id="PER_MONTH">{(txt) => <>{txt}</>}</FormattedMessage></span>
      </span>
    </Visible>
  </div>
  <div className="bell-tv-navigator-tabs-pointer flexStatic">
    <span className="volt-icon icon-Right_arrow txtSize15 inlineBlock" aria-hidden={true}></span>
  </div>
</Link>;

export const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  navigation
}) => <nav className="bell-tv-navigator sticky" role="tablist">
  <div className="virginUltraReg txtSize22 bgGray mobile-menu-header text-uppercase d-block d-md-none">
    <FormattedMessage id="YOUR_TV_CATEGORIES" />
  </div>
  <Search />
  <div className="spacer15 hidden-xs"></div>
  <ul className="bell-tv-navigator-tabs noBullets virgin-scroll accss-focus-outline-override-grey-bg" role="presentation">
    {
      navigation.map(offering => {
        const active = Boolean(useRouteMatch(offering.route));
        return <li key={offering.key} className={`bell-tv-navigator-tab ${active ? "active expanded" : ""}`}  role="tab" aria-selected={active ? "true" : "false"}>
          <OfferingLink {...offering} />
          <Visible when={Array.isArray(offering.children)}>
            <div className={`bell-tv-navigator-tab-more ${active ? "active" : ""} ${active ? "d-block" : "d-none"}`} aria-expanded={location.pathname === offering.route} role="tab">
              {
                ValueOf(offering, "children", []).map((offering: INavigationItem) => <OfferingLink subMenu={true} isActive={Boolean(useRouteMatch(offering.route))} {...offering} />)
              }
            </div>
          </Visible>
        </li>;
      })
    }
  </ul>
  {/* This element is popilated from omf-changpackage-navigation/src/summary/TvSummaryPortal.tsx */}
  <div id="tv-sedebar-summary-portal" className="dockbar-content d-block d-md-none pad-15-top" />
</nav>;

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ navigation }: IStoreState) => ({ navigation })
)(Component);
