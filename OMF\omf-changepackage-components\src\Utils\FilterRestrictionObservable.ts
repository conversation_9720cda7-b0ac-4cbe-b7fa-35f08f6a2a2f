import { concat, ObservableInput, of } from "rxjs";

import { AjaxResponse, EWidgetStatus, Models, Volt } from "../Models";
import { Actions } from "../Actions";
import { Assert } from "./Assert";
import { ValueOf } from "./ExtractProp";

let _restrictions: Array<Volt.IRestriction> = [];

function filterRestriction(restriction: Volt.IRestriction | Array<Volt.IRestriction>, isFinal = true): Array<any> {
  const response: any[] = [];
  if (<PERSON>olean(restriction)) {
    _restrictions = Array.isArray(restriction)
      ? restriction as Array<Volt.IRestriction>
      : [restriction] as Array<Volt.IRestriction>;
  }
  if (Array.isArray(_restrictions) && _restrictions.length) {
    response.push(
      Actions.raiseRestriction(_restrictions.pop() as Volt.IRestriction)
    );
  }
  if (isFinal) {
    response.push(
      Actions.setWidgetStatus(EWidgetStatus.RENDERED)
    );
  }
  return response;
}

/**
 * Every PATCH call needs the same type of error
 * checking before it can proceed with the data
 * updates.
 * @export
 * @param {AjaxResponse<any>} response
 * @param {...(ObservableInput<T>)[]} observables
 * @returns {*}
 */
export function FilterRestrictionObservable<T>(response: AjaxResponse<Volt.IAPIResponse | Volt.IRestrictionAPIResponse>, ...observables: (ObservableInput<T>)[]): any {
  Assert(response, "Expected response object, but got undefined/null");
  Assert(observables, "Expected Array<ObservableInput>, but got undefined/null");
  // Flow has been initilized
  sessionStorage.setItem("omf:Initilized", "yes");
  // Check if there are any message from server
  switch (true) {
    // API has failed
    case !(response.data && typeof response.data === "object"):
      return [ // Not good V
        Actions.errorOccured(new Models.ErrorHandler(response.statusText, { ...(typeof response.data === 'object' ? response.data : {}), url: response.url })),
        Actions.setWidgetStatus(EWidgetStatus.RENDERED)
      ];
      // Final restriction handling:
    case !!ValueOf(response, "data.restriction", false):
      return filterRestriction(ValueOf(response, "data.restriction"));
      // Proceed with normal flow
    default:
      const restrictionActions = filterRestriction(ValueOf(response, "data.productOfferingDetail.restriction"), false);
      const genericOperations = [
        Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(response, "data.productOfferingDetail.productConfigurationTotal")))
      ];
      
      return concat(
        // Non blocking restriction case
        of(...restrictionActions),
        // generic operations to run
        of(...genericOperations),
        // ...do everything else
        ...observables);
  }
}
