import { Components, ValueOf } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { ISummary } from "../../models";

const {
  Visible,
  Currency,
} = Components;

interface IComponentProps {
  summary: ISummary;
  isContinueEnabled: boolean;
  onContinueClick: () => void;
}

const TVSummaryPortal: React.FC<IComponentProps> = ({
  summary,
  isContinueEnabled,
  onContinueClick
}) => <>
  <Visible when={ValueOf(summary, "TV", false)}>
    <Visible when={ValueOf(summary, "TV.currentPrice")}>
      <div className="virgin-menu-dockbar flexStatic flexJustifyBetween-sm bgBlack">
        <p className="noMargin txtSize12 flexGrow txtWhite"><FormattedMessage id="CurrentTV" /></p>
        <span className="virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite">
          <Currency value={ValueOf(summary, "TV.currentPrice.price", 0)} monthly={true} prefixClassName={"txtSize16 txtUppercase"} fractionClassName={"txtSize16"} />
        </span>
      </div>
    </Visible>
    <Visible when={ValueOf(summary, "TV.newPrice")}>
      <div className="virgin-menu-dockbar flexStatic bgOrange flexJustifyBetween-sm">
        <p className="noMargin txtSize12 flexGrow txtWhite"><FormattedMessage id="NewTV" /></p>
        <span className="virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite">
          <Currency value={ValueOf(summary, "TV.newPrice.price", 0)} monthly={true} prefixClassName={"txtSize16 txtUppercase"} fractionClassName={"txtSize16"} />
        </span>
      </div>
    </Visible>
  </Visible>
  <div className="flexBlock preview-btn bgBlack flexJustify pad-25-top pad-25-bottom">
    <button onClick={onContinueClick} disabled={!isContinueEnabled} id="mobileTVContinue"
      className={`btn btn-primary txtWhite txtSize16 relative ${isContinueEnabled ? "" : "disabled"}`}>
      <FormattedMessage id="Review changes" />
      <Visible when={ValueOf(summary, "productOfferingCount", false)}>
        <span className="bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification">
          <span>{ValueOf(summary, "productOfferingCount", 0)}</span>
        </span>
      </Visible>
    </button>
  </div>
  {/* <div className="flexBlock preview-btn bgBlack flexJustify pad-10-top">
      <button className="btn btn-link txtUnderline txtWhite txtSize12">Reset
                                    changes</button>
    </div> */}
</>;

export default TVSummaryPortal;
