import * as React from "react";
import { FormattedMessage } from "react-intl";
import { FormattedHTMLMessage } from "omf-changepackage-components";

export enum HeadingTags {
  H1 = "h1",
  H2 = "h2",
  H3 = "h3",
  H4 = "h4",
  H5 = "h5",
  H6 = "h6"
}

interface ComponentProps {
  tag?: HeadingTags;
  additionalClass?: string;
  content: string;
  description?: string;
}

export const Heading = (props: ComponentProps) => {
  const privateProps = { ...defaultProps, ...props};
  const { tag, additionalClass, content, description } = privateProps;
  const Tag = tag || "h2";

  return (
    <>
      <Tag className={`virginUltra txtBlack txtCapital noMargin ${additionalClass}`}>
        <FormattedMessage id={content} />
      </Tag>
      {
        description ? <>
          <span className="spacer10 col-xs-12 clear"></span>
          <p className="noMargin"><FormattedHTMLMessage id={description} /></p>
        </> : null
      }
    </>
  );
};

const defaultProps = {
  additionalClass: "",
  description: ""
};
