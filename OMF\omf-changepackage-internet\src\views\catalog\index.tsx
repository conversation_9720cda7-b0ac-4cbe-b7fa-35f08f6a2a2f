import * as React from "react";
import { connect } from "react-redux";
import { ValueOf } from "omf-changepackage-components";
import { IStoreState, IPackage } from "../../models";
import { Package } from "./Package";
import { Footer } from "./Legal";
import { toCharacteristicsJSON } from "../../utils/Characteristics";

interface IComponentProps {
  catalog: Array<IPackage>;
}

const Component: React.FC<IComponentProps> = ({
  catalog
}) => <div className="container liquid-container noSpacing" role="radiogroup">
  <style>
    {
      `.icon-upload-ico:before {
                    content: "\\e99d";
                }
                .icon-download-ico:before {
                    content: "\\e929";
                }
                .package-desc li {
                    display: block;
                    list-style: none;
                    position: relative;
                    width: calc(100% / 2);
                }
                    .package-desc li:not(:last-of-type) {
                        padding-right: 15px;
                    }
                    .package-desc li .volt-icon {
                        position: absolute;
                        display: block;
                        color: #cc0000;
                        font-size: 32px;
                        width: 42px;
                        height: 42px;
                        left: 0;
                    }
                    .package-desc li span {
                        display: block;
                        font-size: 12px;
                    }
                    .package-desc .speed-box2 span:first-of-type,
                    .package-desc li span.speed {
                        font-size: 22px;
                        color: black;
                        text-transform: uppercase;
                        font-family: "VMUltramagneticNormalRegular", Helvetica, Arial, sans-serif;
                }
                .package-desc .speed-box2 span.usage {
                    white-space: nowrap;
                }
                .speed-box1 li {
                    margin-top: 10px;
                    padding-left: 42px;
                }
                .package-desc li span.downloadTray {
                    display: none;
                }
                .package-desc .speed-box1.expanded li span.downloadTray {
                    display: block;
                }
                @media (max-width: 991.98px) {
                    .package-desc li {
                        width: 100%;
                    }
                  .pkg-pull-left {
                      margin-left: -40px;
                  }
                }`
    }
  </style>
  {
    // We do not want to show current packages
    catalog.filter(pkg => !pkg.isCurrent)
      .sort(
        (a, b) => (
          ValueOf<number>(toCharacteristicsJSON(a.characteristics), "sortPriority", 0) -
                        ValueOf<number>(toCharacteristicsJSON(b.characteristics), "sortPriority", 0)
        )
      )
      .map(
        internetPackage => <Package {...internetPackage} />
      )
  }
  <Footer />
</div>;


export const Catalog = connect<IComponentProps>(
  ({ catalog }: IStoreState) => ({ catalog })
)(Component);
