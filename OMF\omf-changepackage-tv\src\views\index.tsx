import { Actions, Components, EWidgetRoute, Utils } from "omf-changepackage-components";
import * as React from "react";
import { connect, useDispatch } from "react-redux";
import { BrowserRouter, Redirect, Route, Switch, useLocation, useHistory } from "react-router-dom";
import { IStoreState } from "../models";
import Addons from "./Addons";
import Alacarte from "./Alacarte";
import Browser from "./Browser";
import International from "./International";
import ChannelDetailsModal from "./Modals/Details";
import MultipleWaysToAddModal from "./Modals/MultipleWays";
import MoviesSeries from "./MoviesSeries";
import Navigation from "./Navigation";
import Packages from "./Packages";
import Search from "./Search";
import { CleanupPopoverStack } from "./Components/Tooltip";
import { Header } from "./header";

const {
  RestrictionModal
} = Components;

const {
  errorOccured,
  widgetRenderComplete,
  handleNav
} = Actions;

interface IComponentProps {
  navStatus: boolean;
}

interface IComponentDispatches {
  onErrorEncountered: Function;
  widgetRenderComplete: Function;
  closeNav: any;
}

const AppRouter: React.FC<IComponentProps & IComponentDispatches> = (props) => {
  const dispatch = useDispatch(),
    history = useHistory();
  React.useEffect(() => {
    dispatch(Actions.broadcastUpdate(Actions.setHistoryProvider(history)));
  }, []);
  // Close mobile sidebar whenever we navigate
  const location = useLocation();
  React.useEffect(() => {
    props.closeNav();
    CleanupPopoverStack();
    window.scrollTo(0, 0);
  }, [location]);
  return <main id="mainContent">
    <style dangerouslySetInnerHTML={{
      __html: `
            html {
                scroll-behavior: smooth;
            }
            @media (max-width: 992px) {
                .channel-tooltip {
                    display: none!important;
                }
            }
        `}} />
    <Header />
    <div className="spacer30" />
    <div className="container liquid-container flexRow">
      <div className={`col-md-3 col-xs-12 bell-tv-navigator-menu side-navigation d-md-block ${props.navStatus ? "open-nav-slider" : ""}`}>
        <Navigation />
      </div>
      <div className="floatR col-md-9 col-xs-12 bell-tv-navigator-page accss-focus-outline-override-white-bg">
        <Switch>
          <Route exact path={EWidgetRoute.TV_Packages}>
            <Packages />
          </Route>
          <Route path={EWidgetRoute.TV_MoviesSeries}>
            <MoviesSeries />
          </Route>
          <Route path={EWidgetRoute.TV_Alacarte}>
            <Alacarte />
          </Route>
          <Route path={EWidgetRoute.TV_International}>
            <International />
          </Route>
          <Route path={EWidgetRoute.TV_Addons}>
            <Addons />
          </Route>
          <Route path={EWidgetRoute.TV_Browse}>
            <Browser />
          </Route>
          <Route path={EWidgetRoute.TV_Search}>
            <Search />
          </Route>
          {/* Every unknown path should redirect to base route */}
          <Route path="*">
            <Redirect to={EWidgetRoute.TV_Packages} />
          </Route>
        </Switch>
      </div>
    </div>
    <ChannelDetailsModal />
    <MultipleWaysToAddModal />
    <RestrictionModal id="TV_RESTRICTION_MODAL" />
    <div id="NAV_BACKDROP" onClick={props.closeNav} className={`nav-backdrop ${props.navStatus ? "show" : "hide"}`} aria-hidden={true}></div>
  </main>;
};

class Component extends React.Component<IComponentProps & IComponentDispatches> {
  baseRoute: string;
  componentDidCatch(err: any) {
    this.props.onErrorEncountered(err);
  }

  componentWillMount() {
    this.baseRoute = `/Ordering${Utils.constructPageRoute(EWidgetRoute.TV)}`;
  }

  componentDidMount() {
    this.props.widgetRenderComplete("omf-changepackage-tv");
  }

  render() {
    return (<BrowserRouter basename={this.baseRoute}>
      <AppRouter {...this.props} />
      <div className="spacer60" />
      <div className="spacer60" />
    </BrowserRouter>);
  }
}

export const Application = connect<IComponentProps, IComponentDispatches>(
  ({ navStatus }: IStoreState) => ({ navStatus }),
  (dispatch) => ({
    onErrorEncountered: (error: any) => dispatch(errorOccured(error)),
    widgetRenderComplete: () => dispatch(widgetRenderComplete()),
    closeNav: () => dispatch(handleNav(false))
  })
)(Component);
