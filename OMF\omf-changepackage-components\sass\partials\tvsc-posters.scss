@import "mixins";
.bell-posters {
    // clear the size conflict with Bell-Slider
    &:not(.bell-slider) {
        margin: 0 -15px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        .bell-poster-image .bell-movie-poster,
        .bell-poster-image .bell-poster-detail {
            width: 100%;
        }
    } // --
    .bell-poster-image {
        display: inline-block;
        width: 100%;
        padding: 15px;
        &.condensed {
            padding: 15px 7.5px;
            .bell-poster-detail {
                padding: 5px;
                background-color: none;
            }
        }
        .bell-movie-poster {
            height: auto;
            flex-grow: 1;
        }
        .bell-poster-detail {
            padding: 15px 20px;
            background-color: #013678;
            font-size: 14px;
            flex-grow: 0;
            overflow: hidden;
            height: 70px;
            >* {
                text-overflow: ellipsis;
                overflow: hidden;
                display: block;
                height: 100%; //special fix for iOS item align
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            @media #{$media-tablet} {
                padding: 10px 15px;
                font-size: 12px;
                height: 55px;
            }
            @media #{$media-mobile} {
                padding: 10px;
                font-size: 12px;
                height: 55px;
            }
        }
        flex-basis: 20%;
        @media #{$media-tablet} {
            // flex-basis: 33.3%;
            flex-basis: 25%;
        }
        @media #{$media-mobile} {
            flex-basis: 50%;
        }
    }
    .bell-slider-wrapper {
        @media #{$media-desktop} {
            max-width: 230px
        }
        @media #{$media-tablet} {
            max-width: 197px
        }
    }
    .no-slider {
        width: 162.5px;
        float: left;
    }
    .mwta-slide {
        width: 82px;
    }
}

// @media screen and (max-width: 999px) and (min-width: 768px) {
//     .bell-posters .bell-poster-image .bell-movie-poster {
//         height: auto;
//     }
// }
// @media screen and (max-width: 639px) {
//     .bell-posters .bell-poster-image .bell-movie-poster {
//         height: auto;
//     }
// }
// @media screen and (max-width: 999px) and (min-width: 768px) {
//     .bell-posters .bell-poster-image .bell-poster-detail {
//         padding: 10px 15px;
//         font-size: 12px;
//         height: 55px;
//     }
// }
// @media screen and (max-width: 639px) {
//     .bell-posters .bell-poster-image .bell-poster-detail {
//         padding: 10px;
//         font-size: 12px;
//         height: 55px;
//     }
// }
// @media screen and (max-width: 999px) and (min-width: 768px) {
//     .bell-posters .bell-poster-image {
//         .bell-movie-poster,
//         .bell-poster-detail {
//             width: 150px;
//         }
//     }
// }
// @media screen and (max-width: 639px) {
//     .bell-posters .bell-poster-image {
//         padding: 8px;
//         .bell-movie-poster,
//         .bell-poster-detail {
//             width: 137px;
//         }
//     }
// }

.modal-body .bell-poster-detail {
    height: auto !important;
}