﻿@model eCare.Common.Areas.Ordering.Models.ChangePackage
@using eCare.Common.Utilities

@Html.Partial("~/Areas/Common/Views/Shared/UserControls/AntiForgeryHeader_Widgets.cshtml")
@{
    /**/

    ViewBag.Title = "Change Package";
    Layout = "~/Areas/Ordering/Views/_Layout_Responsive.cshtml";
    Boolean isDebug = Request.Cookies.AllKeys.Contains("debugwidget");
    bool voltPilotEnabled = Utility.GetVOLTPilotToggleSwitch();
}
@if (!Model.isOutage)
{
    <div id="pageContainer"></div>
    <div id="errorContainer" class="container liquid-container" style="display: none">
        <div class="spacer60"></div>
        <div class="brf3-panel pad-30 clearfix">
            @Html.Partial("~/Views/Shared/Component_Styles/_GenericErrorPartial.cshtml", "")
            <p><a href="@Model.exitURL">Back</a></p>
        </div>
        <div class="spacer60"></div>
    </div>
}
else
{
    @Html.Partial("~/Areas/Ordering/Views/_PartialOrderingBanner.cshtml",Model)
}




@section styles
    {
    <!-- =================== Page styles ===================== -->
    <link href="/Resource/BRF3/content/css/volt-icons.css" rel="stylesheet">
    <link href="/Resource/BRF3/content/css/changeflow-bundle.css" rel="stylesheet">
    <!-- Custom JS for Accessibility Fixes -->
    <script src="/Resource/custom/js/customAccessibilityFunctions.js"></script>
    <style>
        .virgin-icon:before {
            font-family: 'volt-icons';
            position: relative;
        }

        @@media screen and (max-width: 600px) {
            #inqC2CImgContainer_AnchoredV2 {
                display: none !important;
            }
        }
    </style>
}

@section scripts
    {
    @if (!voltPilotEnabled)
    {
        <script>
            window.location.href='@Model.exitURL';
        </script>
    }
    @if (!Model.isOutage)
    {
        <!-- =================BWTK frameworks======================= -->
        <script src="/Styles/WidgetAssets/bwtk/v6/lib-loader@(isDebug ? ".js" : ".min.js")" type="text/javascript"></script>
        <script type="text/javascript">
                // Timeout for Testing team to load bundles for slow Internet Connections
                var loader = new BwtkLoader();
                var bundle = "/Styles/Widgets/Ordering/V/myaccount-omf-changepackage/myaccount-omf-changepackage-bundle@(isDebug ? ".js" : ".min.js")";
                loader.start({
                        mode: "@(isDebug ? "development" : "production")",
                        override: {
                            scripts: {
                                reactRouterDom: '/Styles/WidgetAssets/libs/v3/react-router-dom/umd/react-router-dom.min.js',
                            }
                        },
                        bundlePath: bundle
                    }, (Bundle) => {
                    // Temporatry solution
                    var cachedKey = sessionStorage.getItem("omf:OrderId");
                    if (cachedKey !== "@Model.order") {
                        sessionStorage.setItem("omf:OrderId", "@Model.order");
                    }
                    // ---
                    Bundle.initialize(
                        // Widget container and props
                        [{
                            container: "pageContainer",
                            widget: "omf-changepackage-navigation",
                            localization: "Ordering/OMF/omf-changepackage-navigation",
                            props: {
                                flowType: "@Model.flowType.ToString()"
                            } // Optional widget props
                        },
                        { // Preload localization for other widgets without rendering them
                            widget: "omf-changepackage-internet",
                            localization: "Ordering/OMF/omf-changepackage-internet",
                        },{
                            widget: "omf-changepackage-appointment",
                            localization: "Ordering/OMF/omf-changepackage-appointment",
                        }, {
                            widget: "omf-changepackage-review",
                            localization: "Ordering/OMF/omf-changepackage-review",
                        }, {
                            widget: "omf-changepackage-tv",
                            localization: "Ordering/OMF/omf-changepackage-tv",
                        }],
                        // Widget config
                        {
                            "bwtk/": {
                                "services/": {
                                    "localization:": {
                                        "defaultLocale": "@Model.language",
                                        "Url": "@Html.Raw(Model.localizationAPI)",
                                    }
                                }
                            },
                            "Ordering/": {
                                "flowType": "@Model.flowType.ToString()",
                                "headers": {
                                    "brand": "@Model.brand",
                                    "Accept-Language": "@Model.language",
                                    "province": "@Model.province",
                                    "ServiceConsumer": "eCare",
                                    "channel": "VIRGINEXT",
                                    "content-type": "application/json",
                                    "accountNumber": "@Model.accountNumber",
                                    "UserId": "@Model.userId",
                                    "orderid": "@Model.order",
                                    "internetAccountNumber": "@Model.internetAccountNumber",
                                    "x_PriceInfo":true
                                },
                                "environmentVariables": {
                                    "province": "@Model.province",
                                    "brand": "@Model.brand",
                                    "channel": "eCare",
                                    "language": "@Model.language",
                                    //"useMockData": true
                                },
                                "omf-changepackage-navigation:": {
                                    "flowType": "@Model.flowType",
                                    "defaultRoute": "@Model.defaultRoute",
                                    "api": {
                                        "base": "@Html.Raw(Model.baseAPI)"
                                    },
                                    "linkURL": {
                                        "exitURL": "@Model.exitURL",
                                        "logoutURL": "@System.Configuration.ConfigurationManager.AppSettings["virgin.logout.url"]",
                                        "privacyURL": "https://www.virginplus.ca/@Model.language/support/legal-privacy.html",
                                        "securityURL": "",
                                        "legalURL": "http://www.virginplus.ca/@Model.language/support/legal.html",
                                        "entrustIMGURL": "/Resource/mobility-selfserve/content/img/entrust_seal.png",
										"feedbackURL": "javascript:KAMPYLE_ONSITE_SDK.showForm(4149)"
                                    }
                                },
                                "omf-changepackage-internet:": {
                                    "api": {
                                        "base": "@Html.Raw(Model.baseAPI)",
                                        "serviceAccountAPI": "@Html.Raw(Model.internetServiceAccountAPI)",
                                        "catalogAPI": "@Html.Raw(Model.internetCatalogAPI)",
                                        "bundleCatalogAPI": "@Html.Raw(Model.internetCatalogAPI)"
                                    }
                                },
                                "omf-changepackage-appointment:": {
                                    "api": {
                                        "base": "@Html.Raw(Model.baseAPI)",
                                        "appointmentAPI": "@Html.Raw(Model.appointmentAPI)"
                                    }
                                },
                                "omf-changepackage-review:": {
                                    "api": {
                                        "base": "@Html.Raw(Model.baseAPI)",
                                        "internetOrderDetailsAPI": "@Html.Raw(Model.internetDetailsAPI)",
                                        "internetOrderSubmitAPI": "@Html.Raw(Model.internetSubmitAPI)",
                                        "tvOrderDetailsAPI": "@Html.Raw(Model.tvDetailsAPI)",
                                        "tvOrderSubmitAPI": "@Html.Raw(Model.tvSubmitAPI)",
                                        "bundleOrderDetailsAPI": "@Html.Raw(Model.bundleDetailsAPI)",
                                        "bundleOrderSubmitAPI": "@Html.Raw(Model.bundleSubmitAPI)",
                                        "tvAddDetailsAPI": "@Html.Raw(Model.tvAddDetailsAPI)",
                                        "tvAddSubmitAPI": "@Html.Raw(Model.tvAddSubmitAPI)"
                                    },
                                    "pdfDownloadPath": "/styles/pdf/",
                                    "mockdata": {
                                        "ProductOrder": {
                                            "POST": { "confirmationNumber": "XX12345X", "orderDate": "2020-03-13T12:07:42.0701918-04:00", "productOfferingDetail": { "messages": [{ "messageType": "Information", "messageBody": "Your order has been put on hold. A member of our team will be reaching out to you. For more info, please call 1 888-999-2321.", "messageCode": null }] } }
                                        }
                                    }
                                },
                                "omf-changepackage-tv:": {
                                    "api": {
                                        "base": "@Html.Raw(Model.baseAPI)",
                                        "serviceAccountAPI": "@Html.Raw(Model.tvServiceAccountAPI)",
                                        "catalogAPI": "@Html.Raw(Model.tvCatalogAPI)",
                                        "addCatalogAPI": "@Html.Raw(Model.tvAddCatalogAPI)",
                                        "bundleCatalogAPI": "@Html.Raw(Model.bundleTvCatalogAPI)"
                                    }
                                },
                            }
                        },
                        // GlobalActionListener
                        function (action) {
                            var type = action.type,
                                payload = action.payload,
                                meta = action.meta;

                            $("#pageContainer").trigger("widgetevent");

                            switch (type) {
                                case "INIT_SLICK_SLIDER":
                                    initSlickSlider();
                                    break;
                                case "SHOW_HIDE_LOADER":
                                    bellTVNavTabTrapping();
                                    $('.accss-styles-init ul.bell-tv-navigator-tabs li').on('click', function () {
                                        bellTVNavTabTrapping();
                                    });
                                    break;
                                  case "SET_CONTACT_INFO":
                                    if (action.payload.preferredContactMethod == "Phone" && action.payload.primaryPhone.phoneNumber == "")
                                    {
                                        action.payload.primaryPhone.phoneNumber = "************";
                                        $("#Phone_LABEL").val("");
                                    }
                                    break;
                                case "SET_FLOW_SUMMARY_TOTALS":
                                    if ($('input[type="radio"][checked][id^="radio_"]').length > 0 && payload.resetAction == null && payload.summaryAction == null) {
                                        const resetUrl = "/UXP.Services/ecare/Ordering/Tv/ProductOrder/Reset",
                                            summaryUrl = "/UXP.Services/ecare/Ordering/Tv/Add/ProductOrder/Summary";

                                        payload.resetAction = { "rel": "Reset", "method": "POST", "href": resetUrl, "messageBody": null, "name": null, "redirectURLKey": null };
                                        payload.summaryAction = { "rel": "Summary", "method": "GET", "href": summaryUrl, "messageBody": { "op": "Get", "value": [{ "id": "", "path": "", "description": null }] }, "name": null, "redirectURLKey": null };
                                    }
                                    break;
                                default:
                                    break;
                            }
                        });
                });
                function initSlickSlider() {
                    $('.select-timeslot').slick({
                        slidesToShow: 5,
                        slidesToScroll: 1,
                        infinite: false,
                        responsive: [
                            {
                                breakpoint: 767,
                                settings: {
                                    slidesToShow: 1,
                                    slidesToScroll: 1
                                }
                            }
                        ]
                    });
                    // Hide show shaddow on lightbox
                    if ($('.select-timeslot .slick-prev').hasClass('slick-disabled')) {
                        $('.shaddowLeft').hide();
                    }
                    if ($('.select-timeslot .slick-next').hasClass('slick-disabled')) {
                        $('.shaddowRight').hide();
                    }
                    $('.slick-arrow').click(function () {
                        if ($('.select-timeslot .slick-prev').hasClass('slick-disabled')) {
                            $('.shaddowLeft').hide();
                        } else {
                            $('.shaddowLeft').show();
                        }
                        if ($('.select-timeslot .slick-next').hasClass('slick-disabled')) {
                            $('.shaddowRight').hide();
                        } else {
                            $('.shaddowRight').show();
                        }
                    });
            }
            if (!!window.MSInputMethodContext && !!document.documentMode) document.body.classList.add("ie11");

            function bellTVNavTabTrapping() {
                $('.accss-styles-init ul.bell-tv-navigator-tabs li').each(function () {
                    if ($(this).hasClass('active')) {
                         $(this).children('div').children('a').attr('tabindex', 0);
                    } else {
                        $(this).children('div').children('a').attr('tabindex', -1);
                    }
                });
            }

            $(document).ready(function () {

                $(document).on("change", "input[type=radio]", function () {
                    switch ($(this).val()) {
                        case 'Phone':
                            $("#Phone_LABEL").val($("#TextMessage_LABEL").val());
                            break;
                        case 'TextMessage':
                            $("#Phone_LABEL").val("************");
                            break;
                    }
                });
            });
        </script>
    }
    else
    {
        <script>
            $(document).ready(function () {
                $("#errorContainer").show();
            });
        </script>
    }
    @if (String.IsNullOrEmpty(Model.tvAccountNumber))
    {
        <script>var s_oClientID = "I:@Model.internetAccountNumber";</script>
    }
    else
    {
        <script>var s_oClientID = "T:@Model.tvAccountNumber";</script>

        <script>
            $(document).ready(function () {
                $("#pageContainer").on("widgetevent", updateInternetChangePackageElementLabel)
                                   .on("widgetevent", updateInternetChangePackageElementRole)
                                   .on("widgetevent", updateChannelGraphicsFocusability)
                                   .on("widgetevent", updateSidebarMenuTabindex)
                                   .on("widgetevent", removeRedundantTileFocus)
                                   .on("widgetevent", addTVTileFocusListener)
                                   .on("widgetevent", updateChannelControls)
                                   .on("widgetevent", fixReviewBoxSizing);

                function updateInternetChangePackageElementLabel() {
                    if ($(".omf-changepackage-internet #moreInfo #Legal_stuff").length) {
                        $(".omf-changepackage-internet #moreInfo #Legal_stuff").on("click", function () {
                            setTimeout(function () {
                                if ($(".omf-changepackage-internet #moreInfo .moreInfoBox #LEGALBOX_CLOSE").length) {
                                    $(".omf-changepackage-internet #moreInfo .moreInfoBox #LEGALBOX_CLOSE").attr("aria-label", (getIsFrench()) ? "Fermer la section plus d'informations" : "Close more information section");
                                }
                            }, 50);
                        });
                    }

                    if ($(".omf-changepackage-review .review-page .review-tier-block .review-programming-container .review-panel-body").length) {
                        var container = $(".omf-changepackage-review .review-page .review-tier-block .review-programming-container .review-panel-body");

                        $(container).each(function () {
                            if ($(this).find("button[id*='EDIT_NEWPLAN']").length) {
                                var button = $(this).find("button[id*='EDIT_NEWPLAN']");
                                var label = $(button).text().trim() + " " + $(this).find(".txtBlack.txtUppercase.virginUltraReg.differentTextureforHandset").text().trim().toLowerCase();

                                $(button).attr("aria-label", label);
                                $(button).removeAttr("aria-describedby");
                            }
                        });
                    }

                    if ($(".omf-changepackage-review .review-page .review-tier-block #DOWNLOAD_PDF_LINK").length) {
                        var link = $(".omf-changepackage-review .review-page .review-tier-block #DOWNLOAD_PDF_LINK");
                        var text = (getIsFrench()) ? " pour les " : " for ";

                        $(link).attr("aria-label", $(link).find("span").eq(0).text().trim() + text + $(link).siblings(".txtDarkGrey1.txtUppercase.virginUltraReg").text().trim().toLowerCase());
                        $(link).removeAttr("aria-describedby");
                    }

                    if ($(".omf-changepackage-review .review-page .review-tier-block .accordion .accordion-inner a[target='_blank']").length) {
                        var newWindowLinks = $(".omf-changepackage-review .review-page .review-tier-block .accordion .accordion-inner a[target='_blank']");

                        $(newWindowLinks).each(function () {
                            if (!$(this).find(".sr-only").length) {
                                $(this).append("<span class='sr-only'>&nbsp;" + ((getIsFrench()) ? "(ouvre dans une nouvelle fenêtre)" : "(opens in new window)") + "</span>");
                            }
                        });
                    }
                }

                function updateInternetChangePackageElementRole() {
                    if ($(".omf-changepackage-review .review-page .review-tier-block .review-programming-container .review-panel-body button[id*='EDIT_NEWPLAN']").length) {
                        $(".omf-changepackage-review .review-page .review-tier-block .review-programming-container .review-panel-body button[id*='EDIT_NEWPLAN']").attr("role", "link");
                    }
                }

                function updateChannelGraphicsFocusability() {
                    if ($(".omf-changepackage-tv .bell-tv-package .bell-tv-package-main").length) {
                        $("#pageContainer").off("widgetevent", updateChannelGraphicsFocusability);

                        var container = $(".omf-changepackage-tv .bell-tv-package .bell-tv-package-main");
                        var channel = $(container).find(".bell-tv-package-description .bell-tv-package-icons");
                        var price = $(container).find(".graphical_ctrl.ctrl_radioBtn .formatted-currency sup.txtSize22[aria-label='00cents']");

                        $(channel).removeAttr("aria-hidden");
                        $(price).attr("aria-label", "00");
                    }
                }

                function updateSidebarMenuTabindex() {
                    if ($(".omf-changepackage-tv .bell-tv-navigator-tabs .bell-tv-navigator-tab a").length) {
                        $("#pageContainer").off("widgetevent", updateSidebarMenuTabindex);

                        var navigationMenu = $(".omf-changepackage-tv .bell-tv-navigator-tabs .bell-tv-navigator-tab a");

                        function resetTabIndex() {
                            var menu = $(".omf-changepackage-tv .bell-tv-navigator-tabs .bell-tv-navigator-tab .bell-tv-navigator-tab-more a");

                            setTimeout(function () {
                                $(menu).each(function () {
                                    if ($(this).parent().parent().hasClass("expanded")) {
                                        $(this).parent().attr("aria-expanded", "true");
                                        $(this).attr("tabindex", "0");
                                    } else {
                                        $(this).parent().attr("aria-expanded", "false");
                                        $(this).attr("tabindex", "-1");
                                    }
                                });
                            }, 100);
                        }

                        $(navigationMenu).on("click", function () {
                            resetTabIndex();
                        });

                        resetTabIndex();
                    }
                }

                function removeRedundantTileFocus() {
                    setTimeout(function () {
                        if ($(".omf-changepackage-tv .bell-tv-navigator-page div.tooltip-interactive, .omf-changepackage-tv .bell-tv-navigator-page .bell-tv-package button.btn.btn-link").length) {
                            var tile = $(".omf-changepackage-tv .bell-tv-navigator-page div.tooltip-interactive");
                            var button = $(".omf-changepackage-tv .bell-tv-navigator-page .bell-tv-package button.btn.btn-link");

                            if ($(tile).length) {
                                removeBoxTabindex();
                            } else if ($(button).length) {
                                $(button).on("click", function () {
                                    setTimeout(function () {
                                        removeBoxTabindex();
                                    }, 100);
                                });
                            }

                            function removeBoxTabindex() {
                                $($(".omf-changepackage-tv .bell-tv-navigator-page div.tooltip-interactive[tabindex='0']")).each(function () {
                                    $(this).attr("tabindex", "-1");
                                });

                                setTimeout(function () {
                                    if ($(".omf-changepackage-tv .bell-tv-navigator-page div.tooltip-interactive[tabindex='0']").length) {
                                        removeBoxTabindex();
                                    }
                                }, 100);
                            }
                        }
                    }, 100);
                }

                function addTVTileFocusListener() {
                    if ($(".omf-changepackage-tv .bell-tv-navigator-page .bell-tv-package button.btn.btn-link").length) {
                        $(".omf-changepackage-tv .bell-tv-navigator-page .bell-tv-package button.btn.btn-link").on("click", function () {
                            setTimeout(function () {
                                $(".omf-changepackage-tv .bell-tv-navigator-page .bell-tv-channel-title.btn.btn-link").each(function () {
                                    if (!$(this).hasClass("listener-added")) {
                                        $(this).on("focusout blur", function () {
                                            var tile = $(this).parent().parent().parent().parent();
                                            $(tile).tooltip("hide");
                                        });
                                        $(this).on("focus", function () {
                                            var tile = $(this).parent().parent().parent().parent();
                                            $(tile).tooltip("show");
                                        });
                                        $(this).addClass("listener-added");
                                    }
                                });
                                $(".omf-changepackage-tv .floatL.w-100 .tooltip-interactive").each(function () {
                                    if (!$(this).hasClass("listener-added")) {
                                        $(this).on("mouseenter", function () {
                                            $(".omf-changepackage-tv .floatL.w-100 .tooltip-interactive[aria-describedby]").each(function () {
                                                $(this).tooltip("hide");
                                            });
                                        });
                                    }
                                    $(this).addClass("listener-added");
                                });
                            }, 200);
                        });
                    }
                }
                
                function updateChannelControls() {
                    if ($(".bell-tv-navigator-page .bell-tv-package button.btn.btn-link").length) {
                        $(".bell-tv-navigator-page .bell-tv-package button.btn.btn-link").attr("aria-controls", "tv-channels-section");
                        $(".bell-tv-navigator-page .bell-tv-package button.btn.btn-link").on("click", function () {
                            setTimeout(function () {
                                if ($(".bell-tv-package .bell-tv-package-footer").length) {
                                    $(".bell-tv-package .bell-tv-package-footer").attr("id", "tv-channels-section");
                                }
                            }, 100);
                        });
                    }
                }

                function fixReviewBoxSizing() {
                    if ($(".review-div-section").length) {
                        var section = $(".review-panel-body");
                        var childSection1 = section.find(".flexCol").first();
                        var childSection2 = section.find(".flexCol").last();
                        childSection1.css("padding-bottom", "50px");
                        childSection2.css("padding-bottom", "75px");
                    }
                }
            });
        </script>
    }

}
