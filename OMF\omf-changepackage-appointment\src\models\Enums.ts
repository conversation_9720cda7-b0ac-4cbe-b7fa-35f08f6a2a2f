export enum EContactMethod {
  PHONE = "Phone",
  TEXT_MESSAGE = "TextMessage",
  EMAIL = "Email"
}

export enum EDuration {
  AM = "AM",
  PM = "PM",
  Evening = "Evening",
  AllDay = "AllDay",
  Item0810 = "Item0810",
  Item1012 = "Item1012",
  Item1315 = "Item1315",
  Item1517 = "Item1517",
  Item1719 = "Item1719",
  Item1921 = "Item1921"
}

export enum EPreferredContactMethod {
  EMAIL = "Email",
  TEXT_MESSAGE = "TextMessage",
  PHONE = "Phone"
}
