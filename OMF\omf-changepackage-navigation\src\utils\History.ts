import { History } from "history";
import { EFlowType } from "omf-changepackage-components";

let _history: History | null = null;
let _tvhistory: History | null = null;
let _isAppointmentVisited = false;

export const Routes = {
  [EFlowType.INTERNET]: [
    "/Changepackage/Internet",
    "/Changepackage/Internet/Appointment",
    "/Changepackage/Internet/Review",
    "/Changepackage/Internet/Confirmation"
  ],
  [EFlowType.TV]: [
    "/Changepackage/TV",
    "/Changepackage/TV/Review",
    "/Changepackage/TV/Confirmation"
  ],
  [EFlowType.ADDTV]: [
    "/Add/TV",
    "/Add/TV/Review",
    "/Add/TV/Confirmation"
  ],
  [EFlowType.BUNDLE]: [
    "/Bundle/Internet",
    "/Bundle/Internet/Appointment",
    "/Bundle/TV",
    "/Bundle/Review",
    "/Bundle/Confirmation"
  ]
};

export function setHistoryProvider(history: History) {
  _history = _history || history;
}
export function setTVHistoryProvider(history: History) {
  _tvhistory = history || _tvhistory;
}

export function useHistory(): History {
  return _history as History;
}

export function useTVHistory(): History {
  return _tvhistory as History;
}


export function enableAppointementRoute() {
  sessionStorage.setItem("omf:hasAppointmentRoute", "yes");
}

export function getPageName(pathname: string): string {
  switch (true) {
    case pathname.indexOf("Review") > 0: return "REVIEW";
    case pathname.indexOf("Confirm") > 0: return "CONFIRMATION";
    case pathname.indexOf("Appoint") > 0: return "APPOINTMENT";
    case pathname.indexOf("Internet") > 0: return "INTERNET";
    case pathname.indexOf("TV") > 0: return "TV";
    default: return "";
  }
}

export function setAppointmentVisited() {
  _isAppointmentVisited = true;
}

export function checkAppointmentVisited() {
  return _isAppointmentVisited;
}
