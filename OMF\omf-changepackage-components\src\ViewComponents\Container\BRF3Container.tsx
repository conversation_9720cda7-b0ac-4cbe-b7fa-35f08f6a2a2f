import * as React from "react";
import { Models } from "../../Models";

export interface IBRF3ContainerProps extends Models.IBaseComponentProps, React.PropsWithChildren {
}

export interface IBRF3ContainerComponent extends React.FC<IBRF3ContainerProps> {
}


export const BRF3ContainerComponenet: IBRF3ContainerComponent = ({
  className, children
}) => <div className={`brf3-container ${className}`}>{children}</div>;

BRF3ContainerComponenet.defaultProps = {
  className: "",
};
