import { Injectable, CommonFeatures, ServiceLocator, CommonServices } from "bwtk";

const { BaseLocalization } = CommonFeatures;

const SOURCE_WIDGET_ID = "omf-changepackage-appointment";
@Injectable
export class Localization extends BaseLocalization {
  static Instance = null;
  static getLocalizedString(id: string): string {
    Localization.Instance = Localization.Instance ||
      ServiceLocator
        .instance
        .getService(CommonServices.Localization);
    const instance: any = Localization.Instance;
    return instance ? instance.getLocalizedString(SOURCE_WIDGET_ID, id, instance.locale) : id;
  }
}
