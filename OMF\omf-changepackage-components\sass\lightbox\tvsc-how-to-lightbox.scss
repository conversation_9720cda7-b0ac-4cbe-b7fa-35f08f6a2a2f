@import "mixins";
.bell-how-to-lightbox {
    .modal-body {
        @media #{$media-mobile} {
            padding: 30px 15px;
        }
    }
    .bell-how-to-lightbox-information {
        .how-to-lightbox-poster {
            @media #{$media-mobile} {
                float: unset;
            }
        }
        .how-to-lightbox-details {
            th,
            td {
                padding: 5px 0
            }
            th {
                width: 90px;
                font-weight: normal
            }
            td {
                width: auto
            }
        }
    }
    .how-to-lightbox-notification {
        display: flex;
        flex-direction: row;
        align-items: center;
        &:not(.notification-inline) {
            padding: 0 30px;
            height: 77px;
            @media #{$media-mobile} {
                height: 80px;
                padding: 15px;
            }
        }
        &.notification-inline {
            padding: 20px 30px;
            @media #{$media-mobile} {
                min-height: 80px;
                border: 1px solid #e1e1e1
            }
            &.slim {
                min-height: 60px;
            }
        }
        .notification-icon {
            display: block;
            font-size: 36px;
            flex-basis: 50px;
            &:before {
                top: 0
            }
            @media #{$media-mobile} {
                flex-basis: 80px;
            }
        }
        .notification-body {
            display: block;
        }
    }
    .bell-how-to-lightbox-order {
        counter-reset: item;
        @media #{$media-desktop} {
            .txtCurrency {
                position: absolute;
                top: -6px;
            }
        }
        .bell-how-to-day-dropdown {
            width: 210px;
        }
        .bell-how-to-time-dropdown {
            width: 130px;
        }
        ol.bell-how-to-list {
            list-style: none;
            padding-left: 20px;
            >li {
                counter-increment: item;
                margin-bottom: 10px;
                padding-left: 10px;
                &:before {
                    display: inline-block;
                    content: counter(item);
                    text-align: center;
                    margin-left: -30px;
                    margin-right: 10px;
                    padding: 1.5px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: #00549a;
                    color: white;
                }
            }
        }
    }
    .tvcsfilters-filtersContainer {
        display: none;
    }
    .bell-how-to-lightbox-noBorderNoBackground {
        border: 0;
        background-color: transparent;
    }
    .collapsable-container {
        .bell-collapsible-tray {
            max-height: 0;
            overflow: hidden;
            transition: max-height 150ms;
        }
        &.expanded {
            .bell-collapsible-tray {
                max-height: 400px;
            }
        }
    }
}

#ondemand-details-lightbox {
    .modal-title {
        @media #{$media-mobile} {
            height: 22px;
            overflow-y: hidden;
        }     
    }
}