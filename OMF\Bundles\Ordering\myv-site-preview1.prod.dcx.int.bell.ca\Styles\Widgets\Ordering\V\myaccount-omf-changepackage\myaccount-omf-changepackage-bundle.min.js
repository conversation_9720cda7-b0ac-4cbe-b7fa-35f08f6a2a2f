/*! myaccount-omf-changepackage (bundle) 1.0.0 | bwtk 2.7.2 | 2024-08-23T23:50:37.497Z */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("bwtk"),require("react"),require("react-intl"),require("redux-actions"),require("react-redux"),require("redux-observable"),require("redux"),require("react-dom"),require("rxjs"),require("react-router-dom"),require("react-hook-form"));else if("function"==typeof define&&define.amd)define(["bwtk","react","react-intl","redux-actions","react-redux","redux-observable","redux","react-dom","rxjs","react-router-dom","react-hook-form"],t);else{var n="object"==typeof exports?t(require("bwtk"),require("react"),require("react-intl"),require("redux-actions"),require("react-redux"),require("redux-observable"),require("redux"),require("react-dom"),require("rxjs"),require("react-router-dom"),require("react-hook-form")):t(e.bwtk,e.React,e.ReactIntl,e.ReduxActions,e.ReactRedux,e.ReduxObservable,e.Redux,e.ReactDOM,e.Rx,e.ReactRouterDOM,e.ReactHookForm);for(var a in n)("object"==typeof exports?exports:e)[a]=n[a]}}("undefined"!=typeof self?self:this,function(e,t,n,a,r,i,o,c,l,s,u){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=17)}([function(t,n){t.exports=e},function(e,t,n){var a;"undefined"!=typeof self&&self,a=function(e,t,n,a,r,i,o,c){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=15)}([function(t,n){t.exports=e},function(e,t,n){"use strict";n.d(t,"a",function(){return a});var a,r=n(5),i=(n.n(r),n(7));n.n(i),function(e){e.setWidgetStatus=Object(i.createAction)("SET_WIDGET_STATUS"),e.setWidgetProps=Object(i.createAction)("SET_WIDGET_PROPS"),e.getData=Object(i.createAction)("GET_WIDGET_DATA"),e.showHideLoader=Object(i.createAction)("SHOW_HIDE_LOADER"),e.errorOccured=Object(i.createAction)("ERROR_OCCURED"),e.openLightbox=Object(i.createAction)("OPEN_LIGHTBOX"),e.closeLightbox=Object(i.createAction)("CLOSE_LIGHTBOX"),e.setlightboxData=Object(i.createAction)("SET_LIGHTBOX_DATA"),e.broadcastUpdate=Object(i.createAction)("PIPE_SEND_UPDATE",function(e,t){return void 0===t&&(t=0),setTimeout(function(){return r.ServiceLocator.instance.getService(r.CommonServices.EventStream).send(e.type,e.payload)},t),e}),e.refreshTotals=Object(i.createAction)("REFRESH_TOTALS"),e.toggleTVCategoriesTray=Object(i.createAction)("TOGGLE_TV_CATEGORIES_TRAY"),e.setProductConfigurationTotal=Object(i.createAction)("SET_PRODUCT_CONFIGURATION_TOTAL"),e.continueFlow=Object(i.createAction)("FLOW_CONTINUE"),e.handleNav=Object(i.createAction)("HANDLE_NAV"),e.onContinue=Object(i.createAction)("HISTORY_ON_CONTINUE"),e.historyGo=Object(i.createAction)("HISTORY_GO"),e.historyBack=Object(i.createAction)("HISTORY_BACK"),e.historyForward=Object(i.createAction)("HISTORY_FORWARD"),e.applicationReset=Object(i.createAction)("APPLICATION_RESET"),e.applicationExit=Object(i.createAction)("APPLICATION_EXIT"),e.applicationLogout=Object(i.createAction)("APPLICATION_LOGOUT"),e.setHistoryProvider=Object(i.createAction)("SET_HISTORY_PROVIDER"),e.setAppointmentVisited=Object(i.createAction)("APPOINTMENT_PAGE_VISITED"),e.widgetRenderComplete=Object(i.createAction)("WIDGET_RENDER_COMPLETE"),e.raiseRestriction=Object(i.createAction)("RESTRICTION_OCCURRED"),e.acceptRestriction=Object(i.createAction)("RESTRICTION_ACCEPTED"),e.declineRestriction=Object(i.createAction)("RESTRICTION_DECLINED"),e.finalizeRestriction=Object(i.createAction)("RESTRICTION_CYCLE_COMPLETE"),e.clearCachedState=Object(i.createAction)("CLEAR_CACHED_STATE"),e.omniPageLoaded=Object(i.createAction)("OMNITURE_PAGE_LOADED",function(e,t){return e?{name:e,data:t}:void 0}),e.omniPageSubmit=Object(i.createAction)("OMNITURE_PAGE_SUBMIT"),e.omniModalOpen=Object(i.createAction)("OMNITURE_MODAL_OPEN")}(a||(a={}))},function(e,t,n){"use strict";function a(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,r,i=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=i.next()).done;)o.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o}t.c=function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},n.d(t,"a",function(){return i}),t.e=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n},t.b=function(e,t,n,a){var r,i=arguments.length,o=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,a);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o},t.d=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},t.f=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(a(arguments[t]));return e};var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},i=function(){return(i=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)}},function(e,t,n){"use strict";var a,r,i,o,c,l,s,u,d=n(2),m=n(9),p=n(4),f=n(1);!function(e){!function(e){e.Add="add",e.Delete="delete",e.Modify="modify",e.NoCharge="noChange",e.Remove="Remove",e.Change="Change",e.InitiallySelected="InitiallySelected",e.NewlySelected="NewlySelected",e.Removed="removed",e.NotSelected="NotSelected",e.Added="Added",e.UnSelected="UnSelected",e.Create="Create",e.NoChange="NoChange"}(e.EOfferingState||(e.EOfferingState={})),function(e){e.BaseOffering="BaseOffering",e.GroupOffering="GroupOffering",e.SingleOffering="SingleOffering"}(e.EOfferingType||(e.EOfferingType={})),function(e){e.TV_BASE_PRODUCT="TV_BASE_PRODUCT",e.ALACARTE="ALACARTE",e.MOVIE="MOVIE",e.TV="TV",e.SPECIALITY_SPORTS="SPECIALITY_SPORTS",e.ADD_ON="ADD_ON",e.INTERNATIONAL="INTERNATIONAL",e.INTERNATIONAL_COMBOS="INTERNATIONAL_COMBOS",e.INTERNATIONAL_ALACARTE="INTERNATIONAL_ALACARTE",e.BASE_PROGRAMMING="BASE_PROGRAMMING",e.SPECIALITY_CHANNELS="SPECIALITY_CHANNELS",e.OFFERS="OFFERS",e.TV_BROWSE_ALL="TV_BROWSE_ALL",e.PROMOTION="PROMOTION",e.NONE="NONE"}(e.EDIsplayGroupKey||(e.EDIsplayGroupKey={})),function(e){e.PACKAGE="BasePackage",e.COMBO="Combo",e.CHANNEL="Channel",e.NONE="None"}(e.EProductOfferingType||(e.EProductOfferingType={})),function(e){e.Delta="Delta",e.New="New",e.Current="Current",e.Default="Default"}(e.EProductOfferingGroupType||(e.EProductOfferingGroupType={})),function(e){e.TV="TV",e.Internet="Internet"}(e.ELineOfBusiness||(e.ELineOfBusiness={})),function(e){e.AM="AM",e.PM="PM",e.Evening="Evening",e.AllDay="AllDay",e.Item0810="Item0810",e.Item1012="Item1012",e.Item1315="Item1315",e.Item1517="Item1517",e.Item1719="Item1719",e.Item1921="Item1921"}(e.EAppointmentDuration||(e.EAppointmentDuration={})),function(e){e.EMAIL="Email",e.TEXT_MESSAGE="TextMessage",e.PHONE="Phone"}(e.EPreferredContactMethod||(e.EPreferredContactMethod={}))}(a||(a={})),n.d(t,"b",function(){return r}),n.d(t,"f",function(){return i}),n.d(t,"e",function(){return o}),n.d(t,"c",function(){return c}),n.d(t,"d",function(){return l}),n.d(t,"a",function(){return s}),n.d(t,"g",function(){return u}),n.d(t,"h",function(){return a}),(r||(r={})).PREVIEWMODAL="PREVIEW_MODAL",function(e){e[e.INIT=0]="INIT",e[e.RENDERED=1]="RENDERED",e[e.UPDATING=2]="UPDATING",e[e.ERROR=3]="ERROR",e[e.OUTAGERROR=4]="OUTAGERROR"}(i||(i={})),function(e){e.INTERNET="/Internet",e.TV="/TV",e.TV_Packages="/Packages",e.TV_MoviesSeries="/Movies",e.TV_Addons="/Addons",e.TV_Alacarte="/Alacarte",e.TV_International="/International",e.TV_InternationalCombos="/International/Combos",e.TV_InternationalAlacarte="/International/Alacarte",e.TV_Browse="/Browse",e.TV_Search="/Search",e.APPOINTMENT="/Appointment",e.REVIEW="/Review",e.CONFIRMATION="/Confirmation"}(o||(o={})),function(e){e.Summary="summary",e.Review="review",e.Confirmation="confirmation"}(c||(c={})),function(e){e.NAVIGATION="omf-changepackage-navigation",e.INTERNET="omf-changepackage-internet",e.TV="omf-changepackage-tv",e.APPOINTMENT="omf-changepackage-appointment",e.PREVIEW="omf-changepackage-review",e.REVIEW="omf-changepackage-review",e.CONFIRMATION="omf-changepackage-review"}(l||(l={})),function(e){e.INTERNET="Internet",e.TV="TV",e.ADDTV="AddTV",e.BUNDLE="Bundle"}(s||(s={})),function(e){!function(e){e.BELL="B",e.VIRGIN="V",e.LUCKY="L"}(e.EBrand||(e.EBrand={}));var t=function(e){function t(t,n,a){void 0===a&&(a=!1);var r=e.call(this,"object"==typeof t?t.message:t)||this;if(r.debug=!1,r.debug=a||Boolean(p.c.getCookie("debugwidget")),"object"==typeof t&&(r.stack=t.stack),"object"==typeof n)switch(!0){case Boolean(n.url):r.type="API",r.response=n;break;case Boolean(n.componentStack):r.type="widget",r.componentStack=n.componentStack}else r.type="logic";return r}return d.c(t,e),t}(Error);e.ErrorHandler=t,e.ErrorHandlerObservable=function(e){return function(n,a){var r=n.error||n;return m.Observable.merge(m.Observable.of(f.a.errorOccured(new t(e.toString(),r))),a)}},e.noSpecialCharRegex=RegExp(/^[a-zA-Z0-9]+$/i),e.emailRegex=RegExp(/^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i),e.phoneRegex=RegExp(/^[0-9]\d{2}-\d{3}-\d{4}$/i),e.hashCommaRegex=RegExp(/[\,\#]/i),e.onlyNumbersRegex=RegExp(/^[0-9]+$/i)}(u||(u={}))},function(e,t,n){"use strict";function a(e,t,n){if(!Boolean(t))return e||n;for(var a=/^\?/.test(t||""),r=(t||"").replace("?","").split("."),i=e,o=0;o<r.length;o++)i=i&&void 0!==i[r[o]]&&null!==i[r[o]]&&(a||0!==i[r[o]]&&""!==i[r[o]])?i[r[o]]:n;return i}function r(e,t){var n={};if(Object.assign(n,E,"string"==typeof t?{error:t}:t),void 0===e||n.failNullValue&&null===e||n.failZeroValue&&0===e||n.failEmptyString&&""===e||n.test(e))throw n.error;return e}function i(e,t){void 0===t&&(t=!0);var n=[];return Boolean(e)&&(h=Array.isArray(e)?e:[e]),Array.isArray(h)&&h.length&&n.push(b.a.raiseRestriction(h.pop())),t&&n.push(b.a.setWidgetStatus(f.f.RENDERED)),n}function o(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];switch(r(e,"Expected response object, but got undefined/null"),r(t,"Expected Array<ObservableInput>, but got undefined/null"),sessionStorage.setItem("omf:Initilized","yes"),!0){case!(e.data&&"object"==typeof e.data):return[b.a.errorOccured(new f.g.ErrorHandler(e.statusText,l.a({},e.data,{url:e.url}))),b.a.setWidgetStatus(f.f.RENDERED)];case!!a(e,"data.restriction",!1):return i(a(e,"data.restriction"));default:return g.Observable.concat.apply(g.Observable,l.f([i(a(e,"data.productOfferingDetail.restriction"),!1),[b.a.broadcastUpdate(b.a.setProductConfigurationTotal(a(e,"data.productOfferingDetail.productConfigurationTotal")))]],t))}}var c,l=n(2),s=n(12),u=n(17),d=n.n(u),m=n(18),p=n.n(m),f=n(3),E={error:"ReferenceError: test is not defined",failEmptyString:!0,failNullValue:!0,failZeroValue:!1,test:function(){return!1}},g=n(9),b=n(1),h=[];n.d(t,"c",function(){return c}),n.d(t,"a",function(){return r}),n.d(t,"d",function(){return a}),n.d(t,"b",function(){return o}),function(e){function t(e){return a($("#"+e).data("bs.modal"),"_isShown",!1)}function n(){var e=sessionStorage.getItem("omf:Flowtype");if(!e){var t=window.location.pathname;switch(!0){case t.indexOf("Changepackage/Internet")>0:e=f.a.INTERNET;break;case t.indexOf("Changepackage/TV")>0:e=f.a.TV;break;case t.indexOf("Add/TV")>0:e=f.a.ADDTV;break;case t.indexOf("Bundle/")>0:e=f.a.BUNDLE}}return e}e.getCurrentLocale=function(e){var t=e.locale.substr(0,2);return l.a({},e,{formats:e.formats[t],messages:e.messages[t]})},e.showLightbox=function(e){$("#"+e).modal("show")},e.isLightboxOpen=t,e.hideLightbox=function(e){t(e)&&$("#"+e).modal("hide")},e.getCookie=function(e){for(var t=e+"=",n=decodeURIComponent(document.cookie).split(";"),a=0;a<n.length;a++){for(var r=n[a];" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(t))return r.substring(t.length,r.length)}return""},e.isIOS=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,e.isIE11=!!window.MSInputMethodContext&&!!document.documentMode,e.deviceType=function(){var e=$(window).outerWidth();return{isMobile:!(e>767),isTablet:!(e>991)}},e.debounce=function(e,t,n){var a;return function(){var r=this,i=arguments,o=n&&!a;clearTimeout(a),a=setTimeout(function(){a=null,n||e.apply(r,i)},t),o&&e.apply(r,i)}},e.reducer=function(e,t){return Object(s.a)(e,t)},e.persistStateExists=function(e){return Boolean(localStorage.getItem("omf:"+e.key))},e.clearCachedState=function(e){e.forEach(function(e){return sessionStorage.removeItem("omf:"+e)})},e.persistConfig=function(e,t){return void 0===t&&(t=[]),{version:1,keyPrefix:"omf:",storage:p.a,stateReconciler:d.a,key:e,blacklist:l.f(["localization","widgetStatus","error","lightboxData","restriction"],t)}},e.getFlowType=n,e.constructPageRoute=function(e,t){switch(t=t||n()){case f.a.INTERNET:switch(e){case f.e.INTERNET:return"/Changepackage/Internet";case f.e.APPOINTMENT:return"/Changepackage/Internet/Appointment";case f.e.REVIEW:return"/Changepackage/Internet/Review";case f.e.CONFIRMATION:return"/Changepackage/Internet/Confirmation";default:return"/Changepackage"}case f.a.TV:switch(e){case f.e.TV:return"/Changepackage/TV";case f.e.APPOINTMENT:return"/Changepackage/TV/Appointment";case f.e.REVIEW:return"/Changepackage/TV/Review";case f.e.CONFIRMATION:return"/Changepackage/TV/Confirmation";default:return"/Changepackage"}case f.a.ADDTV:switch(e){case f.e.TV:return"/Add/TV";case f.e.REVIEW:return"/Add/TV/Review";case f.e.CONFIRMATION:return"/Add/TV/Confirmation";default:return"/Add"}case f.a.BUNDLE:switch(e){case f.e.INTERNET:return"/Bundle/Internet";case f.e.TV:return"/Bundle/TV";case f.e.APPOINTMENT:return"/Bundle/Internet/Appointment";case f.e.REVIEW:return"/Bundle/Review";case f.e.CONFIRMATION:return"/Bundle/Confirmation";default:return"/Bundle"}}},e.getPageRoute=function(){var e=window.location.pathname;switch(!0){case e.indexOf("Review")>0:return f.e.REVIEW;case e.indexOf("Confirm")>0:return f.e.CONFIRMATION;case e.indexOf("Appoint")>0:return f.e.APPOINTMENT;case e.indexOf("Internet")>0:return f.e.INTERNET;case e.indexOf("TV")>0:return f.e.TV}},e.getURLByFlowType=function(e){return e[sessionStorage.getItem("omf:Flowtype")||"Internet"]},e.appendRefreshOnce=function(e){var t=!sessionStorage.getItem("omf:Initilized");return(e||"")+(t?"?refreshCache=true":"")}}(c||(c={}))},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n.d(t,"Omniture",function(){return a});var a,r=n(2),i=n(0),o=(n.n(i),n(24));for(var c in n.n(o),o)["Omniture","default"].indexOf(c)<0&&function(e){n.d(t,e,function(){return o[e]})}(c);!function(e){e.Ref=function(e){return{ref:e}},function(e){e.Confirmation="C",e.Information="I",e.Warning="W",e.Error="E"}(e.EMessageType||(e.EMessageType={})),function(e){e.Technical="T",e.Business="B",e.Validation="V"}(e.EErrorType||(e.EErrorType={})),function(e){e.Browser="BR",e.Frontend="FE",e.ESB="ESB",e.Backend="BE",e.Servicegrid="SG",e.Cache="C"}(e.EApplicationLayer||(e.EApplicationLayer={})),e.Component=function(e){var t=e.children,n=r.e(e,["children"]),a=i.Children.toArray(t);if(a.length>1)throw"Omniture component may not have more then one child";return a.length>0?i.createElement(i.Fragment,null,a.map(function(e){return i.cloneElement(e,r.a({},e.props,{"data-omni":btoa(JSON.stringify(n))}))})):i.createElement("span",{"data-omni":btoa(JSON.stringify(n))})};var t=function(){},n={trackPage:t,trackFragment:t,trackAction:t,trackError:t,trackFailure:t,updateContext:t};e.useOmniture=function(){return window.OmnitureTracker&&window.OmnitureTracker.getInstance()||n}}(a||(a={}))},function(e,t,n){"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t,n,o){o.debug;var c=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(n,!0).forEach(function(t){i(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({},n);return e&&"object"===a(e)&&Object.keys(e).forEach(function(a){"_persist"!==a&&t[a]===n[a]&&(c[a]=e[a])}),c}function c(e){function t(){if(0===E.length)return g&&clearInterval(g),void(g=null);var e=E.shift(),t=c.reduce(function(t,n){return n.in(t,e,p)},p[e]);if(void 0!==t)try{f[e]=r(t)}catch(e){}else delete f[e];0===E.length&&(Object.keys(f).forEach(function(e){void 0===p[e]&&delete f[e]}),b=d.setItem(u,r(f)).catch(a))}function n(e){return!(o&&-1===o.indexOf(e)&&"_persist"!==e||i&&-1!==i.indexOf(e))}function a(e){m&&m(e)}var r,i=e.blacklist||null,o=e.whitelist||null,c=e.transforms||[],s=e.throttle||0,u="".concat(void 0!==e.keyPrefix?e.keyPrefix:w).concat(e.key),d=e.storage;r=!1===e.serialize?function(e){return e}:"function"==typeof e.serialize?e.serialize:l;var m=e.writeFailHandler||null,p={},f={},E=[],g=null,b=null;return{update:function(e){Object.keys(e).forEach(function(t){n(t)&&p[t]!==e[t]&&-1===E.indexOf(t)&&E.push(t)}),Object.keys(p).forEach(function(t){void 0===e[t]&&n(t)&&-1===E.indexOf(t)&&void 0!==p[t]&&E.push(t)}),null===g&&(g=setInterval(t,s)),p=e},flush:function(){for(;0!==E.length;)t();return b||Promise.resolve()}}}function l(e){return JSON.stringify(e)}function s(e){var t,n=e.transforms||[],a="".concat(void 0!==e.keyPrefix?e.keyPrefix:w).concat(e.key),r=e.storage;return e.debug,t=!1===e.deserialize?function(e){return e}:"function"==typeof e.deserialize?e.deserialize:u,r.getItem(a).then(function(e){if(e)try{var a={},r=t(e);return Object.keys(r).forEach(function(e){a[e]=n.reduceRight(function(t,n){return n.out(t,e,r)},t(r[e]))}),a}catch(e){throw e}})}function u(e){return JSON.parse(e)}function d(e){var t=e.storage,n="".concat(void 0!==e.keyPrefix?e.keyPrefix:w).concat(e.key);return t.removeItem(n,m)}function m(e){}function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(n,!0).forEach(function(t){E(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e,t){var n=void 0!==e.version?e.version:L,a=(e.debug,void 0===e.stateReconciler?o:e.stateReconciler),r=e.getStoredState||s,i=void 0!==e.timeout?e.timeout:V,l=null,u=!1,m=!0,p=function(e){return e._persist.rehydrated&&l&&!m&&l.update(e),e};return function(o,s){var E=o||{},g=E._persist,b=function(e,t){if(null==e)return{};var n,a,r=function(e,t){if(null==e)return{};var n,a,r={},i=Object.keys(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(E,["_persist"]);if(s.type===M){var h=!1,O=function(t,n){h||(s.rehydrate(e.key,t,n),h=!0)};if(i&&setTimeout(function(){!h&&O(void 0,new Error('redux-persist: persist timed out for persist key "'.concat(e.key,'"')))},i),m=!1,l||(l=c(e)),g)return f({},t(b,s),{_persist:g});if("function"!=typeof s.rehydrate||"function"!=typeof s.register)throw new Error("redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.");return s.register(e.key),r(e).then(function(t){(e.migrate||function(e,t){return Promise.resolve(e)})(t,n).then(function(e){O(e)},function(e){O(void 0,e)})},function(e){O(void 0,e)}),f({},t(b,s),{_persist:{version:n,rehydrated:!1}})}if(s.type===j)return u=!0,s.result(d(e)),f({},t(b,s),{_persist:g});if(s.type===R)return s.result(l&&l.flush()),f({},t(b,s),{_persist:g});if(s.type===k)m=!0;else if(s.type===P){if(u)return f({},b,{_persist:f({},g,{rehydrated:!0})});if(s.key===e.key){var v=t(b,s),y=s.payload,N=f({},!1!==a&&void 0!==y?a(y,o,v,e):v,{_persist:f({},g,{rehydrated:!0})});return p(N)}}if(!g)return t(o,s);var x=t(b,s);return x===b?o:p(f({},x,{_persist:g}))}}function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(n,!0).forEach(function(t){v(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t,n,a){a.debug;var r=O({},n);return e&&"object"===b(e)&&Object.keys(e).forEach(function(a){var i;"_persist"!==a&&t[a]===n[a]&&(null===(i=n[a])||Array.isArray(i)||"object"!==b(i)?r[a]=e[a]:r[a]=O({},r[a],{},e[a]))}),r}function N(e,t){return e.stateReconciler=void 0===e.stateReconciler?y:e.stateReconciler,g(e,Object(F.combineReducers)(t))}function x(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function T(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?T(n,!0).forEach(function(t){A(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):T(n).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function A(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function I(e,t,n){var a=n||!1,r=Object(F.createStore)(W,B,t&&t.enhancer?t.enhancer:void 0),i=function(e){r.dispatch({type:D,key:e})},o=function(t,n,i){var o={type:P,payload:n,err:i,key:t};e.dispatch(o),r.dispatch(o),a&&c.getState().bootstrapped&&(a(),a=!1)},c=S({},r,{purge:function(){var t=[];return e.dispatch({type:j,result:function(e){t.push(e)}}),Promise.all(t)},flush:function(){var t=[];return e.dispatch({type:R,result:function(e){t.push(e)}}),Promise.all(t)},pause:function(){e.dispatch({type:k})},persist:function(){e.dispatch({type:M,register:i,rehydrate:o})}});return t&&t.manualPersist||c.persist(),c}function _(e,t){return function(t,n){if(!t)return Promise.resolve(void 0);var a=t._persist&&void 0!==t._persist.version?t._persist.version:L;if(a===n)return Promise.resolve(t);if(a>n)return Promise.resolve(t);var r=Object.keys(e).map(function(e){return parseInt(e)}).filter(function(e){return n>=e&&e>a}).sort(function(e,t){return e-t});try{var i=r.reduce(function(t,n){return e[n](t)},t);return Promise.resolve(i)}catch(e){return Promise.reject(e)}}}function C(e,t){function n(e){return!(!r||-1!==r.indexOf(e))||!(!i||-1===i.indexOf(e))}var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=a.whitelist||null,i=a.blacklist||null;return{in:function(t,a,r){return!n(a)&&e?e(t,a,r):t},out:function(e,a,r){return!n(a)&&t?t(e,a,r):e}}}var w="persist:",R="persist/FLUSH",P="persist/REHYDRATE",k="persist/PAUSE",M="persist/PERSIST",j="persist/PURGE",D="persist/REGISTER",L=-1,V=5e3,F=n(13),B={registry:[],bootstrapped:!1},W=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:B,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case D:return S({},e,{registry:[].concat(x(e.registry),[t.key])});case P:var n=e.registry.indexOf(t.key),a=x(e.registry);return a.splice(n,1),S({},e,{registry:a,bootstrapped:0===a.length});default:return e}};n.d(t,"a",function(){return g}),n.d(t,!1,function(){return N}),n.d(t,"b",function(){return I}),n.d(t,!1,function(){return _}),n.d(t,!1,function(){return C}),n.d(t,!1,function(){return s}),n.d(t,!1,function(){return c}),n.d(t,!1,function(){return d}),n.d(t,!1,function(){return w}),n.d(t,!1,function(){return R}),n.d(t,!1,function(){return P}),n.d(t,!1,function(){return k}),n.d(t,!1,function(){return M}),n.d(t,!1,function(){return j}),n.d(t,!1,function(){return D}),n.d(t,!1,function(){return L})},function(e,t){e.exports=c},function(e,t,n){"use strict";n.d(t,"c",function(){return i}),n.d(t,"b",function(){return o}),n.d(t,"a",function(){return c}),t.d=function(e){return function(t){return r.createElement(c,null,function(n){return r.createElement(e,a.a({},t,n))})}};var a=n(2),r=n(0),i=(n.n(r),r.createContext({})),o=i.Provider,c=i.Consumer},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n(1);n.d(t,"Actions",function(){return a.a});var r=n(16);n.d(t,"BaseClient",function(){return r.a});var i=n(14);n.d(t,"WidgetContext",function(){return i.c}),n.d(t,"ContextProvider",function(){return i.b}),n.d(t,"Context",function(){return i.a}),n.d(t,"withContext",function(){return i.d});var o=n(21);n.d(t,"LifecycleEpics",function(){return o.a}),n.d(t,"RestricitonsEpics",function(){return o.c}),n.d(t,"ModalEpics",function(){return o.b});var c=n(3);n.d(t,"EModals",function(){return c.b}),n.d(t,"EWidgetStatus",function(){return c.f}),n.d(t,"EWidgetRoute",function(){return c.e}),n.d(t,"EReviewMode",function(){return c.c}),n.d(t,"EWidgetName",function(){return c.d}),n.d(t,"EFlowType",function(){return c.a}),n.d(t,"Models",function(){return c.g}),n.d(t,"Volt",function(){return c.h});var l=n(22);n.d(t,"Reducers",function(){return l.a});var s=n(4);n.d(t,"Utils",function(){return s.c}),n.d(t,"Assert",function(){return s.a}),n.d(t,"ValueOf",function(){return s.d}),n.d(t,"FilterRestrictionObservable",function(){return s.b});var u=n(23);n.d(t,"Components",function(){return u.a});var d=n(11);for(var m in d)["Actions","BaseClient","WidgetContext","ContextProvider","Context","withContext","LifecycleEpics","RestricitonsEpics","ModalEpics","EModals","EWidgetStatus","EWidgetRoute","EReviewMode","EWidgetName","EFlowType","Models","Volt","Reducers","Utils","Assert","ValueOf","FilterRestrictionObservable","Components","default"].indexOf(m)<0&&function(e){n.d(t,e,function(){return d[e]})}(m)},function(e,t,n){"use strict";function a(e){return t=function(){return new Promise(function(t){setTimeout(function(){return t({data:e})},350)})},c.Observable.create(function(e){t().then(function(t){e.next(t)}).catch(function(t){e.error(t)}).then(function(){e.complete()})});var t}function r(e){return(e||"").split("/").pop()}n.d(t,"a",function(){return u});var i=n(2),o=n(5),c=(n.n(o),n(9)),l=(n.n(c),n(3),n(4)),s=Boolean(l.c.getCookie("debugwidget")),u=function(e){function t(t,n){var a=e.call(this,t)||this;return a.config=n,a}return i.c(t,e),Object.defineProperty(t.prototype,"useMockData",{get:function(){return s&&void 0!==this.config.mockdata||void 0!==this.config.mockdata&&this.config.environmentVariables.useMockData},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"mockdata",{get:function(){return this.config.mockdata||{}},enumerable:!0,configurable:!0}),t.prototype.get=function(t,n,i){var o=Object(l.d)(this.mockdata,r(t)+".GET",!1);return this.useMockData&&o?a(o):e.prototype.get.apply(this,arguments)},t.prototype.put=function(t,n,i){var o=Object(l.d)(this.mockdata,r(t)+".PUT",!1);return this.useMockData&&o?a(o):e.prototype.put.apply(this,arguments)},t.prototype.post=function(t,n,i){var o=Object(l.d)(this.mockdata,r(t)+".POST",!1);return this.useMockData&&o?a(o):e.prototype.post.apply(this,arguments)},t.prototype.del=function(t,n){var i=Object(l.d)(this.mockdata,r(t)+".DELETE",!1);return this.useMockData&&i?a(i):e.prototype.del.apply(this,arguments)},t.prototype.action=function(e){switch(e.method){case"PUT":return this.put(e.href,e.messageBody);case"POST":return this.post(e.href,e.messageBody);case"DELETE":return this.del(e.href);case"GET":default:return this.get(e.href,e.messageBody)}},Object.defineProperty(t.prototype,"options",{get:function(){return{url:this.config.api.base,cache:!1,credentials:"include",headers:this.config.headers}},enumerable:!0,configurable:!0}),i.b([o.Injectable,i.d("design:paramtypes",[o.AjaxServices,Object])],t)}(o.CommonFeatures.BaseClient)},function(e,t,n){"use strict";t.__esModule=!0,t.default=function(e){return e}},function(e,t,n){"use strict";var a;t.__esModule=!0,t.default=void 0;var r=(0,((a=n(19))&&a.__esModule?a:{default:a}).default)("session");t.default=r},function(e,t,n){"use strict";t.__esModule=!0,t.default=function(e){var t=(0,r.default)(e);return{getItem:function(e){return new Promise(function(n,a){n(t.getItem(e))})},setItem:function(e,n){return new Promise(function(a,r){a(t.setItem(e,n))})},removeItem:function(e){return new Promise(function(n,a){n(t.removeItem(e))})}}};var a,r=(a=n(20))&&a.__esModule?a:{default:a}},function(e,t,n){"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(){}t.__esModule=!0,t.default=function(e){var t="".concat(e,"Storage");return function(e){if("object"!==("undefined"==typeof self?"undefined":a(self))||!(e in self))return!1;try{var t=self[e],n="redux-persist ".concat(e," test");t.setItem(n,"test"),t.getItem(n),t.removeItem(n)}catch(e){return!1}return!0}(t)?self[t]:i};var i={getItem:r,setItem:r,removeItem:r}},function(e,t,n){"use strict";var a=n(10),r=n(1),i=n(3),o=n(4),c=r.a.setWidgetStatus,l=r.a.showHideLoader,s=r.a.errorOccured,u=r.a.clearCachedState,d=function(){function e(){}return e.prototype.combineEpics=function(){return Object(a.combineEpics)(this.onWidgetStatusEpic,this.onErrorOccuredEpic,this.clearCachedStateEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(c.toString()).mergeMap(function(e){switch(e.payload){case i.f.INIT:case i.f.UPDATING:return[l(!0)];case i.f.RENDERED:case i.f.ERROR:return[l(!1)];default:return[]}})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onErrorOccuredEpic",{get:function(){return function(e){return e.ofType(s.toString()).mergeMap(function(e){return e.payload,[c(i.f.ERROR)]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"clearCachedStateEpic",{get:function(){return function(e){return e.ofType(u.toString()).do(function(e){var t=e.payload;o.c.clearCachedState(t)}).mergeMap(function(){return[]})}},enumerable:!0,configurable:!0}),e}(),m=r.a.raiseRestriction,p=r.a.acceptRestriction,f=r.a.declineRestriction,E=r.a.finalizeRestriction,g=r.a.broadcastUpdate,b=r.a.setWidgetStatus,h=r.a.historyGo,O=a.ActionsObservable.concat,v=function(){function e(e,t){this.client=e,this.restrictionModalId=t}return e.prototype.combineEpics=function(){return Object(a.combineEpics)(this.raiseRestrictionEpic,this.fadeRestrictionEpic,this.restrictionActionsEpic)},Object.defineProperty(e.prototype,"raiseRestrictionEpic",{get:function(){var e=this;return function(t){return t.ofType(m.toString()).mergeMap(function(){return o.c.showLightbox(e.restrictionModalId||"RESTRICTIONS_MODAL"),[]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fadeRestrictionEpic",{get:function(){var e=this;return function(t){return t.ofType(p.toString(),f.toString()).mergeMap(function(){return o.c.hideLightbox(e.restrictionModalId||"RESTRICTIONS_MODAL"),[]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"restrictionActionsEpic",{get:function(){var e=this;return function(t){return t.ofType(r.a.acceptRestriction.toString(),r.a.declineRestriction.toString()).filter(function(e){var t=e.payload;return Boolean(t)}).mergeMap(function(t){var n=t.payload;return n.redirectURLKey?[g(h(n.redirectURLKey))]:n.href?O([b(i.f.UPDATING)],e.client.action(n).mergeMap(function(e){return Object(o.b)(e,[Object(o.d)(e,"data.productOfferingDetail.redirectURLKey",!1)?h(Object(o.d)(e,"data.productOfferingDetail.redirectURLKey")):E(e.data)])})):[]}).catch(i.g.ErrorHandlerObservable(r.a.acceptRestriction))}},enumerable:!0,configurable:!0}),e}(),y=r.a.openLightbox,N=r.a.closeLightbox,x=r.a.setlightboxData,T=function(){function e(){}return e.prototype.combineEpics=function(){return Object(a.combineEpics)(this.onOpenLightboxEpic,this.closeLightbox)},Object.defineProperty(e.prototype,"onOpenLightboxEpic",{get:function(){return function(e){return e.ofType(y.toString()).filter(function(e){var t=e.payload;return!o.c.isLightboxOpen("string"==typeof t?t:t.lightboxId)}).do(function(e){var t=e.payload;return o.c.showLightbox("string"==typeof t?t:t.lightboxId)}).mergeMap(function(e){var t=e.payload;return[x("string"==typeof t?t:t.data)]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"closeLightbox",{get:function(){return function(e){return e.ofType(N.toString()).filter(function(e){var t=e.payload;return o.c.isLightboxOpen("string"==typeof t?t:t.lightboxId)}).do(function(e){var t=e.payload;return o.c.hideLightbox("string"==typeof t?t:t.lightboxId)})}},enumerable:!0,configurable:!0}),e}();n.d(t,"a",function(){return d}),n.d(t,"c",function(){return v}),n.d(t,"b",function(){return T})},function(e,t,n){"use strict";var a,r,i=n(7),o=n(5),c=n(1),l=n(3),s=(0,o.CommonFeatures.actionsToComputedPropertyName)(c.a),u=s.setWidgetStatus,d=s.errorOccured,m=(0,o.CommonFeatures.actionsToComputedPropertyName)(c.a),p=m.raiseRestriction,f=m.acceptRestriction,E=m.declineRestriction,g=(0,o.CommonFeatures.actionsToComputedPropertyName)(c.a),b=g.closeLightbox,h=g.setlightboxData;n.d(t,"a",function(){return a}),(r=a||(a={})).WidgetBaseLifecycle=function(e){return{localization:e.createReducer(),widgetStatus:Object(i.handleActions)((t={},t[u]=function(e,t){return t.payload},t),l.f.UPDATING),error:Object(i.handleActions)((n={},n[d]=function(e,t){return t.payload},n),null)};var t,n},r.WidgetRestrictions=function(){return{restriction:Object(i.handleActions)((e={},e[p]=function(e,t){return t.payload||e},e[f]=function(){return null},e[E]=function(){return null},e),null)};var e},r.WidgetLightboxes=function(){return{lightboxData:Object(i.handleActions)((e={},e[h]=function(e,t){return t.payload},e[b]=function(){return null},e),null)};var e}},function(e,t,n){"use strict";function a(e){return e?1===e.length?e+"0":e.slice(0,2):"00"}function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function l(e,t){return(l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u=n(0),d=n(6),m=function(e){var t=e.className,n=e.children;return u.createElement("div",{className:"brf3-panel "+t},n)};m.defaultProps={className:""};var p=function(e){var t=e.className,n=e.children;return u.createElement("div",{className:"brf3-container "+t},n)};p.defaultProps={className:""};var f=function(e){var t=e.className,n=e.children;return u.createElement("div",{className:"container liquid-container "+t},n)};f.defaultProps={className:""};var E,g=n(3),b=n(11),h=n(4),O=function(e){var t=e.details;return u.useEffect(function(){var e=0;switch(h.c.getFlowType()){case g.a.INTERNET:e=543;break;case g.a.TV:e=394}switch(t.response&&t.response.url.includes("ProductOrder/Summary")&&(e=104),t.type){case"API":b.Omniture.useOmniture().trackError({code:"API"+Object(h.d)(t,"response.status","500"),type:b.Omniture.EErrorType.Technical,layer:b.Omniture.EApplicationLayer.Backend,description:{ref:"TechnicalErrorMessage"},ajax:!0},e);break;case"widget":b.Omniture.useOmniture().trackError({code:"WIDGET400",type:b.Omniture.EErrorType.Technical,layer:b.Omniture.EApplicationLayer.Frontend,description:{ref:"TechnicalErrorMessage"}},e);break;case"logic":default:b.Omniture.useOmniture().trackError({code:"LOGIC500",type:b.Omniture.EErrorType.Technical,layer:b.Omniture.EApplicationLayer.Frontend,description:{ref:"TechnicalErrorMessage"}},e)}},[]),u.createElement(f,{className:"error margin-30-bottom"},u.createElement("div",{className:"spacer30","aria-hidden":"true"}),u.createElement(m,{className:"border bgWgite borderGray4 pad-30"},u.createElement("div",{className:"row"},u.createElement("div",{className:"inlineBlock icon-width-40 valign-middle text-center-xs"},u.createElement("span",{className:"txtRed txtSize32 icons icons-info"})),u.createElement("div",{className:"spacer15","aria-hidden":"true"}),u.createElement("div",{className:"inlineBlock pad-20-left no-pad-left-xs content-width valign-middle"},u.createElement("span",{className:"txtBlack2 block txtSize20",id:"TechnicalErrorMessage"},u.createElement(d.FormattedMessage,{id:"TECHNICAL_ERROR"})))),u.createElement("div",{className:"margin-20-top",style:{display:t.debug?"block":"none"}},function(e){switch(t.type){case"API":return u.createElement(u.Fragment,null,u.createElement("p",{className:"margin-10-top"},"API Request failed ",Object(h.d)(t,"response.status","unknown")," (",Object(h.d)(t,"response.statusText","unknown"),")"),u.createElement("p",{className:"margin-10-top",style:{wordBreak:"break-all"}},"URL: ",Object(h.d)(t,"response.url","unknown")),u.createElement("p",{className:"margin-10-top",style:{wordBreak:"break-all"}},"Response: ",JSON.stringify(Object(h.d)(t,"response.data","Null"),null," ")));case"widget":return u.createElement(u.Fragment,null,u.createElement("p",{className:"margin-10-top"},"Widget render failed"),u.createElement("p",{className:"margin-10-top"},"Component: ",u.createElement("pre",null,t.componentStack)));case"logic":default:return u.createElement("p",{className:"margin-10-top"},"General logic falure")}}(),u.createElement("p",{className:"margin-10-top"},"Stack trace: ",u.createElement("pre",null,JSON.stringify(Object(h.d)(t,"stack"),null," "))))))},v=n(2),y=n(8),N=n(1),x=function(e){return("boolean"==typeof e.when?e.when:Boolean(e.when))?e.children:e.placeholder};x.displayName="Conditional visibility container",x.defaultProps={placeholder:null},function(e){e.SHOW="show.bs.modal",e.SHOWN="shown.bs.modal",e.HIDE="hide.bs.modal",e.HIDDEN="hidden.bs.modal"}(E||(E={}));var T=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return v.c(t,e),t.prototype.componentDidMount=function(){var e=this;this.props.onShow&&$("#"+(this.props.id||this.props.modalId)).on(E.SHOW,this.props.onShow),this.props.onShown&&$("#"+(this.props.id||this.props.modalId)).on(E.SHOWN,this.props.onShown),this.props.onHide&&$("#"+(this.props.id||this.props.modalId)).on(E.HIDE,this.props.onHide),this.props.onHidden&&$("#"+(this.props.id||this.props.modalId)).on(E.HIDDEN,this.props.onHidden),this.onClose=this.onClose.bind(this),$("#"+(this.props.id||this.props.modalId)).on(E.HIDDEN,function(){var t=e.props.lightboxData&&e.props.lightboxData.relativeId&&document.getElementById(e.props.lightboxData.relativeId);t&&t.focus(),e.props.clearLightboxData()})},t.prototype.onClose=function(){h.c.isLightboxOpen(this.props.id||this.props.modalId)&&(this.props.onCloseLightbox(this.props.id||this.props.modalId),void 0!==this.props.onClose&&this.props.onClose(this.props.id||this.props.modalId))},t.prototype.render=function(){var e=this.props,t=e.id,n=e.className,a=void 0===n?"":n,r=e.size,i=e.title,o=e.containerClass,c=void 0===o?[]:o,l=e.modalId,s=e.children,d=e.onDismiss,m=e.permanent;return u.createElement("div",{id:t||l,className:"modal modal-vm fade "+a,role:"dialog",tabIndex:-1,"data-backdrop":"static","data-keyboard":"false","aria-modal":"true","aria-labelledby":(t||l)+"_label","aria-hidden":"true"},u.createElement("span",{className:"sr-only"},"dialog"),u.createElement("div",{className:"modal-dialog modal-md modal-bg modal-"+r+" bell-modal-"+r,role:"document"},u.createElement("div",{className:"modal-content noBorderRadius noBorder-xs"},u.createElement("div",{className:"modal-header bgGrayLight2 pad-30-left pad-30-right pad-25-top pad-25-bottom pad-15-left-right-xs align-items-center noBorderRadius accss-focus-outline-override-grey-bg"},u.createElement("h2",{id:(t||l)+"_label",className:"virginUltra txtBlack txtSize24 overflow-ellipsis txtSize18-xs txtUppercase sans-serif-xs lineHeight1_5 margin-b-0"},i),u.createElement(x,{when:!m},u.createElement("button",{onClick:d,id:"close_"+(t||l),type:"button",className:"no-pad close","data-dismiss":"modal","aria-label":"Close Dialog","aria-describedby":(t||l)+"_label",autoFocus:!0},u.createElement("span",{className:"volt-icon icon-big_X"})))),u.createElement("div",{id:(t||l)+"_desc",className:"modal-body pad-0 "+c.join(" ")},s))))},t.prototype.componentWillUnmount=function(){this.props.onShow&&$("#"+(this.props.id||this.props.modalId)).off(E.SHOW,this.props.onShow),this.props.onShown&&$("#"+(this.props.id||this.props.modalId)).off(E.SHOWN,this.props.onShown),this.props.onHide&&$("#"+(this.props.id||this.props.modalId)).off(E.HIDE,this.props.onHide),this.props.onHidden&&$("#"+(this.props.id||this.props.modalId)).off(E.HIDDEN,this.props.onHidden)},t.defaultProps={className:"",size:"md"},t}(u.Component),S=Object(y.connect)(function(e){return{lightboxData:e.lightboxData}},function(e){return{onCloseLightbox:function(t){return e(N.a.closeLightbox(t))},clearLightboxData:function(){return e(N.a.setlightboxData(""))}}})(T),A="",I=Object(y.connect)(function(e){var t=e.restriction;return t?v.a({},t):{}},function(e){return{onAction:function(t){switch(b.Omniture.useOmniture().trackAction({id:"restrictionLightbox",s_oAPT:{actionId:647,actionresult:0,applicationState:0},s_oBTN:t.name}),t.name){case"Cancel":e(N.a.declineRestriction(t));break;default:e(N.a.acceptRestriction(t))}},onDismiss:function(){return e(N.a.declineRestriction())}}})(function(e){var t=e.id,n=e.type,a=e.title,r=e.description,i=e.dynamicData,o=e.footerDescription,c=e.actionLinks,l=e.onDismiss,s=e.onAction,m=e.onComplete;return A=n,u.createElement(S,{modalId:t||"RESTRICTIONS_MODAL",permanent:!0,onClose:function(){l(),m&&m("close")},onShown:function(){return function(e){var t,n={actionId:104,actionresult:0,applicationState:0};switch(A){case"Warning":case"Error":n.actionresult=2,n.applicationState=2,t=b.Omniture.EMessageType.Warning;break;case"Information":default:t=b.Omniture.EMessageType.Information}b.Omniture.useOmniture().trackFragment({id:"restrictionLightbox",s_oAPT:n,s_oPRM:{ref:e+"_label"},s_oLBC:{ref:e+"_description"},s_oPLE:{content:{ref:e+"_description"},type:t}})}(t)},title:a},u.createElement("div",{className:"modal-body bgWhite"},u.createElement("div",{className:"flex"},function(e){switch(n||""){case"Error":return u.createElement("span",{className:"icon2 icon-alert-circled txtSize38 txtRed pad-15-right"});case"Information":case"Warning":return u.createElement("span",{className:"icon2 icon-alert-circled txtSize38 txtYellow pad-15-right"});default:return null}}(),u.createElement("div",{id:t+"_description"},u.createElement("p",{dangerouslySetInnerHTML:{__html:r}}),u.createElement(x,{when:Object(h.d)(i,"productList.length",!1)},u.createElement("ul",null,Object(h.d)(i,"productList",[]).map(function(e){return u.createElement("li",{className:"txtBold txtBlack"},e)}))),u.createElement(x,{when:Object(h.d)(i,"promotion.length",!1)},u.createElement("ul",null,Object(h.d)(i,"promotion",[]).map(function(e){return u.createElement("li",null,u.createElement("span",{className:"txtBold txtBlack"},e.promoName),u.createElement("br",null),u.createElement(x,{when:Boolean(e.promoExpiry)},u.createElement(d.FormattedDate,{value:e.promoExpiry,format:"yMMMd",timeZone:"UTC"},function(e){return u.createElement(d.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))}))),u.createElement(x,{when:Boolean(o)},u.createElement("p",{className:"txtBold",dangerouslySetInnerHTML:{__html:o}}))))),u.createElement(x,{when:Boolean(c&&c.length>0)},u.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),u.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs"},Object(h.d)(c,void 0,[]).map(function(e,t){return u.createElement(u.Fragment,null,u.createElement("button",{id:"ACTION_SUBMIT",className:function(e){switch(!0){case 0===t:return"btn btn-primary fill-xs";case 1===t:return"btn btn-default fill-xs";default:return"btn btn-link"}}(),onClick:function(){s(e),m&&m(e.rel)}},e.name),u.createElement("div",{className:"vSpacer15","aria-hidden":"true"}))}))))}),_=function(e){var t=e.text,n=e.maxLength,a=e.className;return u.createElement("p",{className:"ellipsis-text "+a},t.length<=n?t:t.substring(0,n)+"...")};_.defaultProps={className:""},_.displayName="EllipsisText";var C=function(e){return Boolean(e.localization&&e.localization.messages&&Object.keys(e.localization.messages).length)?u.createElement(d.IntlProvider,v.a({},e.localization,{formats:v.a({},e.localization.formats,{number:{CAD:{currency:"CAD",currencyDisplay:"symbol",style:"currency",minimumFractionDigits:2}}}),locale:e.localization.fullLocale}),u.createElement(u.Fragment,null,function(t){switch(e.widgetStatus){case g.f.RENDERED:case g.f.UPDATING:return e.children;case g.f.ERROR:return u.createElement(O,{details:e.errorHandlerProps});default:return e.placeholder}}())):null};C.defaultProps={placeholder:null};var w=Object(y.connect)(function(e){var t=e.localization,n=e.error;return{localization:t,widgetStatus:e.widgetStatus,errorHandlerProps:n}},{},function(e,t,n){return v.a({},n.propsfilter?n.propsfilter(e):e,t,n)})(C),R=Object(y.connect)(function(e){return{localization:e.localization}},function(e){return{}})(function(e){var t=e.value,n=e.className,r=e.localization,i=e.tag,o=e.tagProps,c=(e.credit,i||"span");return u.createElement(c,v.a({},o,{className:"txtCurrency "+(n||""),dangerouslySetInnerHTML:{__html:function(e,t){try{var n=String(t).split("."),r=n[0],i=n[1],o=t<0;switch(r=r.replace("-",""),Number(r)>0&&(!i||Number(i)),e.locale){default:case"en":var c=parseInt(r).toLocaleString("en");return o?"CR $"+c+"."+a(i):"$"+c+"."+a(i);case"fr":var l=parseInt(r).toLocaleString("fr");return o?"CR "+l+","+a(i)+"&nbsp;$":l+","+a(i)+"&nbsp;$"}}catch(e){return t}}(r,t)}}))});R.displayName="BellCurrency";var P=n(14),k=function(e){var t,n=e.str,a=e.prefixClassName,r=e.fractionClassName,i="",o="",c="";return 0===n.indexOf("$")?(c=(t=n.split("."))[0].substr(0,1),i=t[0].substr(1),o=t[1]):(i=(t=n.split(","))[0],o=t[1]),u.createElement(u.Fragment,null,Boolean(c)?u.createElement("sup",{className:a},c):null,i,u.createElement("sup",{className:r,"aria-hidden":!0},o),"00"!==o&&u.createElement("span",{className:"sr-only"},".",o," cents"))},M=function(e){var t=e.className,n=e.prefixClassName,a=e.fractionClassName,r=e.value,i=e.monthly;return u.createElement(d.FormattedNumber,{value:r,format:"CAD"},function(e){return u.createElement("span",{className:"formatted-currency "+t},u.createElement(k,{str:e,prefixClassName:n,fractionClassName:a}),i?u.createElement("sup",{className:n},u.createElement(d.FormattedMessage,{id:"PER_MO"},function(e){return u.createElement("span",{"aria-hidden":"true"},e)}),u.createElement(d.FormattedMessage,{id:"PER_MONTH"},function(e){return u.createElement("span",{className:"sr-only"},e)})):null)})};M.defaultProps={className:"",prefixClassName:"txtSize22",fractionClassName:"txtSize22"};var j=n(12),D=function(e){function t(){var e,n,a;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var i=arguments.length,l=new Array(i),u=0;u<i;u++)l[u]=arguments[u];return a=(e=o(t)).call.apply(e,[this].concat(l)),s(c(n=!a||"object"!==r(a)&&"function"!=typeof a?c(this):a),"state",{bootstrapped:!1}),s(c(n),"_unsubscribe",void 0),s(c(n),"handlePersistorState",function(){n.props.persistor.getState().bootstrapped&&(n.props.onBeforeLift?Promise.resolve(n.props.onBeforeLift()).finally(function(){return n.setState({bootstrapped:!0})}):n.setState({bootstrapped:!0}),n._unsubscribe&&n._unsubscribe())}),n}var n,a;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}(t,u.PureComponent),n=t,(a=[{key:"componentDidMount",value:function(){this._unsubscribe=this.props.persistor.subscribe(this.handlePersistorState),this.handlePersistorState()}},{key:"componentWillUnmount",value:function(){this._unsubscribe&&this._unsubscribe()}},{key:"render",value:function(){return"function"==typeof this.props.children?this.props.children(this.state.bootstrapped):this.state.bootstrapped?this.props.children:this.props.loading}}])&&i(n.prototype,a),t}();s(D,"defaultProps",{children:null,loading:null});var L,V;n.d(t,"a",function(){return L}),(V=L||(L={})).Error=O,V.Container=f,V.Panel=m,V.BRF3Container=p,V.Modal=S,V.RestrictionModal=I,V.ApplicationRoot=w,V.EllipsisText=_,V.Currency=M,V.BellCurrency=R,V.BrandedMessage=function(e){var t=e.id,n=v.e(e,["id"]);return u.createElement(P.a,null,function(e){var a=e.config;return u.createElement(d.FormattedMessage,v.a({},n,{id:a.environmentVariables.brand+"_"+t}))})},V.PersistGate=function(e){return u.createElement(D,{loading:null,persistor:Object(j.b)(e.store)},e.render())},V.Visible=x},function(e,t){}])},e.exports=a(n(2),n(0),n(3),n(4),n(5),n(11),n(6),n(7))},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t){e.exports=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n(22);Object.defineProperty(t,"AllSubstringsIndexStrategy",{enumerable:!0,get:function(){return a.AllSubstringsIndexStrategy}});var r=n(23);Object.defineProperty(t,"ExactWordIndexStrategy",{enumerable:!0,get:function(){return r.ExactWordIndexStrategy}});var i=n(24);Object.defineProperty(t,"PrefixIndexStrategy",{enumerable:!0,get:function(){return i.PrefixIndexStrategy}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n(25);Object.defineProperty(t,"CaseSensitiveSanitizer",{enumerable:!0,get:function(){return a.CaseSensitiveSanitizer}});var r=n(26);Object.defineProperty(t,"LowerCaseSanitizer",{enumerable:!0,get:function(){return r.LowerCaseSanitizer}})},function(e,t){e.exports=l},function(e,t){e.exports=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n(27);Object.defineProperty(t,"TfIdfSearchIndex",{enumerable:!0,get:function(){return a.TfIdfSearchIndex}});var r=n(28);Object.defineProperty(t,"UnorderedSearchIndex",{enumerable:!0,get:function(){return r.UnorderedSearchIndex}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){t=t||[];for(var n=e=e||{},a=0;a<t.length;a++)if(null==(n=n[t[a]]))return null;return n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n(29);Object.defineProperty(t,"SimpleTokenizer",{enumerable:!0,get:function(){return a.SimpleTokenizer}});var r=n(30);Object.defineProperty(t,"StemmingTokenizer",{enumerable:!0,get:function(){return r.StemmingTokenizer}});var i=n(31);Object.defineProperty(t,"StopWordsTokenizer",{enumerable:!0,get:function(){return i.StopWordsTokenizer}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=t.StopWordsMap={a:!0,able:!0,about:!0,across:!0,after:!0,all:!0,almost:!0,also:!0,am:!0,among:!0,an:!0,and:!0,any:!0,are:!0,as:!0,at:!0,be:!0,because:!0,been:!0,but:!0,by:!0,can:!0,cannot:!0,could:!0,dear:!0,did:!0,do:!0,does:!0,either:!0,else:!0,ever:!0,every:!0,for:!0,from:!0,get:!0,got:!0,had:!0,has:!0,have:!0,he:!0,her:!0,hers:!0,him:!0,his:!0,how:!0,however:!0,i:!0,if:!0,in:!0,into:!0,is:!0,it:!0,its:!0,just:!0,least:!0,let:!0,like:!0,likely:!0,may:!0,me:!0,might:!0,most:!0,must:!0,my:!0,neither:!0,no:!0,nor:!0,not:!0,of:!0,off:!0,often:!0,on:!0,only:!0,or:!0,other:!0,our:!0,own:!0,rather:!0,said:!0,say:!0,says:!0,she:!0,should:!0,since:!0,so:!0,some:!0,than:!0,that:!0,the:!0,their:!0,them:!0,then:!0,there:!0,these:!0,they:!0,this:!0,tis:!0,to:!0,too:!0,twas:!0,us:!0,wants:!0,was:!0,we:!0,were:!0,what:!0,when:!0,where:!0,which:!0,while:!0,who:!0,whom:!0,why:!0,will:!0,with:!0,would:!0,yet:!0,you:!0,your:!0};a.constructor=!1,a.hasOwnProperty=!1,a.isPrototypeOf=!1,a.propertyIsEnumerable=!1,a.toLocaleString=!1,a.toString=!1,a.valueOf=!1},function(e,t,n){"use strict";function a(e){var t=function(e,n,a){void 0===n&&(n=null),void 0===a&&(a={});var i=Object.keys(e),o=r({},a),c=n||"",l={};return i.forEach(function(n){switch(function(e){switch(!0){case/\/$/.test(e):return 1;case/:$/.test(e):return 2;default:return 0}}(n)){case 1:l=r({},l,t(e[n],""+c+n,o));break;case 2:var a=r({},o,e[n]);Object.keys(a).forEach(function(e){return l[""+c+n.slice(0,-1)+"/"+e]=a[e]});break;default:case 0:o[n]=e[n]}}),l};return t(e)}Object.defineProperty(t,"__esModule",{value:!0});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},i=n(0),o=jQuery,c=function(){function e(){this.visible=!1,this.sources=[],this.toggle(!0)}return e.prototype.toggle=function(e,t){var n=null!==e&&(void 0===e?!this.visible:e),a=t||"";if(null!==e&&Boolean(a)){var r=this.sources.indexOf(a);r>-1&&!n?this.sources.splice(r,1):-1===r&&n&&this.sources.push(a),this.visible=this.sources.length>0}else this.visible=n,this.sources=[];this.visible?o("#responsiveLoader").show():o("#responsiveLoader").hide()},e}(),l={"loader.staticWidgetMappings":{"omf-changepackage-components":{factory:function(){return n(1)},namespace:"Ordering"},"omf-changepackage-navigation":{factory:function(){return n(18)},namespace:"Ordering"},"omf-changepackage-internet":{factory:function(){return n(19)},namespace:"Ordering"},"omf-changepackage-tv":{factory:function(){return n(20)},namespace:"Ordering"},"omf-changepackage-appointment":{factory:function(){return n(34)},namespace:"Ordering"},"omf-changepackage-review":{factory:function(){return n(36)},namespace:"Ordering"}}};n.d(t,"loader",function(){return s}),t.initialize=function(e,t,n){void 0===e&&(e=[]),s.toggle(!0),Object(i.Init)(Object.assign({},a(t),l));var o=i.ServiceLocator.instance,c=u.store=o.getService(i.CommonServices.Store),d=u.localization=o.getService(i.CommonServices.Localization);u.pipe=o.getService(i.CommonServices.EventStream),u.widgets=e,d.preloadLocaleData(e.reduce(function(e,t){return r({},e,((n={})[t.widget]=t.localization,n));var n},{})).then(function(){e.filter(function(e){return Boolean(e.container)}).forEach(function(e){var t=e.container,n=e.widget,a=e.props,r=void 0===a?{}:a,o=document.getElementById(t);Object(i.RenderWidget)(n,o,r||{})}),c.createGlobalActionListener(function(t){var a=t.type,r=t.payload,i=t.meta;switch(a){case"FLOW_CONTINUE":case"HISTORY_BACK":case"HISTORY_FORWARD":case"HISTORY_GO":s.toggle(!0);break;case"SHOW_HIDE_LOADER":s.toggle(r,i.source);break;case"ERROR_OCCURED":case"OPEN_LIGHTBOX":s.toggle(!1);break;case"LOCALIZATION_ERROR":var o=document.getElementById("errorContainer");s.toggle(!1),e.filter(function(e){return Boolean(e.container)}).forEach(function(e){var t=e.container;document.getElementById(t).style.display="none"}),o.style.display="block"}n&&n(t)})}).catch(function(e){c.dispatch({type:"LOCALIZATION_ERROR",payload:new i.ApplicationError("Localization request failed")})})},t.store=function(){return u.store},t.localization=function(){return u.localization},t.pipe=function(){return u.pipe},n.d(t,"destroy",function(){return d});var s=new c,u={widgets:[],store:null,localization:null,pipe:null},d=i.DestroyAll},function(e,t,n){var a;"undefined"!=typeof self&&self,a=function(e,t,n,a,r,i,o,c,l,s){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=8)}([function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t){e.exports=c},function(e,t,n){"use strict";function a(e,t){function n(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t,n,a){var r,i=arguments.length,o=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,a);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(){return b}Object.defineProperty(t,"__esModule",{value:!0});var c={};n.d(c,"setFlowType",function(){return le}),n.d(c,"setSummaryTotals",function(){return se}),n.d(c,"checkRestrictions",function(){return ue});var l,s=function(e,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},u=function(){return(u=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},d=n(2),m=n(0),p=n(1),f=n(6),E=n(3),g=n(9),b=null,h=null,O=!1,v=((l={})[m.EFlowType.INTERNET]=["/Changepackage/Internet","/Changepackage/Internet/Appointment","/Changepackage/Internet/Review","/Changepackage/Internet/Confirmation"],l[m.EFlowType.TV]=["/Changepackage/TV","/Changepackage/TV/Review","/Changepackage/TV/Confirmation"],l[m.EFlowType.ADDTV]=["/Add/TV","/Add/TV/Review","/Add/TV/Confirmation"],l[m.EFlowType.BUNDLE]=["/Bundle/Internet","/Bundle/Internet/Appointment","/Bundle/TV","/Bundle/Review","/Bundle/Confirmation"],l),y=n(4),N=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||d.ServiceLocator.instance.getService(d.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString(m.EWidgetName.NAVIGATION,e,t.locale):e},t.Instance=null,n=r([d.Injectable],t);var n}(d.CommonFeatures.BaseLocalization),x=m.Components.Modal,T="APPLICATION_LOGOUT",S=Object(E.connect)(function(e){return{}},function(e){return{onContinueClick:function(){m.Omniture.useOmniture().trackAction({id:"logoutLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_LOGOUT_CONTINUE"}}),e(m.Actions.applicationLogout())},closeLightbox:function(){m.Omniture.useOmniture().trackAction({id:"logoutLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_LOGOUT_CLOSE"}}),e(m.Actions.closeLightbox(T))}}})(function(e){var t=e.onContinueClick,n=e.closeLightbox;return p.createElement(x,{modalId:T,onShown:function(){m.Omniture.useOmniture().trackFragment({id:"logoutLightbox",s_oAPT:{actionId:104},s_oPRM:N.getLocalizedString("APPLICATION_LOGOUT_TITLE"),s_oLBC:N.getLocalizedString("APPLICATION_LOGOUT_TEXT")})},title:p.createElement(y.FormattedMessage,{id:"APPLICATION_LOGOUT_TITLE"})},p.createElement("div",{className:"pad-30"},p.createElement(y.FormattedHTMLMessage,{id:"APPLICATION_LOGOUT_TEXT"})),p.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),p.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},p.createElement("button",{id:"APP_LOGOUT_CONTINUE",className:"btn btn-primary fill-xs",onClick:t},p.createElement(y.FormattedMessage,{id:"APPLICATION_LOGOUT_CONTINUE"})),p.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),p.createElement("button",{id:"APP_LOGOUT_CLOSE",className:"btn btn-default fill-xs",onClick:n},p.createElement(y.FormattedMessage,{id:"APPLICATION_LOGOUT_CLOSE"}))))}),A=Object(E.connect)(function(e){return{}},function(e){return{onLogoutClick:function(t){return e(m.Actions.openLightbox({lightboxId:T,data:{relativeId:t}}))}}})(function(e){var t=e.onLogoutClick;return p.createElement(m.Context,null,function(e){var n=e.config.linkURL;return p.createElement("footer",{className:"accss-focus-outline-override-grey-bg"},p.createElement("a",{id:"skipToMain",href:"#mainContent",className:"skip-to-main-link"},p.createElement(y.FormattedMessage,{id:"Skip to main content"})),p.createElement("div",{className:"simplified-footer pad-15-top pad-30-top-xs container container-fluid flex flex-justify-space-between flexCol-xs pad-20-left pad-20-right"},p.createElement("div",{className:"flex-vCenter"},p.createElement("ul",{className:"footer-links flex list-unstyled no-margin flexCol-xs"},p.createElement("li",{className:"width-100-percent-xs noBorder"},p.createElement("a",{id:"privacy",href:n.privacyURL,className:"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray"},p.createElement(y.FormattedMessage,{id:"Privacy"}))),p.createElement("li",{className:"width-100-percent-xs noBorder"},p.createElement("a",{id:"legal_context",href:n.legalURL,className:"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray"},p.createElement(y.FormattedMessage,{id:"Legal"}))),p.createElement("li",{className:"width-100-percent-xs"},p.createElement("a",{id:"feedback",href:n.feedbackURL,className:"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray"},p.createElement(y.FormattedMessage,{id:"FEEDBACK"})))),p.createElement("div",{className:"spacer15","aria-hidden":"true"}),p.createElement("div",{className:"txtSize14 txtCenter-xs "},p.createElement(y.FormattedMessage,{id:"Copyright"}))),p.createElement("div",{className:"flex flexCol-xs"},p.createElement("span",{className:"spacer30 d-block d-sm-none","aria-hidden":"true"}),p.createElement("button",{id:"footerLogout",onClick:function(){return t("footerLogout")},className:"btn btn-secondary flex middle-align-self line-height-1",type:"button"},p.createElement(y.FormattedMessage,{id:"Log out"})),p.createElement("div",{className:"vSpacer30 hidden-m"}),p.createElement("span",{className:"spacer30 d-block d-sm-none","aria-hidden":"true"}),p.createElement("div",{className:"width-100-percent-xs txtCenter-xs"},p.createElement("img",{className:"img-responsive logo-footer",role:"link",tabIndex:0,src:n.entrustIMGURL,alt:"Entrust label"})))),p.createElement("div",{className:"spacer40","aria-hidden":"true"}))})}),I=m.Components.Modal,_=Object(E.connect)(function(e){return{}},function(e){return{onContinueClick:function(){m.Omniture.useOmniture().trackAction({id:"exitLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_EXIT_CONTINUE"}}),e(m.Actions.applicationExit())},closeLightbox:function(){m.Omniture.useOmniture().trackAction({id:"exitLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"APP_EXIT_CLOSE"}}),e(m.Actions.closeLightbox("APPLICATION_EXIT"))}}})(function(e){var t=e.onContinueClick,n=e.closeLightbox;return p.createElement(I,{modalId:"APPLICATION_EXIT",onShown:function(){m.Omniture.useOmniture().trackFragment({id:"exitLightbox",s_oAPT:{actionId:104},s_oPRM:N.getLocalizedString("APPLICATION_EXIT_TITLE"),s_oLBC:N.getLocalizedString("APPLICATION_EXIT_TEXT")})},title:p.createElement(y.FormattedMessage,{id:"APPLICATION_EXIT_TITLE"})},p.createElement("div",{id:"APPLICATION_EXIT_TEXT",className:"pad-30 pad-15-left-right-xs"},p.createElement(y.FormattedHTMLMessage,{id:"APPLICATION_EXIT_TEXT"})),p.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},p.createElement("button",{id:"APP_EXIT_CLOSE",className:"btn btn-primary fill-xs",onClick:n},p.createElement(y.FormattedMessage,{id:"APPLICATION_EXIT_CLOSE"})),p.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),p.createElement("button",{id:"APP_EXIT_CONTINUE",className:"btn btn-default fill-xs",onClick:t},p.createElement(y.FormattedMessage,{id:"APPLICATION_EXIT_CONTINUE"}))))}),C=Object(E.connect)(function(e){return{routes:e.routes}},function(e){return{onBackClick:function(t,n){t.preventDefault(),t.stopPropagation(),e(m.Actions.historyBack(n))},onExitClick:function(t,n){e(n?m.Actions.applicationExit():m.Actions.openLightbox({lightboxId:"APPLICATION_EXIT",data:{relativeId:t}}))}}})(function(e){var t=e.flowType,n=e.location.pathname,a=e.onBackClick,r=e.onExitClick,i=m.Utils.getPageRoute()===m.EWidgetRoute.CONFIRMATION,o=function(e){switch(!0){case e.indexOf("Review")>0:return"REVIEW";case e.indexOf("Confirm")>0:return"CONFIRMATION";case e.indexOf("Appoint")>0:return"APPOINTMENT";case e.indexOf("Internet")>0:return"INTERNET";case e.indexOf("TV")>0:return"TV";default:return""}}(n)+"_AT_"+(t||"").replace(/[\W_]+/g,"").toUpperCase()+([m.EWidgetRoute.REVIEW,m.EWidgetRoute.CONFIRMATION].indexOf(m.Utils.getPageRoute())>-1&&O?"+":""),c="/ordering/changepackage/internet/review"===window.location.pathname.toLowerCase()?"Back to step 1":"/ordering/changepackage/internet"===window.location.pathname.toLowerCase()?"Back to Overview":"Back";return p.createElement(m.Context,null,function(e){var t=e.config.linkURL;return p.createElement("header",{className:"bgPrimary simplified-header container-flex-box-wrap",role:"banner"},p.createElement("div",{className:"container container-fluid container-flex-box-wrap flex-justify-space-between accss-focus-outline-override-red-bg"},p.createElement("div",{className:"page-back-button container-flex-box-wrap fullHeight align-items-center flex"},!i&&p.createElement("a",{id:"back",onClick:function(e){return a(e,"back")},"aria-label":c,href:t.exitURL,className:"responsive-simplified-header-back txtDecorationNoneHover txtWhite"},p.createElement("span",{className:"virgin-icon icon-Left_arrow txtSize15 inlineBlock","aria-hidden":"true"}),p.createElement("span",{className:"txtSize14 hidden-m margin-10-left txtDecoration_hover"},p.createElement(y.FormattedMessage,{id:"Back"})))),p.createElement("div",{className:"page-heading container-flex-box-wrap fullHeight overflow-ellipsis-parent container-flex-grow-fill justify-center","aria-live":"assertive"},p.createElement("div",{className:"middle-align-self overflow-ellipsis"},p.createElement("h1",{className:"virginUltraReg txtWhite no-margin overflow-ellipsis txtCenter txtSize22 txtUppercase"},p.createElement(y.FormattedHTMLMessage,{id:"PAGE_NAME_FOR_"+o})),!i&&p.createElement(y.FormattedHTMLMessage,{id:"STEP_COUNT_FOR_"+o},function(e){return p.createElement(m.Components.Visible,{when:!!e&&e!=="STEP_COUNT_FOR_"+o},p.createElement("p",{className:"txtWhite txtSize14 no-margin-bottom sans-serif txtCenter header-steps",dangerouslySetInnerHTML:{__html:e}}))}))),p.createElement("div",{className:"page-right-button flex-vCenter d-none d-md-flex d-lg-flex"},p.createElement("button",{id:"exit",onClick:function(){return r("exit",i)},"data-href":t.exitURL,className:"btn btn-secondary-inverted margin-5-right"},p.createElement(y.FormattedMessage,{id:"EXIT_CTA"})))))})}),w=m.Components.Modal,R=Object(E.connect)(function(e){return{}},function(e){return{onContinueClick:function(){return e(m.Actions.applicationReset())},closeLightbox:function(){return e(m.Actions.closeLightbox("APPLICATION_RESET"))}}})(function(e){var t=e.onContinueClick,n=e.closeLightbox;return p.createElement(w,{modalId:"APPLICATION_RESET",onShown:function(){m.Omniture.useOmniture().trackFragment({id:"exitLightbox",s_oAPT:{actionId:104},s_oPRM:N.getLocalizedString("APPLICATION_RESET_TITLE"),s_oLBC:N.getLocalizedString("APPLICATION_RESET_TEXT")})},title:p.createElement(y.FormattedMessage,{id:"APPLICATION_RESET_TITLE"})},p.createElement("div",{className:"pad-30"},p.createElement(y.FormattedHTMLMessage,{id:"APPLICATION_RESET_TEXT"})),p.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),p.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},p.createElement("button",{id:"APP_RESET_CONTINUE",className:"btn btn-primary fill-xs",onClick:t},p.createElement(y.FormattedMessage,{id:"APPLICATION_RESET_CONTINUE"})),p.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),p.createElement("button",{id:"APP_RESET_CLOSE",className:"btn btn-default fill-xs",onClick:n},p.createElement(y.FormattedMessage,{id:"APPLICATION_RESET_CLOSE"}))))}),P=m.Components.Modal,k=m.EModals.PREVIEWMODAL,M=Object(E.connect)(function(e){var t=e.lightboxData,n=e.summary;return{summaryAction:Object(m.ValueOf)(n,"summaryAction",null),isContinueEnabled:!!Object(m.ValueOf)(n,"nextAction",!1),isOpen:t&&t.lightbox===k}},function(e){return{onContinueClick:function(){e(m.Actions.closeLightbox(k)),e(m.Actions.broadcastUpdate(m.Actions.onContinue()))},closeLightbox:function(){m.Omniture.useOmniture().trackAction({id:"previewLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"BUTTON_CLOSE_"+k}}),e(m.Actions.closeLightbox(k))},dismissLightbox:function(){m.Omniture.useOmniture().trackAction({id:"previewLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"}),e(m.Actions.setlightboxData(""))}}})(function(e){var t=e.isOpen,n=e.summaryAction,a=e.isContinueEnabled,r=e.onContinueClick,i=e.closeLightbox,o=e.dismissLightbox;return p.createElement(P,{modalId:k,className:"do-not-center-in",onDismiss:o,title:p.createElement(y.FormattedMessage,{id:k+"_TITLE"})},t&&p.createElement(d.WidgetLoader,{widget:m.EWidgetName.PREVIEW,mode:m.EReviewMode.Summary,summaryAPI:Object(m.ValueOf)(n,"href")}),p.createElement("div",{className:"spacer30","aria-hidden":"true"}),p.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),p.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},p.createElement("button",{id:"BUTTON_CONTINUE_"+k,disabled:!a,className:"btn btn-primary fill-xs",onClick:r},p.createElement(y.FormattedMessage,{id:k+"_CONTINUE"})),p.createElement("span",{className:"vSpacer15","aria-hidden":"true"}),p.createElement("button",{id:"BUTTON_CLOSE_"+k,className:"btn btn-secondary fill-xs",onClick:i},p.createElement(y.FormattedMessage,{id:k+"_CLOSE"}))))}),j=function(e){var t=e.title;return p.useEffect(function(){sessionStorage.setItem("omf:hasAppointmentRoute","yes"),document.title=t+" - "+document.title},[]),p.createElement(d.WidgetLoader,{widget:m.EWidgetName.APPOINTMENT})},D=function(e){var t=e.title;return document.title=t+" - "+document.title,p.createElement(d.WidgetLoader,{widget:m.EWidgetName.CONFIRMATION,mode:m.EWidgetRoute.CONFIRMATION})},L=function(e){var t=e.title;return document.title=t+" - "+document.title,p.createElement(d.WidgetLoader,{widget:m.EWidgetName.INTERNET})},V=function(e){var t=e.title;return document.title=t+" - "+document.title,p.createElement(d.WidgetLoader,{widget:m.EWidgetName.REVIEW,mode:m.EWidgetRoute.REVIEW})},F=function(e){var t=e.title;return document.title=t+" - "+document.title,p.createElement(d.WidgetLoader,{widget:m.EWidgetName.TV})},B=m.Components.Visible,W=m.Components.Currency,G=function(e){var t=e.summary,n=e.isContinueEnabled,a=e.onContinueClick;return p.createElement(p.Fragment,null,p.createElement(B,{when:Object(m.ValueOf)(t,"TV",!1)},p.createElement(B,{when:Object(m.ValueOf)(t,"TV.currentPrice")},p.createElement("div",{className:"virgin-menu-dockbar flexStatic flexJustifyBetween-sm bgBlack"},p.createElement("p",{className:"noMargin txtSize12 flexGrow txtWhite"},p.createElement(y.FormattedMessage,{id:"CurrentTV"})),p.createElement("span",{className:"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite"},p.createElement(W,{value:Object(m.ValueOf)(t,"TV.currentPrice.price",0),monthly:!0,prefixClassName:"txtSize16 txtUppercase",fractionClassName:"txtSize16"})))),p.createElement(B,{when:Object(m.ValueOf)(t,"TV.newPrice")},p.createElement("div",{className:"virgin-menu-dockbar flexStatic bgOrange flexJustifyBetween-sm"},p.createElement("p",{className:"noMargin txtSize12 flexGrow txtWhite"},p.createElement(y.FormattedMessage,{id:"NewTV"})),p.createElement("span",{className:"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite"},p.createElement(W,{value:Object(m.ValueOf)(t,"TV.newPrice.price",0),monthly:!0,prefixClassName:"txtSize16 txtUppercase",fractionClassName:"txtSize16"}))))),p.createElement("div",{className:"flexBlock preview-btn bgBlack flexJustify pad-25-top pad-25-bottom"},p.createElement("button",{onClick:a,disabled:!n,id:"mobileTVContinue",className:"btn btn-primary txtWhite txtSize16 relative "+(n?"":"disabled")},p.createElement(y.FormattedMessage,{id:"Review changes"}),p.createElement(B,{when:Object(m.ValueOf)(t,"productOfferingCount",!1)},p.createElement("span",{className:"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification"},p.createElement("span",null,Object(m.ValueOf)(t,"productOfferingCount",0)))))))},U=m.Components.Visible,z=m.Components.Currency,H=function(){var e="TOOLTIP_"+(m.Utils.getPageRoute()||"").replace("/","").toUpperCase(),t=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,r,i=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=i.next()).done;)o.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o}(p.useState(!1),2),n=t[0],a=t[1];return p.createElement("button",{id:"tierContinue",onClick:function(){return a(!n)},onKeyUp:function(){return a(!n)},onMouseOver:function(){return a(!0)},onMouseOut:function(){return a(!1)},className:"btn btn-primary fill-xs tooltip-interactive relative alignIconWithText disabled","aria-disabled":"true"},p.createElement(y.FormattedMessage,{id:"Continue"}),p.createElement(U,{when:n},p.createElement(y.FormattedMessage,{id:e},function(t){return Boolean(t)&&t!==e?p.createElement("div",{className:"tooltip fade bs-tooltip-top show",role:"tooltip",id:"tooltip504192",style:{position:"absolute",width:"240px",top:"-110px",left:"50%",transform:"translateX(-50%)"}},p.createElement("div",{className:"arrow",style:{left:"50%"}}),p.createElement("div",{className:"tooltip-inner",style:{width:"100%"}},p.createElement("div",{className:"flexRow bgWhite txtBlack"},p.createElement("div",{className:"olt-icon icon-warning txtSize22 margin-10-right"},p.createElement("span",{className:"volt-icon path1 yellowIcon"}),p.createElement("span",{className:"volt-icon path2"})),p.createElement("div",{className:"margin-5-top"},p.createElement(y.FormattedMessage,{id:e}))))):p.createElement("span",null)})))},K=function(e){var t=e.label,n=e.price,a=e.regularPrice,r=e.className,i=void 0===r?"":r;return p.createElement("div",{className:"virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify "+i},p.createElement("p",{className:"noMargin txtSize12 txtWhite"},t),p.createElement("div",null,p.createElement(U,{when:Object(m.ValueOf)(a,void 0,!1)},p.createElement("p",{className:"noMargin txtSize12 txtWhite"},p.createElement(y.FormattedMessage,{id:"Price after credit"}))),p.createElement("span",{className:"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite"},p.createElement(z,{value:Object(m.ValueOf)(n,"price",0),monthly:!0,prefixClassName:"txtSize16 txtUppercase",fractionClassName:"txtSize16"})),p.createElement(U,{when:Object(m.ValueOf)(a,void 0,!1)},p.createElement("p",{className:"noMargin txtSize12 txtWhite"},p.createElement(y.FormattedMessage,{id:"Current price",values:Object(m.ValueOf)(a,void 0)})))))},q=Object(E.connect)(function(e){var t=e.summary;return{summary:t,isContinueEnabled:!!Object(m.ValueOf)(t,"nextAction",!1)}},function(e){return{onSummaryClick:function(t){return e(m.Actions.openLightbox({lightboxId:k,data:{relativeId:t,lightbox:k}}))},onCancelClick:function(t){return e(m.Actions.openLightbox({lightboxId:"APPLICATION_RESET",data:{relativeId:t}}))},onCategoriesClick:function(){return e(m.Actions.broadcastUpdate(m.Actions.toggleTVCategoriesTray()))},onContinueClick:function(){return e(m.Actions.broadcastUpdate(m.Actions.onContinue()))},handleNav:function(){return e(m.Actions.broadcastUpdate(m.Actions.handleNav(!0)))}}})(function(e){var t=e.summary,n=(e.flowType,e.location),a=e.isContinueEnabled,r=e.onSummaryClick,i=e.onCancelClick,o=e.onContinueClick,c=(e.onCategoriesClick,e.handleNav),l=window.location.href.indexOf(m.EWidgetRoute.TV)>-1,s=!(n.pathname.indexOf(m.EWidgetRoute.REVIEW)>0||n.pathname.indexOf(m.EWidgetRoute.CONFIRMATION)>0),u=document.getElementById("tv-sedebar-summary-portal");return p.createElement(U,{when:s},p.createElement("nav",null,p.createElement("div",{className:"virgin-dockbar col1 scrollTop"},p.createElement("div",{className:"nopad bgBlack accss-focus-outline-override-black-bg",style:{opacity:"92%"}},p.createElement("div",{className:"virgin-dockbar-panel flexRow block-xs container container-fluid no-pad-xs"},p.createElement("div",{className:"flexRow block-xs"},p.createElement(U,{when:Object(m.ValueOf)(t,"Internet",!1)},p.createElement(U,{when:Object(m.ValueOf)(t,"?Internet.currentPrice")},p.createElement(K,{label:p.createElement(y.FormattedMessage,{id:"Current"}),price:Object(m.ValueOf)(t,"?Internet.currentPrice"),regularPrice:Object(m.ValueOf)(t,"?Internet.regularCurrentPrice")})),p.createElement(U,{when:Object(m.ValueOf)(t,"?Internet.newPrice")},p.createElement(K,{label:p.createElement(y.FormattedMessage,{id:"NewInternet"}),className:"bgOrange",price:Object(m.ValueOf)(t,"?Internet.newPrice"),regularPrice:Object(m.ValueOf)(t,"?Internet.regularNewPrice")}))),p.createElement(U,{when:Object(m.ValueOf)(t,"TV",!1)},p.createElement(U,{when:Object(m.ValueOf)(t,"?TV.currentPrice")},p.createElement(K,{label:p.createElement(y.FormattedMessage,{id:"CurrentTV"}),price:Object(m.ValueOf)(t,"?TV.currentPrice"),regularPrice:Object(m.ValueOf)(t,"?TV.regularCurrentPrice")})),p.createElement(U,{when:Object(m.ValueOf)(t,"?TV.newPrice")},p.createElement(K,{label:p.createElement(y.FormattedMessage,{id:"NewTV"}),className:"bgOrange",price:Object(m.ValueOf)(t,"?TV.newPrice"),regularPrice:Object(m.ValueOf)(t,"?TV.regularNewPrice")})))),p.createElement(U,{when:Object(m.ValueOf)(t,"summaryAction")},p.createElement("div",{className:"virgin-dockbar-row flexCol-xs preview-btn"},p.createElement("button",{id:"orderReview",onClick:function(){return r("orderReview")},className:"btn btn-link txtUnderline txtWhite txtSize12 pad-10-left accss-changeplan-preview"},p.createElement(y.FormattedMessage,{id:"Preview"})))),p.createElement(U,{when:Object(m.ValueOf)(t,"resetAction")},p.createElement("div",{className:"flexStatic flexCol-xs preview-btn"},p.createElement("button",{id:"orderCancel",onClick:function(){return i("orderCancel")},className:"btn btn-link txtWhite txtUnderline txtSize12 dockbar-cancel"},p.createElement(y.FormattedMessage,{id:"Reset"})))),p.createElement("div",{className:"spacer10 visible-m","aria-hidden":"true"}),p.createElement("div",{className:"flexGrow"}),p.createElement("div",{className:"flex dockbar-buttons continue-button fullWidth-xs align-items-center flexCol-xs bgBlack"},p.createElement(U,{when:l},p.createElement("div",{className:"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs d-md-none"},p.createElement("button",{id:"TV_CATEGORIES",className:"btn btn-secondary-inverted p-2",onClick:c},p.createElement(y.FormattedMessage,{id:"TV_CATEGORIES"})))),p.createElement("div",{className:"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs"},p.createElement(U,{when:a,placeholder:p.createElement(H,null)},p.createElement("button",{onClick:o,id:"tier_Continue",className:"btn btn-primary fill-xs tooltip-interactive alignIconWithText pointer relative p-2"},p.createElement(y.FormattedMessage,{id:"Continue"}),p.createElement(U,{when:l&&Object(m.ValueOf)(t,"productOfferingCount",!1)},p.createElement("span",{className:"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification"},p.createElement("span",null,Object(m.ValueOf)(t,"productOfferingCount",0))))))))))),u&&f.createPortal(p.createElement(G,{summary:t,isContinueEnabled:a,onContinueClick:o}),u)))}),X=m.Components.RestrictionModal,Y=m.Actions.errorOccured,$=m.Actions.widgetRenderComplete,J=function(e){var t=Object(g.useLocation)(),n=p.useContext(m.WidgetContext).config.environmentVariables;return p.createElement(p.Fragment,null,p.createElement("style",null,"\n                .brf .modal.fade.do-not-center-in .modal-dialog {\n                    transform: none!important;\n                    top: auto;\n                }\n                .brf .modal.fade.do-not-center-in .modal-content {\n                    min-height: 460px;\n                }\n            "),p.createElement(C,u({},e,{location:t})),p.createElement(g.Route,{path:"*",component:function(e){return function(e){b=b||e}(e.history),null}}),p.createElement(g.Switch,null,p.createElement(g.Route,{path:["/Changepackage/Internet/Appointment","/Bundle/Internet/Appointment"]},p.createElement(j,{title:"Appointment"})),p.createElement(g.Route,{path:["/Changepackage/Internet/Review","/Changepackage/TV/Review","/Add/TV/Review","/Bundle/Review"]},p.createElement(V,{title:"Review"})),p.createElement(g.Route,{path:["/Changepackage/Internet/Confirmation","/Changepackage/TV/Confirmation","/Add/TV/Confirmation","/Bundle/Confirmation"]},p.createElement(D,{title:"Confirmation"})),p.createElement(g.Route,{path:["/Changepackage/Internet","/Bundle/Internet"]},p.createElement(L,{title:"Internet"})),p.createElement(g.Route,{path:["/Changepackage/TV","/Bundle/TV","/Add/TV"]},p.createElement(F,{title:"fr"===n.language?"Configurez vos service - TV":"Set up your service - TV"})),p.createElement(g.Route,{path:"*"},p.createElement(g.Redirect,{to:e.defaultRoute}))),p.createElement(q,u({},e,{location:t})),p.createElement(A,null),p.createElement(M,null),p.createElement(X,{id:"NAVIGATION_RESTRICTION_MODAL"}),p.createElement(R,null),p.createElement(_,null),p.createElement(S,null),p.createElement("div",{className:"spacer20 hidden-xs hidden-m","aria-hidden":"true"}),p.createElement("div",{className:"spacer60 hidden-xs hidden-m","aria-hidden":"true"}))},Z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.componentDidCatch=function(e){this.props.onErrorEncountered(e)},t.prototype.componentDidMount=function(){this.props.widgetRenderComplete("omf-changepackage-navigation")},t.prototype.render=function(){return p.createElement(g.BrowserRouter,{basename:"/Ordering"},p.createElement(J,u({},this.props)))},t}(p.Component),Q=Object(E.connect)(function(e){return{defaultRoute:e.defaultRoute,flowType:e.flowType}},function(e){return{onErrorEncountered:function(t){return e(Y(t))},widgetRenderComplete:function(){return e($())}}})(Z),ee=m.Components.ApplicationRoot,te=function(){return p.createElement(ee,null,p.createElement(Q,null))},ne=d.CommonFeatures.BaseConfig,ae=d.CommonFeatures.configProperty,re=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),r([ae(""),i("design:type",String)],t.prototype,"flowType",void 0),r([ae({}),i("design:type",Object)],t.prototype,"environmentVariables",void 0),r([ae({}),i("design:type",Object)],t.prototype,"mockdata",void 0),r([ae({}),i("design:type",Object)],t.prototype,"headers",void 0),r([ae({base:"http://127.0.0.1:8881"}),i("design:type",Object)],t.prototype,"api",void 0),r([ae(""),i("design:type",String)],t.prototype,"defaultRoute",void 0),r([ae({}),i("design:type",Object)],t.prototype,"linkURL",void 0),r([d.Injectable],t)}(ne),ie=n(10),oe=n(7),ce=n(5),le=Object(oe.createAction)("SET_FLOW_TYPE",function(e){switch(sessionStorage.setItem("omf:Flowtype",e),e){case m.EFlowType.INTERNET:m.Omniture.useOmniture().updateContext({s_oSS2:"Internet"});break;case m.EFlowType.TV:m.Omniture.useOmniture().updateContext({s_oSS2:"Change package"});break;case m.EFlowType.ADDTV:m.Omniture.useOmniture().updateContext({s_oSS2:"Add TV"});break;case m.EFlowType.BUNDLE:m.Omniture.useOmniture().updateContext({s_oSS2:"Bundle"})}return e}),se=Object(oe.createAction)("SET_FLOW_SUMMARY_TOTALS",function(e){var t=Object(m.ValueOf)(e,"priceOvertime",[]);return u({},e,{Internet:t.find(function(e){return"Internet"===e.flowType}),TV:t.find(function(e){return"TV"===e.flowType})})}),ue=Object(oe.createAction)("CHECK_NAVIGATION_RESTRICTIONS"),de=function(e){function t(t,n){return e.call(this,t,n)||this}return a(t,e),r([d.Injectable,i("design:paramtypes",[d.AjaxServices,re])],t)}(m.BaseClient),me=m.Actions.showHideLoader,pe=m.Actions.continueFlow,fe=m.Actions.historyBack,Ee=m.Actions.historyForward,ge=m.Actions.historyGo,be=m.Actions.openLightbox,he=m.Actions.applicationExit,Oe=m.Actions.applicationLogout,ve=m.Actions.setWidgetStatus,ye=m.Actions.applicationReset,Ne=m.Actions.closeLightbox,xe=ce.ActionsObservable.concat,Te=function(){function e(e,t){this.client=e,this.config=t}return e.prototype.combineEpics=function(){return Object(ce.combineEpics)(this.historyGoEpic,this.historyForwardEpic,this.historyBackEpic,this.applicationExitEpic,this.applicationLogoutEpic,this.checkRestrictionsEpic,this.applicationResetEpic)},Object.defineProperty(e.prototype,"checkRestrictionsEpic",{get:function(){var e=this;return function(t,n){return t.ofType(ue.toString()).filter(function(e){var t=e.payload;return Boolean(t)}).mergeMap(function(t){var n=t.payload;return xe([ve(e.widgetState=m.EWidgetStatus.UPDATING)],e.client.action(n).mergeMap(function(e){return Object(m.FilterRestrictionObservable)(e,[ge(e.data.redirectURLKey)])}))}).catch(m.Models.ErrorHandlerObservable(ue))}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"historyGoEpic",{get:function(){return function(e,t){return e.ofType(ge.toString()).filter(function(e){return"string"==typeof e.payload}).mergeMap(function(e){var n=e.payload,a=t.getState().flowType,r=n,i="";switch(n){case"APPOINTMENT":case m.EWidgetRoute.APPOINTMENT:r=m.Utils.constructPageRoute(m.EWidgetRoute.APPOINTMENT),i=m.EWidgetRoute.APPOINTMENT;break;case"INTERNET_REVIEW":case"TV_REVIEW":case"BUNDLE_REVIEW":case"REVIEW":case m.EWidgetRoute.REVIEW:r=m.Utils.constructPageRoute(m.EWidgetRoute.REVIEW),i=m.EWidgetRoute.REVIEW;break;case"INTERNET_CONFIRMATION":case"TV_CONFIRMATION":case"BUNDLE_CONFIRMATION":case"CONFIRMATION":case m.EWidgetRoute.CONFIRMATION:r=m.Utils.constructPageRoute(m.EWidgetRoute.CONFIRMATION),i=m.EWidgetRoute.CONFIRMATION;break;case"ADD_TV_REVIEW":r=m.Utils.constructPageRoute(m.EWidgetRoute.REVIEW,m.EFlowType.ADDTV),i=m.EWidgetRoute.REVIEW;break;case"INTERNET_CP":case m.EWidgetRoute.INTERNET:r=m.Utils.constructPageRoute(m.EWidgetRoute.INTERNET),i=m.EWidgetRoute.INTERNET;break;case"TV_CP":case m.EWidgetRoute.TV:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV),i=m.EWidgetRoute.TV;break;case"ADD_TV":r=m.Utils.constructPageRoute(m.EWidgetRoute.TV,m.EFlowType.ADDTV),i=m.EWidgetRoute.TV,a=m.EFlowType.ADDTV;break;case"BUNDLE_TV":r=m.Utils.constructPageRoute(m.EWidgetRoute.TV,m.EFlowType.BUNDLE),i=m.EWidgetRoute.TV,a=m.EFlowType.BUNDLE;break;case m.Volt.EDIsplayGroupKey.BASE_PROGRAMMING:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_Packages,i=m.EWidgetRoute.TV;break;case m.Volt.EDIsplayGroupKey.ADD_ON:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_Addons,i=m.EWidgetRoute.TV;break;case m.Volt.EDIsplayGroupKey.ALACARTE:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_Alacarte,i=m.EWidgetRoute.TV;break;case m.Volt.EDIsplayGroupKey.MOVIE:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_MoviesSeries,i=m.EWidgetRoute.TV;break;case m.Volt.EDIsplayGroupKey.INTERNATIONAL:case m.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_InternationalCombos,i=m.EWidgetRoute.TV;break;case m.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_InternationalAlacarte,i=m.EWidgetRoute.TV;break;case m.Volt.EDIsplayGroupKey.TV_BROWSE_ALL:r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_Browse,i=m.EWidgetRoute.TV;break;case"TV_SEARCH":r=m.Utils.constructPageRoute(m.EWidgetRoute.TV)+m.EWidgetRoute.TV_Search,i=m.EWidgetRoute.TV;break;case"INTERNET_OVERVIEW":case"TV_OVERVIEW":return[he()]}if(m.Utils.getPageRoute()===m.EWidgetRoute.TV&&i===m.EWidgetRoute.TV){var c=h;return window.requestAnimationFrame(function(){return c.push(r.replace(/\/Ordering|\/Changepackage|\/TV|\/Add\b|\/Bundle/gi,""))}),[me(null)]}if(m.Utils.getPageRoute()===m.EWidgetRoute.INTERNET&&i===m.EWidgetRoute.INTERNET)return[me(null)];var l=o();return window.requestAnimationFrame(function(){return l.push(r)}),[le(a)]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"historyForwardEpic",{get:function(){return function(e,t){return e.ofType(Ee.toString(),pe.toString()).mergeMap(function(){var e=o(),n=t.getState().routes,a=Object(m.ValueOf)(t.getState(),"summary.nextAction",{});switch(!0){case Boolean(a.href):return[ue(a)];case Boolean(a.redirectURLKey):return[ge(a.redirectURLKey)];default:var r=e.location.pathname,i=n.findIndex(function(e){return r.indexOf(e)>-1});return i===n.length-1?[]:(r=n[i+=1],e.push(r),[])}})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"historyBackEpic",{get:function(){return function(e,t){return e.ofType(fe.toString()).mergeMap(function(e){var n=e.payload,a=o(),r=t.getState().routes,i=Object(m.ValueOf)(t.getState(),"summary.backAction",{});switch(!0){case Boolean(i.href):return[ue(i)];case Boolean(i.redirectURLKey):return"INTERNET_OVERVIEW"===i.redirectURLKey||"TV_OVERVIEW"===i.redirectURLKey?[me(!1),be({lightboxId:"APPLICATION_EXIT",data:{relativeId:n}})]:[ge(i.redirectURLKey)];default:var c=(o().location.pathname||"").replace(/\/Ordering|\/Packages|\/Movies|\/Addons|\/Alacarte|\/International|\/Combos|\/Browse|\/Search/i,""),l=r.findIndex(function(e){return c===e});return 0===l?[me(!1),be({lightboxId:"APPLICATION_EXIT",data:{relativeId:n}})]:(l-=1,c&&((c=r[l]).indexOf("Appointment")>-1&&!sessionStorage.getItem("omf:hasAppointmentRoute")&&(c=r[l-=1]),a.push(c)),[])}})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"applicationExitEpic",{get:function(){var e=this;return function(t){return t.ofType(he.toString()).mergeMap(function(){return sessionStorage.clear(),window.location=Object(m.ValueOf)(e.config,"linkURL.exitURL",""),[me(!0)]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"applicationResetEpic",{get:function(){var e=this;return function(t,n){return t.ofType(ye.toString()).mergeMap(function(){var t=Object(m.ValueOf)(n.getState(),"summary.resetAction",{});return xe([me(!0)],e.client.post(t.href,t.messageBody).mergeMap(function(e){return[Ne("APPLICATION_RESET"),window.location.reload()]}))})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"applicationLogoutEpic",{get:function(){var e=this;return function(t){return t.ofType(Oe.toString()).mergeMap(function(){return sessionStorage.clear(),window.location=Object(m.ValueOf)(e.config,"linkURL.logoutURL",""),[me(!0)]})}},enumerable:!0,configurable:!0}),r([d.Injectable,i("design:paramtypes",[de,re])],e)}(),Se=m.Actions.setWidgetStatus,Ae=function(){function e(e){this.navigationEpics=e}return e.prototype.combineEpics=function(){return Object(ce.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(Se.toString()).filter(function(e){return e.payload===m.EWidgetStatus.INIT}).mergeMap(function(){return[le(m.Utils.getFlowType()),Se(m.EWidgetStatus.RENDERED)]})}},enumerable:!0,configurable:!0}),r([d.Injectable,i("design:paramtypes",[Te])],e)}(),Ie=d.CommonFeatures.BaseStore,_e=d.CommonFeatures.actionsToComputedPropertyName,Ce=_e(m.Actions).setWidgetProps,we=_e(c),Re=we.setFlowType,Pe=we.setSummaryTotals,ke=function(e){function t(t,n,a,r){var i=e.call(this,n)||this;return i.client=t,i.epics=a,i.localization=r,i}return a(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){return Object(ie.combineReducers)(u({},m.Reducers.WidgetBaseLifecycle(this.localization),m.Reducers.WidgetLightboxes(),m.Reducers.WidgetRestrictions(),{defaultRoute:Object(oe.handleActions)((e={},e[Ce]=function(e,t){var n=t.payload;return n&&n.defaultRoute||e},e),"/"),flowType:Object(oe.handleActions)((t={},t[Ce]=function(e,t){var n=t.payload;return n&&n.flowType||e},t[Re]=function(e,t){var n=t.payload;return n&&n||e},t),""),routes:Object(oe.handleActions)((n={},n[Ce]=function(e,t){var n=t.payload;return n&&n.flowType&&v[n.flowType]||e},n[Re]=function(e,t){var n=t.payload;return n&&v[n]||e},n),[]),summary:Object(oe.handleActions)((a={},a[Pe]=function(e,t){var n=t.payload;return n&&n||e},a),{})}));var e,t,n,a},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return[Object(ce.createEpicMiddleware)(this.epics.navigationEpics.combineEpics()),Object(ce.createEpicMiddleware)(this.epics.combineEpics()),Object(ce.createEpicMiddleware)((new m.ModalEpics).combineEpics()),Object(ce.createEpicMiddleware)(new m.RestricitonsEpics(this.client,"NAVIGATION_RESTRICTION_MODAL").combineEpics()),Object(ce.createEpicMiddleware)((new m.LifecycleEpics).combineEpics())]},enumerable:!0,configurable:!0}),r([d.Injectable,i("design:paramtypes",[de,d.Store,Ae,N])],t)}(Ie),Me=function(e){function t(n){var a=e.call(this,n)||this;return t.instance=a,a}return a(t,e),t.Subscriptions=function(e){return(t={})[m.Actions.historyGo.toString()]=function(t){var n=t.payload;t.meta,e.dispatch(m.Actions.historyGo(n))},t[m.Actions.historyBack.toString()]=function(){e.dispatch(m.Actions.historyBack())},t[m.Actions.historyForward.toString()]=function(){e.dispatch(m.Actions.historyForward())},t[m.Actions.applicationExit.toString()]=function(){e.dispatch(m.Actions.applicationExit())},t[m.Actions.applicationLogout.toString()]=function(){e.dispatch(m.Actions.applicationLogout())},t[m.Actions.refreshTotals.toString()]=function(){e.dispatch(m.Actions.refreshTotals())},t[m.Actions.setProductConfigurationTotal.toString()]=function(t){var n=t.payload;e.dispatch(se(n))},t[m.Actions.closeLightbox.toString()]=function(t){var n=t.payload;e.dispatch(m.Actions.closeLightbox(n))},t[m.Actions.setHistoryProvider.toString()]=function(e){var t=e.payload;h=t||h},t[m.Actions.setAppointmentVisited.toString()]=function(){O=!0},t;var t},t}(d.CommonFeatures.BasePipe),je=m.Actions.setWidgetProps,De=m.Actions.setWidgetStatus,Le=function(e){function t(t,n,a,r){var i=e.call(this)||this;return i.store=t,i.params=n,i.config=a,i.pipe=r,i}return a(t,e),t.prototype.init=function(){this.pipe.subscribe(Me.Subscriptions(this.store)),this.store.dispatch(je(this.config)),this.store.dispatch(je(this.params.props)),this.store.dispatch(De(m.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store,n=window.location.pathname.split("/");f.render(p.createElement(m.ContextProvider,{value:{config:this.config,mode:"/"+n[n.length-1]}},p.createElement(E.Provider,u({},{store:t}),p.createElement(te,null))),e)},r([Object(d.Widget)({namespace:"Ordering"}),i("design:paramtypes",[ke,d.ParamsProvider,re,Me])],t)}(d.ViewWidget);t.default=Le},function(e,t){e.exports=l},function(e,t){e.exports=s}])},e.exports=a(n(1),n(2),n(0),n(5),n(3),n(6),n(8),n(4),n(12),n(7))},function(e,t,n){var a;"undefined"!=typeof self&&self,a=function(e,t,n,a,r,i,o,c,l){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=7)}([function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t,n){"use strict";function a(e,t){function n(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t,n,a){var r,i=arguments.length,o=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,a);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,r,i=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=i.next()).done;)o.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o}function c(e){return Object(f.ValueOf)(e,void 0,[]).reduce(function(e,t){return t.name&&(e[t.name]=t.value),e},{})}Object.defineProperty(t,"__esModule",{value:!0});var l={};n.d(l,"getAccountDetails",function(){return O}),n.d(l,"setAccountDetails",function(){return v}),n.d(l,"getInternetCatalog",function(){return y}),n.d(l,"setInternetCatalog",function(){return N}),n.d(l,"togglePackageSelection",function(){return x}),n.d(l,"updateInternetCatalog",function(){return T});var s=function(e,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},u=function(){return(u=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},d=n(2),m=n(8),p=n(3),f=n(0),E=n(1),g=n(9),b=n(6),h=n(4),O=Object(b.createAction)("GET_ACCOUNT_DETAILS"),v=Object(b.createAction)("SET_ACCOUNT_DETAILS",function(e){return Object(f.ValueOf)(e,"ProductOfferings",[{Unavailable:!0}])}),y=Object(b.createAction)("GET_INTERNET_CATALOG"),N=Object(b.createAction)("SET_INTERNET_CATALOG",function(e){var t=Object(f.ValueOf)(e,"productOfferingDetail.productOfferingGroups",[]).find(function(e){return e.lineOfBusiness===f.Volt.ELineOfBusiness.Internet&&e.productOfferingGroupType===f.Volt.EProductOfferingGroupType.Default});return Object(f.ValueOf)(t,"productOfferings",[])}),x=Object(b.createAction)("TOGGLE_INTERNET_PACKAGE"),T=Object(b.createAction)("UPDATE_INTERNET_CATALOG",function(e,t){var n=Object(f.ValueOf)(e,"productOfferingDetail.productOfferingGroups",[]).find(function(e){return e.lineOfBusiness===f.Volt.ELineOfBusiness.Internet&&e.productOfferingGroupType===f.Volt.EProductOfferingGroupType.Delta});return Object(f.ValueOf)(n,"productOfferings",[]).forEach(function(e){var n=t.find(function(t){return t.id===e.id})||{};Object.assign(n,e)}),function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(o(arguments[t]));return e}(t)}),S=E.CommonFeatures.BaseConfig,A=E.CommonFeatures.configProperty,I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),r([A(""),i("design:type",String)],t.prototype,"flowType",void 0),r([A({}),i("design:type",Object)],t.prototype,"environmentVariables",void 0),r([A({}),i("design:type",Object)],t.prototype,"mockdata",void 0),r([A({}),i("design:type",Object)],t.prototype,"headers",void 0),r([A({base:"http://127.0.0.1:8881"}),i("design:type",Object)],t.prototype,"api",void 0),r([E.Injectable],t)}(S),_=function(e){function t(t,n){return e.call(this,t,n)||this}return a(t,e),r([E.Injectable,i("design:paramtypes",[E.AjaxServices,I])],t)}(f.BaseClient),C=h.ActionsObservable.concat,w=f.Actions.errorOccured,R=f.Actions.setWidgetStatus,P=f.Actions.clearCachedState,k=f.Actions.finalizeRestriction,M=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=f.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(h.combineEpics)(this.requestCatalogEpic,this.togglePlanSelectionEpic,this.finalizeRestrictionEpic)},Object.defineProperty(e.prototype,"requestCatalogEpic",{get:function(){var e=this;return function(t){return t.ofType(y.toString()).filter(function(){return e.widgetState!==f.EWidgetStatus.UPDATING}).mergeMap(function(){return C([R(e.widgetState=f.EWidgetStatus.UPDATING)],e.client.get(f.Utils.appendRefreshOnce(f.Utils.getURLByFlowType((t={},t[f.EFlowType.TV]=e.config.api.catalogAPI,t[f.EFlowType.INTERNET]=e.config.api.catalogAPI,t[f.EFlowType.BUNDLE]=e.config.api.bundleCatalogAPI,t)))).mergeMap(function(t){return Object(f.FilterRestrictionObservable)(t,[N(t.data),f.Actions.omniPageLoaded(),R(e.widgetState=f.EWidgetStatus.RENDERED)])}));var t}).catch(function(e){return[w(new f.Models.ErrorHandler("getInternetCatalog",e))]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"togglePlanSelectionEpic",{get:function(){var e=this;return function(t,n){return t.ofType(x.toString()).filter(function(){return e.widgetState!==f.EWidgetStatus.UPDATING}).mergeMap(function(t){var a=t.payload;return C([R(e.widgetState=f.EWidgetStatus.UPDATING)],e.client.action(a).mergeMap(function(t){return Object(f.FilterRestrictionObservable)(t,[T(t.data,n.getState().catalog),P([f.EWidgetName.PREVIEW]),R(e.widgetState=f.EWidgetStatus.RENDERED)])}))}).catch(f.Models.ErrorHandlerObservable(x))}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"finalizeRestrictionEpic",{get:function(){var e=this;return function(t,n){return t.ofType(k.toString()).filter(function(t){var n=t.payload;return Boolean(n)&&Boolean(n.productOfferingDetail)&&e.widgetState!==f.EWidgetStatus.UPDATING}).mergeMap(function(t){var a=t.payload;return[f.Actions.broadcastUpdate(f.Actions.setProductConfigurationTotal(Object(f.ValueOf)(a,"productOfferingDetail.productConfigurationTotal"))),T(a,n.getState().catalog),P([f.EWidgetName.PREVIEW]),R(e.widgetState=f.EWidgetStatus.RENDERED)]})}},enumerable:!0,configurable:!0}),r([E.Injectable,i("design:paramtypes",[_,I])],e)}(),j=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=f.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(h.combineEpics)(this.requestDataEpic)},Object.defineProperty(e.prototype,"requestDataEpic",{get:function(){var e=this;return function(t,n){return t.ofType(O.toString()).filter(function(){return e.widgetState!==f.EWidgetStatus.UPDATING}).mergeMap(function(){return e.client.get(e.config.api.serviceAccountAPI).mergeMap(function(e){var t=e.data;return[v(t),y()]})}).catch(function(e){return[]})}},enumerable:!0,configurable:!0}),r([E.Injectable,i("design:paramtypes",[_,I])],e)}(),D=f.Actions.omniPageLoaded,L=f.Actions.omniPageSubmit,V=function(){function e(){this.widgetState=f.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(h.combineEpics)(this.pageLoadedEpic,this.pageSubmitEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){return function(e,t){return e.ofType(D.toString()).mergeMap(function(){var e=t.getState().accountDetails;return f.Omniture.useOmniture().trackFragment({id:"InternetPage",s_oSS1:"~",s_oSS2:"~",s_oSS3:"~",s_oPGN:"~",s_oAPT:{actionresult:1},s_oPLE:{type:f.Omniture.EMessageType.Information,content:Object(f.ValueOf)(e,"0.Name","")}}),[]}).catch(function(e){return[]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pageSubmitEpic",{get:function(){return function(e,t){return e.ofType(L.toString()).mergeMap(function(){var e=t.getState().catalog;return f.Omniture.useOmniture().trackAction({id:"internetPageSubmit",s_oAPT:{actionId:647},s_oBTN:"Continue",s_oPRD:e.filter(function(e){return e.isSelected&&!e.isCurrent}).map(function(e){return{category:"Internet",name:e.name,sku:"",quantity:"1",price:Object(f.ValueOf)(e,"regularPrice.price","0"),promo:Object(f.ValueOf)(e,"promotionDetails.promotionalPrice.price","")}})}),[]}).catch(function(e){return[]})}},enumerable:!0,configurable:!0}),r([E.Injectable],e)}(),F=f.Actions.setWidgetStatus,B=function(){function e(e,t,n){this.catalogEpics=e,this.userAccountEpics=t,this.omniture=n}return e.prototype.combineEpics=function(){return Object(h.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(F.toString()).filter(function(e){return e.payload===f.EWidgetStatus.INIT}).mergeMap(function(){var e,t="~";switch(f.Utils.getFlowType()){case f.EFlowType.INTERNET:e=523,t="Internet";break;case f.EFlowType.BUNDLE:t="Bundle"}return f.Omniture.useOmniture().updateContext({s_oSS1:"~",s_oSS2:t,s_oSS3:"Change package",s_oPGN:"Setup your service",s_oAPT:{actionId:e}}),[O()]})}},enumerable:!0,configurable:!0}),r([E.Injectable,i("design:paramtypes",[M,j,V])],e)}(),W=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||E.ServiceLocator.instance.getService(E.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString(f.EWidgetName.INTERNET,e,t.locale):e},t.Instance=null,n=r([E.Injectable],t);var n}(E.CommonFeatures.BaseLocalization),G=E.CommonFeatures.BaseStore,U=(0,E.CommonFeatures.actionsToComputedPropertyName)(l),z=U.setAccountDetails,H=U.setInternetCatalog,K=U.updateInternetCatalog,q=function(e){function t(t,n,a,r){var i=e.call(this,n)||this;return i.client=t,i.epics=a,i.localization=r,i}return a(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){return Object(g.combineReducers)(u({},f.Reducers.WidgetBaseLifecycle(this.localization),f.Reducers.WidgetLightboxes(),f.Reducers.WidgetRestrictions(),{accountDetails:Object(b.handleActions)((e={},e[z]=function(e,t){return t.payload||e},e),[{}]),catalog:Object(b.handleActions)((t={},t[H]=function(e,t){return t.payload||e},t[K]=function(e,t){return t.payload||e},t),[])}));var e,t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return[Object(h.createEpicMiddleware)(this.epics.omniture.combineEpics()),Object(h.createEpicMiddleware)(this.epics.userAccountEpics.combineEpics()),Object(h.createEpicMiddleware)(this.epics.catalogEpics.combineEpics()),Object(h.createEpicMiddleware)(this.epics.combineEpics()),Object(h.createEpicMiddleware)((new f.ModalEpics).combineEpics()),Object(h.createEpicMiddleware)(new f.RestricitonsEpics(this.client,"INTERNET_RESTRICTION_MODAL").combineEpics()),Object(h.createEpicMiddleware)((new f.LifecycleEpics).combineEpics())]},enumerable:!0,configurable:!0}),r([E.Injectable,i("design:paramtypes",[_,E.Store,B,W])],t)}(G),X=function(e){function t(n){var a=e.call(this,n)||this;return t.instance=a,a}return a(t,e),t.Subscriptions=function(e){return(t={})[f.Actions.onContinue.toString()]=function(){e.dispatch(f.Actions.omniPageSubmit()),f.Actions.broadcastUpdate(f.Actions.historyForward())},t[f.Actions.omniPageSubmit.toString()]=function(){f.Actions.broadcastUpdate(f.Actions.omniPageSubmit())},t;var t},t}(E.CommonFeatures.BasePipe),Y=n(5),J=Object(p.connect)(function(e){return{accountDetails:e.accountDetails||[]}})(function(e){var t=e.accountDetails,n=o(d.useState(!1),2),a=n[0],r=n[1];d.useEffect(function(){a&&f.Omniture.useOmniture().trackAction({id:"myCurrentPackageClick",s_oAPT:{actionId:648},s_oEPN:"My current Home Internet package"})},[a]);var i=a?"icon-Collapse":"icon-Expand";return d.createElement("section",{className:"bgVirginGradiant accss-focus-outline-override-pad"},d.createElement("div",{className:"container liquid-container sans-serif"},d.createElement("div",{className:"accordion-group internet-current-package flexCol"},d.createElement("div",{className:"accordion-heading col-xs-12 noPaddingImp"},d.createElement("a",{id:"accordion_expand_link",href:"javascript:void(0)",onClick:function(){return r(!a)},"aria-controls":"div1-accessible",className:"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content","aria-expanded":a,role:"button"},d.createElement("span",{className:"sr-only accordion-label","aria-live":"polite","aria-atomic":"true","aria-hidden":"true"},d.createElement(Y.FormattedMessage,{id:a?"Collapse":"Expand"})),d.createElement("span",{className:i+" virgin-icon txtSize24 virginRedIcon","aria-hidden":"true"},d.createElement("span",{className:"virgin-icon path1 "+i}),d.createElement("span",{className:"virgin-icon path2 "+i})),d.createElement("div",{className:"margin-15-left flexCol"},d.createElement("span",{className:"txtWhite txtBold txtSize18"},d.createElement(Y.FormattedMessage,{id:"My current Home Internet package"})),d.createElement("span",{className:"expand txtWhite txtSize12 no-margin-top",style:{display:a?"none":void 0}},d.createElement(Y.FormattedMessage,{id:"Expand to view details"}))))),d.createElement("div",{id:"div1-accessible",className:"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left",style:{display:a?"block":"none"}},d.createElement("div",{className:"accordion-inner"},t.map(function(e){var t=e.Name,n=e.RegularPrice,a=e.PromotionDetails;return d.createElement("div",{className:"col-sm-5"},d.createElement("div",{className:"spacer10","aria-hidden":"true"}),d.createElement("div",{className:"flexRow flexEnd"},d.createElement("div",{className:"flexGrow"},t),d.createElement("div",{style:{whiteSpace:"nowrap"}},d.createElement(f.Components.BellCurrency,{value:Object(f.ValueOf)(n,"Price",0)}),d.createElement(Y.FormattedMessage,{id:"PER_MO"}))),d.createElement(f.Components.Visible,{when:!!a},d.createElement("div",{className:"spacer5","aria-hidden":"true"}),d.createElement(f.Components.Visible,{when:Object(f.ValueOf)(a,"Description",!1)},d.createElement("div",{className:"flexRow"},d.createElement("div",{className:"flexGrow"},Object(f.ValueOf)(a,"Description","")),d.createElement("div",null,d.createElement(f.Components.BellCurrency,{value:Object(f.ValueOf)(a,"PromotionalPrice.Price",0)}),d.createElement(Y.FormattedMessage,{id:"PER_MO"})))),d.createElement(f.Components.Visible,{when:Object(f.ValueOf)(a,"ExpiryDate",!1)},d.createElement("div",null,d.createElement(Y.FormattedDate,{value:Object(f.ValueOf)(a,"ExpiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return d.createElement(Y.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))))}))))))}),Z=f.Components.Visible,Q=f.Components.Currency,ee=Object(p.connect)(function(e){return{}},function(e){return{onPackageClicked:function(t){return e(x(t))}}})(function(e){var t=e.id,n=e.name,a=e.shortDescription,r=e.usagePlan,i=(e.isSelectable,e.isSelected),c=e.regularPrice,l=e.promotionDetails,s=e.offeringAction,u=e.onPackageClicked,m=function(e){e.stopPropagation(),e.preventDefault(),i||void 0!==e.keyCode&&32!==e.keyCode&&13!==e.keyCode||u(s)},p=o(d.useState(!1),2),E=p[0],g=p[1],b=function(e){void 0!==e.keyCode&&13!==e.keyCode||!e.target.classList.contains("txtUnderline")||g(!E)};return d.useEffect(function(){$("#"+t).find("[data-toggle]").addClass("txtUnderline txtBlue pointer accss-text-blue-on-bg-white accss-width-fit-content").attr("tabindex","0").next().addClass("downloadTray").removeAttr("id")}),d.createElement("div",{id:t,className:"virgin-internet-box txtGray margin-15-bottom "+(i?"selected":"")},d.createElement("div",{className:"flexRow bgWhite border-radius-3 virgin-title-block pad-30 pad-15-left-right-sm accss-focus-outline-override-white-bg"},d.createElement("div",{className:"package_ctrl"},d.createElement("span",{id:"CTA_"+t,className:"graphical_ctrl ctrl_radioBtn pointer",onClick:m},d.createElement("input",{id:"OPT_"+t,name:"internetpackage",checked:i,type:"radio","aria-labelledby":"PACKAGE_CTA_"+t,"aria-describedby":"PACKAGE_CTA_DESC_"+t,"aria-checked":i,className:"radioBtn-active data-feature"}),d.createElement("span",{className:"ctrl_element pointer data-addon-active data-addon-border"}))),d.createElement("div",{className:"package-desc fill"},d.createElement("div",{id:"PACKAGE_CTA_"+t,className:"fill pad-15-left content-width valign-top pad-0-xs pointer",onClick:m},d.createElement("h2",{className:"virginUltraReg txtSize16 floatL txtUppercase no-margin"},n)),d.createElement("div",{className:"spacer10 d-none d-sm-block d-md-none clear","aria-hidden":"true"}),d.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),d.createElement("div",{className:"spacer1 bgGrayLight6 clear margin-30-right","aria-hidden":"true"}),d.createElement("div",{className:"spacer15 hidden-m","aria-hidden":"true"}),d.createElement("div",{className:"pkg-pull-left neg-margin-left-40-sm flexBlock",id:"PACKAGE_CTA_DESC_"+t},d.createElement("div",{className:"flexRow fill flexCol-xs"},d.createElement("ul",{id:"UPLOAD_CTA_"+t,className:"speed-box1 flexRow flexCol-xs mb-0 pl-0 list-unstyled "+(E?"expanded":""),onKeyUp:b,onClick:b,dangerouslySetInnerHTML:{__html:a}}),d.createElement("ul",{className:"speed-box2 mb-0 list-unstyled",dangerouslySetInnerHTML:{__html:r}}),d.createElement("div",{className:"speed-box3"},d.createElement("div",{className:"pad-30-left no-pad-xs margin-10-left-xs"},d.createElement(Z,{when:Object(f.ValueOf)(l,"expiryDate",!1)},d.createElement("span",{className:"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3"},d.createElement(Y.FormattedDate,{value:Object(f.ValueOf)(l,"expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return d.createElement(Y.FormattedMessage,{id:"Your monthly credit expires",values:{expiryDate:e}})}))),d.createElement(Z,{when:Object(f.ValueOf)(l,"discountDuration",!1)},d.createElement("span",{className:"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3"},d.createElement(Y.FormattedMessage,{id:"Get a credit for months",values:{credit:Math.abs(Object(f.ValueOf)(l,"discountPrice.price",0)),duration:Object(f.ValueOf)(l,"discountDuration",0)}}))),d.createElement(Z,{when:Object(f.ValueOf)(!l,void 0,!1)},d.createElement("div",{className:"spacer15 clear hidden-m","aria-hidden":"true"})),d.createElement("div",{className:"price virginUltraReg txtSize40 line-height-1 margin-10-top"},d.createElement(Z,{when:Object(f.ValueOf)(l,void 0,!1)},d.createElement(Y.FormattedMessage,{id:"Now"})," "),d.createElement(Q,{value:!1===Object(f.ValueOf)(l,"?promotionalPrice.price",!1)?Object(f.ValueOf)(c,"price",0):Object(f.ValueOf)(l,"promotionalPrice.price",0),monthly:!0}),d.createElement(Z,{when:Object(f.ValueOf)(l,void 0,!1)},d.createElement("p",{className:"txtSize12 txtBlack txtBold sans-serif no-margin"},d.createElement(Y.FormattedMessage,{id:"Current Price",values:Object(f.ValueOf)(c,void 0,{})})))),d.createElement(Z,{when:Object(f.ValueOf)(l,void 0,!1)},d.createElement("p",{className:"txtSize12 txtBlack sans-serif no-margin pad-10-top"},Object(f.ValueOf)(l,"legalMessage",d.createElement(Y.FormattedMessage,{id:"Prices may increase legal"})))))))))))}),te=function(){var e=o(d.useState(!1),2),t=e[0],n=e[1];return d.useEffect(function(){t&&f.Omniture.useOmniture().trackAction({id:"ligalStuffClick",s_oAPT:{actionId:648},s_oEPN:"Legal Stuff"})},[t]),d.createElement("div",{className:"virginUltraReg margin-15-top",id:"moreInfo"},d.createElement("button",{id:"Legal_stuff",className:"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray accss-focus-outline-override-grey-bg-element",onClick:function(){return n(!t)},"aria-expanded":t},d.createElement("span",{className:"volt-icon "+(t?"icon-collapse_m":"icon-expand_m"),"aria-hidden":"true"}),"  ",d.createElement(Y.FormattedMessage,{id:"Legal stuff label"})),d.createElement("div",{className:"spacer30","aria-hidden":"true"}),d.createElement(f.Components.Visible,{when:t},d.createElement("div",{className:"moreInfoBox bgWhite pad30 margin-30-bottom accss-link-override accss-focus-outline-override-white-bg"},d.createElement("button",{id:"LEGALBOX_CLOSE",type:"button",onClick:function(){return n(!1)},className:"close moreInfoLink x-inner txtDarkGrey txtSize18 txtBold","aria-label":"close"},d.createElement("span",{className:"virgin-icon icon-big_X","aria-hidden":"true"})),d.createElement(Y.FormattedHTMLMessage,{id:"GOOD TO KNOW"}))))},ne=Object(p.connect)(function(e){return{catalog:e.catalog}})(function(e){var t=e.catalog;return d.createElement("div",{className:"container liquid-container noSpacing",role:"radiogroup"},d.createElement("style",null,'.icon-upload-ico:before {\n                    content: "\\e99d";\n                }\n                .icon-download-ico:before {\n                    content: "\\e929";\n                }\n                .package-desc li {\n                    display: block;\n                    list-style: none;\n                    position: relative;\n                    width: calc(100% / 2);\n                }\n                    .package-desc li:not(:last-of-type) {\n                        padding-right: 15px;\n                    }\n                    .package-desc li .volt-icon {\n                        position: absolute;\n                        display: block;\n                        color: #cc0000;\n                        font-size: 32px;\n                        width: 42px;\n                        height: 42px;\n                        left: 0;\n                    }\n                    .package-desc li span {\n                        display: block;\n                        font-size: 12px;\n                    }\n                    .package-desc .speed-box2 span:first-of-type,\n                    .package-desc li span.speed {\n                        font-size: 22px;\n                        color: black;\n                        text-transform: uppercase;\n                        font-family: "VMUltramagneticNormalRegular", Helvetica, Arial, sans-serif;\n                }\n                .package-desc .speed-box2 span.usage {\n                    white-space: nowrap;\n                }\n                .speed-box1 li {\n                    margin-top: 10px;\n                    padding-left: 42px;\n                }\n                .package-desc li span.downloadTray {\n                    display: none;\n                }\n                .package-desc .speed-box1.expanded li span.downloadTray {\n                    display: block;\n                }\n                @media (max-width: 991.98px) {\n                    .package-desc li {\n                        width: 100%;\n                    }\n                  .pkg-pull-left {\n                      margin-left: -40px;\n                  }\n                }'),t.filter(function(e){return!e.isCurrent}).sort(function(e,t){return Object(f.ValueOf)(c(e.characteristics),"sortPriority",0)-Object(f.ValueOf)(c(t.characteristics),"sortPriority",0)}).map(function(e){return d.createElement(ee,u({},e))}),d.createElement(te,null))}),ae=f.Components.RestrictionModal,re=f.Actions.errorOccured,ie=f.Actions.widgetRenderComplete,oe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.componentDidCatch=function(e){this.props.onErrorEncountered(e)},t.prototype.componentDidMount=function(){this.props.widgetRenderComplete(f.EWidgetName.INTERNET)},t.prototype.render=function(){return d.createElement("main",{id:"mainContent"},d.createElement(J,null),d.createElement("div",{className:"spacer30","aria-hidden":"true"}),d.createElement(ne,null),d.createElement(ae,{id:"INTERNET_RESTRICTION_MODAL"}))},t}(d.Component),ce=Object(p.connect)(function(e){return{}},function(e){return{onErrorEncountered:function(t){return e(re(t))},widgetRenderComplete:function(){return e(ie())}}})(oe),le=f.Components.ApplicationRoot,se=function(){return d.createElement(le,null,d.createElement(ce,null))},ue=f.Actions.setWidgetProps,de=f.Actions.setWidgetStatus,me=function(e){function t(t,n,a,r){var i=e.call(this)||this;return i.store=t,i.params=n,i.config=a,i.pipe=r,i}return a(t,e),t.prototype.init=function(){this.pipe.subscribe(X.Subscriptions(this.store)),this.store.dispatch(ue(this.config)),this.store.dispatch(ue(this.params.props)),this.store.dispatch(de(f.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;m.render(d.createElement(f.ContextProvider,{value:{config:this.config}},d.createElement(p.Provider,u({},{store:t}),d.createElement(se,null))),e)},r([Object(E.Widget)({namespace:"Ordering"}),i("design:paramtypes",[q,E.ParamsProvider,I,X])],t)}(E.ViewWidget);t.default=me},function(e,t){e.exports=c},function(e,t){e.exports=l}])},e.exports=a(n(1),n(0),n(2),n(5),n(6),n(3),n(4),n(8),n(7))},function(e,t,n){var a;"undefined"!=typeof self&&self,a=function(e,t,n,a,r,i,o,c,l,s,u){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=8)}([function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t){e.exports=c},function(e,t,n){"use strict";function a(e,t){function n(){this.constructor=e}T(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t,n,a){var r,i=arguments.length,o=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,a);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,r,i=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=i.next()).done;)o.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o}function c(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(o(arguments[t]));return e}function l(e){var t=e.map(function(e){var t=e.characteristics.find(function(e){return"callSign"===e.name});return S({},e,{callSign:t?t.value:""})});(x=new D.Search("id")).addIndex("name"),x.addIndex("channelNumber"),x.addIndex("callSign"),x.addDocuments(t)}function s(e){var t=Object(I.ValueOf)(e,"productOfferingDetail.productOfferingGroups",[]).find(function(e){return"TV"===e.lineOfBusiness}),n=Object(I.ValueOf)(t,"productOfferings",[]),a={index:n,offerings:n.filter(function(e){return e.displayGroupKey&&e.displayGroupKey!==I.Volt.EDIsplayGroupKey.NONE}).reduce(function(e,t){var n=t.displayGroupKey;return e[n]=e[n]||[],e[n].push(t),e},{}),channels:function(e){function t(e){e.productOfferingType===I.Volt.EProductOfferingType.CHANNEL&&n.indexOf(e.id)<0&&(a.push(e),n.push(e.id))}var n=[],a=[];return e.forEach(function(e){Array.isArray(e.childOfferings)?e.childOfferings.forEach(t):t(e)}),a}(n)};return a.refresh=1e3*Math.random(),l(a.channels),a}function u(e){return Object(I.ValueOf)(e,void 0,[]).reduce(function(e,t){return t.name&&(e[t.name]=(t.value||"").trim()),e},{})}function d(e){return Object(I.ValueOf)(e,void 0,[]).reduce(function(e,t){var n=u(t.characteristics).language;return Boolean(n)&&n.split(",").map(function(e){return e.trim()}).filter(Boolean).forEach(function(t){e.indexOf(t)<0&&e.push(t)}),e.sort()},[]).filter(Boolean).sort()}function m(e,t){return e.filter(function(e){return(u(e.characteristics).language||"").indexOf(t)>-1})}function p(e,t){return void 0===t&&(t="asc"),Object(I.ValueOf)(e,void 0,[]).sort(function(e,n){return(Object(I.ValueOf)(u(e.characteristics),"sortPriority",0)-Object(I.ValueOf)(u(n.characteristics),"sortPriority",0))*("asc"===t?1:-1)})}function f(e){return Boolean(e)?e.split(",").map(function(e){return e=e.trim(),ue.getLocalizedString(e)}).join(", "):e}function E(){for(var e in Re)Re[e]&&Re[e].destroy();Array.from(document.querySelectorAll(".channel-tooltip")).forEach(function(e){return e.remove()}),Re={}}function g(e,t){var n=e.current,a=n.parentElement,r=n.clientHeight,i=a.getBoundingClientRect(),o=i.top<-r;t({isFloating:o,leftPos:o?i.right-85:"auto"})}function b(e){return JSON.parse(JSON.stringify(e))}function h(){return b(Ge)}function O(e,t){var n=e.indexOf(t);return n>-1?e.splice(n,1):e.push(t),e}function v(e){var t=o(_.useState(e||h()),2),n=t[0],a=t[1];return[n,{toggleGenre:function(e){return a(S({},n,{genre:O(n.genre,e)}))},setGenre:function(e){return a(S({},n,{genre:e?[e]:[]}))},toggleLanguage:function(e){return a(S({},n,{language:O(n.language,e)}))},setLanguage:function(e){return a(S({},n,{language:[e]}))},toggleDontHave:function(){return a(S({},n,{isDontHave:!n.isDontHave}))},toggleHave:function(){return a(S({},n,{isHave:!n.isHave}))},reset:function(){return a(h())},setSort:function(e){return a(S({},n,{sortBy:e,sortOrder:n.sortBy===e?"desc"===n.sortOrder?"asc":"desc":"asc"}))},hasGenre:function(e){return e?n.genre.indexOf(e)>-1:0===n.genre.length},onlyGenre:function(e){return n.genre.indexOf(e)>-1&&1===n.genre.length},hasLanguage:function(e){return n.language.indexOf(e)>-1},hasDontHave:function(){return n.isDontHave},hasHave:function(){return n.isHave},whichSort:function(){return n.sortBy+n.sortOrder},selectedGenre:function(){return n.genre[0]||"All"},getState:function(){return b(n)},setState:function(e){return a(b(e))}}]}function y(e){var t=_.useRef();return _.useEffect(function(){t.current=e},[e]),t.current}Object.defineProperty(t,"__esModule",{value:!0});var N={};n.d(N,"getAccountDetails",function(){return L}),n.d(N,"setAccountDetails",function(){return V}),n.d(N,"getCatalog",function(){return F}),n.d(N,"setCatalog",function(){return B}),n.d(N,"setNavigation",function(){return W}),n.d(N,"toggleSelection",function(){return G}),n.d(N,"updateCatalog",function(){return U});var x,T=function(e,t){return(T=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},S=function(){return(S=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},A=n(4),I=n(0),_=n(1),C=n(9),w=n(3),R=n(6),P=n(2),k=n(10),M=n(7),j=n(5),D=n(11),L=Object(M.createAction)("GET_ACCOUNT_DETAILS"),V=Object(M.createAction)("SET_ACCOUNT_DETAILS",function(e){var t=Object(I.ValueOf)(e,"ProductOfferings",[]),n=[],a=[],r=function(e){var t=Object(I.ValueOf)(e,"Characteristics",[]).find(function(e){return"sortPriority"===e.Name});return 1*Object(I.ValueOf)(t,"Value",0)};return t.forEach(function(e){e.DisplayGroupKey&&a.indexOf(e.DisplayGroupKey)<0&&(a.push(e.DisplayGroupKey),n.push({displayGroupKey:e.DisplayGroupKey,offerings:t.filter(function(t){return t.DisplayGroupKey===e.DisplayGroupKey}).sort(function(e,t){return r(e)-r(t)})}))}),n.sort(function(e,t){switch(!0){case"BASE_PROGRAMMING"===e.displayGroupKey:return-1;case"PROMOTION"===e.displayGroupKey:return 1;default:return 0}})}),F=Object(M.createAction)("GET_TV_CATALOG"),B=Object(M.createAction)("SET_TV_CATALOG",s),W=Object(M.createAction)("SET_TV_NAVIGATION",function(e){var t=function(e,t){return e.sortPriority-t.sortPriority},n=Object(I.ValueOf)(e,"productOfferingDetail.displayGroup",{}),a=Object(I.ValueOf)(n,"baseOffering",null),r=Object(I.ValueOf)(n,"additionalOfferings",[]).filter(function(e){return e.key&&e.key!==I.Volt.EDIsplayGroupKey.NONE}).map(function(e){return S({},e,{offeringKey:e.key})}),i=r.filter(function(e){return e.isRoot}),o=r.filter(function(e){return!e.isRoot});delete a.count;var l=a?c([a],i):i;return l.push({offeringKey:I.Volt.EDIsplayGroupKey.TV_BROWSE_ALL,sortPriority:99}),l.forEach(function(e){switch(e.offeringKey=e.offeringKey||e.key,e.children=o.filter(function(t){return t.parentKey===e.key}).sort(t).map(function(e){switch(e.offeringKey){case I.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:e.route=I.EWidgetRoute.TV_InternationalCombos;break;case I.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:e.route=I.EWidgetRoute.TV_InternationalAlacarte}return e}),e.offeringKey){case I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING:case I.Volt.EDIsplayGroupKey.TV_BASE_PRODUCT:e.route=I.EWidgetRoute.TV_Packages;break;case I.Volt.EDIsplayGroupKey.ALACARTE:e.route=I.EWidgetRoute.TV_Alacarte;break;case I.Volt.EDIsplayGroupKey.MOVIE:e.route=I.EWidgetRoute.TV_MoviesSeries;break;case I.Volt.EDIsplayGroupKey.ADD_ON:e.route=I.EWidgetRoute.TV_Addons;break;case I.Volt.EDIsplayGroupKey.INTERNATIONAL:e.route=I.EWidgetRoute.TV_International;break;case I.Volt.EDIsplayGroupKey.TV_BROWSE_ALL:e.route=I.EWidgetRoute.TV_Browse}e.refresh=1e3*Math.random()}),l.filter(function(e){return!Boolean(e.parentDisplayGroup)}),l.sort(t)}),G=Object(M.createAction)("TOGGLE_TV_SELECTION"),U=Object(M.createAction)("UPDATE_TV_CATALOG",function(e,t){var n=function(e){return function(t){var a=e.find(function(e){return e.id===t.id});a&&(a.isCurrent=t.isCurrent,a.isDisabled=t.isDisabled,a.isSelectable=t.isSelectable,a.isSelected=t.isSelected,a.isAlreadyIncludedIn=t.isAlreadyIncludedIn,a.offeringAction=t.offeringAction,a.promotionDetails=t.promotionDetails,a.childOfferings&&t.childOfferings&&t.childOfferings.forEach(n(a.childOfferings)))}},a=Object(I.ValueOf)(e,"productOfferingDetail.productOfferingGroups",[]).find(function(e){return"TV"===e.lineOfBusiness}),r=Object(I.ValueOf)(a,"productOfferings",[]);return"Delta"===Object(I.ValueOf)(a,"productOfferingGroupType","")?(r.forEach(n(t.index)),t.offerings.refresh=1e3*Math.random(),t.channels=c(t.channels),l(t.channels)):t=s(e),S({},t,{refresh:1e3*Math.random()})}),z=A.CommonFeatures.BaseConfig,H=A.CommonFeatures.configProperty,K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),r([H({languages:["English","French"],genres:["Family","Movies","News","Sports"],sortBy:"name",sortOrder:"desc"}),i("design:type",String)],t.prototype,"filters",void 0),r([H(""),i("design:type",String)],t.prototype,"flowType",void 0),r([H({}),i("design:type",Object)],t.prototype,"environmentVariables",void 0),r([H({}),i("design:type",Object)],t.prototype,"mockdata",void 0),r([H({}),i("design:type",Object)],t.prototype,"headers",void 0),r([H({base:"http://127.0.0.1:8881"}),i("design:type",Object)],t.prototype,"api",void 0),r([A.Injectable],t)}(z),q=function(e){function t(t,n){return e.call(this,t,n)||this}return a(t,e),r([A.Injectable,i("design:paramtypes",[A.AjaxServices,K])],t)}(I.BaseClient),X=j.ActionsObservable.concat,Y=I.Actions.errorOccured,J=I.Actions.setWidgetStatus,Z=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=I.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(j.combineEpics)(this.requestCatalogEpic)},Object.defineProperty(e.prototype,"requestCatalogEpic",{get:function(){var e=this;return function(t){return t.ofType(F.toString()).filter(function(){return e.widgetState!==I.EWidgetStatus.UPDATING}).mergeMap(function(){return X([J(e.widgetState=I.EWidgetStatus.UPDATING)],e.client.get(I.Utils.appendRefreshOnce(I.Utils.getURLByFlowType((t={},t[I.EFlowType.TV]=e.config.api.catalogAPI,t[I.EFlowType.ADDTV]=e.config.api.addCatalogAPI,t[I.EFlowType.BUNDLE]=e.config.api.bundleCatalogAPI,t)))).mergeMap(function(t){return Object(I.FilterRestrictionObservable)(t,[B(t.data),W(t.data),I.Actions.omniPageLoaded(),J(e.widgetState=I.EWidgetStatus.RENDERED)])}));var t}).catch(function(e){return[Y(new I.Models.ErrorHandler("getCatalog",e))]})}},enumerable:!0,configurable:!0}),r([A.Injectable,i("design:paramtypes",[q,K])],e)}(),Q=j.ActionsObservable.concat,ee=I.Actions.setWidgetStatus,te=I.Actions.finalizeRestriction,ne=I.Actions.clearCachedState,ae=function(){function e(e){this.client=e,this.widgetState=I.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(j.combineEpics)(this.toggleSelectionEpic,this.finalizeRestrictionEpic)},Object.defineProperty(e.prototype,"toggleSelectionEpic",{get:function(){var e=this;return function(t,n){return t.ofType(G.toString()).filter(function(t){var n=t.payload;return Boolean(n)&&e.widgetState!==I.EWidgetStatus.UPDATING}).mergeMap(function(t){var a=t.payload;return Q([ee(e.widgetState=I.EWidgetStatus.UPDATING)],e.client.action(a).mergeMap(function(t){return Object(I.FilterRestrictionObservable)(t,[U(t.data,n.getState().catalog),W(t.data),ne([I.EWidgetName.PREVIEW]),ee(e.widgetState=I.EWidgetStatus.RENDERED)])}))}).catch(I.Models.ErrorHandlerObservable(G))}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"finalizeRestrictionEpic",{get:function(){var e=this;return function(t,n){return t.ofType(te.toString()).filter(function(t){var n=t.payload;return Boolean(n)&&Boolean(n.productOfferingDetail)&&e.widgetState!==I.EWidgetStatus.UPDATING}).mergeMap(function(t){var a=t.payload;return[I.Actions.broadcastUpdate(I.Actions.setProductConfigurationTotal(Object(I.ValueOf)(a,"productOfferingDetail.productConfigurationTotal"))),U(a,n.getState().catalog),W(a),ne([I.EWidgetName.PREVIEW]),ee(e.widgetState=I.EWidgetStatus.RENDERED)]})}},enumerable:!0,configurable:!0}),r([A.Injectable,i("design:paramtypes",[q])],e)}(),re=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=I.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(j.combineEpics)(this.requestDataEpic)},Object.defineProperty(e.prototype,"requestDataEpic",{get:function(){var e=this;return function(t,n){return t.ofType(L.toString()).filter(function(){return e.widgetState!==I.EWidgetStatus.UPDATING}).mergeMap(function(){return e.client.get(e.config.api.serviceAccountAPI).mergeMap(function(e){var t=e.data;return[V(t),F()]})}).catch(function(){return[F()]})}},enumerable:!0,configurable:!0}),r([A.Injectable,i("design:paramtypes",[q,K])],e)}(),ie=I.Actions.omniPageLoaded,oe=I.Actions.omniPageSubmit,ce=function(){function e(){this.widgetState=I.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(j.combineEpics)(this.pageLoadedEpic,this.pageSubmitEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){return function(e,t){return e.ofType(ie.toString()).filter(function(e){var t=e.payload;return Boolean(t)}).mergeMap(function(e){var t=e.payload,n=t.name,a=t.data,r=void 0===a?{}:a,i=I.Omniture.useOmniture(),o=S({id:n+"Pageload",s_oSS1:"~",s_oSS2:"~",s_oSS3:"~",s_oPGN:"Setup your service:"+n,s_oAPT:"~"},r);return I.Utils.getFlowType()!==I.EFlowType.TV||o.s_oAPT||(o.s_oAPT={actionId:394,actionresult:1}),I.Utils.getFlowType()!=I.EFlowType.TV&&i.trackFragment(o),[]}).catch(function(e){return[]})}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pageSubmitEpic",{get:function(){return function(e,t){return e.ofType(oe.toString()).mergeMap(function(){var e=t.getState().catalog;return I.Omniture.useOmniture().trackAction({id:"tvPageSubmit",s_oAPT:{actionId:647},s_oBTN:"Continue",s_oPRD:e.index.filter(function(e){return e.isSelected}).map(function(e){return{category:e.displayGroupKey,name:e.name,sku:"",quantity:"1",price:Object(I.ValueOf)(e,"regularPrice.price","0"),promo:Object(I.ValueOf)(e,"promotionDetails.promotionalPrice.price","")}})}),[]}).catch(function(e){return[]})}},enumerable:!0,configurable:!0}),r([A.Injectable],e)}(),le=I.Actions.setWidgetStatus,se=function(){function e(e,t,n,a){this.omnitureEpics=e,this.userAccountEpic=t,this.catalogEpics=n,this.orderingEpics=a}return e.prototype.combineEpics=function(){return Object(j.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(le.toString()).filter(function(e){return e.payload===I.EWidgetStatus.INIT}).mergeMap(function(){var e="~";switch(I.Utils.getFlowType()){case I.EFlowType.TV:case I.EFlowType.ADDTV:e="TV";break;case I.EFlowType.BUNDLE:e="Bundle"}switch(I.Utils.getFlowType()){case I.EFlowType.TV:I.Omniture.useOmniture().updateContext({s_oSS2:e,s_oSS3:"Change package"});break;case I.EFlowType.ADDTV:I.Omniture.useOmniture().updateContext({s_oSS2:e,s_oSS3:"Add Tv",s_oAPT:{actionId:507,actionresult:1,applicationState:0}});break;case I.EFlowType.BUNDLE:I.Omniture.useOmniture().updateContext({s_oSS2:e,s_oSS3:"Add Tv",s_oAPT:{actionId:508,actionresult:1,applicationState:0}})}return[L()]})}},enumerable:!0,configurable:!0}),r([A.Injectable,i("design:paramtypes",[ce,re,Z,ae])],e)}(),ue=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||A.ServiceLocator.instance.getService(A.CommonServices.Localization);var t=n.Instance,a=t?t.getLocalizedString(I.EWidgetName.TV,e,t.locale):e;return Boolean(a)?a:e},t.Instance=null,n=r([A.Injectable],t);var n}(A.CommonFeatures.BaseLocalization),de=A.CommonFeatures.BaseStore,me=A.CommonFeatures.actionsToComputedPropertyName,pe=me(N),fe=pe.setNavigation,Ee=pe.setCatalog,ge=pe.updateCatalog,be=pe.setAccountDetails,he=me(I.Actions).handleNav,Oe=function(e){function t(t,n,a,r){var i=e.call(this,n)||this;return i.client=t,i.epics=a,i.localization=r,i}return a(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){return Object(k.combineReducers)(S({},I.Reducers.WidgetBaseLifecycle(this.localization),I.Reducers.WidgetLightboxes(),I.Reducers.WidgetRestrictions(),{navigation:Object(M.handleActions)((e={},e[fe]=function(e,t){return t.payload||e},e),[]),accountDetails:Object(M.handleActions)((t={},t[be]=function(e,t){return t.payload||e},t),[]),catalog:Object(M.handleActions)((n={},n[Ee]=function(e,t){return t.payload||e},n[ge]=function(e,t){return t.payload||e},n),{}),navStatus:Object(M.handleActions)((a={},a[he]=function(e,t){return t.payload},a),!1)}));var e,t,n,a},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return[Object(j.createEpicMiddleware)(this.epics.omnitureEpics.combineEpics()),Object(j.createEpicMiddleware)(this.epics.userAccountEpic.combineEpics()),Object(j.createEpicMiddleware)(this.epics.catalogEpics.combineEpics()),Object(j.createEpicMiddleware)(this.epics.orderingEpics.combineEpics()),Object(j.createEpicMiddleware)(this.epics.combineEpics()),Object(j.createEpicMiddleware)((new I.ModalEpics).combineEpics()),Object(j.createEpicMiddleware)(new I.RestricitonsEpics(this.client,"TV_RESTRICTION_MODAL").combineEpics()),Object(j.createEpicMiddleware)((new I.LifecycleEpics).combineEpics())]},enumerable:!0,configurable:!0}),r([A.Injectable,i("design:paramtypes",[q,A.Store,se,ue])],t)}(de),ve=I.Components.Modal,ye="CHANNEL_DETIALS",Ne=Object(w.connect)(function(e){var t=e.lightboxData;return Object(I.ValueOf)(t,void 0,{})},function(e){return{closeLightbox:function(){return e(I.Actions.closeLightbox(ye))}}})(function(e){var t=e.name,n=e.imagePath,a=e.characteristics,r=e.shortDescription,i=e.longDescription,o=e.channelNumber,c=(e.closeLightbox,u(a)),l=c.language,s=c.genre,d=c.culture;return _.createElement(ve,{modalId:ye,onDismiss:function(){I.Omniture.useOmniture().trackAction({id:"channelDetailsLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},onShown:function(){I.Omniture.useOmniture().trackFragment({id:"channelDetailsLightbox",s_oAPT:{actionId:104},s_oPRM:"Channel details"})},title:_.createElement(P.FormattedMessage,{id:"CHANNEL_DETIALS_TITLE",values:{name:t}})},_.createElement("div",{className:"pad-30 pad-15-left-right-xs"},_.createElement("div",{className:"d-flex flex-column flex-sm-row"},_.createElement("div",{className:"heightFitContent flexStatic"},_.createElement("div",{style:{width:"94px",height:"94px"},className:"margin-15-bottom margin-30-right borderGrayLight6 flex align-center"},_.createElement("img",{src:n,alt:t,className:"fill pad-5"}))),_.createElement("div",{className:"pad-30-left"},_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"Channel number"})),_.createElement("p",{className:"no-margin txtSize16 txtVirginBlue line-height-18"},o),_.createElement("div",{className:"spacer5","aria-hidden":"true"}),_.createElement("p",{className:"no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold"},[f(s),f(d),f(l)].filter(function(e){return Boolean(e)}).join(" / ")),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),_.createElement("p",{className:"no-margin txtSize14 vm-dark-grey2 line-height-18",dangerouslySetInnerHTML:{__html:i||r}})))))}),xe=I.Components.Visible,Te=I.Components.Currency,Se=function(e){var t=e.regularPrice,n=e.promotionDetails;return _.createElement(_.Fragment,null,_.createElement("div",{className:"spacer5"}),_.createElement(xe,{when:Object(I.ValueOf)(n,"description",!1)},_.createElement("span",{className:"package-name pad-5 fill-xs txtSize12 txtGray border-radius-3 bgGrey sans-serif txtBold pad-10-left pad-10-right inline-block"},_.createElement(xe,{when:Object(I.ValueOf)(n,"discountDuration",!1)}," ",_.createElement(P.FormattedMessage,{id:"PromotionValid",values:{price:Math.abs(Object(I.ValueOf)(n,"discountPrice.price",0)),discountDuration:Object(I.ValueOf)(n,"discountDuration","")}})),_.createElement(xe,{when:Object(I.ValueOf)(n,"expiryDate",!1)}," ",_.createElement(P.FormattedDate,{value:Object(I.ValueOf)(n,"expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return _.createElement(P.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))),_.createElement("div",{className:"txtCurrency virginUltraReg txtBlack txtSize40 fill-xs"},_.createElement(xe,{when:Object(I.ValueOf)(n,void 0,!1)},_.createElement(P.FormattedMessage,{id:"Now"})," "),_.createElement(Te,{value:isNaN(Object(I.ValueOf)(n,"promotionalPrice",{}).price)?Object(I.ValueOf)(t,"price",0):Object(I.ValueOf)(n,"promotionalPrice.price",0),monthly:!0}),_.createElement(xe,{when:Object(I.ValueOf)(n,void 0,!1)},_.createElement("p",{className:"txtSize12 txtGray txtBold fill-xs flex"},_.createElement(P.FormattedMessage,{id:"Current Price",values:Object(I.ValueOf)(t,void 0,{})})))),_.createElement(xe,{when:Object(I.ValueOf)(n,"legalMessage",!1)},_.createElement("p",{className:"txtSize12 txtGray"},Object(I.ValueOf)(n,"legalMessage",_.createElement(P.FormattedMessage,{id:"Prices may increase legal"})))))},Ae=I.Components.Modal,Ie=I.Components.Visible,_e=function(e){var t=e.id,n=e.name,a=e.regularPrice,r=e.promotionDetails,i=e.childOfferings,o=e.isSelected,c=e.onSelect,l=Object(I.ValueOf)(i,"length",0)-1;return _.createElement("div",{className:"boxContainer borderGrayLight6 pad-15 margin-20-bottom "+(o?"borderBlack":"")},_.createElement("label",{id:t+"selectCTA",className:"graphical_ctrl pointer ctrl_radioBtn txtSize15",onClick:function(){return c(e)}},_.createElement("input",{type:"radio",name:"offering",checked:o}),_.createElement("span",{className:"ctrl_element"}),_.createElement("span",{className:"radio-text"},n),_.createElement(Ie,{when:l>0},_.createElement("p",{className:"no-margin"},_.createElement(P.FormattedMessage,{id:"Get additional channels",values:{additionalCahnnels:l}}))),_.createElement(Se,{regularPrice:a,promotionDetails:r})))},Ce={id:"NO",productOfferingType:I.Volt.EProductOfferingType.NONE},we=Object(w.connect)(function(e){var t=e.catalog,n=e.lightboxData,a=Object(I.ValueOf)(n,void 0,{}),r=Object(I.ValueOf)(a,"multipleWaysToAdd",[]).map(function(e){return t.index.find(function(t){return t.id===e})}).filter(Boolean);return{channel:a,parents:r,defaultSelection:r.find(function(e){return e.isSelected})||Ce}},function(e){return{onContinueClick:function(t){t&&e(G(t)),e(I.Actions.closeLightbox("MULTIPLE_WAYS_TO_ADD"))},closeLightbox:function(){I.Omniture.useOmniture().trackAction({id:"mutipleWaysLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"MULTIPLE_WAYS_CLOSE"}}),e(I.Actions.closeLightbox("MULTIPLE_WAYS_TO_ADD"))}}})(function(e){function t(e){d(e)}var n=e.channel,a=e.parents,r=e.defaultSelection,i=e.closeLightbox,c=e.onContinueClick,l=o(_.useState(Ce),2),s=l[0],d=l[1];_.useEffect(function(){d(r)},[r]);var m=n.name,p=n.imagePath,E=n.channelNumber,g=n.characteristics,b=n.shortDescription,h=n.longDescription,O=u(g),v=O.language,y=O.genre,N=O.culture;return _.createElement(Ae,{modalId:"MULTIPLE_WAYS_TO_ADD",className:"do-not-center-in",onShown:function(){I.Omniture.useOmniture().trackFragment({id:"mutipleWaysLightbox",s_oAPT:{actionId:104},s_oPRM:"Multiple ways to order"})},onDismiss:function(){I.Omniture.useOmniture().trackAction({id:"mutipleWaysLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},title:_.createElement(P.FormattedMessage,{id:"MULTIPLE_WAYS_TO_ADD_TITLE",values:{name:m}})},_.createElement("div",{className:""},_.createElement("div",{className:"pad-30 pad-15-left-right-xs"},_.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),_.createElement("div",{className:"d-flex flex-column flex-sm-row"},_.createElement("div",{className:"heightFitContent flexStatic"},_.createElement("div",{style:{width:"94px",height:"94px"},className:"margin-15-bottom margin-30-right borderGrayLight6 flex align-center"},_.createElement("img",{src:p,alt:m,className:"fill pad-5"}))),_.createElement("div",{className:"pad-30-left"},_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"Channel number"})),_.createElement("p",{className:"no-margin txtSize16 txtVirginBlue line-height-18"},E),_.createElement("div",{className:"spacer5","aria-hidden":"true"}),_.createElement("p",{className:"no-margin txtSize16 vm-dark-grey2 line-height-18 txtBold"},[f(y),f(N),f(v)].filter(function(e){return Boolean(e)}).join(" / ")),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),_.createElement("p",{className:"no-margin txtSize14 vm-dark-grey2 line-height-18",dangerouslySetInnerHTML:{__html:h||b}})))),_.createElement("form",{className:"pad-30 pad-15-left-right-xs"},_.createElement("div",{className:"spacer1 bgGrayLight3","aria-hidden":"true"}),_.createElement("div",{className:"spacer25 clear","aria-hidden":"true"}),_.createElement("p",{className:"txtSize16"},_.createElement(P.FormattedMessage,{id:"Ways to add this channel"})),a.map(function(e){return _.createElement(_e,S({},e,{isSelected:e.id===s.id,onSelect:t}))}),_.createElement(P.FormattedMessage,{id:"No thanks",values:{name:m}},function(e){return _.createElement(_e,S({},Ce,{name:e,isSelected:Ce.id===s.id,onSelect:t}))}))),_.createElement("div",{className:"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},_.createElement("button",{id:"MULTIPLE_WAYS_CONTINUE",className:"btn btn-primary fill-xs",onClick:function(){if(s.id!==r.id){var e="NO"===s.id?r:s;c(e.offeringAction),I.Omniture.useOmniture().trackAction({id:"mutipleWaysLightbox",s_oAPT:{actionId:647},s_oBTN:{ref:"MULTIPLE_WAYS_CONTINUE"},s_oPRD:{category:e.displayGroupKey,name:e.name,sku:"",quantity:"1",price:Object(I.ValueOf)(e,"regularPrice.price",""),promo:Object(I.ValueOf)(e,"promotionDetails.description","")}})}else i()}},_.createElement(P.FormattedMessage,{id:"MULTIPLE_WAYS_TO_ADD_CONTINUE"})),_.createElement("div",{className:"vSpacer15","aria-hidden":"true"}),_.createElement("button",{id:"MULTIPLE_WAYS_CLOSE",className:"btn btn-default fill-xs",onClick:i},_.createElement(P.FormattedMessage,{id:"MULTIPLE_WAYS_TO_ADD_CLOSE"}))))}),Re={},Pe=function(){function e(e){this.visible=!1,Re[e]&&Re[e].destroy(),this.elId=e,this.$triggerEl=$("#"+e),this.show=this.show.bind(this),this._hide=this._hide.bind(this),this.hide=this.hide.bind(this),this.destroy=this.destroy.bind(this),this._onTooltipClick=this._onTooltipClick.bind(this),this.$triggerEl.tooltip({trigger:"manual"}),this.triggerEl.addEventListener("mouseenter",this.show),Re[e]=this}return Object.defineProperty(e.prototype,"triggerEl",{get:function(){return document.getElementById(this.elId)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tooltipEl",{get:function(){return document.querySelector("."+this.elId+"_inst")},enumerable:!0,configurable:!0}),e.prototype.show=function(){var e=this;document.body.addEventListener("mousemove",this._hide),this.visible||this._delayed||(window.addEventListener("scroll",this._hide),this._delayed=setTimeout(function(){e._delayed=null,e.visible=!0,e.$triggerEl.tooltip("show"),requestAnimationFrame(function(){return e.tooltipEl.addEventListener("click",e._onTooltipClick)})},500))},e.prototype._hide=function(e){var t=e.target;(function(e,t){return e.filter(function(e){return Boolean(e)}).map(function(e){return function(e,t){return e.top-10<t.y&&e.bottom+10>t.y&&e.left<t.x&&e.right>t.x}(e,t)}).find(function(e){return e})||!1})([this.triggerEl&&this.triggerEl.getBoundingClientRect(),this.tooltipEl&&this.tooltipEl.getBoundingClientRect()],e)&&!t.classList.contains("tooltip-interactive")||(this.hide(),clearTimeout(this._delayed),this._delayed=null)},e.prototype._onTooltipClick=function(e){this.onTooltipClick(e)},e.prototype.hide=function(){this.visible&&(this.visible=!1,this.tooltipEl.removeEventListener("click",this._onTooltipClick),document.body.removeEventListener("mousemove",this._hide),window.removeEventListener("scroll",this._hide),this.$triggerEl.tooltip("hide"))},e.prototype.destroy=function(){document.body.removeEventListener("mousemove",this._hide),window.removeEventListener("scroll",this._hide),this.triggerEl.removeEventListener("mouseenter",this.show),Re[this.elId]=null},e}(),ke=function(e){var t=e.id,n=e.name,a=e.channelNumber,r=e.imagePath,i=e.characteristics,o=e.shortDescription,c=e.children,l=e.className,s=e.connectCtrl,d=_.useMemo(function(){return"tooltip"+t+Math.floor(100*Math.random())},[t]);_.useEffect(function(){var e=new Pe(d);return s(e),function(){return e.destroy()}},[]),Re[d]&&s(Re[d]);var m,p=u(i),E=p.culture,g=p.genre,b=p.language,h='<div style="display:flex; flex-direction: row;">\n      <div style="flex-shrink:0; padding-right:20px">\n        <img width="75" class="img-responsive channel-border" src="'+r+'" alt="'+n+'" />\n      </div>\n      <div>\n        <div class="txtVirginBlue txtSize18 noMargin">'+a+'</div>\n        <div class="txtBlack txtSize18 noMargin">'+[f(g),f(E),f(b)].filter(function(e){return Boolean(e)}).join(" / ")+'</div>\n        <div class="tooltip-description txtSize14" style="color:#333">'+((m=o).length>80?m.substr(0,80)+"...":m)+'</div>\n        <div class="spacer15"></div>\n        <button id="viewDetails'+t+'" class="txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal txtVirginBlue">'+ue.getLocalizedString("View details")+"</button>\n      </div></div>";return _.createElement("div",{className:l},_.createElement("div",{className:"floatL w-100"},_.createElement("div",{id:d,className:"tooltip-interactive w-100 alignIconWithText pointer",tabIndex:0,role:"tooltip","data-delay":"100","data-html":"true","data-placement":"top","data-container":"body","data-template":'<div class="tooltip channel-tooltip top in '+d+'_inst" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',"data-title":h},c)))},Me=I.Components.Visible,je=Object(w.connect)(function(e){return{}},function(e){return{onInfoClick:function(t,n){return e(I.Actions.openLightbox({lightboxId:ye,data:S({},t,{relativeId:n})}))},onMultipleWaysToAdd:function(t,n){return e(I.Actions.openLightbox({lightboxId:"MULTIPLE_WAYS_TO_ADD",data:S({},t,{relativeId:n})}))},onActionClick:function(t){return e(G(t))}}})(function(e){var t,n=e.id,a=e.name,r=e.imagePath,i=e.regularPrice,o=e.promotionDetails,c=e.offeringAction,l=e.characteristics,s=e.isAlreadyIncludedIn,d=e.isSelectable,m=e.isSelected,p=e.isDisabled,f=e.multipleWaysToAdd,E=e.onActionClick,g=e.onInfoClick,b=e.onMultipleWaysToAdd,h=e.channelNumber,O=u(l).callSign,v=d&&Object(I.ValueOf)(f,"length",0)>0,y=function(a){a&&(a.preventDefault(),a.stopPropagation()),t&&t.hide(),g(e,"channel_"+n)};return _.createElement(ke,S({key:n,connectCtrl:function(e){t=e,e.onTooltipClick=y}},e,{className:"col-12 col-sm-3 col-md-3 pad-15-left"}),_.createElement("div",{className:"",id:n,"data-cs":O},_.createElement("div",{className:"bell-tv-channel flexCol flexRow-xs\n              "+(d&&m?" selected":"")+"\n              "+(d?"":" bell-tv-channel-nonselectable")+"\n              "+(d&&p?" disabled":"")},_.createElement("div",{className:"bell-tv-channel-icon flexBlock flexCenter floatL-xs","aria-hidden":"true"},_.createElement("img",{src:r,alt:a})),_.createElement("div",{className:"bell-tv-channel-description flexGrow flex flex-column"},_.createElement("div",{className:"spacer5 d-none d-sm-block","aria-hidden":"true"}),Boolean(a)&&_.createElement("button",{id:"channel_"+n,className:"bell-tv-channel-title txtUnderline btn btn-link p-0 m-0 txtSize14 txtNormal flexGrow links-blue-on-bg-white",onClick:y},a),_.createElement("p",{className:"bell-tv-channel-number noMargin"},h),_.createElement(Me,{when:d&&Object(I.ValueOf)(i,"price",0)>0},_.createElement("p",{className:"bell-tv-channel-price txtBlue noMargin"},_.createElement(P.FormattedNumber,{value:Object(I.ValueOf)(o,"promotionalPrice.price",!1)||Object(I.ValueOf)(i,"price",0),format:"CAD"}),_.createElement(Me,{when:Object(I.ValueOf)(o,"promotionalPrice.price",!1)}," ",_.createElement("del",null,_.createElement(P.FormattedNumber,{value:Object(I.ValueOf)(i,"price",0),format:"CAD"})))))),_.createElement(Me,{when:!p&&v},_.createElement("div",{className:"bell-tv-channel-tile-description txtSize12"},_.createElement("dfn",null,_.createElement(P.FormattedMessage,{id:"Multipleways to add"})))),_.createElement(Me,{when:d&&p&&!Boolean(s)},_.createElement("div",{className:"bell-tv-channel-tile-description txtSize12"},_.createElement("dfn",null,_.createElement(P.FormattedMessage,{id:"Already selected"})))),_.createElement(Me,{when:Boolean(s)},_.createElement("div",{className:"bell-tv-channel-tile-description txtSize12"},_.createElement("dfn",null,_.createElement(P.FormattedMessage,{id:"Already included in",values:{name:s}})))),_.createElement(Me,{when:d},_.createElement("label",{htmlFor:"offeringWays_"+n,className:"bell-tv-channel-checkbox graphical_ctrl graphical_ctrl_checkbox absolute",onClick:function(t){t.preventDefault(),t.stopPropagation(),p||(v?b(e,"channel_"+n):E(c))}},_.createElement("input",{id:"offeringWays_"+n,type:"checkbox",name:"packages",checked:m,disabled:p}),_.createElement("span",{className:"ctrl_element chk_radius"}),_.createElement("span",{className:"sr-only"},a))))))}),De=I.Components.Visible,Le=Object(w.connect)(function(e){return{}},function(e){return{onActionClick:function(t){return e(G(t))}}})(function(e){var t=e.id,n=e.name,a=e.imagePath,r=e.isAlreadyIncludedIn,i=e.isSelectable,c=e.isSelected,l=(e.isCurrent,e.isDisabled),s=e.regularPrice,u=e.shortDescription,d=e.longDescription,m=e.promotionDetails,f=e.offeringAction,E=e.childOfferings,g=e.onActionClick,b=e.intl,h=o(_.useState(!1),2),O=h[0],v=h[1];_.useEffect(function(){O&&I.Omniture.useOmniture().trackAction({id:"showChannelsClick",s_oAPT:{actionId:648},s_oEPN:"Show Channel"})},[O]);var y=Object(I.ValueOf)(E,"length",0)>0;return _.createElement("div",{className:"bell-tv-package bell-tv-movie-pack noBorder "+(c?"selected":"")+" "+(l?"disabled":""),id:t},_.createElement("div",{className:"bell-tv-package-body flexRow"},_.createElement("div",{className:"bell-tv-package-left flexGrow flexCol"},_.createElement("label",{id:"combo_"+t,onClick:function(e){e.preventDefault(),e.stopPropagation(),i&&!l&&g(f)},className:"bell-tv-package-checkbox graphical_ctrl graphical_ctrl_checkbox txtSize15 block"},_.createElement("input",{type:"checkbox",name:"packages",checked:c,disabled:l||!i}),_.createElement("span",{className:"block txtSize16 pad-5-left txtBlack"},n),_.createElement(De,{when:i&&l&&!Boolean(r)},_.createElement("span",{className:"block bell-tv-channel-tile-description txtSize12"},_.createElement("dfn",null,_.createElement(P.FormattedMessage,{id:"Already selected"})))),_.createElement(De,{when:Boolean(r)},_.createElement("span",{className:"block bell-tv-channel-tile-description txtSize12"},_.createElement("dfn",null,_.createElement(P.FormattedMessage,{id:"Already included in",values:{name:r}})))),_.createElement(Se,{regularPrice:s,promotionDetails:m}),_.createElement("span",{className:"ctrl_element chk_radius borderGrayLight7"})),_.createElement("ul",{className:"flexRow flexWrap bell-tv-individual-channels virgin-channel-block"},Object(I.ValueOf)(E,void 0,[]).slice(0,3).map(function(e){return _.createElement("li",null,_.createElement("img",{src:Object(I.ValueOf)(e,"imagePath",""),alt:Object(I.ValueOf)(e,"name",""),title:Object(I.ValueOf)(e,"name","")}))})),_.createElement("div",{className:"flexGrow","aria-hidden":"true"}),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),_.createElement(De,{when:y},_.createElement("div",{className:" flexBlock flexRow"},_.createElement("button",{id:"View_all_channels_"+t,onClick:function(){return v(!O)},className:"btn btn-link no-pad links-blue-on-bg-white txtDecorationNoneHover txtSize14","aria-controls":"View_all_channels_"+t,"aria-expanded":O,"aria-label":b.formatMessage({id:"Show channels"})+" "+b.formatMessage({id:"FOR_TEXT"})+" "+n},_.createElement("span",{className:"volt-icon links-blue-on-bg-white margin-5-top "+(O?"icon-Collapse":"icon-Expand")},_.createElement("span",{className:"volt-icon path1 icon-Collapse"}),_.createElement("span",{className:"volt-icon path2 icon-Collapse"})),_.createElement("span",{className:"sans-serif margin-10-left"},_.createElement(P.FormattedMessage,{id:"Show channels"})))))),_.createElement("div",{className:"spacer10 flexStatic d-block d-sm-none","aria-hidden":"true"}),_.createElement("div",{className:"bell-tv-package-right flexStatic block-xs"},_.createElement(De,{when:Boolean(a)},_.createElement("img",{src:a,alt:n})))),_.createElement(De,{when:O},_.createElement("div",{className:"bell-tv-package-footer bgGrayLight4 pad-30 pad-15-left-right-xs expanded",role:"region","aria-hidden":!O},_.createElement("div",{className:"bell-tv-package-filters-row no-pad"},_.createElement(De,{when:Boolean(d||u)},_.createElement("p",{dangerouslySetInnerHTML:{__html:d||u}}),_.createElement("hr",null)),_.createElement("div",{className:"bell-tv-channels bell-tv-channel-picker flexRow"},p(Object(I.ValueOf)(E,void 0,[])).map(function(e){return _.createElement(je,S({key:e.id},e,{isSelectable:!1}))}))))))}),Ve=Object(P.injectIntl)(Le),Fe=function(e){e.pageName;var t=e.label,n=e.content,a=o(_.useState(!1),2),r=a[0],i=a[1];return _.useEffect(function(){r&&I.Omniture.useOmniture().trackAction({id:"ligalStuffClick",s_oAPT:{actionId:648},s_oEPN:"Legal Stuff"})},[r]),_.createElement("div",{className:"virginUltraReg more-info pad-15-top accss-focus-outline-override-grey-bg",id:"moreInfo"},_.createElement("button",{id:"Legal",className:"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray",onClick:function(){return i(!r)},"aria-expanded":r},_.createElement("span",{className:"volt-icon "+(r?"icon-collapse_m":"icon-expand_m"),"aria-hidden":"true"}),"  ",_.createElement(P.FormattedMessage,{id:t})),_.createElement("div",{className:"spacer30","aria-hidden":"true"}),_.createElement(I.Components.Visible,{when:r},_.createElement("div",{className:"moreInfoBox"},_.createElement("button",{id:"legal_close",type:"button",onClick:function(){return i(!1)},className:"close moreInfoLink x-inner txtDarkGrey txtSize18","aria-label":"close"},_.createElement("span",{className:"virgin-icon icon-big_X","aria-hidden":"true"})),_.createElement(P.FormattedHTMLMessage,{id:n}))))},Be=function(e){var t=e.name,n=e.data,a=e.children,r=Object(w.useDispatch)();return _.useEffect(function(){r(I.Actions.omniPageLoaded(t,n))},[]),_.createElement(_.Fragment,null,a)},We=Object(w.connect)(function(e){var t=e.catalog;return{packages:Object(I.ValueOf)(t,"offerings."+I.Volt.EDIsplayGroupKey.ADD_ON,[]).filter(function(e){return e.productOfferingType===I.Volt.EProductOfferingType.COMBO})}})(function(e){var t=e.packages;return _.createElement(Be,{name:"Addons"},_.createElement("div",{className:"flexRow flex-justify-space-between"},_.createElement("div",{className:"margin-xs"},_.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(P.FormattedMessage,{id:"Addons page"})),_.createElement(P.FormattedHTMLMessage,{id:"Addons page description"},function(e){return _.createElement(I.Components.Visible,{when:Boolean(e)},_.createElement("div",{className:"spacer5"}),_.createElement("p",{className:"noMargintxtSize14"},e))}))),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),p(t).map(function(e){return _.createElement(Ve,S({key:e.id},e))}),_.createElement(Fe,{pageName:I.Volt.EDIsplayGroupKey.ADD_ON,label:"LEGAL_LABEL_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,content:"LEGAL_COPY_"+I.Volt.EDIsplayGroupKey.ADD_ON}))}),Ge={genre:[],language:[],isDontHave:!1,isHave:!1,sortBy:"name",sortOrder:"asc"},Ue=function(e,t,n){32!==e.keyCode&&"keydown"===e.type||(e.preventDefault(),e.stopPropagation(),n(t))},ze=function(e,t){32!==e.keyCode&&"keydown"===e.type||(e.preventDefault(),e.stopPropagation(),t())},He=function(e){var t=e.list,n=e.formatter,a=o(_.useState(t.length>15?0:t.length),2),r=a[0],i=a[1],c=y(t);return _.useEffect(function(){c&&c.length===t.length||i(t.length>15?0:t.length)},[t]),_.useEffect(function(){r<t.length&&requestAnimationFrame(function(){return i(r+15)})},[r]),_.createElement(_.Fragment,null,t.slice(0,r).map(n))},Ke=I.Components.Visible,qe=function(){return _.createElement("div",{className:"pad-30"},_.createElement("div",{className:"flexBlock flexRow"},_.createElement("div",{className:""},_.createElement("span",{className:"virgin-icon icon-BIG_WARNING txtSize38"},_.createElement("span",{className:"virgin-icon path1"}),_.createElement("span",{className:"virgin-icon path2"}))),_.createElement("div",{className:"pad-15-left"},_.createElement("h3",{className:"noMargin txtSize20 pad-15-bottom"},_.createElement(P.FormattedMessage,{id:"NO_CHANNELS_FOUND_MESSAGE"})),_.createElement("p",{className:"noMargin"},_.createElement(P.FormattedMessage,{id:"NO_CHANNELS_FOUND_DESCRIPTION"})))))},Xe=function(e){var t=e.Filter,n=e.filters,a=e.toggleTray,r=v(t.getState())[1];return _.createElement("div",{className:"bell-tv-package-filters-tray",style:{maxHeight:"9999em"}},_.createElement("div",{className:"spacer1 bgGray","aria-hidden":"true"}),_.createElement("form",{className:"bell-tv-package-filters-row bgGray19",onSubmit:function(e){e.preventDefault(),I.Omniture.useOmniture().trackAction({id:"advanceFilterApply",s_oAPT:{actionId:647},s_oBTN:"Advance Filters"}),t.setState(r.getState()),requestAnimationFrame(function(){return a(!1)})},onReset:function(e){t.reset(),requestAnimationFrame(function(){return a(!1)})}},_.createElement("div",{className:"flexRow flex-justify-space-between txtSize12-xs"},_.createElement("p",{className:"txtBold txtBlack1"},_.createElement(P.FormattedMessage,{id:"Refine by"}))),_.createElement("div",{className:"flexRow flex-justify-space-between flexCol-xs"},_.createElement("div",{className:"flexCol",role:"group","aria-labelledby":"refineGenresLabel"},_.createElement("p",{className:"txtBlack1",id:"refineGenresLabel"},_.createElement(P.FormattedMessage,{id:"Genres"})),_.createElement("ul",{className:"noMargin noBullets"},Object(I.ValueOf)(n,"genres",[]).sort().map(function(e){return _.createElement("li",{key:e},_.createElement("label",{id:"checkboxLabel_"+e,className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(t){return Ue(t,e,r.toggleGenre)},onKeyDown:function(t){return Ue(t,e,r.toggleGenre)}},_.createElement("input",{id:"checkbox_"+e,type:"checkbox",name:"genres",value:e,checked:r.hasGenre(e)}),_.createElement(P.FormattedMessage,{id:e}),_.createElement("span",{className:"ctrl_element chk_radius"})),_.createElement("div",{className:"spacer10","aria-hidden":"true"}))}))),_.createElement("div",{className:"flexCol",role:"group","aria-labelledby":"refineLanguageLabel"},_.createElement("p",{className:"txtBlack1",id:"refineLanguageLabel"},_.createElement(P.FormattedMessage,{id:"Language"})),_.createElement("ul",{className:"noMargin noBullets"},Object(I.ValueOf)(n,"languages",[]).sort().map(function(e){return _.createElement("li",{key:e},_.createElement("label",{id:"label_Lang_"+e,className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(t){return Ue(t,e,r.toggleLanguage)},onKeyDown:function(t){return Ue(t,e,r.toggleLanguage)}},_.createElement("input",{id:"checkbox_Lang_"+e,type:"checkbox",name:"languages",value:e,checked:r.hasLanguage(e)}),_.createElement(P.FormattedMessage,{id:e}),_.createElement("span",{className:"ctrl_element chk_radius"})),_.createElement("div",{className:"spacer10","aria-hidden":"true"}))}))),_.createElement("div",{className:"flexCol flex-justify-space-between",role:"group","aria-labelledby":"refineOtherLabel"},_.createElement("div",null,_.createElement("p",{className:"txtBlack1",id:"refineOtherLabel"},_.createElement(P.FormattedMessage,{id:"Other"})),_.createElement("ul",{className:"noMargin noBullets"},_.createElement("li",null,_.createElement("label",{id:"label_dont_have",className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(e){return ze(e,r.toggleDontHave)},onKeyDown:function(e){return ze(e,r.toggleDontHave)}},_.createElement("input",{id:"checkbox_dont_have",type:"checkbox",name:"dontHave",value:"yes",checked:r.hasDontHave()}),_.createElement("span",null,_.createElement(P.FormattedMessage,{id:"Channels I dont have"})),_.createElement("span",{className:"ctrl_element chk_radius"})),_.createElement("div",{className:"spacer10","aria-hidden":"true"})),_.createElement("li",null,_.createElement("label",{id:"label_have_channels",className:"graphical_ctrl graphical_ctrl_checkbox pointer",onClick:function(e){return ze(e,r.toggleHave)},onKeyDown:function(e){return ze(e,r.toggleHave)}},_.createElement("input",{id:"checkbox_have_channels",type:"checkbox",name:"have",value:"yes",checked:r.hasHave()}),_.createElement("span",null,_.createElement(P.FormattedMessage,{id:"Channels I have"})),_.createElement("span",{className:"ctrl_element chk_radius"})),_.createElement("div",{className:"spacer10","aria-hidden":"true"})))),_.createElement("div",{className:" txtRight"},_.createElement("button",{id:"RESET_FILTER",type:"reset",className:"btn btn-link txtVirginBlueFix txtDecoration_hover"},_.createElement(P.FormattedMessage,{id:"Reset filters"})),_.createElement("span",{className:"vSpacer15","aria-hidden":"true"}),_.createElement("button",{id:"APPLY_BTN",type:"submit",className:"btn btn-primary txtSize12-xs"},_.createElement(P.FormattedMessage,{id:"Apply"})))))))},Ye=function(e){var t=e.allowMultipleWaysToAdd,n=e.allowSelection,a=e.forceSelectable,r=e.channels,i=e.label,c=(e.groupName,e.showHeader),l=e.showFilters,s=o(_.useState(!1),2),d=s[0],m=s[1],p=o(function(e,t){var n=o(_.useState({filters:{},channels:[]}),2),a=n[0],r=n[1],i=o(v(h()),2),c=i[0],l=i[1],s=y(e);return _.useEffect(function(){var t,n=a.filters;n.genres&&n.languages&&e.length===s.length||(n={genres:(t=e,Object(I.ValueOf)(t,void 0,[]).reduce(function(e,t){var n=u(t.characteristics).genre;return Boolean(n)&&n.split(",").map(function(e){return e.trim()}).filter(Boolean).forEach(function(t){e.indexOf(t)<0&&e.push(t)}),e.sort()},[]).filter(Boolean).sort()),languages:["English","French","Other"]});var i=e.filter(function(e){var t=e.isSelected,n=e.isCurrent,a=u(e.characteristics),r=a.language,i=a.genre,o=!0;return c.isDontHave?o=!(t||n):c.isHave&&(o=t||n),o&&c.genre.length>0&&(o=!!c.genre.find(function(e){return(i||"").indexOf(e)>-1})),o&&c.language.length>0&&(o=c.language.indexOf("Other")>-1?!/(english|french)/i.test(r||""):!!c.language.find(function(e){return(r||"").indexOf(e)>-1})),o}).sort(function(e,t){var n=e[c.sortBy]||u(e.characteristics)[c.sortBy]||"",a=t[c.sortBy]||u(t.characteristics)[c.sortBy]||"";return n.localeCompare(a,void 0,{numeric:!0,sensitivity:"base"})*("desc"===c.sortOrder?-1:1)});r({filters:n,channels:i})},[c,e]),[a,l]}(r),2),f=p[0],E=p[1],g=_.useContext(I.WidgetContext).config.filters,b=Object(I.ValueOf)(f,"channels",[]),O=!(r&&r.length>0);return _.createElement("div",{className:"panel panel-shadow"},_.createElement(Ke,{when:c},_.createElement("nav",{className:"bell-tv-package-filters-row bgWhite flexRow border-radius-top flexCenter",role:"tablist"},_.createElement("ul",{className:"noMargin noBullets bell-tv-package-filters-nav margin-10-bottom-xs",role:"presentation"},_.createElement("li",{tabIndex:E.hasGenre()?0:-1,className:"table-cell "+(E.hasGenre()?"active":""),role:"tab",onClick:function(){return E.setGenre()},onKeyUp:function(){return E.setGenre()},"aria-selected":E.hasGenre()?"true":"false"},_.createElement("label",{id:"filter_All",className:"pointer"},_.createElement(P.FormattedMessage,{id:"FILTER_TEXT_All"}))),Object(I.ValueOf)(f,"filters.genres",[]).filter(function(e){return g.genres.indexOf(e)>-1}).map(function(e){return _.createElement("li",{tabIndex:E.onlyGenre(e)?0:-1,role:"tab",id:e,key:e,onKeyUp:function(t){void 0!==t.keyCode&&13!==t.keyCode||(E.setGenre(e),m(!1))},onClick:function(){E.setGenre(e),m(!1)},className:"table-cell "+(E.onlyGenre(e)?"active":""),"aria-selected":E.onlyGenre(e)?"true":"false"},_.createElement("label",{className:"pointer",id:"filter_"+e},_.createElement(P.FormattedMessage,{id:"FILTER_TEXT_"+e})))})),_.createElement("div",{className:"spacer2 fill-xs border-filter bgVirginCustomGray1 d-block d-sm-none","aria-hidden":"true"}),_.createElement("div",{className:"vSpacer10 flexGrow hidden-xs","aria-hidden":"true"}),_.createElement("div",{className:"spacer10","aria-hidden":"true"}),_.createElement("ul",{className:"noMargin noBullets bell-tv-package-filters-nav",role:"presentation"},_.createElement("li",null,_.createElement("button",{id:"Advanced_Filters",onClick:function(){return m(!d)},"aria-expanded":d,disabled:O,className:"btn btn-link no-pad txtDecorationNoneHover"},_.createElement("span",{className:"sans-serif icon-blue showText txtDecoration_hover links-blue-on-bg-white"},_.createElement(P.FormattedMessage,{id:"Advanced Filters"}),"  ",_.createElement("span",{className:"volt-icon txtSize16 icon-blue icon-"+(d?"Collapse":"Expand")},_.createElement("span",{className:"volt-icon path1 icon-"+(d?"Collapse":"Expand")}),_.createElement("span",{className:"volt-icon path2 icon-"+(d?"Collapse":"Expand")})))))))),_.createElement(Ke,{when:d},_.createElement(Xe,{filters:f.filters,Filter:E,toggleTray:m})),_.createElement("div",{className:"bell-tv-package-filters-row bgGray19 block-xs"},_.createElement("div",{className:"spacer6 visible-xs","aria-hidden":"true"}),_.createElement("div",{className:"txtSize18 flexGrow"},_.createElement(Ke,{when:Boolean(i),placeholder:_.createElement("span",null,_.createElement(P.FormattedMessage,{id:"Number of channels"})," (",Object(I.ValueOf)(f,"channels.length",0),")")},i),_.createElement(Ke,{when:l},_.createElement("div",{className:"spacer15"}))),_.createElement(Ke,{when:l},_.createElement("div",{className:"flexRow block-xs flexCenter"},_.createElement("div",{className:"flexStatic",role:"group","aria-labelledby":"languageLabel"},_.createElement("span",{className:"txtBold txtBlack",id:"languageLabel"},_.createElement(P.FormattedMessage,{id:"Language"})),_.createElement("span",{className:"vSpacer15","aria-hidden":"true"}),Object(I.ValueOf)(f,"filters.languages",[]).filter(function(e){return g.languages.indexOf(e)>-1}).map(function(e,t){return _.createElement(_.Fragment,{key:t},_.createElement("label",{id:"lang_"+e,onClick:function(t){Ue(t,e,E.toggleLanguage),m(!1)},onKeyDown:function(t){Ue(t,e,E.toggleLanguage),m(!1)},className:"graphical_ctrl graphical_ctrl_checkbox pointer"},_.createElement("input",{id:"input_"+e,type:"checkbox",name:"packages",disabled:O,checked:E.hasLanguage(e)}),_.createElement(P.FormattedMessage,{id:e}),_.createElement("span",{className:"ctrl_element chk_radius"})),_.createElement("span",{className:"vSpacer30","aria-hidden":"true"}))})),_.createElement("div",{className:"flexGrow spacer15","aria-hidden":"true"}),_.createElement("div",{className:"flexStatic"},_.createElement("span",{className:"txtBold txtBlack"},_.createElement(P.FormattedMessage,{id:"Sort by"})),_.createElement("button",{id:"sortBy_num",onClick:function(){return E.setSort("channelNumber")},disabled:O,className:"btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray"},_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"Sort by"})),_.createElement(Ke,{when:"channelNumberdesc"===E.whichSort(),placeholder:_.createElement(P.FormattedMessage,{id:"0-9"})},_.createElement(P.FormattedMessage,{id:"9-0"}))),_.createElement("button",{id:"sortBy_alpha",onClick:function(){return E.setSort("name")},disabled:O,className:"btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray"},_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"Sort by"})),_.createElement(Ke,{when:"namedesc"===E.whichSort(),placeholder:_.createElement(P.FormattedMessage,{id:"A-Z"})},_.createElement(P.FormattedMessage,{id:"Z-A"}))))))),_.createElement("div",{role:"group","aria-labelledby":"filter_"+E.selectedGenre(),className:"bell-tv-package-filters-row bgWhite"},_.createElement(Ke,{when:!O,placeholder:_.createElement(qe,null)},_.createElement(Ke,{when:Object(I.ValueOf)(b,"length",0)>0,placeholder:_.createElement(qe,null)},_.createElement("div",{className:"bell-tv-channels bell-tv-channel-picker flexRow"},_.createElement(He,{list:b,formatter:function(e){return _.createElement(je,S({key:e.id},e,{isSelectable:a||e.isSelectable&&!!n,multipleWaysToAdd:t?e.multipleWaysToAdd:[]}))}}))))))};Ye.defaultProps={showHeader:!0,showFilters:!0};var $e=Ye,Je=I.Components.Modal,Ze=function(e){return e.filter(function(e){return e.isSelected})},Qe=Object(w.connect)(function(e){return{}},function(e){return{onRemoveChannel:function(t){return e(G(t))}}})(function(e){var t=e.channels,n=e.totalPrice,a=e.onRemoveChannel,r=e.intl;return _.createElement(Je,{modalId:"SELECTED_CANNELS",className:t.length>8?"do-not-center-in":"",onShown:function(){I.Omniture.useOmniture().trackFragment({id:"selectedChannelsLightbox",s_oAPT:{actionId:104},s_oPRM:"Selected channels"})},onDismiss:function(){I.Omniture.useOmniture().trackAction({id:"selectedChannelsLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},flexDisplay:!0,title:_.createElement(P.FormattedMessage,{id:"SELECTED_CANNELS_TITLE"})},_.createElement("div",{className:"pad-30 pad-15-left-right-xs"},_.createElement("p",{className:"no-margin txtSize18 txtBold vm-dark-grey2 line-height-18"},t?_.createElement(P.FormattedMessage,{id:"SELECTED_CANNELS_LABEL",values:{total:Ze(t).length,price:n}}):null),_.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),_.createElement("div",{className:"flexRow flexCol-xs flexWrap "},t&&Ze(t).map(function(e){var t=e.id,n=e.name,i=e.imagePath,o=e.offeringAction,c=u(e.characteristics).callsign;return _.createElement("div",{className:"selectchannelBlock pad-15 fill-xs txtCenter-xs margin-15-right pad-0-bottom"},_.createElement("button",{id:t+"RemoveChannel",onClick:function(){return a(o)},type:"button",className:"no-pad close no-margin","aria-label":r.formatMessage({id:"REMOVE_TEXT"})+" "+n},_.createElement("span",{className:"volt-icon icon-big_X txtSize16 close-channel"})),_.createElement("div",{className:"channelImage margin-15-bottom margin-10-left justify-center no-margin-xs fill-xs"},_.createElement("img",{src:i,alt:n})),_.createElement("div",{className:"pad-5-left"},_.createElement("p",{className:"channelName no-margin txtSize14 txtVirginBlue line-height-18 txtUnderline"},n),_.createElement("div",{className:"spacer10","aria-hidden":"true"}),_.createElement("p",{className:"margin-15-bottom"},c)))}),_.createElement("div",{className:"spacer30 clear","aria-hidden":"true"}))),_.createElement("div",{className:"modal-footer bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg"},_.createElement("div",{className:"col1 flexRow"},_.createElement("button",{id:"SEL_CANNELS_CLOSE",className:"btn btn-primary",onClick:function(){I.Omniture.useOmniture().trackAction({id:"selectedChannelsLightbox",s_oAPT:{actionId:647},s_oBTN:"Close"})},"data-dismiss":"modal"},_.createElement(P.FormattedMessage,{id:"SELECTED_CANNELS_CLOSE"})))))}),et=Object(P.injectIntl)(Qe),tt=Object(w.connect)(function(e){var t=e.catalog,n=e.navigation;return{channels:Object(I.ValueOf)(t,"offerings."+I.Volt.EDIsplayGroupKey.ALACARTE,[]),navigation:Object(I.ValueOf)(n,void 0,[]).find(function(e){return e.key===I.Volt.EDIsplayGroupKey.ALACARTE})}},function(e){return{openLightbox:function(t){return e(I.Actions.openLightbox({lightboxId:"SELECTED_CANNELS",data:{relativeId:t}}))}}})(function(e){var t=e.channels,n=e.navigation,a=e.openLightbox,r=_.useRef(null),i=o(_.useState({isFloating:!1,leftPos:"auto"}),2),c=i[0],l=i[1],s=Object(I.ValueOf)(n,"count",0),u=Object(I.ValueOf)(n,"subTotalPrice.price",0);return _.useEffect(function(){var e=function(){g(r,l)};return window.addEventListener("scroll",e),function(){window.removeEventListener("scroll",e)}},[]),_.createElement(Be,{name:"Alacarte"},_.createElement("div",{className:"flexRow flex-justify-space-between"},_.createElement("div",{className:"margin-xs"},_.createElement("h2",{id:"group-Alacarte",className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(P.FormattedMessage,{id:"A la carte page"})),_.createElement(P.FormattedHTMLMessage,{id:"A la carte page Description"},function(e){return _.createElement(I.Components.Visible,{when:Boolean(e)},_.createElement("div",{className:"spacer5"}),_.createElement("p",{className:"noMargintxtSize14"},e))})),_.createElement("div",{id:"wrap",ref:r,tabIndex:0,role:"button",onClick:function(e){return s>0&&a("wrap")},onKeyDown:function(e){return("Enter"===e.key||32===e.keyCode||"keydown"!==e.type)&&s>0&&a("wrap")},className:"floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer "+(c.isFloating?"fix-floating-notification":""),style:{left:c.leftPos}},_.createElement("div",{className:" txtSize26 virginUltraReg pad-10-top"},s),_.createElement("span",{className:""},_.createElement(P.FormattedMessage,{id:"CHANNELS_SELECTED"})),_.createElement("div",{className:"txtBold"},_.createElement(I.Components.BellCurrency,{value:u})))),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),_.createElement($e,{groupName:"Alacarte",channels:t,allowSelection:!0}),_.createElement(Fe,{pageName:I.Volt.EDIsplayGroupKey.ALACARTE,label:"LEGAL_LABEL_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,content:"LEGAL_COPY_"+I.Volt.EDIsplayGroupKey.ALACARTE}),_.createElement(et,{channels:t.filter(function(e){return e.isSelected}),totalPrice:u}))}),nt=Object(w.connect)(function(e){var t=e.catalog;return{channels:Object(I.ValueOf)(t,"channels",[]),refresh:1e3*Math.random()}})(function(e){var t=e.channels;return e.refresh,_.createElement(Be,{name:"Browse all Channels"},_.createElement("div",{className:"flexRow flex-justify-space-between"},_.createElement("div",{className:"margin-xs"},_.createElement("h2",{id:"group-BrowseAll",className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(P.FormattedMessage,{id:"All channels page"})),_.createElement(P.FormattedHTMLMessage,{id:"All channels page description"},function(e){return _.createElement(I.Components.Visible,{when:Boolean(e)},_.createElement("div",{className:"spacer5"}),_.createElement("p",{className:"noMargintxtSize14"},e))}))),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),_.createElement($e,{groupName:"group-BrowseAll",channels:t,allowSelection:!0,forceSelectable:!0,allowMultipleWaysToAdd:!0}),_.createElement(Fe,{pageName:I.Volt.EDIsplayGroupKey.TV_BROWSE_ALL,label:"LEGAL_LABEL_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,content:"LEGAL_COPY_"+I.Volt.EDIsplayGroupKey.TV_BROWSE_ALL}))}),at=I.Components.BellCurrency,rt=I.Components.Visible,it=function(e){var t=e.Name,n=e.RegularPrice,a=e.PromotionDetails,r=e.ChannelCount,i=e.displayGroupKey;return _.createElement("div",null,_.createElement("div",{className:"flexRow"},_.createElement("div",{className:"flexGrow"},t,_.createElement(rt,{when:i===I.Volt.EDIsplayGroupKey.ALACARTE&&r>0},_.createElement(P.FormattedMessage,{id:"Count of channels",values:{count:r}}))),_.createElement("div",null,_.createElement(at,{value:Object(I.ValueOf)(n,"Price",0)}),_.createElement("span",{"aria-hidden":!0},_.createElement(P.FormattedMessage,{id:"PER_MO"})),_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"PER_MONTH"},function(e){return _.createElement(_.Fragment,null,e)})))),_.createElement(rt,{when:!!a},_.createElement("div",{className:"spacer5","aria-hidden":"true"}),_.createElement(rt,{when:Object(I.ValueOf)(a,"Description",!1)},_.createElement("div",{className:"flexRow"},_.createElement("div",{className:"flexGrow"},Object(I.ValueOf)(a,"Description","")),_.createElement("div",null,_.createElement(at,{value:Object(I.ValueOf)(a,"PromotionalPrice.Price",0)}),_.createElement("span",{"aria-hidden":!0},_.createElement(P.FormattedMessage,{id:"PER_MO"})),_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"PER_MONTH"},function(e){return _.createElement(_.Fragment,null,e)}))))),_.createElement(rt,{when:Object(I.ValueOf)(a,"ExpiryDate",!1)},_.createElement("div",null,_.createElement(P.FormattedDate,{value:Object(I.ValueOf)(a,"ExpiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return _.createElement(P.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))))},ot=Object(w.connect)(function(e){return{accountDetails:e.accountDetails||[]}})(function(e){var t=e.accountDetails,n=o(_.useState(!1),2),a=n[0],r=n[1],i=a?"icon-Collapse":"icon-Expand";return _.useEffect(function(){a&&I.Omniture.useOmniture().trackAction({id:"myCurrentPackageCTA",s_oAPT:{actionId:648},s_oEPN:"My current TV package"})},[a]),_.createElement(rt,{when:Array.isArray(t)&&t.length>0},_.createElement("section",{className:"bgVirginGradiant"},_.createElement("div",{className:"container liquid-container sans-serif"},_.createElement("div",{className:"accordion-group internet-current-package flexCol accss-focus-outline-override-black-bg"},_.createElement("div",{className:"accordion-heading col-xs-12 noPaddingImp accss-focus-outline-override-black-bg"},_.createElement("a",{role:"button",id:"my_currentPack",href:"javascript:void(0)",onClick:function(){return r(!a)},"aria-controls":"div1-accessible",className:"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content","aria-expanded":a},_.createElement("span",{className:"sr-only accordion-label","aria-live":"polite","aria-atomic":"true","aria-hidden":"true"},_.createElement(P.FormattedMessage,{id:a?"Collapse":"Expand"})),_.createElement("span",{className:i+" virgin-icon txtSize24 virginRedIcon","aria-hidden":"true"},_.createElement("span",{className:"virgin-icon path1 "+i}),_.createElement("span",{className:"virgin-icon path2 "+i})),_.createElement("div",{className:"margin-15-left flexCol"},_.createElement("span",{className:"txtWhite txtBold txtSize18"},_.createElement(P.FormattedMessage,{id:"My current TV package"})),_.createElement("span",{className:"expand txtWhite txtSize12 no-margin-top",style:{display:a?"none":void 0}},_.createElement(P.FormattedMessage,{id:"Expand to view details"}))))),_.createElement("div",{id:"div1-accessible",className:"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left",style:{display:a?"block":"none"}},_.createElement("div",{className:"accordion-inner flexWrap flexJustifySpace flexRow"},t.map(function(e){var t=e.displayGroupKey,n=e.offerings;return _.createElement("div",{className:"col-sm-5 margin-15-bottom"},_.createElement("strong",null,_.createElement(P.FormattedMessage,{id:"HEADER_"+t})),n.map(function(e){return _.createElement(it,S({},e,{displayGroupKey:t}))}))})))))))}),ct=function(e){var t=e.languages,n=e.channels,a=e.navigation,r=e.openLightbox,i=(e.refresh,function(e){var t=a.find(function(e){return e.key===I.Volt.EDIsplayGroupKey.INTERNATIONAL});return Object(I.ValueOf)(t,"children",[]).find(function(e){return e.key===I.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE})}()),c=_.useRef(null),l=o(_.useState({isFloating:!1,leftPos:"auto"}),2),s=l[0],u=l[1],d=Object(I.ValueOf)(i,"subTotalPrice.price",0);return _.useEffect(function(){var e=function(){g(c,u)};return window.addEventListener("scroll",e),function(){window.removeEventListener("scroll",e)}},[]),_.createElement(Be,{name:"International Alacarte"},_.createElement("div",{className:"section-bell-tv-international-channels-individual-tv-channels clearfix"},_.createElement("div",{className:"spacer5"}),_.createElement("div",{className:"flexRow flex-justify-space-between"},_.createElement("div",{className:"margin-xs"},_.createElement("h2",{id:I.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE,className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(P.FormattedMessage,{id:"International a la carte page"})),_.createElement(P.FormattedHTMLMessage,{id:"International a la carte page description"},function(e){return _.createElement(I.Components.Visible,{when:Boolean(e)},_.createElement("div",{className:"spacer5"}),_.createElement("p",{className:"noMargintxtSize14"},e))})),_.createElement("div",{id:"wrap",ref:c,onClick:function(){return r("wrap")},tabIndex:0,role:"button",className:"floating-div line-height-1 txtWhite txtSize12 txtCenter sans-serif pointer "+(s.isFloating?"fix-floating-notification":null),style:{left:s.leftPos}},_.createElement("div",{className:" txtSize26 virginUltraReg pad-10-top"},Object(I.ValueOf)(i,"count",0)),_.createElement("span",{className:""},_.createElement(P.FormattedMessage,{id:"CHANNELS_SELECTED"})),_.createElement("div",{className:"txtBold"},_.createElement(I.Components.BellCurrency,{value:Object(I.ValueOf)(i,"subTotalPrice.price",0)})))),_.createElement("div",{className:"spacer15"}),_.createElement("div",{className:"panel-body bell-tv-package-filters-row bgWhite"},t.map(function(e){var t=m(n,e);return _.createElement(I.Components.Visible,{key:e,when:t.length>0},_.createElement("fieldset",null,_.createElement("legend",{className:"txtSize18 txtBlack txtBold"},_.createElement(P.FormattedMessage,{id:e})),_.createElement("div",{className:"bell-tv-channels bell-tv-channel-picker flexRow"},p(t).map(function(e){return _.createElement(je,S({key:e.id},e,{multipleWaysToAdd:[]}))}))),_.createElement("div",{className:"spacer40"}))}))),_.createElement(et,{channels:n.filter(function(e){return e.isSelected}),totalPrice:d}))},lt=function(e){var t=e.languages,n=e.combos;return _.createElement(Be,{name:"International Combos"},_.createElement("div",{className:"section-bell-tv-international-channels-combo"},_.createElement("div",{className:"spacer10"}),_.createElement("h3",{id:I.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS,className:"virginUltraReg noMargin txtBlack text-uppercase txtSize20"},_.createElement(P.FormattedMessage,{id:"International Combos page"})),_.createElement("div",{className:"spacer15"}),t.map(function(e){var t=m(n,e);return _.createElement(I.Components.Visible,{key:e,when:t.length>0},_.createElement("h4",{className:"txtSize18 txtBlack txtBold"},_.createElement(P.FormattedMessage,{id:e})),p(t).map(function(e){return _.createElement(Ve,S({key:e.id},e))}))})))},st=function(e){var t=e.filter,n=e.languages,a=e.setFitler;return _.createElement("div",{id:"filtersContainer",className:"tvcsfilters-filtersContainer bell-tv-package col-xs-12 bgGray19 container-full-width-xs"},_.createElement("div",{id:"filterInputsContainer",className:"tvcsfilters-filtersInputsContainer bell-tv-package-body clearfix bgWhite"},_.createElement("div",{className:"tvcsfilters-conditionalInlineBlock flexBlock flexCenter flexCol-xs"},_.createElement("div",{className:"col-xs-12 col-sm-3"},_.createElement("label",{htmlFor:"Select_Language",className:"tvcsfilters-conditionalTitleFormatting noMargin txtLightGray3"},_.createElement(P.FormattedMessage,{id:"Select a language region"})),_.createElement("span",{className:"spacer10 col-xs-12 visible-xs"})),_.createElement("div",{className:"col-xs-12 col-sm-9"},_.createElement("div",{className:"tvcsfilters-conditionalFilterPadding15 tvcsfilters-conditionalInlineBlock"},_.createElement("div",{className:"form-control-select-box tvcsfilters-xs-select-dropdown col-xs-12 col-sm-8"},_.createElement("select",{id:"Select_Language",value:t,onChange:function(e){return a(e.target.value)},className:"form-control form-control-select tvcsfilters-select-dropdown-filter txtSize14 bgGrayLight1"},_.createElement(P.FormattedMessage,{id:"All languages"},function(e){return _.createElement("option",{id:"all",value:"all"},e)}),n.map(function(e,t){return _.createElement(P.FormattedMessage,{id:e},function(n){return _.createElement("option",{id:"option_"+t,value:e},n)})})),_.createElement("span",{"aria-hidden":"true",style:{backgroundImage:"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAYBAMAAADT3mpnAAAAD1BMVEUAAADhCgrhCgrhCgrhCiGX/cTIAAAAA3RSTlMAv5hn/23fAAAAPklEQVQI12MAAgUGMGByhNDCJmABRmNjRzDX2NhEAMwFCoC4IAAUoCqAmwuxxxAqIABxhyFEBdRWRhAX5m4AQWUIfOEz3hMAAAAASUVORK5CYII=)",backgroundPosition:"center left",backgroundRepeat:"no-repeat",width:"24px",backgroundSize:"40%"}}))))),_.createElement("div",{className:"tvcsfilters-conditionalSpacerShadow"})))},ut=Object(w.connect)(function(e){var t=e.catalog,n=e.navigation,a=Object(I.ValueOf)(t,"offerings."+I.Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS,[]),r=Object(I.ValueOf)(t,"offerings."+I.Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE,[]);return{comboLanguages:d(a),channelLanguages:d(r),combos:a,channels:r,navigation:n,refresh:1e3*Math.random()}},function(e){return{openLightbox:function(t){return e(I.Actions.openLightbox({lightboxId:"SELECTED_CANNELS",data:{relativeId:t}}))}}})(function(e){var t=e.comboLanguages,n=e.channelLanguages,a=e.combos,r=e.channels,i=e.navigation,c=e.openLightbox,l=e.refresh,s=o(_.useState("all"),2),u=s[0],d=s[1],m=Object(R.useLocation)();return _.useEffect(function(){d("all")},[m]),_.createElement(_.Fragment,null,_.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack text-uppercase"},_.createElement(P.FormattedMessage,{id:"International page"})),_.createElement("div",{className:"spacer10"}),_.createElement(R.Switch,null,_.createElement(R.Route,{path:I.EWidgetRoute.TV_InternationalCombos},_.createElement(st,{filter:u,setFitler:d,languages:t}),_.createElement(lt,{combos:a,languages:t.filter(function(e){return"all"===u||e.indexOf(u)>-1})})),_.createElement(R.Route,{path:I.EWidgetRoute.TV_InternationalAlacarte},_.createElement(st,{filter:u,setFitler:d,languages:n}),_.createElement(ct,{openLightbox:c,channels:r,navigation:i,languages:n.filter(function(e){return"all"===u||e.indexOf(u)>-1}),refresh:l})),_.createElement(R.Redirect,{to:I.EWidgetRoute.TV_InternationalCombos})),_.createElement(Fe,{pageName:I.Volt.EDIsplayGroupKey.INTERNATIONAL,label:"LEGAL_LABEL_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,content:"LEGAL_COPY_"+I.Volt.EDIsplayGroupKey.INTERNATIONAL}))}),dt=Object(w.connect)(function(e){var t=e.catalog;return{refresh:1e3*Math.random(),packages:Object(I.ValueOf)(t,"offerings."+I.Volt.EDIsplayGroupKey.MOVIE,[])}})(function(e){var t=e.packages;return _.createElement(Be,{name:"Movies and Series"},_.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(P.FormattedMessage,{id:"Movies and Series page"})),_.createElement(P.FormattedHTMLMessage,{id:"Movies and Series page Description"},function(e){return _.createElement(I.Components.Visible,{when:Boolean(e)},_.createElement("div",{className:"spacer5"}),_.createElement("p",{className:"noMargintxtSize14"},e))}),_.createElement("div",{className:"spacer20"}),_.createElement("div",{className:"section-bell-tv-packages accss-focus-outline-override-white-bg"},p(t).map(function(e){return _.createElement(Ve,S({key:e.id},e))})),_.createElement(Fe,{pageName:I.Volt.EDIsplayGroupKey.MOVIE,label:"LEGAL_LABEL_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,content:"LEGAL_COPY_"+I.Volt.EDIsplayGroupKey.MOVIE}))}),mt=Object(w.connect)(function(e){return{catalog:e.catalog}},function(e){return{}})(function(e){function t(e){e&&e.preventDefault&&e.preventDefault();var t=(("string"==typeof e?e:m)||"").trim();Boolean(t)&&(I.Omniture.useOmniture().trackAction({id:"searchSubmit",s_oAPT:{actionId:395,actionresult:0,applicationState:0},s_oSRT:t}),n.push({pathname:I.EWidgetRoute.TV_Search,search:"?query="+encodeURIComponent(t)}),p(t),u(!0),c([]))}e.catalog;var n=Object(R.useHistory)(),a=Object(R.useLocation)(),r=o(_.useState([]),2),i=r[0],c=r[1],l=o(_.useState(!0),2),s=l[0],u=l[1],d=o(_.useState(""),2),m=d[0],p=d[1];return _.useEffect(function(){"/Search"!==a.pathname&&p("")},[a]),_.useEffect(function(){function e(e){0===$(e.target).closest("#search_area").length&&c([])}return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}},[]),_.createElement("div",{id:"search_area"},_.createElement("form",{id:"search-bar",className:"bell-tv-search-bar",onSubmit:t},_.createElement("div",{className:"bell-search-field relative accss-focus-outline-override-grey-bg"},_.createElement(P.FormattedMessage,{id:"Search channels"},function(e){return _.createElement("input",{"aria-label":"Search",onChange:function(e){return function(e){var t=e.target.value;p(t),Boolean(t)&&u(!1),c(function(e){return x&&e.length>0?x.search(e):[]}(t))}(e)},value:m,type:"text",className:"form-control bell-search-field-input",placeholder:e,"aria-autocomplete":"both",onFocus:function(){return u(!1)}})}),_.createElement("div",{className:"absolute bell-search-field-button"},_.createElement("button",{id:"SEARCH_ICON",type:"submit",className:"btn btn-search-submi txtSize20",onFocus:function(e){return c([]),void u(!0)},"aria-label":"Search"},_.createElement("span",{className:"volt-icon icon-search txtDarkGrey"}))))),_.createElement("div",{role:"status","aria-live":"assertive","aria-relevant":"additions",className:"sr-only"},!s&&Boolean(m)?Object(I.ValueOf)(i,"length",0)>0?_.createElement(P.FormattedMessage,{id:"AVAILABLE_SEARCH_RESULTS",values:{value:i.length}}):_.createElement(P.FormattedMessage,{id:"NO_SEARCH_RESULTS"}):null),_.createElement(I.Components.Visible,{when:Object(I.ValueOf)(i,"length",0)>0},_.createElement("div",{role:"tooltip","aria-label":"Search suggestions",className:"bell-search-suggestions tooltip fade bottom in bs-tooltip-bottom"},_.createElement("div",{className:"arrow",style:{left:"50%"},"aria-hidden":"true"}),i&&Boolean(i.length)&&_.createElement("div",{className:"tooltip-inner"},_.createElement("ul",{className:"noBullets",role:"listbox"},i.map(function(e){return _.createElement("li",null,_.createElement("button",{id:"SEARCH_"+e.name,role:"option",onClick:function(){t(e.name)},className:"btn txtLeft pad-0 txtNormal"},e.name))}))))))}),pt=I.Components.Visible,ft=function(e){return _.createElement(R.Link,{id:"MENU_"+e.offeringId,to:e.route,role:"link",className:"bell-tv-navigator-tab-row flexRow "+(e.isActive?"active":"")},_.createElement("div",{className:"bell-tv-navigator-tabs-text flexGrow"},_.createElement("span",{className:(e.subMenu?"sans-serif txtSize14":"virginUltraReg txtSize16 text-uppercase")+" noPadding block submenu-name"},_.createElement(P.FormattedMessage,{id:e.offeringKey||"NONE"}),_.createElement(pt,{when:void 0!==e.count}," (",e.count,")")),_.createElement(pt,{when:Boolean(e.name)},_.createElement("span",{className:"virginUltraReg txtSize16 noPadding submenu-name text-uppercase"},e.name," - ")),_.createElement(pt,{when:Boolean(e.subTotalPrice)},_.createElement("span",{className:"noPadding submenu-price submenu-price txtSize14"},_.createElement(I.Components.BellCurrency,{value:Object(I.ValueOf)(e,"subTotalPrice.price",0)}),_.createElement("span",{"aria-hidden":!0},_.createElement(P.FormattedMessage,{id:"PER_MO"})),_.createElement("span",{className:"sr-only"},_.createElement(P.FormattedMessage,{id:"PER_MONTH"},function(e){return _.createElement(_.Fragment,null,e)}))))),_.createElement("div",{className:"bell-tv-navigator-tabs-pointer flexStatic"},_.createElement("span",{className:"volt-icon icon-Right_arrow txtSize15 inlineBlock","aria-hidden":!0})))},Et=Object(w.connect)(function(e){return{navigation:e.navigation}})(function(e){var t=e.navigation;return _.createElement("nav",{className:"bell-tv-navigator sticky",role:"tablist"},_.createElement("div",{className:"virginUltraReg txtSize22 bgGray mobile-menu-header text-uppercase d-block d-md-none"},_.createElement(P.FormattedMessage,{id:"YOUR_TV_CATEGORIES"})),_.createElement(mt,null),_.createElement("div",{className:"spacer15 hidden-xs"}),_.createElement("ul",{className:"bell-tv-navigator-tabs noBullets virgin-scroll accss-focus-outline-override-grey-bg",role:"presentation"},t.map(function(e){var t=Boolean(Object(R.useRouteMatch)(e.route));return _.createElement("li",{key:e.key,className:"bell-tv-navigator-tab "+(t?"active expanded":""),role:"tab","aria-selected":t?"true":"false"},_.createElement(ft,S({},e)),_.createElement(pt,{when:Array.isArray(e.children)},_.createElement("div",{className:"bell-tv-navigator-tab-more "+(t?"active":"")+" "+(t?"d-block":"d-none"),"aria-expanded":location.pathname===e.route,role:"tab"},Object(I.ValueOf)(e,"children",[]).map(function(e){return _.createElement(ft,S({key:e.key,subMenu:!0,isActive:Boolean(Object(R.useRouteMatch)(e.route))},e))}))))})),_.createElement("div",{id:"tv-sedebar-summary-portal",className:"dockbar-content d-block d-md-none pad-15-top"}))}),gt=I.Components.Visible,bt=Object(P.injectIntl)(Object(w.connect)(function(e){return{}},function(e){return{onActionClick:function(t){return e(G(t))}}})(function(e){var t=e.id,n=e.name,a=e.shortDescription,r=e.longDescription,i=e.regularPrice,c=e.promotionDetails,l=e.childOfferings,s=e.offeringAction,u=e.isSingle,d=(e.isSelectable,e.isSelected),m=e.isDisabled,p=e.isCurrent,f=e.onActionClick,E=e.intl,g=o(_.useState(!1),2),b=g[0],h=g[1];_.useEffect(function(){b&&I.Omniture.useOmniture().trackAction({id:"showChannelsClick",s_oAPT:{actionId:648},s_oEPN:"Show Channel"})},[b]);var O=Object(I.ValueOf)(l,"length",0)>0;return _.createElement("div",{id:t,className:"bell-tv-package bell-tv-base-pack accss-focus-outline-override-white-bg "+(m?"disabled":"")+" "+(u||d?"selected":"")},_.createElement("div",{className:"bell-tv-package-main flexRow block-xs bgWhite"},_.createElement("div",{className:"relative bell-tv-package-controls flexStatic no-margin-xs"},_.createElement(gt,{when:p},_.createElement("div",{className:"absolute pad-5-top pad-5-bottom pad-30-left pad-30-right bgOrange txtWhite current-flag"},_.createElement(P.FormattedMessage,{id:"Current package"})),_.createElement("div",{className:"spacer30"})),_.createElement("label",{id:"label_"+t,className:"graphical_ctrl ctrl_radioBtn pointer "+(m?"disabled":""),onClick:function(){return!m&&!d&&!u&&f(s)}},_.createElement("input",{id:"radio_"+t,type:"radio",checked:u||d,disabled:m}),_.createElement("span",{className:"ctrl_element"}),_.createElement("span",{className:"package-name block inlineBlock-xs txtSize18 txtNormal txtBlack"},n),_.createElement(Se,{regularPrice:i,promotionDetails:c}))),_.createElement("div",{className:"bell-tv-package-separator flexStatic"}),_.createElement("div",{className:"bell-tv-package-description relative flexBlock flexWrap"},_.createElement("div",{className:"bell-tv-package-channels-detail flexStatic flexBasis100 order1"},_.createElement("p",{className:"noMargin txtSize14"},r||a)),_.createElement("div",{className:"spacer30 flexStatic flexBasis100 order2"}," "),_.createElement("div",{className:"order4 flex1"},_.createElement("div",{className:"spacer10 visible-sm"}),_.createElement("div",{className:"bell-tv-package-icons noMargin flexBlock flexWrap","aria-hidden":"true"},Object(I.ValueOf)(l,void 0,[]).slice(0,10).map(function(e){return _.createElement("div",{className:"channel-item"},_.createElement("img",{src:Object(I.ValueOf)(e,"imagePath",""),alt:Object(I.ValueOf)(e,"name","")}))})),_.createElement("div",{className:"spacer15 col-xs-12 order5"}),_.createElement(gt,{when:O},_.createElement("div",{className:"col-xs-12 order6 flex-container"},_.createElement("button",{id:"ACCORDION_ICON_"+t,onClick:function(){return h(!b)},"aria-controls":"div1-accessible","data-toggle":"collapse",className:"btn btn-link no-pad txtVirginBlue accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center","aria-expanded":b,"aria-label":E.formatMessage({id:"Show channels"})+" "+E.formatMessage({id:"FOR_TEXT"})+" "+n},_.createElement("span",{className:"volt-icon txtSize22 icon-blue icon-"+(b?"Collapse":"Expand")},_.createElement("span",{className:"volt-icon path1 icon-"+(b?"Collapse":"Expand")}),_.createElement("span",{className:"volt-icon path2 icon-"+(b?"Collapse":"Expand")})),_.createElement("span",{className:"txtSize12 sans-serif txtBlue margin-10-left"},_.createElement(P.FormattedMessage,{id:"Show channels"})))))),_.createElement("div",{className:"clear"}))),_.createElement(gt,{when:b},_.createElement("div",{className:"bell-tv-package-footer bgGray19 expanded",role:"region"},_.createElement("div",{className:"spacer1 bgGray"}),_.createElement($e,{groupName:"radio_"+t,channels:Object(I.ValueOf)(l,void 0,[]),label:_.createElement(P.FormattedMessage,{id:"Package channels",values:{name:n}})}))))})),ht=Object(w.connect)(function(e){var t=e.catalog;return{packages:Object(I.ValueOf)(t,"offerings."+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,[]).filter(function(e){return e.productOfferingType===I.Volt.EProductOfferingType.PACKAGE})}})(function(e){var t=e.packages;return _.createElement(Be,{name:"Your TV package"},_.createElement("h2",{className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(P.FormattedMessage,{id:"Available Core Packages page"})),_.createElement(P.FormattedHTMLMessage,{id:"Available Core Packages page Description"},function(e){return _.createElement(I.Components.Visible,{when:Boolean(e)},_.createElement("div",{className:"spacer15"}),_.createElement("p",{className:"noMargintxtSize14"},e))}),_.createElement("div",{className:"spacer30 hidden-xs","aria-hidden":!0}),p(t).map(function(e){return _.createElement(bt,S({key:e.displayGroupKey,isSingle:t.length<2},e,{isDisabled:!Object(I.ValueOf)(e,"offeringAction.href",!1)}))}),_.createElement(Fe,{pageName:I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,label:"LEGAL_LABEL_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING,content:"LEGAL_COPY_"+I.Volt.EDIsplayGroupKey.BASE_PROGRAMMING}))}),Ot=I.Components.Visible,vt="",yt=function(e){var t=Object(w.useDispatch)(),n=Object(R.useLocation)(),a=_.useMemo(function(){return Object(I.ValueOf)((e=n.search,Boolean(e)?e.replace("?","").split("&").reduce(function(e,t){var n=t.split("=");return e[n[0]]=decodeURIComponent(n[1]||""),e},{}):{}),"query","");var e},[n]),r=function(e){var t=o(_.useState(null),2),n=t[0],a=t[1],r=Object(w.useSelector)(function(e){return Object(I.ValueOf)(e,"catalog.channels",[])});return _.useEffect(function(){var t;a((t=e,x&&t.length>0?x.search(t):[]))},[e,r]),n}(a);return _.useEffect(function(){vt!==a&&r&&t(I.Actions.omniPageLoaded("search",{id:"Search result",s_oAPT:{actionId:395,actionresult:2,applicationState:r.length>0?1:2},s_oSRT:vt=a}))},[r]),r?_.createElement(_.Fragment,null,_.createElement("div",{className:"flexRow flex-justify-space-between"},_.createElement("div",{className:"margin-xs"},_.createElement("h2",{id:"Search",className:"virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs"},_.createElement(Ot,{when:r.length>0,placeholder:_.createElement(P.FormattedMessage,{id:"NO_SEARCH_RESULT_FOR",values:{value:a}},function(e){return _.createElement("span",{"aria-live":"polite",role:"alert"},e)})},_.createElement(P.FormattedMessage,{id:"SEARCH_RESULT_FOR",values:{value:a}},function(e){return _.createElement("span",{"aria-live":"polite",role:"alert"},e)}))))),_.createElement("div",{className:"spacer15","aria-hidden":"true"}),_.createElement($e,{groupName:"Search",channels:r,allowSelection:!0,forceSelectable:!0,allowMultipleWaysToAdd:!0,showFilters:!1,showHeader:!1})):null},Nt=I.Components.RestrictionModal,xt=I.Actions.errorOccured,Tt=I.Actions.widgetRenderComplete,St=I.Actions.handleNav,At=function(e){var t=Object(w.useDispatch)(),n=Object(R.useHistory)();_.useEffect(function(){t(I.Actions.broadcastUpdate(I.Actions.setHistoryProvider(n)))},[]);var a=Object(R.useLocation)();return _.useEffect(function(){e.closeNav(),E(),window.scrollTo(0,0)},[a]),_.createElement("main",{id:"mainContent"},_.createElement("style",{dangerouslySetInnerHTML:{__html:"\n            html {\n                scroll-behavior: smooth;\n            }\n            @media (max-width: 992px) {\n                .channel-tooltip {\n                    display: none!important;\n                }\n            }\n        "}}),_.createElement(ot,null),_.createElement("div",{className:"spacer30"}),_.createElement("div",{className:"container liquid-container flexRow"},_.createElement("div",{className:"col-md-3 col-xs-12 bell-tv-navigator-menu side-navigation d-md-block "+(e.navStatus?"open-nav-slider":"")},_.createElement(Et,null)),_.createElement("div",{className:"floatR col-md-9 col-xs-12 bell-tv-navigator-page accss-focus-outline-override-white-bg"},_.createElement(R.Switch,null,_.createElement(R.Route,{exact:!0,path:I.EWidgetRoute.TV_Packages},_.createElement(ht,null)),_.createElement(R.Route,{path:I.EWidgetRoute.TV_MoviesSeries},_.createElement(dt,null)),_.createElement(R.Route,{path:I.EWidgetRoute.TV_Alacarte},_.createElement(tt,null)),_.createElement(R.Route,{path:I.EWidgetRoute.TV_International},_.createElement(ut,null)),_.createElement(R.Route,{path:I.EWidgetRoute.TV_Addons},_.createElement(We,null)),_.createElement(R.Route,{path:I.EWidgetRoute.TV_Browse},_.createElement(nt,null)),_.createElement(R.Route,{path:I.EWidgetRoute.TV_Search},_.createElement(yt,null)),_.createElement(R.Route,{path:"*"},_.createElement(R.Redirect,{to:I.EWidgetRoute.TV_Packages}))))),_.createElement(Ne,null),_.createElement(we,null),_.createElement(Nt,{id:"TV_RESTRICTION_MODAL"}),_.createElement("div",{id:"NAV_BACKDROP",onClick:e.closeNav,className:"nav-backdrop "+(e.navStatus?"show":"hide"),"aria-hidden":!0}))},It=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.componentDidCatch=function(e){this.props.onErrorEncountered(e)},t.prototype.componentWillMount=function(){this.baseRoute="/Ordering"+I.Utils.constructPageRoute(I.EWidgetRoute.TV)},t.prototype.componentDidMount=function(){this.props.widgetRenderComplete("omf-changepackage-tv")},t.prototype.render=function(){return _.createElement(R.BrowserRouter,{basename:this.baseRoute},_.createElement(At,S({},this.props)),_.createElement("div",{className:"spacer60"}),_.createElement("div",{className:"spacer60"}))},t}(_.Component),_t=Object(w.connect)(function(e){return{navStatus:e.navStatus}},function(e){return{onErrorEncountered:function(t){return e(xt(t))},widgetRenderComplete:function(){return e(Tt())},closeNav:function(){return e(St(!1))}}})(It),Ct=I.Components.ApplicationRoot,wt=function(){return _.createElement(Ct,null,_.createElement(_t,null))},Rt=function(e){function t(n){var a=e.call(this,n)||this;return t.instance=a,a}return a(t,e),t.Subscriptions=function(e){return(t={})[I.Actions.handleNav.toString()]=function(t){var n=t.payload;E(),e.dispatch(I.Actions.handleNav(n))},t[I.Actions.onContinue.toString()]=function(){E(),e.dispatch(I.Actions.omniPageSubmit()),I.Actions.broadcastUpdate(I.Actions.historyForward())},t;var t},t}(A.CommonFeatures.BasePipe),Pt=I.Actions.setWidgetProps,kt=I.Actions.setWidgetStatus,Mt=w.Provider,jt=function(e){function t(t,n,a,r){var i=e.call(this)||this;return i.store=t,i.params=n,i.config=a,i.pipe=r,i}return a(t,e),t.prototype.init=function(){this.pipe.subscribe(Rt.Subscriptions(this.store)),this.store.dispatch(Pt(this.config)),this.store.dispatch(Pt(this.params.props)),this.store.dispatch(kt(I.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;C.render(_.createElement(I.ContextProvider,{value:{config:this.config}},_.createElement(Mt,S({},{store:t}),_.createElement(wt,null))),e)},r([Object(A.Widget)({namespace:"Ordering"}),i("design:paramtypes",[Oe,A.ParamsProvider,K,Rt])],t)}(A.ViewWidget);t.default=jt},function(e,t){e.exports=l},function(e,t){e.exports=s},function(e,t){e.exports=u}])},e.exports=a(n(1),n(2),n(3),n(5),n(0),n(6),n(12),n(4),n(8),n(7),n(21))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n(9);Object.defineProperty(t,"AllSubstringsIndexStrategy",{enumerable:!0,get:function(){return a.AllSubstringsIndexStrategy}}),Object.defineProperty(t,"ExactWordIndexStrategy",{enumerable:!0,get:function(){return a.ExactWordIndexStrategy}}),Object.defineProperty(t,"PrefixIndexStrategy",{enumerable:!0,get:function(){return a.PrefixIndexStrategy}});var r=n(10);Object.defineProperty(t,"CaseSensitiveSanitizer",{enumerable:!0,get:function(){return r.CaseSensitiveSanitizer}}),Object.defineProperty(t,"LowerCaseSanitizer",{enumerable:!0,get:function(){return r.LowerCaseSanitizer}});var i=n(13);Object.defineProperty(t,"TfIdfSearchIndex",{enumerable:!0,get:function(){return i.TfIdfSearchIndex}}),Object.defineProperty(t,"UnorderedSearchIndex",{enumerable:!0,get:function(){return i.UnorderedSearchIndex}});var o=n(15);Object.defineProperty(t,"SimpleTokenizer",{enumerable:!0,get:function(){return o.SimpleTokenizer}}),Object.defineProperty(t,"StemmingTokenizer",{enumerable:!0,get:function(){return o.StemmingTokenizer}}),Object.defineProperty(t,"StopWordsTokenizer",{enumerable:!0,get:function(){return o.StopWordsTokenizer}});var c=n(32);Object.defineProperty(t,"Search",{enumerable:!0,get:function(){return c.Search}});var l=n(16);Object.defineProperty(t,"StopWordsMap",{enumerable:!0,get:function(){return l.StopWordsMap}});var s=n(33);Object.defineProperty(t,"TokenHighlighter",{enumerable:!0,get:function(){return s.TokenHighlighter}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.AllSubstringsIndexStrategy=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return a(e,[{key:"expandToken",value:function(e){for(var t,n=[],a=0,r=e.length;a<r;++a){t="";for(var i=a;i<r;++i)t+=e.charAt(i),n.push(t)}return n}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.ExactWordIndexStrategy=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return a(e,[{key:"expandToken",value:function(e){return e?[e]:[]}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.PrefixIndexStrategy=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return a(e,[{key:"expandToken",value:function(e){for(var t=[],n="",a=0,r=e.length;a<r;++a)n+=e.charAt(a),t.push(n);return t}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.CaseSensitiveSanitizer=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return a(e,[{key:"sanitize",value:function(e){return e?e.trim():""}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.LowerCaseSanitizer=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return a(e,[{key:"sanitize",value:function(e){return e?e.toLocaleLowerCase().trim():""}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TfIdfSearchIndex=void 0;var a,r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}(),o=n(14),c=(a=o)&&a.__esModule?a:{default:a};t.TfIdfSearchIndex=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._uidFieldName=t,this._tokenToIdfCache={},this._tokenMap={}}return i(e,[{key:"indexDocument",value:function(e,t,n){this._tokenToIdfCache={};var a,i=this._tokenMap;"object"!==r(i[e])?i[e]=a={$numDocumentOccurrences:0,$totalNumOccurrences:1,$uidMap:{}}:(a=i[e]).$totalNumOccurrences++;var o=a.$uidMap;"object"!==r(o[t])?(a.$numDocumentOccurrences++,o[t]={$document:n,$numTokenOccurrences:1}):o[t].$numTokenOccurrences++}},{key:"search",value:function(e,t){for(var n={},a=0,i=e.length;a<i;a++){var o=e[a],c=this._tokenMap[o];if(!c)return[];if(0===a)for(var l=0,s=(u=Object.keys(c.$uidMap)).length;l<s;l++)n[d=u[l]]=c.$uidMap[d].$document;else{var u;for(l=0,s=(u=Object.keys(n)).length;l<s;l++){var d=u[l];"object"!==r(c.$uidMap[d])&&delete n[d]}}}var m=[];for(var d in n)m.push(n[d]);var p=this._createCalculateTfIdf();return m.sort(function(n,a){return p(e,a,t)-p(e,n,t)})}},{key:"_createCalculateIdf",value:function(){var e=this._tokenMap,t=this._tokenToIdfCache;return function(n,a){if(!t[n]){var r=void 0!==e[n]?e[n].$numDocumentOccurrences:0;t[n]=1+Math.log(a.length/(1+r))}return t[n]}}},{key:"_createCalculateTfIdf",value:function(){var e=this._tokenMap,t=this._uidFieldName,n=this._createCalculateIdf();return function(a,r,i){for(var o=0,l=0,s=a.length;l<s;++l){var u,d=a[l],m=n(d,i);m===1/0&&(m=0),u=t instanceof Array?r&&(0,c.default)(r,t):r&&r[t],o+=(void 0!==e[d]&&void 0!==e[d].$uidMap[u]?e[d].$uidMap[u].$numTokenOccurrences:0)*m}return o}}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.UnorderedSearchIndex=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._tokenToUidToDocumentMap={}}return r(e,[{key:"indexDocument",value:function(e,t,n){"object"!==a(this._tokenToUidToDocumentMap[e])&&(this._tokenToUidToDocumentMap[e]={}),this._tokenToUidToDocumentMap[e][t]=n}},{key:"search",value:function(e,t){for(var n={},r=this._tokenToUidToDocumentMap,i=0,o=e.length;i<o;i++){var c=r[e[i]];if(!c)return[];if(0===i)for(var l=0,s=(d=Object.keys(c)).length;l<s;l++)n[u=d[l]]=c[u];else for(l=0,s=(d=Object.keys(n)).length;l<s;l++){var u=d[l];"object"!==a(c[u])&&delete n[u]}}var d,m=[];for(i=0,s=(d=Object.keys(n)).length;i<s;i++)u=d[i],m.push(n[u]);return m}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}(),r=/[^a-zа-яё0-9\-']+/i;t.SimpleTokenizer=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return a(e,[{key:"tokenize",value:function(e){return e.split(r).filter(function(e){return e})}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}();t.StemmingTokenizer=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._stemmingFunction=t,this._tokenizer=n}return a(e,[{key:"tokenize",value:function(e){return this._tokenizer.tokenize(e).map(this._stemmingFunction)}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StopWordsTokenizer=void 0;var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}(),r=n(16);t.StopWordsTokenizer=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._tokenizer=t}return a(e,[{key:"tokenize",value:function(e){return this._tokenizer.tokenize(e).filter(function(e){return!r.StopWordsMap[e]})}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Search=void 0;var a,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}(),i=n(14),o=(a=i)&&a.__esModule?a:{default:a},c=n(9),l=n(10),s=n(13),u=n(15);t.Search=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!t)throw Error("js-search requires a uid field name constructor parameter");this._uidFieldName=t,this._indexStrategy=new c.PrefixIndexStrategy,this._searchIndex=new s.TfIdfSearchIndex(t),this._sanitizer=new l.LowerCaseSanitizer,this._tokenizer=new u.SimpleTokenizer,this._documents=[],this._searchableFields=[]}return r(e,[{key:"addDocument",value:function(e){this.addDocuments([e])}},{key:"addDocuments",value:function(e){this._documents=this._documents.concat(e),this.indexDocuments_(e,this._searchableFields)}},{key:"addIndex",value:function(e){this._searchableFields.push(e),this.indexDocuments_(this._documents,[e])}},{key:"search",value:function(e){var t=this._tokenizer.tokenize(this._sanitizer.sanitize(e));return this._searchIndex.search(t,this._documents)}},{key:"indexDocuments_",value:function(e,t){this._initialized=!0;for(var n=this._indexStrategy,a=this._sanitizer,r=this._searchIndex,i=this._tokenizer,c=this._uidFieldName,l=0,s=e.length;l<s;l++){var u,d=e[l];u=c instanceof Array?(0,o.default)(d,c):d[c];for(var m=0,p=t.length;m<p;m++){var f,E=t[m];if(null!=(f=E instanceof Array?(0,o.default)(d,E):d[E])&&"string"!=typeof f&&f.toString&&(f=f.toString()),"string"==typeof f)for(var g=i.tokenize(a.sanitize(f)),b=0,h=g.length;b<h;b++)for(var O=g[b],v=n.expandToken(O),y=0,N=v.length;y<N;y++){var x=v[y];r.indexDocument(x,u,d)}}}}},{key:"indexStrategy",set:function(e){if(this._initialized)throw Error("IIndexStrategy cannot be set after initialization");this._indexStrategy=e},get:function(){return this._indexStrategy}},{key:"sanitizer",set:function(e){if(this._initialized)throw Error("ISanitizer cannot be set after initialization");this._sanitizer=e},get:function(){return this._sanitizer}},{key:"searchIndex",set:function(e){if(this._initialized)throw Error("ISearchIndex cannot be set after initialization");this._searchIndex=e},get:function(){return this._searchIndex}},{key:"tokenizer",set:function(e){if(this._initialized)throw Error("ITokenizer cannot be set after initialization");this._tokenizer=e},get:function(){return this._tokenizer}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TokenHighlighter=void 0;var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,n,a){return n&&e(t.prototype,n),a&&e(t,a),t}}(),r=n(9),i=n(10);t.TokenHighlighter=function(){function e(t,n,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._indexStrategy=t||new r.PrefixIndexStrategy,this._sanitizer=n||new i.LowerCaseSanitizer,this._wrapperTagName=a||"mark"}return a(e,[{key:"highlight",value:function(e,t){for(var n=this._wrapText("").length,a=Object.create(null),r=0,i=t.length;r<i;r++)for(var o=this._sanitizer.sanitize(t[r]),c=this._indexStrategy.expandToken(o),l=0,s=c.length;l<s;l++){var u=c[l];a[u]?a[u].push(o):a[u]=[o]}for(var d="",m="",p=0,f=(r=0,e.length);r<f;r++){var E=e.charAt(r);" "===E?(d="",m="",p=r+1):(d+=E,m+=this._sanitizer.sanitize(E)),a[m]&&a[m].indexOf(m)>=0&&(d=this._wrapText(d),e=e.substring(0,p)+d+e.substring(r+1),r+=n,f+=n)}return e}},{key:"_wrapText",value:function(e){var t=this._wrapperTagName;return"<"+t+">"+e+"</"+t+">"}}]),e}()},function(e,t,n){var a;"undefined"!=typeof self&&self,a=function(e,t,n,a,r,i,o,c,l,s){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=8)}([function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t){e.exports=c},function(e,t,n){"use strict";function a(e,t){function n(){this.constructor=e}E(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t,n,a){var r,i=arguments.length,o=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,a);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,r,i=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=i.next()).done;)o.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o}function c(e){try{var t=e.split("T"),n=new Date(t[0]);return n.setMinutes(new Date(e).getMinutes()+new Date(e).getTimezoneOffset()),n.setHours(0),n.setMinutes(0),n}catch(t){return e}}function l(e,t){return Object.keys(e).map(function(t){return e[t]}).map(function(e){return t(e)})}function s(e){var t=u(e);return 10===(t=t.substr(0,10)).length?t.slice(0,3)+"-"+t.slice(3,6)+"-"+t.slice(6):t}function u(e){return e.replace(/\D/g,"")}Object.defineProperty(t,"__esModule",{value:!0});var d={};n.d(d,"getOderDetails",function(){return S}),n.d(d,"getAppointment",function(){return A}),n.d(d,"setAppointment",function(){return I}),n.d(d,"setAvailableDates",function(){return _}),n.d(d,"contactInformation",function(){return C}),n.d(d,"setDuration",function(){return w}),n.d(d,"setInstallationAddress",function(){return R}),n.d(d,"setAdditionalDetails",function(){return P}),n.d(d,"setIsInstallationRequired",function(){return k}),n.d(d,"setForErrors",function(){return M}),n.d(d,"initSlickSlider",function(){return j});var m,p,f,E=function(e,t){return(E=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},g=function(){return(g=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},b=n(0),h=n(9),O=n(5),v=n(1),y=n(3),N=n(10),x=n(7),T=n(6),S=Object(x.createAction)("GET_ORDER_DETAILS"),A=Object(x.createAction)("GET_APPOINTMENT"),I=Object(x.createAction)("SET_APPOINTMENT"),_=Object(x.createAction)("SET_AVAIALBLE_DATES"),C=Object(x.createAction)("SET_CONTACT_INFO"),w=Object(x.createAction)("SET_DURATION"),R=Object(x.createAction)("SET_INSTALLATION_ADDRESS"),P=Object(x.createAction)("SET_ADDITIONAL_DETAILS"),k=Object(x.createAction)("SET_INSTALLATION_REQUIRED"),M=Object(x.createAction)("SET_FORM_ERRORS"),j=Object(x.createAction)("INIT_SLICK_SLIDER"),D=y.CommonFeatures.BaseConfig,L=y.CommonFeatures.configProperty,V=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),r([L({}),i("design:type",Object)],t.prototype,"headers",void 0),r([L({}),i("design:type",Object)],t.prototype,"environmentVariables",void 0),r([L({}),i("design:type",Object)],t.prototype,"mockdata",void 0),r([L({base:"http://127.0.0.1:8881",orderDetailsAPI:"/",appointmentAPI:"/",orderSubmitAPI:"/"}),i("design:type",Object)],t.prototype,"api",void 0),r([y.Injectable],t)}(D),F=function(e){function t(t,n){return e.call(this,t,n)||this}return a(t,e),r([y.Injectable,i("design:paramtypes",[y.AjaxServices,V])],t)}(v.BaseClient),B=n(11),W=T.ActionsObservable.concat,G=v.Actions.errorOccured,U=v.Actions.setWidgetStatus,z=v.Actions.setProductConfigurationTotal,H=v.Actions.broadcastUpdate,K=v.Actions.historyGo,q=v.Actions.clearCachedState,X=v.Actions.omniPageLoaded,Y=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=v.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(T.combineEpics)(this.appointmentEpic,this.submitAppointmentEpic)},Object.defineProperty(e.prototype,"appointmentEpic",{get:function(){var e=this;return function(t){return t.ofType(A.toString()).filter(function(){return e.widgetState!==v.EWidgetStatus.UPDATING}).mergeMap(function(){return W([U(e.widgetState=v.EWidgetStatus.UPDATING)],e.client.get(e.config.api.appointmentAPI).mergeMap(function(t){var n=t.data;return[_(n.appointment.availableDates),w(n.appointment.duration),R(n.appointment.installationAddress),C(n.appointment.contactInformation),P(n.appointment.additionalDetails),k(n.appointment.isInstallationRequired),H(z(Object(v.ValueOf)(n,"productConfigurationTotal"))),U(e.widgetState=v.EWidgetStatus.RENDERED),X()]}))}).catch(function(e){return[G(new v.Models.ErrorHandler("getAppointment",e))]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitAppointmentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(I.toString()).filter(function(){return e.widgetState!==v.EWidgetStatus.UPDATING}).mergeMap(function(t){var a=t.payload;return W([U(e.widgetState=v.EWidgetStatus.UPDATING)],e.client.put(e.config.api.appointmentAPI,B.MapRequestData.create(a,B.Request,n.getState())).mergeMap(function(e){return e.data,[H(K(v.EWidgetRoute.REVIEW)),q([v.EWidgetName.REVIEW])]}))}).catch(function(e){return[G(new v.Models.ErrorHandler("getAppointment",e))]})}},enumerable:!1,configurable:!0}),r([y.Injectable,i("design:paramtypes",[F,V])],e)}(),$=v.Actions.omniPageLoaded,J=function(){function e(){this.widgetState=v.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(T.combineEpics)(this.pageLoadedEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){return function(e,t){return e.ofType($.toString()).mergeMap(function(){var e,t,n=v.Omniture.useOmniture();switch(v.Utils.getFlowType()){case v.EFlowType.INTERNET:t="Internet",e="Change package";break;case v.EFlowType.TV:case v.EFlowType.ADDTV:break;case v.EFlowType.BUNDLE:t="Bundle",e="Add Tv"}return n.trackPage({id:"AppointmentPage",s_oSS1:"~",s_oSS2:t||"~",s_oSS3:e||"Change package",s_oPGN:"Installation",s_oPLE:{type:v.Omniture.EMessageType.Warning,content:{ref:"IstallationMessageBanner"}}}),[]}).catch(function(e){return[]})}},enumerable:!1,configurable:!0}),r([y.Injectable],e)}(),Z=v.Actions.setWidgetStatus,Q=v.Actions.broadcastUpdate,ee=v.Actions.setAppointmentVisited,te=function(){function e(e,t){this.omnitureEpics=e,this.appointmentEpics=t}return e.prototype.combineEpics=function(){return Object(T.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(Z.toString()).filter(function(e){return e.payload===v.EWidgetStatus.INIT}).mergeMap(function(){return[Q(ee()),A()]})}},enumerable:!1,configurable:!0}),r([y.Injectable,i("design:paramtypes",[J,Y])],e)}(),ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return a(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||y.ServiceLocator.instance.getService(y.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString("omf-changepackage-appointment",e,t.locale):e},t.Instance=null,n=r([y.Injectable],t)}(y.CommonFeatures.BaseLocalization),ae=y.CommonFeatures.BaseStore,re=(0,y.CommonFeatures.actionsToComputedPropertyName)(d),ie=re.setAvailableDates,oe=re.setDuration,ce=re.setInstallationAddress,le=re.contactInformation,se=re.setAdditionalDetails,ue=re.setIsInstallationRequired,de=function(e){function t(t,n,a,r){var i=e.call(this,n)||this;return i.client=t,i.epics=a,i.localization=r,i}return a(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,a,r,i;return Object(N.combineReducers)(g(g(g(g({},v.Reducers.WidgetBaseLifecycle(this.localization)),v.Reducers.WidgetLightboxes()),v.Reducers.WidgetRestrictions()),{availableDates:Object(x.handleActions)((e={},e[ie]=function(e,t){return t.payload||e},e),null),duration:Object(x.handleActions)((t={},t[oe]=function(e,t){return t.payload||e},t),null),installationAddress:Object(x.handleActions)((n={},n[ce]=function(e,t){return t.payload||e},n),null),contactInformation:Object(x.handleActions)((a={},a[le]=function(e,t){return t.payload||e},a),null),additionalDetails:Object(x.handleActions)((r={},r[se]=function(e,t){return t.payload||e},r),null),isInstallationRequired:Object(x.handleActions)((i={},i[ue]=function(e,t){return t.payload||e},i),!1)}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return[Object(T.createEpicMiddleware)(this.epics.omnitureEpics.combineEpics()),Object(T.createEpicMiddleware)(this.epics.appointmentEpics.combineEpics()),Object(T.createEpicMiddleware)(this.epics.combineEpics()),Object(T.createEpicMiddleware)((new v.ModalEpics).combineEpics()),Object(T.createEpicMiddleware)(new v.RestricitonsEpics(this.client,"APPOINTMENT_RESTRICTION_MODAL").combineEpics()),Object(T.createEpicMiddleware)((new v.LifecycleEpics).combineEpics())]},enumerable:!1,configurable:!0}),r([y.Injectable,i("design:paramtypes",[F,y.Store,te,ne])],t)}(ae),me=n(4);!function(e){e.PHONE="Phone",e.TEXT_MESSAGE="TextMessage",e.EMAIL="Email"}(m||(m={})),function(e){e.AM="AM",e.PM="PM",e.Evening="Evening",e.AllDay="AllDay",e.Item0810="Item0810",e.Item1012="Item1012",e.Item1315="Item1315",e.Item1517="Item1517",e.Item1719="Item1719",e.Item1921="Item1921"}(p||(p={})),function(e){e.EMAIL="Email",e.TEXT_MESSAGE="TextMessage",e.PHONE="Phone"}(f||(f={}));var pe,fe=/^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i,Ee=/^[0-9]\d{2}-\d{3}-\d{4}$/i,ge=n(2),be=function(e){var t=g(g({},he),e),n=t.label,a=t.value,r=t.handleChange,i=t.subLabel,o=t.checked,c=Object(me.useFormContext)().register;return b.createElement("div",{className:"flexBlock flexCol-xs margin-15-bottom"},b.createElement("label",{htmlFor:"additionalPhoneNumber",className:"installation-form-label"},b.createElement("span",{className:"txtBold block"},b.createElement(ge.FormattedMessage,{id:n})),i?b.createElement("span",{className:"txtItalic block txtNormal"},b.createElement(ge.FormattedHTMLMessage,{id:i})):null),b.createElement("div",{className:"flexCol margin-5-top"},b.createElement("label",{className:"graphical_ctrl graphical_ctrl_checkbox txtNormal"},b.createElement(ge.FormattedHTMLMessage,{id:a+"_LABEL"}),b.createElement("input",{type:"checkbox",ref:c,id:n,name:n,defaultChecked:o,onChange:function(e){return r(e)}}),b.createElement("span",{className:"ctrl_element chk_radius"}))))},he={checked:!1},Oe=function(e){var t=g(g({},ve),e),n=t.label,a=t.value,r=t.handleChange,i=t.checked,o=t.requiredInput,c=Object(me.useFormContext)().register;return n?b.createElement("label",{className:"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom"},b.createElement("label",{className:"txtBold block",htmlFor:"option_"+a},b.createElement(ge.FormattedMessage,{id:a})),b.createElement("input",{type:"radio",id:"option_"+a,ref:c({required:o}),name:n,value:a,checked:i,onChange:function(e){return r(e)}}),b.createElement("span",{className:"ctrl_element"}),"OTHER"===a&&!0===i?b.createElement("span",{className:"topArrow text-left otherOption","aria-hidden":"true"}):null):null},ve={checked:!1,requiredInput:!1},ye=function(e){var t=g(g({},Ne),e),n=t.label,a=t.required,r=t.value,i=t.subLabel,c=t.handleChange,l=t.requiredInput,s=t.maxLength,u=Object(me.useFormContext)().register,d=o(b.useState((s||0)-(r||"").length),2),m=d[0],p=d[1];return b.createElement("div",{className:"flexBlock flexCol-xs margin-15-bottom"},b.createElement("label",{htmlFor:n,className:"installation-form-label"},b.createElement("span",{className:"txtBold"},b.createElement(ge.FormattedMessage,{id:n})),a?b.createElement("span",{className:"txtNormal"},"(optional)"):"",i?b.createElement("span",{className:"txtItalic block txtNormal"},b.createElement(ge.FormattedHTMLMessage,{id:i})):null),b.createElement("div",{className:"flexCol"},b.createElement("textarea",{ref:u({required:l}),id:n,name:n,defaultValue:r,maxLength:s,className:"brf3-textarea form-control",onChange:function(e){p((s||0)-(e.currentTarget.value||"").length),c(e)}}),b.createElement("p",null,b.createElement(ge.FormattedMessage,{id:n+"_DESCRIPTION",values:{max:s,count:m}}))))},Ne={required:!1,requiredInput:!1,value:"",subLabel:""},xe=function(e){var t=g(g({},Te),e),n=t.label,a=t.subLabel,r=t.handleChange,i=t.containerClass,o=t.extention,c=t.optionalExtenstion,l=t.requiredInput,s=t.requiredPattern,u=t.value,d=t.subValue,m=Object(me.useFormContext)(),p=m.register,f=m.errors;return b.createElement("div",{className:"flexBlock flexCol-xs margin-15-bottom flexWrap "+i},b.createElement("label",{htmlFor:"additionalPhoneNumber",className:"installation-form-label "+(l?"form-required":"")+" "+(f&&f[n]?"error":"")},b.createElement("span",{className:"txtBold block"},b.createElement(ge.FormattedMessage,{id:n})),a?b.createElement("span",{className:"txtItalic block txtNormal","aria-label":function(e){switch(e){case"TELEPHONE_FORMAT":case"PREFERED_PHONE_FORMAT":case"PREFERED_TEXT_MESSAGE_FORMAT":case"Phone_FORMAT":case"TextMessage_FORMAT":return ne.getLocalizedString("TELEPHONE_FORMAT_ARIA");default:return ne.getLocalizedString(e)}}(a)},b.createElement(ge.FormattedHTMLMessage,{id:a})):null),b.createElement("div",{className:"flexCol relative "+(f&&f[n]?"has-error":"")},b.createElement("span",{className:"topArrow text-left hide","aria-hidden":"true"}),b.createElement("input",{type:"text",ref:p({required:l,pattern:s}),className:"brf3-virgin-form-input form-control",id:n,name:n,title:n,defaultValue:u,onBlur:r,onChange:function(e){return r(e)}}),f&&f[n]?b.createElement("span",{className:"error margin-5-top"},b.createElement("span",{className:"virgin-icon icon-warning margin-15-right","aria-hidden":!0},b.createElement("span",{className:"volt-icon path1"}),b.createElement("span",{className:"volt-icon path2"})),b.createElement("span",{className:"txtSize12"},b.createElement(ge.FormattedHTMLMessage,{id:"pattern"!==f[n].type?"INLINE_ERROR_required":"INLINE_ERROR_"+n+"_pattern"}))):null),o?b.createElement("div",{className:"flexCol brf3-virgin-form-subInput fill-sm"},b.createElement("div",{className:"flexBlock flexCol-xs"},b.createElement("label",{htmlFor:"extension",className:"installation-form-label"},b.createElement("span",{className:"txtBold block"},b.createElement(ge.FormattedMessage,{id:o})),c?b.createElement("span",{className:"txtItalic block txtNormal"},b.createElement(ge.FormattedMessage,{id:"OPTIONAL_LABEL"})):null),b.createElement("div",{className:"flexCol"},b.createElement("input",{type:"text",ref:p,className:"brf3-virgin-form-input form-control",id:o,name:o,title:o,maxLength:10,defaultValue:d,onBlur:r,onChange:function(e){return r(e)}})))):null)},Te={requiredInput:!1,requiredPattern:/.*/i,containerClass:"",value:"",subValue:""},Se=function(e){var t=e.legend,n=e.required,a=e.accessibleLegend,r=e.legendAdditionalClass;return t?b.createElement("legend",{className:"installation-form-label "+(n?"form-required":"")+" "+(a?"sr-only":"")+" "+r},b.createElement(ge.FormattedMessage,{id:t})):null},Ae=function(e){var t=e.className,n=e.children,a=e.legend,r=e.accessibleLegend,i=e.legendAdditionalClass,o=e.required,c=e.additionalClass;return b.createElement("fieldset",{className:"margin-15-bottom "+t},r?b.createElement(b.Fragment,null,b.createElement(Se,{legend:a,required:o,accessibleLegend:r,legendAdditionalClass:i}),n):b.createElement("div",{className:"flexBlock flexCol-xs "+c},b.createElement(Se,{legend:a,required:o,accessibleLegend:r,legendAdditionalClass:i}),n))};Ae.defaultProps={className:"",accessibleLegend:!0,legendAdditionalClass:"",required:!1,additionalClass:""},function(e){e.ERROR="icon-warning",e.NOTE="icon-info",e.VALIDATION="icon-Big_check_confirm",e.INFO="icon-BIG_WARNING"}(pe||(pe={}));var Ie,_e=function(e){var t=g(g({},Ce),e),n=t.iconType,a=t.heading,r=t.message,i=t.messages,o=t.iconSizeCSS;return b.createElement(v.Components.Container,null,b.createElement(v.Components.Panel,{className:"flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack"},b.createElement("span",{className:"virgin-icon "+n+" "+o+" txtSize36","aria-hidden":!0},b.createElement("span",{className:"virgin-icon path1"}),b.createElement("span",{className:"virgin-icon path2"})),b.createElement("div",{id:"IstallationMessageBanner",className:"flexCol pad-15-left content-width valign-top pad-0-xs"},b.createElement("h4",{className:"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase"},b.createElement(ge.FormattedHTMLMessage,{id:a})),r?b.createElement("p",{className:"txtSize14 txtGray4A sans-serif no-margin"},b.createElement(ge.FormattedHTMLMessage,{id:r})):null,i?b.createElement("ul",null,i&&i.map(function(e){return b.createElement("li",{className:"error"},b.createElement("a",{id:"message_"+e.id,href:"#"+e.id,className:"txtRed txtBold txtUnderline",title:e.id},b.createElement(ge.FormattedMessage,{id:e.id})),b.createElement("span",{className:"txtDarkGrey"}," - ","required"===e.error?b.createElement(ge.FormattedMessage,{id:"INLINE_ERROR_required"}):b.createElement(ge.FormattedMessage,{id:"INLINE_ERROR_"+e.id+"_"+e.error})))})):null)))},Ce={iconType:pe.INFO,iconSizeCSS:"txtSize36"};!function(e){e.H1="h1",e.H2="h2",e.H3="h3",e.H4="h4",e.H5="h5",e.H6="h6"}(Ie||(Ie={}));var we=function(e){var t=g(g({},Re),e),n=t.tag,a=t.additionalClass,r=t.content,i=t.description,o=n||"h2";return b.createElement(b.Fragment,null,b.createElement(o,{className:"virginUltra txtBlack txtCapital noMargin "+a},b.createElement(ge.FormattedMessage,{id:r})),i?b.createElement(b.Fragment,null,b.createElement("span",{className:"spacer10 col-xs-12 clear"}),b.createElement("p",{className:"noMargin"},b.createElement(ge.FormattedHTMLMessage,{id:i}))):null)},Re={additionalClass:"",description:""},Pe=function(e){function t(t){var n=e.call(this,t)||this;return n.headingProps={tag:Ie.H2,classNames:"txtSize28 txtSize24-xs",content:"INSTALLATION_PAGE_HEADING"},n}return a(t,e),t.prototype.render=function(){return b.createElement(b.Fragment,null,b.createElement(v.Components.Container,null,b.createElement(v.Components.BRF3Container,null,b.createElement("span",{className:"spacer5 flex col-12"}),b.createElement(we,g({},this.headingProps)),b.createElement("span",{className:"spacer25 flex col-12 clear"}))),b.createElement(_e,{iconType:pe.INFO,heading:"INSTALLATION_HEADING",message:"INSTALLATION_MESSAGE"}),Object.keys(this.props.errors).length?b.createElement(_e,{iconType:pe.ERROR,heading:"ERRORS_HEADING",messages:(e=this.props.errors,n=[],v.Utils.getFlowType()===v.EFlowType.INTERNET&&(t=523),v.Utils.getFlowType()===v.EFlowType.BUNDLE&&(t=508),Object.keys(e).map(function(t){var a=e[t];n.push({id:a.ref.name,error:a.type})}),v.Omniture.useOmniture().trackError(n.map(function(e){return{code:e.id,type:v.Omniture.EErrorType.Validation,layer:v.Omniture.EApplicationLayer.Frontend,description:e.error}}),t),n)}):null);var e,t,n},t}(b.PureComponent),ke=function(){var e,t,n=Object(O.useSelector)(function(e){return null===e||void 0===e?void 0:e.contactInformation}),a=Object(O.useSelector)(function(e){return null===e||void 0===e?void 0:e.additionalDetails}),r=o(b.useState(m.PHONE),2),i=r[0],c=r[1],d=Object(me.useFormContext)().setValue,p=function(e){var t=e.target,n=t.value,a=t.name;switch(n){case m.PHONE:case m.EMAIL:case m.TEXT_MESSAGE:c(n)}switch(a){case m.PHONE+"_LABEL":case m.TEXT_MESSAGE+"_LABEL":case"ADDITIONAL_PHONE_NUMBER":d(a,s(n),!0);break;case m.PHONE+"_EXT":case"ADDITIONAL_PHONE_EXT":d(a,u(n),!0);break;case"SUPERINTENDANT_PHONE":d(a,s(n),!0);break;case m.EMAIL+"_LABEL":d(a,n,!0)}};b.useEffect(function(){c((null===n||void 0===n?void 0:n.preferredContactMethod)?n.preferredContactMethod:m.PHONE)},[n]);var f={tag:Ie.H2,additionalClass:"txtSize22 txtSize24-xs",content:"CONTACT_INFORMATION"};return b.createElement("div",{className:"margin-30-bottom",id:"section2"},b.createElement(we,g({},f)),b.createElement("span",{className:"spacer10 visible-xs"}),b.createElement("div",{className:"pad-25-top no-pad-xs"},b.createElement(Ae,{legend:"PREFERED_METHOD_OF_CONTACT",required:!0,additionalClass:"flexWrap",accessibleLegend:!1},b.createElement("div",{className:"flexCol lineHeight18"},b.createElement("div",{className:"spacer15 visible-xs"}),l(m,function(e){return b.createElement(Oe,{label:"PREFERED_METHOD_OF_CONTACT",value:e,handleChange:p,checked:e===i})})),l(m,function(e){var t;return b.createElement(xe,{requiredInput:i===e,label:e+"_LABEL",containerClass:"sub-option flex-wrap "+(e===i?"show":"hide"),subLabel:e+"_FORMAT",extention:e===m.PHONE&&e+"_EXT",optionalExtenstion:!0,requiredPattern:e===m.EMAIL?fe:Ee,value:function(e,t){var n,a;switch(e){case m.EMAIL:return null===t||void 0===t?void 0:t.email;case m.PHONE:return(null===(n=null===t||void 0===t?void 0:t.primaryPhone)||void 0===n?void 0:n.phoneNumber)&&s(null===(a=null===t||void 0===t?void 0:t.primaryPhone)||void 0===a?void 0:a.phoneNumber);case m.TEXT_MESSAGE:return(null===t||void 0===t?void 0:t.textMessage)&&s(null===t||void 0===t?void 0:t.textMessage);default:return""}}(e,n),subValue:null===(t=null===n||void 0===n?void 0:n.primaryPhone)||void 0===t?void 0:t.phoneExtension,handleChange:function(e){return p(e)}})})),b.createElement(Ae,{legend:"ADDITIONAL_PHONE_NUMBER",required:!1,accessibleLegend:!0},b.createElement(xe,{requiredInput:!1,label:"ADDITIONAL_PHONE_NUMBER",subLabel:"TELEPHONE_FORMAT",extention:"ADDITIONAL_PHONE_EXT",optionalExtenstion:!0,requiredPattern:Ee,value:null===(e=null===n||void 0===n?void 0:n.additionalPhone)||void 0===e?void 0:e.phoneNumber,subValue:null===(t=null===n||void 0===n?void 0:n.additionalPhone)||void 0===t?void 0:t.phoneExtension,handleChange:function(e){return p(e)}}),b.createElement(xe,{label:"APPARTMENT",value:null===a||void 0===a?void 0:a.apartment,handleChange:function(e){return p(e)}}),b.createElement(xe,{label:"ENTRY_CODE",value:null===a||void 0===a?void 0:a.entryCode,handleChange:function(e){return p(e)}}),b.createElement(xe,{label:"SUPERINTENDANT_NAME",value:null===a||void 0===a?void 0:a.superintendantName,handleChange:function(e){return p(e)}}),b.createElement(xe,{label:"SUPERINTENDANT_PHONE",requiredPattern:Ee,value:null===a||void 0===a?void 0:a.superintendantPhone,handleChange:function(e){return p(e)}}),b.createElement(be,{label:"INFORMED_SUPERINTENDANT",value:"YES",checked:null===a||void 0===a?void 0:a.informedSuperintendant,handleChange:function(e){return p(e)}}),b.createElement(ye,{label:"SPECIAL_INSTRUCTIONS",subLabel:"SPECIAL_INSTRUCTIONS_SUBLABEL",value:null===a||void 0===a?void 0:a.specialInstructions,maxLength:200,handleChange:function(e){return p(e)}}))))},Me=b.memo(function(e){var t,n=e.handleChange,a=e.preferredDate,r=e.checked,i=Object(me.useFormContext)().register;return b.createElement(b.Fragment,null,b.createElement("label",{id:"dateAndTime"+a.date,className:"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom"},b.createElement("input",{type:"radio",ref:i({required:!0}),id:"timeOption"+a.date,name:"dateAndTime",value:JSON.stringify(a),onChange:function(e){return n(e)},checked:r.date===a.date}),b.createElement("label",{className:"block no-margin",htmlFor:"timeOption"+a.date},Boolean(a.date)?b.createElement(ge.FormattedDate,{value:a.date,year:"numeric",weekday:"long",month:"long",day:"2-digit",timeZone:"UTC"}):"No Appointment Details"),Boolean(a.timeSlots.length)?b.createElement("span",{className:"txtNormal block"},b.createElement(ge.FormattedMessage,{id:null===(t=a.timeSlots.find(function(e){return e.isAvailable}))||void 0===t?void 0:t.intervalType})):null,b.createElement("span",{className:"ctrl_element"})))}),je=function(e){function t(t){return e.call(this,t)||this}return a(t,e),t.prototype.componentDidMount=function(){this.props.initSlickSlider()},t.prototype.render=function(){var e=this.props,t=e.availableDates,n=e.selectDate,a=e.selectedDateTime;return b.createElement("div",{className:"flexBlock margin-15-bottom sub-option relative timeslot-picker"},b.createElement("div",{className:"select-timeslot fill"},t&&t.map(function(e,t){return b.createElement("div",{className:""},b.createElement("div",{className:e.timeSlots[0].intervalType===p.AllDay?"allDayContainer":"day-container"},b.createElement("label",{htmlFor:"dayIndex_"+t,className:"virginUltra sans-serif-xs txtBold-xs txtSize16 txtBlack lineHeight1-3 margin-15-bottom"},b.createElement(ge.FormattedDate,{value:c(e.date),weekday:"long",timeZone:"UTC"}),b.createElement("br",{className:"hidden-m"}),b.createElement("span",{className:"d-sm-none d-md-none d-lg-none d-xl-none"},", "),b.createElement(ge.FormattedDate,{value:c(e.date),year:"numeric",month:"short",day:"2-digit",timeZone:"UTC"})),b.createElement("ul",{className:"noMargin list-unstyled timeItem","aria-labelledby":"mondayList"},e.timeSlots.map(function(t){var r=a.timeSlots[0].intervalType===t.intervalType&&a.date===e.date;return b.createElement("li",{className:"txtBlue "+(r?"selected":"")},b.createElement("button",{id:"slot_"+t.intervalType,onClick:function(a){return n(a,e.date,t)},className:"btn btn-link "+(t.intervalType===p.AllDay?"flexCol flexJustify":"")+" "+(t.isAvailable?"":"disabled")+" "+(t.isSelected?"selected":""),tabIndex:0},b.createElement(ge.FormattedHTMLMessage,{id:t.intervalType})))}))))})))},t.displayName="TimeSlots",t}(b.Component),De=v.Components.Visible,Le=function(e){function t(t){var n=e.call(this,t)||this;return n.handleChange=function(e){var t=e.target.value;switch(t){case"OTHER":n.setState({showTimeSlots:!0});break;default:n.setState({showTimeSlots:!1,selectedDateTime:JSON.parse(t)})}},n.selectDate=function(e,t,a){e.preventDefault();var r=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(o(arguments[t]));return e}(n.state.preferredDates);n.state.preferredDates[0].date===t&&n.state.preferredDates[0].timeSlots[0].intervalType===a.intervalType?n.setState({preferredDates:n.state.preferredDates,selectedDateTime:n.state.preferredDates[0],showTimeSlots:!1,showOther:!1}):(r[1]={date:t,timeSlots:[g(g({},a),{isSelected:!0})]},n.setState({preferredDates:r,selectedDateTime:r[1],showTimeSlots:!1,showOther:!1}))},n.changeBtn=function(e){e.preventDefault(),n.setState({showOther:!0,showTimeSlots:!0,preferredDates:[n.state.preferredDates[0]]})},n.state={showTimeSlots:!1,selectedDateTime:null,preferredDates:[],showOther:!0},n.handleChange.bind(n),n.changeBtn.bind(n),n}return a(t,e),t.prototype.componentDidUpdate=function(e){if(this.props.availableDates&&this.props.availableDates.length&&JSON.stringify(this.props.availableDates)!==JSON.stringify(e.availableDates)){var t=(a=(n=this.props.availableDates).filter(function(e){return e.timeSlots.find(function(e){return!0===e.isSelected})})).length>0?a:[n[0]];this.setState({preferredDates:t,selectedDateTime:t[0].date?t[0]:null,showOther:!(t.length>1)})}var n,a},t.prototype.render=function(){var e=this,t=this.props,n=t.installationAddress,a=t.availableDates,r=t.initSlickSlider,i=this.state,o=i.showTimeSlots,c=i.selectedDateTime,l=i.showOther,s=i.preferredDates,u={tag:Ie.H2,additionalClass:"txtSize22 txtSize24-xs",content:"INSTALLATION_DETAILS",description:"INSTALLATION_DETAILS_DESC"};return b.createElement("div",{className:"margin-30-bottom",id:"section1"},b.createElement(we,g({},u)),b.createElement("span",{className:"spacer10 flex col-12 clear"}),b.createElement("p",{className:"noMargin txtItalic"},b.createElement(ge.FormattedMessage,{id:"REQUIRED_INFO_FLAG"})),b.createElement("div",{className:"pad-15-top"},b.createElement(Ae,{legend:"DATE_AND_TIME_LABEL",required:!0,accessibleLegend:!1,additionalClass:"flex-wrap"},b.createElement("div",{className:"spacer10 visible-xs"}),b.createElement("div",{className:"flexCol lineHeight18"},s&&s.length&&s.map(function(t){return b.createElement(Me,{handleChange:e.handleChange,preferredDate:t,checked:o||c})}),b.createElement(De,{when:l,placeholder:b.createElement("div",{className:"pad-35-left relative changeBtn"},b.createElement("button",{id:"CHANGE_BTN",className:"btn btn-link pad-0 txtSize14 txtUnderline txtVirginBlue",onClick:function(t){return e.changeBtn(t)}},"Change"))},b.createElement(Oe,{handleChange:this.handleChange,requiredInput:!0,checked:o,label:"dateAndTime",value:"OTHER"}))),o?b.createElement(je,{selectDate:this.selectDate,availableDates:a,initSlickSlider:r,selectedDateTime:c}):null),b.createElement(De,{when:Boolean(c)},c&&"OTHER"!==c?b.createElement(Ae,{legend:"ESTIMATED_DURATION",required:!1,accessibleLegend:!1},b.createElement("div",{className:"flexCol"},b.createElement("span",{className:"block"},b.createElement(ge.FormattedMessage,{id:c.timeSlots[0].duration})),b.createElement("span",{className:"block"},b.createElement(ge.FormattedMessage,{id:"ARRIVAL_OF_TECHNICIAN"})))):null),b.createElement(De,{when:Boolean(n)},b.createElement(Ae,{legend:"SHIPPING_INSTALLATION_ADDRESS",required:!1,accessibleLegend:!1},b.createElement("div",{className:"flexCol"},b.createElement("span",{className:"block"},b.createElement(De,{when:Object(v.ValueOf)(n,"apartmentNumber",!1)},Object(v.ValueOf)(n,"apartmentNumber","")," - "),Object(v.ValueOf)(n,"address1","")," ",Object(v.ValueOf)(n,"address2","")," ",Object(v.ValueOf)(n,"streetType",""),", ",Object(v.ValueOf)(n,"city",""),", ",Object(v.ValueOf)(n,"province",""),", ",Object(v.ValueOf)(n,"postalCode","")),b.createElement("span",{className:"margin-10-top"},b.createElement(ge.FormattedHTMLMessage,{id:"CONTACT_US_NOTE"})))))))},t}(b.Component),Ve=Object(O.connect)(function(e){return{installationAddress:e.installationAddress,availableDates:e.availableDates,duration:e.duration}},function(e){return{initSlickSlider:function(){return e(j())}}})(Le),Fe=null,Be=function(e){var t=b.useRef(null),n=Object(me.useFormContext)().handleSubmit,a=Object(O.useDispatch)();return Fe=function(){t.current.click()},b.createElement("form",{id:"AppointmentForm",onSubmit:function(e){return t=void 0,i=function(){return function(e,t){function n(n){return function(o){return function(n){if(a)throw new TypeError("Generator is already executing.");for(;c;)try{if(a=1,r&&(i=2&n[0]?r.return:n[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,n[1])).done)return i;switch(r=0,i&&(n=[2&n[0],i.value]),n[0]){case 0:case 1:i=n;break;case 4:return c.label++,{value:n[1],done:!1};case 5:c.label++,r=n[1],n=[0];continue;case 7:n=c.ops.pop(),c.trys.pop();continue;default:if(!(i=(i=c.trys).length>0&&i[i.length-1])&&(6===n[0]||2===n[0])){c=0;continue}if(3===n[0]&&(!i||n[1]>i[0]&&n[1]<i[3])){c.label=n[1];break}if(6===n[0]&&c.label<i[1]){c.label=i[1],i=n;break}if(i&&c.label<i[2]){c.label=i[2],c.ops.push(n);break}i[2]&&c.ops.pop(),c.trys.pop();continue}n=t.call(e,c)}catch(e){n=[6,e],r=0}finally{a=i=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,o])}}var a,r,i,o,c={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o}(this,function(t){return e.preventDefault(),n(function(e){a(I(e))})(e),[2]})},new((r=void 0)||(r=Promise))(function(e,n){function a(e){try{c(i.next(e))}catch(e){n(e)}}function o(e){try{c(i.throw(e))}catch(e){n(e)}}function c(t){t.done?e(t.value):new r(function(e){e(t.value)}).then(a,o)}c((i=i.apply(t,[])).next())});var t,r,i}},b.createElement("div",{className:"spacer45 hidden-m"}),b.createElement("div",{className:"spacer20 d-block d-sm-none"}),b.createElement(Ve,null)," ",b.createElement(ke,null)," ",b.createElement("button",{ref:t,type:"submit","aria-hidden":"true",style:{display:"none"}}))};Be.useSubmitRef=function(){return Fe};var We=Be,Ge=function(e){function t(n){var a=e.call(this,n)||this;return t.instance=a,a}return a(t,e),t.Subscriptions=function(e){var t;return(t={})[v.Actions.onContinue.toString()]=function(t){var n=We.useSubmitRef();n&&n(),e.dispatch(v.Actions.setWidgetStatus(v.EWidgetStatus.RENDERED))},t},t}(y.CommonFeatures.BasePipe),Ue=v.Components.RestrictionModal,ze=v.Actions.widgetRenderComplete,He=function(e){var t=Object(O.useDispatch)(),n=Object(me.useFormContext)().errors;return b.useEffect(function(){t(ze(v.EWidgetName.APPOINTMENT))},[]),b.createElement("main",{id:"mainContent"},b.createElement("span",{className:"flex spacer30 col-12","aria-hidden":"true"}),b.createElement(Ue,{id:"APPOINTMENT_RESTRICTION_MODAL"}),b.createElement(Pe,{errors:n}),b.createElement(v.Components.Container,null,b.createElement(v.Components.Panel,{className:"pad-25-left pad-25-right clearfix"},b.createElement(We,null))))},Ke=v.Components.ApplicationRoot,qe=function(e){var t=Object(me.useForm)();return b.createElement(Ke,null,b.createElement(me.FormContext,g({},t)," ",b.createElement(He,null)))},Xe=v.Actions.setWidgetProps,Ye=v.Actions.setWidgetStatus,$e=O.Provider,Je=function(e){function t(t,n,a,r){var i=e.call(this)||this;return i.store=t,i.params=n,i.config=a,i.pipe=r,i}return a(t,e),t.prototype.init=function(){this.pipe.subscribe(Ge.Subscriptions(this.store)),this.store.dispatch(Xe(this.config)),this.store.dispatch(Xe(this.params.props)),this.store.dispatch(Ye(v.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;h.render(b.createElement(v.ContextProvider,{value:{config:this.config}},b.createElement($e,g({},{store:t}),b.createElement(qe,null))),e)},r([Object(y.Widget)({namespace:"Ordering"}),i("design:paramtypes",[de,y.ParamsProvider,V,Ge])],t)}(y.ViewWidget);t.default=Je},function(e,t){e.exports=l},function(e,t){e.exports=s},function(e,t,n){"use strict";var a=n(12);n.n(a),n.o(a,"MapRequestData")&&n.d(t,"MapRequestData",function(){return a.MapRequestData}),n.o(a,"Request")&&n.d(t,"Request",function(){return a.Request});var r=n(13);n.n(r),n.o(r,"MapRequestData")&&n.d(t,"MapRequestData",function(){return r.MapRequestData}),n.o(r,"Request")&&n.d(t,"Request",function(){return r.Request});var i=n(14);n.n(i),n.o(i,"MapRequestData")&&n.d(t,"MapRequestData",function(){return i.MapRequestData}),n.o(i,"Request")&&n.d(t,"Request",function(){return i.Request});var o=n(15);n.d(t,"MapRequestData",function(){return o.a}),n.d(t,"Request",function(){return o.b})},function(e,t){},function(e,t){},function(e,t){},function(e,t,n){"use strict";n.d(t,"b",function(){return a}),n.d(t,"a",function(){return r});var a={availableDates:null,duration:"",installationAddress:{address1:"",address2:"",city:"",province:"",postalCode:"",apartmentType:"",apartmentNumber:""},contactInformation:{preferredContactMethod:"",primaryPhone:{phoneNumber:"",phoneExtension:""},mobileNumber:null,additionalPhone:{phoneNumber:"",phoneExtension:""},textMessage:"",email:""},additionalDetails:{apartment:"",entryCode:"",specialInstructions:"",superintendantName:"",superintendantPhone:"",informedSuperintendant:null},isInstallationRequired:null},r=function(){function e(){}return e.create=function(e,t,n){var a,r;return t.installationAddress.address1=n.installationAddress&&n.installationAddress.address1?n.installationAddress.address1:"",t.installationAddress.address2=n.installationAddress&&n.installationAddress.address2?n.installationAddress.address2:"",t.installationAddress.city=n.installationAddress&&n.installationAddress.city?n.installationAddress.city:"",t.installationAddress.postalCode=n.installationAddress&&n.installationAddress.postalCode?n.installationAddress.postalCode:"",t.installationAddress.province=n.installationAddress&&n.installationAddress.province?n.installationAddress.province:"",t.installationAddress.apartmentType=n.installationAddress&&n.installationAddress.province?n.installationAddress.apartmentType:"",t.installationAddress.apartmentNumber=n.installationAddress&&n.installationAddress.province?n.installationAddress.apartmentNumber:"",t.isInstallationRequired=n.isInstallationRequired,t.duration=n.duration,t.contactInformation.primaryPhone.phoneNumber=e.Phone_LABEL?e.Phone_LABEL:"",t.contactInformation.primaryPhone.phoneExtension=e.Phone_EXT?e.Phone_EXT:"",t.contactInformation.additionalPhone.phoneNumber=e.ADDITIONAL_PHONE_NUMBER,t.contactInformation.additionalPhone.phoneExtension=e.ADDITIONAL_PHONE_EXT,t.contactInformation.preferredContactMethod=e.PREFERED_METHOD_OF_CONTACT,t.contactInformation.email=e.Email_LABEL?e.Email_LABEL:"",t.contactInformation.textMessage=e.TextMessage_LABEL?e.TextMessage_LABEL:"",t.availableDates=(a=n.availableDates,r=JSON.parse(e.dateAndTime),null===a||void 0===a||a.forEach(function(e){e.timeSlots.forEach(function(e){return e.isSelected=!1})}),null===a||void 0===a||a.forEach(function(e){return e.timeSlots.forEach(function(t){return t.isSelected=!(e.date!==r.date||!r.timeSlots.map(function(e){return e.intervalType===t.intervalType}))})}),a),t.additionalDetails.apartment=e.APPARTMENT,t.additionalDetails.entryCode=e.ENTRY_CODE,t.additionalDetails.informedSuperintendant=e.INFORMED_SUPERINTENDANT,t.additionalDetails.specialInstructions=e.SPECIAL_INSTRUCTIONS,t.additionalDetails.superintendantName=e.SUPERINTENDANT_NAME,t.additionalDetails.superintendantPhone=e.SUPERINTENDANT_PHONE,t},e}()}])},e.exports=a(n(2),n(1),n(3),n(0),n(35),n(5),n(6),n(4),n(8),n(7))},function(e,t){e.exports=u},function(e,t,n){var a;"undefined"!=typeof self&&self,a=function(e,t,n,a,r,i,o,c,l,s){return function(e){function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=7)}([function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=a},function(e,t){e.exports=r},function(e,t){e.exports=i},function(e,t){e.exports=o},function(e,t,n){"use strict";function a(e,t){function n(){this.constructor=e}g(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t,n,a){var r,i=arguments.length,o=i<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,n):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,a);else for(var c=e.length-1;c>=0;c--)(r=e[c])&&(o=(i<3?r(o):i>3?r(t,n,o):r(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,r,i=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=i.next()).done;)o.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o}function c(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(o(arguments[t]));return e}function l(e){return Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Remove||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Removed||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Delete}function s(e){return Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Modify||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.NoChange||l(e)}function u(e,t){var n={category:e,name:Object(y.ValueOf)(t,"name",""),sku:"",quantity:l(t)?"-1":"1",price:(l(t)?"-":"")+Object(y.ValueOf)(t,"regularPrice.price","0")};return!1!==Object(y.ValueOf)(t,"promotionDetails.discountPrice.price",!1)&&(n.promo=Object(y.ValueOf)(t,"promotionDetails.discountPrice.price","")),n}function d(e){var t=Object(y.ValueOf)(e,"lineOfBusiness.Internet.Current.0.productOfferings",[]).filter(s),n=Object(y.ValueOf)(e,"lineOfBusiness.Internet.New.0.productOfferings",[]),a=Object(y.ValueOf)(e,"lineOfBusiness.TV.Current.0.productOfferings",[]).filter(s),r=Object(y.ValueOf)(e,"lineOfBusiness.TV.New.0.productOfferings",[]);return c(t.map(function(e){return u("Internet",e)}),n.map(function(e){return u("Internet",e)}),a.map(function(e){return u(Object(y.ValueOf)(e,"displayGroupKey",""),e)}),r.map(function(e){return u(Object(y.ValueOf)(e,"displayGroupKey",""),e)}))}function m(e){try{var t=0;return Array.from(e.childNodes).forEach(function(e){return t+=e.getBoundingClientRect().height}),t}catch(e){return 0}}Object.defineProperty(t,"__esModule",{value:!0});var p={};n.d(p,"getOrderSummary",function(){return P}),n.d(p,"getOrderDetails",function(){return k}),n.d(p,"setReviewMessages",function(){return M}),n.d(p,"setAcceptedTerms",function(){return j}),n.d(p,"setOrderSummary",function(){return D}),n.d(p,"setAppointmentDetails",function(){return L}),n.d(p,"setOrderConfirmation",function(){return V}),n.d(p,"submitOrder",function(){return F}),n.d(p,"downloadPDF",function(){return B}),n.d(p,"modifyChannelSelection",function(){return W});var f,E,g=function(e,t){return(g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},b=function(){return(b=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},h=n(1),O=n(8),v=n(3),y=n(0),N=n(4),x=n(9),T=n(6),S=n(5),A="NOT_FOUND",I=function(e,t){return e===t},_=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return function(){for(var t=arguments.length,a=new Array(t),r=0;r<t;r++)a[r]=arguments[r];var i,o=0,c={memoizeOptions:void 0},l=a.pop();if("object"==typeof l&&(c=l,l=a.pop()),"function"!=typeof l)throw new Error("createSelector expects an output function after the inputs, but received: ["+typeof l+"]");var s=c.memoizeOptions,u=void 0===s?n:s,d=Array.isArray(u)?u:[u],m=function(e){var t=Array.isArray(e[0])?e[0]:e;if(!t.every(function(e){return"function"==typeof e})){var n=t.map(function(e){return"function"==typeof e?"function "+(e.name||"unnamed")+"()":typeof e}).join(", ");throw new Error("createSelector expects all input-selectors to be functions, but received the following types: ["+n+"]")}return t}(a),p=e.apply(void 0,[function(){return o++,l.apply(null,arguments)}].concat(d)),f=e(function(){for(var e=[],t=m.length,n=0;n<t;n++)e.push(m[n].apply(null,arguments));return i=p.apply(null,e)});return Object.assign(f,{resultFunc:l,memoizedResultFunc:p,dependencies:m,lastResult:function(){return i},recomputations:function(){return o},resetRecomputations:function(){return o=0}}),f}}(function(e,t){function n(){var t=m.get(arguments);if(t===A){if(t=e.apply(null,arguments),u){var n=m.getEntries().find(function(e){return u(e.value,t)});n&&(t=n.value)}m.put(arguments,t)}return t}var a,r,i="object"==typeof t?t:{equalityCheck:t},o=i.equalityCheck,c=void 0===o?I:o,l=i.maxSize,s=void 0===l?1:l,u=i.resultEqualityCheck,d=function(e){return function(t,n){if(null===t||null===n||t.length!==n.length)return!1;for(var a=t.length,r=0;r<a;r++)if(!e(t[r],n[r]))return!1;return!0}}(c),m=1===s?(a=d,{get:function(e){return r&&a(r.key,e)?r.value:A},put:function(e,t){r={key:e,value:t}},getEntries:function(){return r?[r]:[]},clear:function(){r=void 0}}):function(e,t){function n(e){var n=a.findIndex(function(n){return t(e,n.key)});if(n>-1){var r=a[n];return n>0&&(a.splice(n,1),a.unshift(r)),r.value}return A}var a=[];return{get:n,put:function(t,r){n(t)===A&&(a.unshift({key:t,value:r}),a.length>e&&a.pop())},getEntries:function(){return a},clear:function(){a=[]}}}(s,d);return n.clearCache=function(){return m.clear()},n}),C=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},w=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(t,n){return e.toUpperCase().indexOf(n.toUpperCase())>=0||t},!1)},R=function(e){return Object.keys(e).reduce(function(t,n){return e[n]?c(t,[n]):t},[]).join("_").toUpperCase()};(E=f||(f={})).select=function(e){return function(t){return function(e,t){void 0===t&&(t={});var n=(""+e).split("."),a=n.length-1;return n.reduce(function(e,t,n){return null!==e&&C(e,t)?n===a?e[t]:b({},e[t]):null},t)}(e,t)}},E.lineOfBusiness=function(e,t){return function(){return _(E.select("summary.lineOfBusiness."+t+"."+e),function(e){return e||[]})}},E.totalCharge=function(e){return function(){return _(E.select("summary."+e),function(e){return e})}},E.additionalCharge=function(e){return function(){return _([E.lineOfBusiness(e,"TV")(),E.lineOfBusiness(e,"Internet")()],function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n="";return{price:e.reduce(function(e,t){return c(e,t)},[]).reduce(function(e,t){return t.additionalCharges?c(e,t.additionalCharges):e},[]).reduce(function(e,t){return n=t.priceDetail.priceType,e+ +t.priceDetail.price},0),priceType:n}})}},E.productOfferings=function(e,t){return function(){return _(E.lineOfBusiness(e,t)(),function(e){return e.reduce(function(e,t){return c(e,t.productOfferings)},[])})}},E.sortAndGroupByOfferingType=function(e){return e.sort(function(e,t){return e.displayGroupKey===y.Volt.EDIsplayGroupKey.PROMOTION?1:t.displayGroupKey===y.Volt.EDIsplayGroupKey.PROMOTION?-1:e.sortPriority>t.sortPriority?1:-1}),e.reduce(function(e,t){var n;return Object.assign(e,((n={})[t.displayGroupKey]=(e[t.displayGroupKey]||[]).concat(t),n))},{})};var P=Object(T.createAction)("GET_ORDER_SUMMARY"),k=Object(T.createAction)("GET_ORDER_DETAILS"),M=Object(T.createAction)("SET_REVIEW_MESSAGES",function(e){return e.productOfferingDetail.messages||[]}),j=Object(T.createAction)("SET_ACCEPTED_TERMS",function(e,t){return e.some(function(e){return e===t})?e.filter(function(e){return e!==t}):c(e,[t])}),D=Object(T.createAction)("SET_ORDER_SUMMARY",function(e){var t=e.productOfferingDetail,n=t.productOfferingGroups,a=t.productConfigurationTotal,r=t.displayGroup,i=n.filter(function(e){return null!==e.lineOfBusiness}).map(function(e){return e.productOfferings.map(function(e){return Object(y.ValueOf)(r,"baseOffering.key","")===e.displayGroupKey?e.sortPriority=r.baseOffering.sortPriority:Object(y.ValueOf)(r,"additionalOfferings",[]).map(function(t){t.key===e.displayGroupKey&&(e.sortPriority=t.sortPriority)}),e}),e}).reduce(function(e,t){var n;return b(b({},e),((n={})[t.lineOfBusiness]=C(e,t.lineOfBusiness)?c(e[t.lineOfBusiness],[t]):[t],n))},{}),o=Object.keys(i).reduce(function(e,t){var n,a=i[t].reduce(function(e,t){var n;return b(b({},e),((n={})[t.productOfferingGroupType]=C(e,t.productOfferingGroupType)?c(e[t.productOfferingGroupType],[t]):[t],n))},{});return b(b({},e),((n={})[t]=a,n))},{}),l=Object(y.ValueOf)(a,"priceOvertime",[]).find(function(e){return"AllLOBs"===e.flowType});return l||(l=Object(y.ValueOf)(a,"priceOvertime",[]).reduce(function(e,t){return e.newPrice||e.currentPrice?(e.currentPrice={price:Object(y.ValueOf)(e,"currentPrice.price",0)+Object(y.ValueOf)(t,"currentPrice.price",0),priceType:"Recurring"},e.newPrice={price:Object(y.ValueOf)(e,"newPrice.price",0)+Object(y.ValueOf)(t,"newPrice.price",0),priceType:"Recurring"}):e=t,e},{})),{currentTotal:Object(y.ValueOf)(l,"currentPrice",{}),newTotal:Object(y.ValueOf)(l,"newPrice",{}),lineOfBusiness:o}}),L=Object(T.createAction)("SET_ORDER_APPOINTMENT_DETAILS",function(e){var t,n=e.productOfferingDetail;try{var a=((t=n.appointment).availableDates||[]).find(function(e){return e.timeSlots.find(function(e){return e.isSelected})});t.preferredDate=a?{date:a.date,intervalType:(a.timeSlots.find(function(e){return e.isSelected})||{}).intervalType}:{}}catch(e){t={}}return{appointmentDetails:t,customerInformation:Object(y.ValueOf)(n,"customerInformation",{})}}),V=Object(T.createAction)("SET_ORDER_CONFIRMATION"),F=Object(T.createAction)("SUBMIT_COMPLETE_ORDER"),B=Object(T.createAction)("DOWNLOAD_PDF"),W=Object(T.createAction)("MODIFY_CHANNEL_SELECTION"),G=n(10),U=n(16),z=N.CommonFeatures.BaseConfig,H=N.CommonFeatures.configProperty,K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),r([H(""),i("design:type",String)],t.prototype,"flowType",void 0),r([H({}),i("design:type",Object)],t.prototype,"environmentVariables",void 0),r([H({}),i("design:type",Object)],t.prototype,"mockdata",void 0),r([H({}),i("design:type",Object)],t.prototype,"headers",void 0),r([H("/styles/pdf/"),i("design:type",String)],t.prototype,"pdfDownloadPath",void 0),r([H({base:""}),i("design:type",Object)],t.prototype,"api",void 0),r([N.Injectable],t)}(z),q=function(e){function t(t,n){return e.call(this,t,n)||this}return a(t,e),r([N.Injectable,i("design:paramtypes",[N.AjaxServices,K])],t)}(y.BaseClient),X=S.ActionsObservable.concat,Y=y.Actions.errorOccured,$=y.Actions.setWidgetStatus,J=y.Actions.broadcastUpdate,Z=y.Actions.setProductConfigurationTotal,Q=function(){function e(e,t,n){this.client=e,this.config=t,this.params=n,this.widgetState=y.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(S.combineEpics)(this.requestOrderSummaryEpic,this.requestOrderDetailsEpic)},Object.defineProperty(e.prototype,"requestOrderSummaryEpic",{get:function(){var e=this;return function(t,n){return t.ofType(P.toString()).filter(function(){return e.widgetState!==y.EWidgetStatus.UPDATING}).mergeMap(function(){return X([$(e.widgetState=y.EWidgetStatus.UPDATING)],e.client.get(Object(y.ValueOf)(e.params,"props.summaryAPI")).mergeMap(function(e){return[D(e.data),M(e.data),L(e.data),$(y.EWidgetStatus.RENDERED),y.Actions.omniModalOpen()]}))}).catch(function(e,t){return U.Observable.merge(U.Observable.of(Y(new y.Models.ErrorHandler(P.toString(),e))),t)})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"requestOrderDetailsEpic",{get:function(){var e=this;return function(t){return t.ofType(k.toString()).filter(function(){return e.widgetState!==y.EWidgetStatus.UPDATING}).mergeMap(function(){var t;return X([$(e.widgetState=y.EWidgetStatus.UPDATING)],e.client.get(y.Utils.getURLByFlowType((t={},t[y.EFlowType.INTERNET]=e.config.api.internetOrderDetailsAPI,t[y.EFlowType.TV]=e.config.api.tvOrderDetailsAPI,t[y.EFlowType.ADDTV]=e.config.api.tvAddDetailsAPI,t[y.EFlowType.BUNDLE]=e.config.api.bundleOrderDetailsAPI,t))).mergeMap(function(t){var n=t.data;return[M(n),D(n),L(n),$(e.widgetState=y.EWidgetStatus.RENDERED),J(Z(Object(y.ValueOf)(n,"productOfferingDetail.productConfigurationTotal"))),y.Actions.omniPageLoaded()]}))}).catch(function(e){return[Y(new y.Models.ErrorHandler("getOrderDetails",e))]})}},enumerable:!1,configurable:!0}),r([N.Injectable,i("design:paramtypes",[q,K,N.ParamsProvider])],e)}(),ee=S.ActionsObservable.concat,te=y.Actions.setWidgetStatus,ne=y.Actions.broadcastUpdate,ae=y.Actions.historyGo,re=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=y.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(S.combineEpics)(this.submitEpic)},Object.defineProperty(e.prototype,"submitEpic",{get:function(){var e=this;return function(t,n){return t.ofType(F.toString()).filter(function(){return e.widgetState!==y.EWidgetStatus.UPDATING}).mergeMap(function(t){var n;return t.payload,ee([te(e.widgetState=y.EWidgetStatus.UPDATING)],e.client.post(y.Utils.getURLByFlowType((n={},n[y.EFlowType.INTERNET]=e.config.api.internetOrderSubmitAPI,n[y.EFlowType.TV]=e.config.api.tvOrderSubmitAPI,n[y.EFlowType.ADDTV]=e.config.api.tvAddSubmitAPI,n[y.EFlowType.BUNDLE]=e.config.api.bundleOrderSubmitAPI,n)),null).mergeMap(function(e){return Object(y.FilterRestrictionObservable)(e,[V(e.data),M(e.data),te(y.EWidgetStatus.RENDERED),ne(ae(y.EWidgetRoute.CONFIRMATION),10)])}))}).catch(y.Models.ErrorHandlerObservable(F))}},enumerable:!1,configurable:!0}),r([N.Injectable,i("design:paramtypes",[q,K])],e)}(),ie=y.Actions.omniPageLoaded,oe=y.Actions.omniModalOpen,ce=function(){function e(e){this.params=e,this.widgetState=y.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return Object(S.combineEpics)(this.pageLoadedEpic,this.summaryLightboxEpic,this.submitEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){var e=this;return function(t,n){return t.ofType(ie.toString()).mergeMap(function(){var t,a,r,i,o,c=y.Omniture.useOmniture(),l=n.getState(),s=l.summary,u=l.confirmation,m=y.Utils.getFlowType(),p=w(e.params.props.mode,y.EReviewMode.Review)?"Review":"Confirmation";switch(m){case y.EFlowType.INTERNET:t="Internet",r=523,i=1,o=2;break;case y.EFlowType.TV:t="TV",r=394,i=1,o=2;break;case y.EFlowType.ADDTV:t="TV",a="Add Tv",r=507,o=2,i=1;break;case y.EFlowType.BUNDLE:t="Bundle",a="Add Tv",r=508,o=2,i=1}switch(p){case"Review":c.trackPage({id:"ReviewPage",s_oSS1:"~",s_oSS2:t,s_oSS3:a||"Change package",s_oPGN:p,s_oPLE:{type:y.Omniture.EMessageType.Warning,content:{ref:"MSG_PARAGRAPH_"+m}},s_oPRD:d(s)});break;case"Confirmation":c.trackPage({id:"ConfirmationPage",s_oSS1:"~",s_oSS2:t,s_oSS3:a||"Change package",s_oPGN:p,s_oPLE:[{type:y.Omniture.EMessageType.Confirmation,content:{ref:"ORDER_SUBMITTED"}},{type:y.Omniture.EMessageType.Warning,content:{ref:"IMPORTANT_MESSAGE_"+m}}],s_oPRD:d(s),s_oPID:Object(y.ValueOf)(u,"confirmationNumber"),s_oAPT:{actionId:r,applicationState:i,actionresult:o}})}return[]}).catch(function(e){return[]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"summaryLightboxEpic",{get:function(){return function(e,t){return e.ofType(oe.toString()).mergeMap(function(){var e=y.Omniture.useOmniture(),t="",n=104;switch(y.Utils.getFlowType()){case y.EFlowType.INTERNET:t="Internet ";break;case y.EFlowType.ADDTV:case y.EFlowType.TV:t="TV ";break;case y.EFlowType.BUNDLE:t="Bundle "}switch(y.Utils.getPageRoute()){case y.EWidgetRoute.INTERNET:case y.EWidgetRoute.TV:n=104}return e.trackAction({id:"summaryLigthbox",s_oAPT:{actionId:n},s_oPRM:t+"Preview order"}),[]}).catch(function(e){return[]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitEpic",{get:function(){return function(e,t){return e.ofType(F.toString()).mergeMap(function(){var e=y.Omniture.useOmniture(),n=t.getState().summary;return e.trackAction({id:"submit",s_oAPT:{actionId:647},s_oBTN:{ref:"ACCEPT_TERMS_AND_SUBMIT"},s_oPRD:d(n)}),[]})}},enumerable:!1,configurable:!0}),r([N.Injectable,i("design:paramtypes",[N.ParamsProvider])],e)}(),le=y.Actions.setWidgetStatus,se=function(){function e(e,t,n){this.reviewEpics=e,this.orderEpics=t,this.omnitureEpics=n}return e.prototype.combineEpics=function(){return Object(S.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.ofType(le.toString()).filter(function(e){return e.payload===y.EWidgetStatus.INIT}).mergeMap(function(){return[]})}},enumerable:!1,configurable:!0}),r([N.Injectable,i("design:paramtypes",[Q,re,ce])],e)}(),ue=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return a(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||N.ServiceLocator.instance.getService(N.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString(y.EWidgetName.REVIEW,e,t.locale):e},t.Instance=null,n=r([N.Injectable],t)}(N.CommonFeatures.BaseLocalization),de=N.CommonFeatures.BaseStore,me=(0,N.CommonFeatures.actionsToComputedPropertyName)(p),pe=me.setOrderSummary,fe=me.setAppointmentDetails,Ee=me.setReviewMessages,ge=me.setAcceptedTerms,be=me.setOrderConfirmation,he=function(e){function t(t,n,a,r){var i=e.call(this,n)||this;return i.client=t,i.epics=a,i.localization=r,i}return a(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,a,r;return y.Utils.reducer(G.PersistConfig,Object(x.combineReducers)(b(b(b(b({},y.Reducers.WidgetBaseLifecycle(this.localization)),y.Reducers.WidgetLightboxes()),y.Reducers.WidgetRestrictions()),{summary:Object(T.handleActions)((e={},e[pe]=function(e,t){return t.payload||e},e),{}),messages:Object(T.handleActions)((t={},t[Ee]=function(e,t){return t.payload||e},t),[]),acceptedTerms:Object(T.handleActions)((n={},n[ge]=function(e,t){return t.payload||e},n),[]),appointment:Object(T.handleActions)((a={},a[fe]=function(e,t){return t.payload||e},a),{}),confirmation:Object(T.handleActions)((r={},r[be]=function(e,t){return t.payload||e},r),{})})))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return[Object(S.createEpicMiddleware)(this.epics.omnitureEpics.combineEpics()),Object(S.createEpicMiddleware)(this.epics.reviewEpics.combineEpics()),Object(S.createEpicMiddleware)(this.epics.orderEpics.combineEpics()),Object(S.createEpicMiddleware)(this.epics.combineEpics()),Object(S.createEpicMiddleware)((new y.ModalEpics).combineEpics()),Object(S.createEpicMiddleware)(new y.RestricitonsEpics(this.client,"REVIEW_RESTRICTION_MODAL").combineEpics()),Object(S.createEpicMiddleware)((new y.LifecycleEpics).combineEpics())]},enumerable:!1,configurable:!0}),r([N.Injectable,i("design:paramtypes",[q,N.Store,se,ue])],t)}(de),Oe=function(e){function t(n){var a=e.call(this,n)||this;return t.instance=a,a}return a(t,e),t.Subscriptions=function(e){return{}},t}(N.CommonFeatures.BasePipe),ve=n(2),ye=y.Components.Visible,Ne=Object(v.connect)(function(e){var t=e.confirmation,n=e.appointment;return{isReview:!Object(y.ValueOf)(t,"confirmationNumber",!1),appointmentDetails:Object(y.ValueOf)(n,"appointmentDetails",{}),customerInformation:Object(y.ValueOf)(n,"customerInformation",{})}},function(e){return{onEdit:function(){y.Omniture.useOmniture().trackAction({id:"editClick",s_oAPT:{actionId:647},s_oBTN:{ref:"review_installation_edit"}}),e(y.Actions.broadcastUpdate(y.Actions.historyGo(y.EWidgetRoute.APPOINTMENT)))}}})(function(e){var t=e.isReview,n=e.appointmentDetails,a=e.customerInformation,r=e.onEdit;return h.createElement(h.Fragment,null,h.createElement(ye,{when:Object(y.ValueOf)(a,void 0,!1)},h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}),h.createElement("div",{className:"bgWhite pad-30-left pad-40-right padding-25-xs border-radius-3"},h.createElement("div",{className:"spacer30 d-none d-sm-block"}),h.createElement("div",{className:"spacer25 d-block d-sm-none"}),h.createElement("div",{className:""},h.createElement("div",{className:"flexCol"},h.createElement("h2",{className:"txtBlack txtSize22 noMargin differentTextureforHandset floatL virginUltraReg txtUppercase"},h.createElement(ve.FormattedMessage,{id:"customer information"})))),h.createElement("div",{className:"spacer10"}),h.createElement("p",{className:"txtSize14 txtGray4A sans-serif no-margin"},h.createElement(ve.FormattedMessage,{id:"MAKE_CHANGES"})),h.createElement("div",{className:"spacer20"}),h.createElement(ye,{when:Object(y.ValueOf)(a,"name",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_NAME"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin "},Object(y.ValueOf)(a,"name","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(a,"email",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_EMAIL"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin "},Object(y.ValueOf)(a,"email","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(a,"address",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_SERVICE_ADDRESS"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin "},h.createElement("div",null,h.createElement(ye,{when:Object(y.ValueOf)(a,"address.apartmentNumber",!1)},Object(y.ValueOf)(a,"address.apartmentNumber","")," - "),Object(y.ValueOf)(a,"address.address1","")," ",Object(y.ValueOf)(a,"address.address2","")," ",Object(y.ValueOf)(a,"address.streetType","")),h.createElement("div",null,Object(y.ValueOf)(a,"address.city",""),","),h.createElement("div",null,Object(y.ValueOf)(a,"address.province",""),", ",Object(y.ValueOf)(a,"address.postalCode",""))))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(a,"paymentMethod",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"PAYMENT_INFORMATION"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement(ye,{when:"Regular"===Object(y.ValueOf)(a,"paymentMethod","")},h.createElement("p",{className:"txtBlack txtSize14 no-margin "},h.createElement(ve.FormattedMessage,{id:"PAYMENT_INFORMATION_REGULAR"}))),h.createElement(ye,{when:"PreAuthBank"===Object(y.ValueOf)(a,"paymentMethod","")},h.createElement("p",{className:"txtBlack txtSize14 no-margin "},h.createElement(ve.FormattedMessage,{id:"PAYMENT_INFORMATION_PREAUTH"}))),h.createElement(ye,{when:"PreAuthCreditCard"===Object(y.ValueOf)(a,"paymentMethod","")},h.createElement("p",{className:"txtBlack txtSize14 no-margin "},h.createElement(ve.FormattedMessage,{id:"PAYMENT_INFORMATION_PREAUTHCC"}))))),h.createElement("div",{className:"spacer5"})),h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}))),h.createElement(ye,{when:Object(y.ValueOf)(n,"isInstallationRequired",!1)},h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}),h.createElement("div",{className:"bgWhite pad-30-left pad-40-right padding-25-xs border-radius-3"},h.createElement("div",{className:"spacer30 d-none d-sm-block"}),h.createElement("div",{className:"spacer25 d-block d-sm-none"}),h.createElement("div",{className:""},h.createElement("div",{className:"flexCol"},h.createElement("h4",{className:"txtBlack txtSize22 noMargin differentTextureforHandset floatL virginUltraReg txtUppercase"},h.createElement(ve.FormattedMessage,{id:"installation details"}),h.createElement(ye,{when:t},h.createElement("button",{id:"review_installation_edit",onClick:r,className:"txtBlue txtSize14 margin-20-left noBorder bgTransparent pointer"},h.createElement(ve.FormattedMessage,{id:"Edit"}),h.createElement("span",{className:"virgin-icon icon-edit txtBlue txtSize14 margin-5-left","aria-hidden":"true"})))))),h.createElement("div",{className:"spacer20"}),h.createElement(ye,{when:Object(y.ValueOf)(n,"preferredDate",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_DATE_AND_TIME"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft txtBlack txtBold txtSize14"},h.createElement("p",{className:"txtBlack mb-0"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(n,"preferredDate.date",""),year:"numeric",weekday:"long",month:"long",day:"2-digit",timeZone:"UTC"})),h.createElement("div",null,h.createElement(ve.FormattedHTMLMessage,{id:""+Object(y.ValueOf)(n,"preferredDate.intervalType","")})))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"installationAddress",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_SHIPPING_INSTALLATION_ADDRESS"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},h.createElement(ye,{when:Object(y.ValueOf)(n,"installationAddress.apartmentNumber",!1)},Object(y.ValueOf)(n,"installationAddress.apartmentNumber","")," - "),Object(y.ValueOf)(n,"installationAddress.address1","")," ",Object(y.ValueOf)(n,"installationAddress.address2","")," ",Object(y.ValueOf)(n,"installationAddress.streetType",""),", ",Object(y.ValueOf)(n,"installationAddress.city",""),", ",Object(y.ValueOf)(n,"installationAddress.province",""),", ",Object(y.ValueOf)(n,"installationAddress.postalCode","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:"Email"!==Object(y.ValueOf)(n,"contactInformation.preferredContactMethod","")},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement(ye,{when:"Phone"===Object(y.ValueOf)(n,"contactInformation.preferredContactMethod","")},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_PHONE_NUMBER"}))),h.createElement(ye,{when:"TextMessage"===Object(y.ValueOf)(n,"contactInformation.preferredContactMethod","")},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"TEXT_MESSAGE"})))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"contactInformation.primaryPhone.phoneNumber","")," ",h.createElement(ye,{when:Object(y.ValueOf)(n,"contactInformation.primaryPhone.phoneExtension",!1)},h.createElement(ve.FormattedMessage,{id:"EXT"})," ",Object(y.ValueOf)(n,"contactInformation.primaryPhone.phoneExtension",""))))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:"Email"===Object(y.ValueOf)(n,"contactInformation.preferredContactMethod","")},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_EMAIL"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"contactInformation.email","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"contactInformation.additionalPhone",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"ADDITIONAL_PHONE_NUMBER"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"contactInformation.additionalPhone.phoneNumber","")," ",h.createElement(ye,{when:Object(y.ValueOf)(n,"contactInformation.additionalPhone.phoneExtension",!1)},h.createElement(ve.FormattedMessage,{id:"EXT"})," ",Object(y.ValueOf)(n,"contactInformation.additionalPhone.phoneExtension",""))))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails.apartment",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_APARTMENT_NUMBER"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"additionalDetails.apartment","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails.entryCode",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_ENTRY_CODE"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"additionalDetails.entryCode","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails.superintendantName",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_SUPERINTENDANT_NAME"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"additionalDetails.superintendantName","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails.superintendantPhone",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_SUPERINTENDANT_PHONE"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"additionalDetails.superintendantPhone","")))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_SUPERINTENDANT_INFORMED"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails.informedSuperintendant",!1),placeholder:h.createElement(ve.FormattedMessage,{id:"NO"})},h.createElement(ve.FormattedMessage,{id:"YES"}))))),h.createElement("div",{className:"spacer5"})),h.createElement(ye,{when:Object(y.ValueOf)(n,"additionalDetails.specialInstructions",!1)},h.createElement("div",{className:"flexRow col-12"},h.createElement("div",{className:"col-xs-6 col-sm-6 col-md-3 no-pad"},h.createElement("div",{className:"txtBlack txtBold txtSize14"},h.createElement(ve.FormattedMessage,{id:"APPOINTMENT_SPECIAL_INSTRUCTIONS"}))),h.createElement("div",{className:"col-xs-7 col-sm-6 col-md-9 txtLeft"},h.createElement("p",{className:"txtBlack txtSize14 no-margin"},Object(y.ValueOf)(n,"additionalDetails.specialInstructions",""))))),h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}))))}),xe=y.Utils.getFlowType(),Te=function(){var e=Object(v.useSelector)(function(e){return e.messages},v.shallowEqual),t=Object(v.useSelector)(function(e){return e.confirmation},v.shallowEqual),n=Object(v.useDispatch)();return h.createElement(h.Fragment,null,h.createElement("div",{className:"panel-body bgWhite section-packages-listing notification-container"},h.createElement("div",{className:"notification success"},h.createElement("span",{className:"virgin-icon icon-Big_check_confirm txtSize40 float-left",style:{marginLeft:"-55px",marginTop:"-5px"}},h.createElement("span",{className:"virgin-icon path1 greenIcon"}),h.createElement("span",{className:"volt-icon path2"})),h.createElement("div",{id:"CONFIRMATION_MESSAGE_"+xe},h.createElement("div",{id:"ORDER_SUBMITTED",className:"txtSize18 notification-title txtBlack2 pad-5-top txtBold txtUppercase",role:"heading","aria-level":2},h.createElement(ve.FormattedMessage,{id:"ORDER_SUBMITTED"})),h.createElement("div",{className:"spacer15","araia-hidden":!0}),h.createElement("div",{className:"notification-message"},h.createElement("span",{className:"block"},h.createElement("strong",null,h.createElement(ve.FormattedMessage,{id:"CONFIRMATION_NUMBER"}),": "),t.confirmationNumber),h.createElement("span",{className:"block"},h.createElement("strong",null,h.createElement(ve.FormattedMessage,{id:"CONFIRMATION_DATE"}),": "),h.createElement(ve.FormattedDate,{value:t.orderDate,year:"numeric",month:"long",day:"2-digit"})),h.createElement("div",{className:"spacer15","araia-hidden":!0}),h.createElement("span",{className:"block"},h.createElement(ve.FormattedMessage,{id:"CONFIRMATION_MESSAGE_"+xe})))),h.createElement("div",{className:"spacer15","araia-hidden":!0}),h.createElement("button",{id:"app_exit_btn",className:"btn btn-primary fill-xs",role:"link",onClick:function(){return n(y.Actions.broadcastUpdate(y.Actions.applicationExit()))}},h.createElement(ve.FormattedMessage,{id:"RETURN_TO_ACCOUNT_"+xe})),h.createElement("div",{className:"bgWhite flexBlock pad-20-right pad-20-top margin-15-bottom txtBlack"},h.createElement("span",{className:"virgin-icon icon-warning txtSize36"},h.createElement("span",{className:"virgin-icon path1 yellowIcon"}),h.createElement("span",{className:"volt-icon path2"})),h.createElement("div",{id:"IMPORTANT_MESSAGE_"+xe,className:"flexCol pad-15-left content-width valign-top pad-0-xs"},h.createElement("h3",{className:"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase"},h.createElement(ve.FormattedMessage,{id:"IMPORTANT_INFO"})),h.createElement("p",{className:"txtSize14 txtGray4A sans-serif no-margin"},h.createElement(ve.FormattedHTMLMessage,{id:"IMPORTANT_MESSAGE_"+xe})),(e||[]).map(function(e){return h.createElement("p",{className:"txtSize14 txtGray4A sans-serif no-margin",key:e.messageCode,dangerouslySetInnerHTML:{__html:e.messageBody}})}))))),h.createElement("div",{className:"spacer10 clear"}))},Se="_overlayFloaterRefID",Ae=0,Ie=function(){var e=o(h.useState(!0),2),t=e[0],n=e[1];return h.useEffect(function(){var e=document.getElementById(Se);Ae=e.getBoundingClientRect().top||0;var a=function(e){t&&window.scrollY+window.innerHeight>Ae&&n(!1),!t&&window.scrollY+window.innerHeight<Ae&&n(!0)};return window.addEventListener("scroll",a),function(){return window.removeEventListener("scroll",a)}}),h.createElement(h.Fragment,null,h.createElement("div",{id:Se}),h.createElement("div",{className:"block bgBlack txtWhite txtCenter pad-10-top pad-10-bottom",style:{position:"fixed",bottom:"0",left:"0",width:"100%",zIndex:999,pointerEvents:"none",opacity:t?"1":"0"}},h.createElement("i",{className:"virgin-icon icon-Down_arrow1 pad-15-right",style:{fontSize:"50%"}}),h.createElement(ve.FormattedMessage,{id:"SCROLL_TO_THE_BOTTOM"},function(e){return h.createElement("span",{"aria-hidden":""+!t},e)}),h.createElement("i",{className:"virgin-icon icon-Down_arrow1 pad-15-left",style:{fontSize:"50%"}})))},_e=function(){var e=o(h.useState(!1),2),t=e[0],n=e[1];return h.createElement("button",{id:"ACCEPT_AND_SUBMIT",onClick:function(){return n(!t)},onMouseOver:function(){return n(!0)},onMouseOut:function(){return n(!1)},className:"btn btn-primary btn-block-xs floatR fullWidth-xs relative",disabled:!0},h.createElement(ve.FormattedMessage,{id:"ACCEPT_AND_SUBMIT"}),h.createElement(y.Components.Visible,{when:t},h.createElement("div",{className:"tooltip fade bs-tooltip-top show",role:"tooltip",id:"tooltip504192",style:{position:"absolute",width:"400px",top:"-90px",left:"50%",transform:"translateX(-50%)"}},h.createElement("div",{className:"arrow",style:{left:"50%"}}),h.createElement("div",{className:"tooltip-inner",style:{maxWidth:"400px",width:"100%"}},h.createElement("div",{className:"flexRow bgWhite txtBlack"},h.createElement("div",{className:"olt-icon icon-warning txtSize22 margin-10-right"},h.createElement("span",{className:"volt-icon path1 yellowIcon"}),h.createElement("span",{className:"volt-icon path2"})),h.createElement("div",{className:"margin-5-top"},h.createElement(ve.FormattedMessage,{id:"Check terms to continue"})))))))},Ce=function(){var e=Object(v.useDispatch)(),t=Object(v.useSelector)(f.select("acceptedTerms")),n=Object(v.useSelector)(f.select("summary.lineOfBusiness.TV")),a=(y.Utils.getFlowType()===y.EFlowType.ADDTV||y.Utils.getFlowType()===y.EFlowType.BUNDLE)&&t.length<1;return h.createElement(h.Fragment,null,h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}),h.createElement(Ie,null),h.createElement("div",{className:"flexBlock flex-direction-row-reverse flexJustifySpace flexCol-xs accss-focus-outline-override-grey-bg"},h.createElement(y.Components.Visible,{when:!a,placeholder:h.createElement(_e,null)},h.createElement("button",{id:"ACCEPT_TERMS_AND_SUBMIT",onClick:function(){return e(F())},className:"btn btn-primary btn-block-xs floatR fullWidth-xs"},h.createElement(ve.FormattedMessage,{id:"ACCEPT_AND_SUBMIT"}))),h.createElement("div",{className:"spacer10 d-block d-sm-none","aria-hidden":"true"}),h.createElement(y.Components.Visible,{when:n},h.createElement("button",{id:"MODIFY_SELECTION",className:"btn btn-default modifychannel",onClick:function(){y.Omniture.useOmniture().trackAction({id:"modifySelectionClick",s_oAPT:{actionId:647},s_oBTN:{ref:"MODIFY_SELECTION"}}),e(y.Actions.broadcastUpdate(y.Actions.historyGo(y.EWidgetRoute.TV)))}},h.createElement(ve.FormattedMessage,{id:"MODIFY_MY_CHANNEL_SELECTION"})))),h.createElement("div",{className:"spacer40 clear"}))},we=function(){var e=o(h.useState(!1),2),t=e[0],n=e[1];return h.useEffect(function(){t&&y.Omniture.useOmniture().trackAction({id:"learmMOreElectronicDeliveryClick",s_oAPT:{actionId:648},s_oEPN:{ref:"electronic_delivery_more"}})},[t]),h.createElement("div",{className:"accss-focus-outline-override-pad",id:"electronic_delivery"},h.createElement("button",{id:"electronic_delivery_more",className:"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray",onClick:function(){return n(!t)},"aria-expanded":t},h.createElement("span",{className:"volt-icon "+(t?"icon-collapse_m":"icon-expand_m"),"aria-hidden":"true"}),"  ",h.createElement(ve.FormattedMessage,{id:"electronic_delivery_more"})),h.createElement(y.Components.Visible,{when:t},h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}),h.createElement("div",{className:"moreInfoBox bgWhite pad30 margin-30-bottom"},h.createElement(ve.FormattedHTMLMessage,{id:"ABOUT_SELECTRONIC_DELIVERY_TEXT"}))))},Re=function(){var e=o(h.useState(220),2),t=e[0],n=e[1],a=Object(v.useDispatch)(),r=Object(v.useSelector)(f.select("acceptedTerms"))||[],i=h.useContext(y.Context),c="QC"===i.config.environmentVariables.province,l=ue.getLocalizedString("DOWNLOAD_PDF_FILE"+(c?"_QC":"")),s=y.Utils.getFlowType();return h.createElement(h.Fragment,null,h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}),h.createElement("div",{className:"panel-border bgGrey2 flexBlock"},h.createElement("div",{className:"bgWhite box-border-gray-light pad-30-left pad-30-top pad-30-bottom pad-10-right col-xs-12 accss-link-override accss-focus-outline-override-white-bg",style:{maxWidth:"100%"}},h.createElement("h3",{className:"no-margin virginUltraReg txtSize22 txtDarkGrey1 txtUppercase txtLineHeight-24",id:"TOS_Label"},h.createElement(ve.FormattedMessage,{id:"Virgin Mobile Terms of Service"})),h.createElement("a",{id:"DOWNLOAD_PDF_LINK",onClick:function(e){e.stopPropagation(),y.Omniture.useOmniture().trackAction({id:"downloadPDFClick",s_oAPT:{actionId:341},s_oBTN:{ref:"DOWNLOAD_PDF_LINK"}})},"aria-describedby":"TOS_Label",className:"txtUnderline noBorder bgTransparent pointer txtBlue",href:""+(i.config.pdfDownloadPath+l),download:!0},h.createElement(ve.FormattedMessage,{id:"DOWNLOAD_PDF"},function(e){return h.createElement("span",{"aria-hidden":"true"},e)}),h.createElement("span",{className:"sr-only"},h.createElement(ve.FormattedMessage,{id:"DOWNLOAD_TERMS_PDF"},function(e){return h.createElement(h.Fragment,null,e)}))),h.createElement("div",{className:"pad-30-right"},h.createElement("div",{className:"spacer10 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer1 bgGrayLight6 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"})),h.createElement("div",{className:"col-xs-12 "},h.createElement("div",{className:"accordion"},h.createElement("div",{className:"accordion-group"},h.createElement("div",{id:"terms-conditions",className:"scrollAdjust",style:{height:t+"px"}},h.createElement(ve.FormattedHTMLMessage,{id:"TERMS_AND_CONDITIONS"+(c?"_QC":"")}),h.createElement("div",{className:"clear"})),h.createElement("div",{className:"accordion-heading"},h.createElement("div",{className:"col-xs-12 pad-10-top"},h.createElement("button",{id:"EXPAND_TERMS",className:"expand-toggle txtUnderline expand-collapse noBorder bgTransparent pointer txtBlue accss-link-underline-override links-blue-on-bg-white",onClick:function(){n(220===t?440:220)}},h.createElement(ve.FormattedMessage,{id:220===t?"Expand terms and conditions":"Collapse terms and conditions"}))),h.createElement("div",{className:"clear"}))))),h.createElement("div",{className:"clear"}))),h.createElement(y.Components.Visible,{when:s===y.EFlowType.ADDTV||s===y.EFlowType.BUNDLE,placeholder:h.createElement(h.Fragment,null,h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("p",{className:"txtRight"},h.createElement(ve.FormattedHTMLMessage,{id:"ACCEPT_TERMS_AND_DESCRIPTION"})))},h.createElement("div",{className:"flexBlock bgGray19 pad-h-15-xs"},h.createElement("div",{className:""},h.createElement("div",{className:""},["TERM_1"].map(function(e){return h.createElement(h.Fragment,{key:e},h.createElement("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),h.createElement("div",{className:"pad-20 "},h.createElement("label",{id:"label_"+e,className:"graphical_ctrl pointer graphical_ctrl_checkbox"},h.createElement(ve.FormattedMessage,{id:e}),h.createElement("input",{id:"checkbox_"+e,type:"checkbox",value:e,defaultChecked:r.some(function(t){return t===e}),onClick:function(){a(j(r,e))}}),h.createElement("span",{className:"ctrl_element chk_radius"}))))}))))),h.createElement("div",{className:"spacer15 clear","aria-hidden":"true"}),h.createElement("div",{className:"spacer15 d-sm-block clear","aria-hidden":"true"}),h.createElement(we,null))},Pe=y.Utils.getFlowType(),ke=function(){var e=Object(v.useSelector)(function(e){return e.messages},v.shallowEqual);return h.createElement(h.Fragment,null,h.createElement("div",{className:"bgWhite flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack"},h.createElement("span",{className:"virgin-icon icon-warning txtSize36"},h.createElement("span",{className:"virgin-icon path1 yellowIcon"}),h.createElement("span",{className:"volt-icon path2"})),h.createElement("div",{id:"MSG_PARAGRAPH_"+Pe,className:"pad-15-left content-width valign-top pad-0-xs"},h.createElement("h2",{className:"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase accss-info-text-header"},h.createElement(ve.FormattedMessage,{id:"HEADS_UP"})),h.createElement("span",null,h.createElement(ve.FormattedHTMLMessage,{id:"MSG_PARAGRAPH_"+Pe})),(e||[]).map(function(e){return h.createElement("p",{className:"txtSize14 txtGray4A sans-serif no-margin",key:e.messageCode,dangerouslySetInnerHTML:{__html:e.messageBody}})}))))},Me=function(){return h.createElement(h.Fragment,null,h.createElement("span",{className:"spacer25 d-none d-sm-block","aria-hidden":"true"}),h.createElement("span",{className:"spacer15 d-block d-sm-none","aria-hidden":"true"}),h.createElement("div",{className:"spacer1 bgGrayLight6 margin-25left-xs margin-25right-xs","aria-hidden":"true"}),h.createElement("span",{className:"spacer25 d-none d-sm-block","aria-hidden":"true"}),h.createElement("span",{className:"spacer15 d-block d-sm-none","aria-hidden":"true"}))},je=function(e){var t=e.message;return h.createElement("span",{className:"new"===t?"review-label margin-10-left bgOrange txtWhite txtSize12 txtBold txtUppercase":"review-label margin-10-left bgGray txtBlack txtSize12 txtBold txtUppercase"},h.createElement(ve.FormattedMessage,{id:t}))},De=function(e){var t=e.offer,n=e.supressFlag;return h.createElement(h.Fragment,null,h.createElement("p",{className:"txtBlack clear floatL flexCenter no-margin"},t.name||t.id,h.createElement(y.Components.Visible,{when:t.displayGroupKey===y.Volt.EDIsplayGroupKey.ALACARTE&&t.childOfferings&&t.childOfferings.length>0},": ",h.createElement(ve.FormattedMessage,{id:"CHANNELS_COUNT",values:{value:Object(y.ValueOf)(t,"childOfferings.length",0)}}))),h.createElement(y.Components.Visible,{when:!n&&Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.Add||Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.Added||Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.Create||Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.NewlySelected},h.createElement(je,{message:"new"})),h.createElement(y.Components.Visible,{when:!n&&Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.Remove||Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.Removed||Object(y.ValueOf)(t,"state")===y.Volt.EOfferingState.Delete},h.createElement(je,{message:"removed"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(t,"displayGroupKey")===y.Volt.EDIsplayGroupKey.PROMOTION&&Object(y.ValueOf)(t,"promotionDetails.expiryDate",!1)},h.createElement("br",null),h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(t,"promotionDetails.expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return h.createElement(ve.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})}))),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(t,"promotionDetails.description",!1)},h.createElement("p",{className:"txtBlack clear floatL flexCenter no-margin"},h.createElement("span",{className:"spacer15 d-none d-sm-block","aria-hidden":"true"}),Object(y.ValueOf)(t,"promotionDetails.description"),h.createElement(y.Components.Visible,{when:!n&&Object(y.ValueOf)(t,"promotionDetails.state")===y.Volt.EOfferingState.Remove||Object(y.ValueOf)(t,"promotionDetails.state")===y.Volt.EOfferingState.Removed||Object(y.ValueOf)(t,"promotionDetails.state")===y.Volt.EOfferingState.Delete},h.createElement(je,{message:"removed"})),h.createElement(y.Components.Visible,{when:!n&&Object(y.ValueOf)(t,"promotionDetails.state")===y.Volt.EOfferingState.Add},h.createElement(je,{message:"new"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(t,"promotionDetails.expiryDate",!1)},h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(t,"promotionDetails.expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return h.createElement(ve.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})}))),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(t,"promotionDetails.discountDuration",!1)},h.createElement("span",{className:"d-block"},h.createElement(ve.FormattedMessage,{id:"PromotionValid",values:{price:Math.abs(Object(y.ValueOf)(t,"promotionDetails.discountPrice.price",0)),discountDuration:Object(y.ValueOf)(t,"promotionDetails.discountDuration","")}}))))))},Le=function(e){var t=e.offerings,n=e.isReview,a=e.confirmation,r=e.isCurrent,i=Object(y.ValueOf)(t,"0.displayGroupKey","NULL"),o="ALACARTE"===i,c=Object(v.useDispatch)(),l=h.useMemo(function(){return y.Utils.getFlowType()},[]);return h.createElement("div",{className:"flexCol"},h.createElement("span",{className:"spacer15 d-block d-sm-none","aria-hidden":"true"}),h.createElement("div",{className:"flexRow flexJustifySpace"},h.createElement("p",{className:"txtBlack txtSize18 no-margin floatL"},h.createElement(ve.FormattedMessage,{id:i}),h.createElement(y.Components.Visible,{when:!r&&n&&l!==y.EFlowType.INTERNET&&!Object(y.ValueOf)(a,"confirmationNumber",!1)&&i!==y.Volt.EDIsplayGroupKey.PROMOTION},h.createElement("span",{className:"flexRow flexCenter floatR pad-2-top"},h.createElement("a",{id:"offering_edit_"+i,className:"txtBlue txtSize14 margin-20-left special-underline",href:"#",onClick:function(e){e.preventDefault(),y.Omniture.useOmniture().trackAction({id:"editClick",s_oAPT:{actionId:647},s_oBTN:"Edit - "+i}),c(y.Actions.broadcastUpdate(y.Actions.historyGo(i))),c(y.Actions.broadcastUpdate(y.Actions.closeLightbox(y.EModals.PREVIEWMODAL)))}},h.createElement(ve.FormattedMessage,{id:"Edit"})," ",h.createElement("span",{className:"virgin-icon icon-edit txtBlue txtSize14","aria-hidden":"true"})))))),h.createElement("div",{className:"flexCol"},h.createElement("div",{className:"list-unstyled no-margin",role:"list"},o?h.createElement(h.Fragment,null,h.createElement("p",{"aria-hidden":"true"},h.createElement(ve.FormattedMessage,{id:"ALACARTE_DESC",values:{value:t.length}})),t.map(function(e){return h.createElement("div",{className:"flexRow alaCarteList flexCenter",role:"listitem"},h.createElement("p",{className:"noMargin flexRow flexGrow flexCenter"},h.createElement(De,{offer:e,isCurrent:r,supressFlag:i===y.Volt.EDIsplayGroupKey.PROMOTION})),h.createElement("p",{className:"txtSize14 noMargin txtBold txtRight floatL-xs"},h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"regularPrice.price")||0}),e.regularPrice&&"Recurring"===e.regularPrice.priceType&&h.createElement(ve.FormattedMessage,{id:"PER_MO"})))})):t.map(function(e){return h.createElement("div",{className:"flexRow flexJustifySpace"},h.createElement("p",{className:"noMargin"},h.createElement(De,{offer:e,isCurrent:r,supressFlag:i===y.Volt.EDIsplayGroupKey.PROMOTION})),h.createElement("p",{className:"txtSize14 noMargin txtBold txtRight floatL-xs"},h.createElement("p",{className:"noMargin txtBold txtSize14"},h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"regularPrice.price")||0}),e.regularPrice&&"Recurring"===e.regularPrice.priceType&&h.createElement(ve.FormattedMessage,{id:"PER_MO"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.description",!1)},h.createElement("p",{className:"noMargin txtBold txtSize14 txtNoWrap"},h.createElement("span",{className:"spacer15 d-none d-sm-block","aria-hidden":"true"}),h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"promotionDetails.discountPrice.price")||0}),"Recurring"===Object(y.ValueOf)(e,"promotionDetails.discountPrice.priceType","")&&h.createElement(ve.FormattedMessage,{id:"PER_MO"})))))}))),h.createElement("div",{className:"spacer25","aria-hidden":"true"}),h.createElement("div",{className:"spacer1 bgGrayLight6 margin-25left-xs margin-25right-xs","aria-hidden":"true"}),h.createElement("span",{className:"spacer25 visible-lg","aria-hidden":"true"}),h.createElement("span",{className:"spacer15 d-block d-sm-none","aria-hidden":"true"}))};Le.displayName="ChargeItem";var Ve=h.memo(function(e){var t=e.productOfferings;return h.createElement(h.Fragment,null,t.map(function(e,t){return h.createElement(h.Fragment,null,h.createElement("div",{className:"flexCol",key:e.id},h.createElement("div",{className:"flexRow flex-justify-space-between"},h.createElement("p",{className:"txtBlack txtSize18 no-margin floatL"},h.createElement(ve.FormattedMessage,{id:""+e.displayGroupKey}))),h.createElement("div",{className:"flexRow flex-justify-space-between block-xs"},h.createElement("p",{className:"noMargin"},e.name||e.id,h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Remove||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Removed||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Delete},h.createElement(je,{message:"removed"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"displayGroupKey")===y.Volt.EDIsplayGroupKey.PROMOTION&&Object(y.ValueOf)(e,"promotionDetails.expiryDate",!1)},h.createElement("br",null),h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(e,"promotionDetails.expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return h.createElement(ve.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))),h.createElement("p",{className:"noMargin txtBold txtSize14"},h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"regularPrice.price")||0}),e.regularPrice&&"Recurring"===e.regularPrice.priceType&&h.createElement(ve.FormattedMessage,{id:"PER_MO"}))),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.description",!1)},h.createElement("div",{className:"flexRow flex-justify-space-between block-xs"},h.createElement("p",{className:"noMargin"},h.createElement("span",{className:"spacer15 d-none d-sm-block","aria-hidden":"true"}),Object(y.ValueOf)(e,"promotionDetails.description"),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.Remove||Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.Removed||Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.Delete},h.createElement(je,{message:"removed"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.expiryDate",!1)},h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(e,"promotionDetails.expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return h.createElement(ve.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))),h.createElement("p",{className:"noMargin txtBold txtSize14"},h.createElement("span",{className:"spacer15 d-none d-sm-block","aria-hidden":"true"}),h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"promotionDetails.discountPrice.price")||0}),"Recurring"===Object(y.ValueOf)(e,"promotionDetails.discountPrice.priceType","")&&h.createElement(ve.FormattedMessage,{id:"PER_MO"}))))),h.createElement(Me,null))}))}),Fe=h.memo(function(e){var t=e.productOfferings,n=h.useContext(y.WidgetContext).mode,a=w(n,y.EReviewMode.Review),r=t&&f.sortAndGroupByOfferingType(t);return h.createElement(h.Fragment,null,r&&Object.keys(r).map(function(e){return h.createElement(Le,{isCurrent:!0,offerings:r[e],isReview:a})}))}),Be=h.memo(function(e){var t=e.additionalCharge;return t.price?h.createElement(h.Fragment,null,h.createElement("div",{className:"pad-30-left pad-40-right padding-25-xs rate-plan-total txtBlack"},h.createElement("div",{className:"flexBlock flex-justify-space-between block-xs"},h.createElement("div",null,h.createElement("span",{className:"block txtSize14"},h.createElement(ve.FormattedMessage,{id:"Additional charges"}))),h.createElement("div",null,h.createElement("span",{className:"txtCurrency item-price bellSlimSemibold txtBlack txtSize14 txtBold no-margin-bottom"},h.createElement(ve.FormattedNumber,{value:t.price,format:"CAD"}),"Recurring"===t.priceType&&h.createElement(ve.FormattedMessage,{id:"PER_MO"}))))),h.createElement("span",{className:"spacer25 d-none d-md-block d-lg-block d-xl-block","aria-hidden":"true"}),h.createElement("span",{className:"spacer15 d-block d-sm-none","aria-hidden":"true"})):null}),We=y.Utils.getFlowType(),Ge=h.memo(function(e){var t=e.totalCharge,n=e.isNew;return e.isTV,h.createElement("div",{className:"flexBlock border-radius-bottom flex-justify-space-between pad-30-left pad-40-right pad-10-top pad-10-bottom padding-25-xs rate-plan-total borderGrayLight6-top "+(n?"bgOrange txtWhite":"bgGrey1 txtBlack totalLeft")},h.createElement("div",null,h.createElement("div",{className:"spacer15","aria-hidden":"true"}),h.createElement("span",{className:"block txtSize18"},We===y.EFlowType.BUNDLE&&h.createElement(ve.FormattedMessage,{id:n?"New Total":"Total"}),We===y.EFlowType.ADDTV&&h.createElement(ve.FormattedMessage,{id:"ADDTV_TOTAL"}),We===y.EFlowType.TV&&h.createElement(ve.FormattedMessage,{id:n?"NEW_TV_TOTAL":"CURRENT_TV_TOTAL"}),We===y.EFlowType.INTERNET&&h.createElement(ve.FormattedMessage,{id:n?"NEW_INT_TOTAL":"CURRENT_INT_TOTAL"}))),h.createElement("div",{className:"floatR"},h.createElement("div",{className:"spacer5","aria-hidden":"true"}),h.createElement("span",{className:"txtCurrency item-price pad-5-left bellSlimSemibold "+(n?"txtWhite":"txtBlack")+" txtSize40 no-margin-bottom virginUltraReg"},h.createElement(y.Components.Currency,{value:t.price||0,monthly:"Recurring"===t.priceType}))))}),Ue=function(e){e.isConfirmationStep;var t=Object(v.useSelector)(f.totalCharge("currentTotal")()),n=Object(v.useSelector)(f.additionalCharge("Current")()),a=Object(v.useSelector)(f.productOfferings("Current","TV")())||[],r=Object(v.useSelector)(f.productOfferings("Current","Internet")())||[],i=a.length>0,o=r.length>0,c=R({renderTV:i,renderInternet:o});return o||i?h.createElement("div",{className:"review-div-section col-lg-6"},h.createElement("div",{className:"flexCol fullWidth"},h.createElement("div",{className:"review-div-section"},h.createElement("div",{className:"review-panel-body  border-radius-top fullHeight pad-30-left pad-40-right padding-25-xs accss-focus-outline-override-white-bg"},h.createElement("div",null,h.createElement("div",{className:"spacer30 d-none d-sm-block","aria-hidden":"true"}),h.createElement("div",{className:"spacer25 d-block d-sm-none","aria-hidden":"true"}),h.createElement("h2",{className:"txtBlack txtSize22 noMargin differentTextureforHandset virginUltraReg txtUppercase"},h.createElement(ve.FormattedMessage,{id:"CURRENT_"+c})),h.createElement(Me,null)),o&&h.createElement(Ve,{productOfferings:r}),i&&h.createElement(Fe,{productOfferings:a}))),h.createElement(Be,b({},{additionalCharge:n})),h.createElement(Ge,b({},{totalCharge:t,isNew:!1,isTV:i})))):null},ze=h.memo(function(e){var t=e.productOfferings,n=h.useContext(y.WidgetContext).mode,a=Object(v.useSelector)(function(e){return e.confirmation}),r=Object(v.useDispatch)(),i=w(n,y.EReviewMode.Review,y.EReviewMode.Summary),o=h.useMemo(function(){return y.Utils.getFlowType()},[]);return h.createElement(h.Fragment,null,t.map(function(e,t){return h.createElement(h.Fragment,null,h.createElement("div",{className:"flexCol",key:e.id},h.createElement("div",{className:"flexRow flex-justify-space-between"},h.createElement("p",{className:"txtBlack txtSize18 no-margin floatL",id:e.displayGroupKey+"_"+t},h.createElement(ve.FormattedMessage,{id:""+e.displayGroupKey}),h.createElement(y.Components.Visible,{when:i&&o!==y.EFlowType.TV&&o!==y.EFlowType.ADDTV&&!Object(y.ValueOf)(a,"confirmationNumber",!1)&&Object(y.ValueOf)(e,"displayGroupKey")!==y.Volt.EDIsplayGroupKey.PROMOTION},h.createElement("span",{className:"flexRow flexCenter floatR accss-focus-outline-override"},h.createElement("button",{id:"EDIT_NEWPLAN_"+e.id,role:"link","aria-describedby":e.displayGroupKey+"_"+t,className:"txtBlue txtSize14 margin-20-left noBorder bgTransparent pointer accss-text-blue-on-bg-white",onClick:function(){y.Omniture.useOmniture().trackAction({id:"editClick",s_oAPT:{actionId:647},s_oBTN:"Edit "+e.displayGroupKey}),w(n,y.EReviewMode.Summary)&&r(y.Actions.closeLightbox(y.EModals.PREVIEWMODAL)),r(y.Actions.broadcastUpdate(y.Actions.historyGo(y.EWidgetRoute.INTERNET)))}},h.createElement(ve.FormattedMessage,{id:"Edit"})," ",h.createElement("span",{className:"virgin-icon icon-edit txtBlue txtSize14 margin-5-left accss-text-blue-on-bg-white","aria-hidden":"true"})))))),h.createElement("div",{className:"flexRow flex-justify-space-between block-xs"},h.createElement("p",{className:"noMargin"},e.name||e.id,h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Add||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Added||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.Create||Object(y.ValueOf)(e,"state")===y.Volt.EOfferingState.NewlySelected},h.createElement(je,{message:"new"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"displayGroupKey")===y.Volt.EDIsplayGroupKey.PROMOTION&&Object(y.ValueOf)(e,"promotionDetails.expiryDate",!1)},h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(e,"promotionDetails.expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return h.createElement(ve.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})})))),h.createElement("p",{className:"noMargin txtBold txtSize14"},h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"regularPrice.price")||0}),e.regularPrice&&"Recurring"===e.regularPrice.priceType&&h.createElement(ve.FormattedMessage,{id:"PER_MO"}))),Boolean(Object(y.ValueOf)(e,"promotionDetails.description"))&&h.createElement("div",{className:"flexRow flex-justify-space-between block-xs"},h.createElement("p",{className:"noMargin"},h.createElement("span",{className:"spacer15 d-none d-sm-block","aria-hidden":"true"}),Object(y.ValueOf)(e,"promotionDetails.description"),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.Add||Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.Added||Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.Create||Object(y.ValueOf)(e,"promotionDetails.state")===y.Volt.EOfferingState.NewlySelected},h.createElement(je,{message:"new"})),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.expiryDate",!1)},h.createElement("br",null),h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedDate,{value:Object(y.ValueOf)(e,"promotionDetails.expiryDate",""),format:"yMMMMd",timeZone:"UTC"},function(e){return h.createElement(ve.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})}))),h.createElement(y.Components.Visible,{when:Object(y.ValueOf)(e,"promotionDetails.discountDuration",!1)},h.createElement("span",{className:"d-sm-block pad-5-left-xs"},h.createElement(ve.FormattedMessage,{id:"PromotionValid",values:{price:Math.abs(Object(y.ValueOf)(e,"promotionDetails.discountPrice.price",0)),discountDuration:Object(y.ValueOf)(e,"promotionDetails.discountDuration","")}})))),h.createElement("p",{className:"noMargin txtBold txtSize14"},h.createElement("span",{className:"spacer15 d-none d-sm-block","aria-hidden":"true"}),h.createElement(y.Components.BellCurrency,{value:Object(y.ValueOf)(e,"promotionDetails.discountPrice.price")||0}),"Recurring"===Object(y.ValueOf)(e,"promotionDetails.discountPrice.priceType","")&&h.createElement(ve.FormattedMessage,{id:"PER_MO"})))),h.createElement(Me,null))}))}),He=h.memo(function(e){var t=e.productOfferings,n=Object(v.useSelector)(function(e){return e.confirmation}),a=h.useContext(y.WidgetContext).mode,r=w(a,y.EReviewMode.Review,y.EReviewMode.Summary),i=t&&f.sortAndGroupByOfferingType(t);return h.createElement(h.Fragment,null,i&&Object.keys(i).map(function(e){return h.createElement(Le,{isCurrent:!1,offerings:i[e],isReview:r,confirmation:n})}))}),Ke=function(e){e.isConfirmationStep;var t=Object(v.useSelector)(f.totalCharge("newTotal")()),n=Object(v.useSelector)(f.additionalCharge("New")()),a=Object(v.useSelector)(f.productOfferings("New","TV")())||[],r=Object(v.useSelector)(f.productOfferings("New","Internet")())||[],i=a.length>0,o=r.length>0,c=R({renderTV:i,renderInternet:o});return o||i?h.createElement("div",{className:"review-div-section"},h.createElement("div",{className:"flexCol fullWidth"},h.createElement("div",{className:"review-div-section"},h.createElement("div",{className:"review-panel-body  border-radius-top fullHeight pad-30-left pad-40-right padding-25-xs accss-focus-outline-override-white-bg"},h.createElement("div",null,h.createElement("div",{className:"spacer30 d-none d-sm-block","aria-hidden":"true"}),h.createElement("div",{className:"spacer25 d-block d-sm-none","aria-hidden":"true"}),h.createElement("h2",{className:"txtBlack txtSize22 noMargin differentTextureforHandset virginUltraReg txtUppercase"},h.createElement(ve.FormattedMessage,{id:"NEW_"+c})),h.createElement(Me,null)),o&&h.createElement(ze,{productOfferings:r}),i&&h.createElement(He,{productOfferings:a}))),h.createElement(Be,b({},{additionalCharge:n})),h.createElement(Ge,b({},{totalCharge:t,isNew:!0,isTV:i})))):null},qe=function(e){var t=e.isConfirmationStep;return h.useEffect(function(){window.requestAnimationFrame(function(){var e=Array.from(document.querySelectorAll(".review-programming-container .review-panel-body")),t=e.map(m).reduce(function(e,t){return Math.max(t,e)},0);e.forEach(function(e){return e.style.height=t>0?t+"px":"auto"})})}),h.createElement("section",{className:"container-flex-box-wrap noPadding"},h.createElement("div",{className:"flexBlock review-programming-container bgWhite panel-border noBorder-xs fullWidth border-radius-top"},h.createElement(Ue,{isConfirmationStep:t}),h.createElement(Ke,{isConfirmationStep:t})))},Xe=y.Components.RestrictionModal,Ye=y.Actions.showHideLoader,$e=y.Actions.setWidgetStatus,Je=function(){return h.createElement("div",{className:"pad-30-top pad-20-bottom"},h.createElement("h2",{className:"txtSize28 txtBlack txtSize26-xs noMargin pad-0-xs virginUltraReg txtUppercase"},h.createElement(ve.FormattedMessage,{id:"Review and submit changes"})))},Ze=function(){var e=h.useContext(y.WidgetContext).mode,t=w(e,y.EReviewMode.Summary),n=Object(v.useSelector)(function(e){return e.confirmation},v.shallowEqual),a=Boolean(n.confirmationNumber),r=Object(v.useDispatch)(),i=w(e,y.EReviewMode.Confirmation);return h.useEffect(function(){if(y.Utils.getPageRoute()!==y.EWidgetRoute.CONFIRMATION?r(t?P():k()):(r($e(y.EWidgetStatus.RENDERED)),r(y.Actions.omniPageLoaded()),r(Ye(null))),t){var e=document.querySelector("#"+y.EModals.PREVIEWMODAL+" .modal-dialog");e&&(e.style.width="962px")}},[]),h.createElement("div",{id:"modalContent",className:"container liquid-container fullWidthContainer-xs accss-overflow-unset"},h.createElement("div",{className:t?"":"review-page"},h.createElement("div",{className:"spacer20","aria-hidden":"true"}),a&&h.createElement(Te,null),!a&&w(e,y.EReviewMode.Review)&&h.createElement(ke,null),h.createElement("div",{className:"review-tier-block"},!a&&w(e,y.EReviewMode.Review)&&h.createElement(Je,null),h.createElement(qe,{isConfirmationStep:i}),!t&&h.createElement(Ne,null),!a&&w(e,y.EReviewMode.Review)&&h.createElement(h.Fragment,null,h.createElement(Re,null),h.createElement(Ce,null))),h.createElement(Xe,{id:"REVIEW_RESTRICTION_MODAL"})))},Qe=y.Components.ApplicationRoot,et=function(){return h.createElement(Qe,null,h.createElement(Ze,null))},tt=y.Actions.setWidgetProps,nt=y.Actions.setWidgetStatus,at=v.Provider,rt=function(e){function t(t,n,a,r){var i=e.call(this)||this;return i.store=t,i.params=n,i.config=a,i.pipe=r,i}return a(t,e),t.prototype.init=function(){this.pipe.subscribe(Oe.Subscriptions(this.store)),this.store.dispatch(tt(this.config)),this.store.dispatch(tt(this.params.props)),this.store.dispatch(nt(y.EWidgetStatus.RENDERED))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;O.render(h.createElement(y.ContextProvider,{value:{config:this.config,mode:this.params.props.mode}},h.createElement(at,b({},{store:t}),h.createElement(y.Components.PersistGate,{render:function(){return h.createElement(et,null)},store:t}))),e)},r([Object(N.Widget)({namespace:"Ordering"}),i("design:paramtypes",[he,N.ParamsProvider,K,Oe])],t)}(N.ViewWidget);t.default=rt},function(e,t){e.exports=c},function(e,t){e.exports=l},function(e,t,n){"use strict";var a=n(11);n.d(t,"PersistConfig",function(){return a.a});var r=n(13),i=(n.n(r),n(14)),o=(n.n(i),n(15));n.n(o)},function(e,t,n){"use strict";n.d(t,"a",function(){return r});var a=n(0),r=(n.n(a),a.Utils.persistConfig(n(12).name))},function(e,t){e.exports={name:"omf-changepackage-review",version:"0.1.0",description:"Virgin ordering flow review and confirmation widget",main:"dist/widget.js",private:!0,scripts:{dev:"webpack --env.dev -w",build:"webpack","build:dev":"webpack --env.dev","build:prod":"webpack --env.prod",clean:"rm -rf ./node_modules & rm -rf ./dist & rm -rf ./package-lock.json & rm -rf ./yarn.lock",lint:"tslint -p src/tsconfig.json -c tslint.json -t stylish --fix && tsc -p src --pretty --noEmit",test:"react-scripts test"},keywords:[],author:"IBM",license:"MIT",devDependencies:{"@types/react-redux":"^7.1.5",reselect:"^4.0.0",bwtk:"git+https://tfs.prod.dcx.int.bell.ca/tfs/bellca/UXP/_git/bwtk","omf-changepackage-components":"file:../omf-changepackage-components","@babel/preset-env":"^7.7.7","@babel/preset-react":"^7.7.4","@testing-library/jest-dom":"^4.2.4","@testing-library/react":"^9.4.0","@types/jest":"^24.0.17","@types/node":"12.7.1","@types/react":"16.8.25","@types/react-dom":"16.8.5","@types/react-router-dom":"^5.1.3","@types/redux":"^3.6.0","@types/redux-mock-store":"^1.0.1","@types/redux-persist":"^4.3.1","@types/reflect-metadata":"^0.1.0","babel-jest":"^24.9.0","babel-preset-env":"^1.7.0","babel-preset-react":"^6.24.1","jest-config":"^24.9.0",react:"^16.8.6","react-dom":"^16.8.6","react-redux":"^7.1.0","react-router-dom":"^5.1.2","react-scripts":"3.0.1",redux:"^4.0.4","redux-mock-store":"^1.5.4","redux-persist":"^6.0.0","reflect-metadata":"^0.1.13","ts-jest":"^24.2.0",typescript:"^3.5.3",husky:"4.3.8"},peerDependencies:{bwtk:"*",react:"*","react-dom":"*","react-intl":"*","react-redux":"*",rxjs:"*",redux:"*","redux-actions":"*","redux-observable":"*","prop-types":"*",scarlet:"*","omf-changepackage-components":"*"},husky:{hooks:{"pre-commit":"node pre-commit.js"}}}},function(e,t){},function(e,t){},function(e,t){},function(e,t){e.exports=s}])},e.exports=a(n(1),n(2),n(3),n(5),n(0),n(6),n(4),n(8),n(7),n(11))}])});
//# sourceMappingURL=myaccount-omf-changepackage-bundle.min.js.map