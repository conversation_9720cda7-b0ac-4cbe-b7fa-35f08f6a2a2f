@import "mixins";
.bell-tv-navigator-tray {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    background-color: rgba(0, 0, 0, 0);
    transition: background-color 150ms ease-out;
    pointer-events: none;
    display: none;
    @media #{$media-tab-mobile} {
        display: block;
    }
    &.active {
        background-color: rgba(0, 0, 0, 0.5);
        pointer-events: all;
        >.bell-tv-navigator-tray-container {
            left: 0;
        }
    }
    >.bell-tv-navigator-tray-container {
        position: relative;
        left: -100%;
        width: calc(100% - 20px);
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        transition: left 250ms ease-in;
        @media #{$media-tablet} {
            width: calc(100% - 40px);
        }
    }
}