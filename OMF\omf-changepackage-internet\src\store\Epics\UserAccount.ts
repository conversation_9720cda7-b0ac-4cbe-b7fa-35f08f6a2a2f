import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { EWidgetStatus, AjaxResponse } from "omf-changepackage-components";
import { filter, mergeMap, catchError , of } from 'rxjs';

import { Client } from "../../Client";
import {
  IStoreState, IServiceAccountAPI,
} from "../../models";
import {
  getAccountDetails,
  setAccountDetails,
  getInternetCatalog,
} from "../Actions";
import { Config } from "../../Config";
import { Action } from "redux-actions";

@Injectable
export class UserAccountEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.requestDataEpic,
    );
  }

  private get requestDataEpic(): Epic<Action<any>, Action<any>, any, IStoreState> {
    return (action$, state$) =>
      action$.pipe(
        ofType(getAccountDetails.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => this.client.get<AjaxResponse<IServiceAccountAPI>>(this.config.api.serviceAccountAPI).pipe(
          mergeMap(({ data }: { data: IServiceAccountAPI }) => of(
            setAccountDetails(data),
            getInternetCatalog()
          )),
          catchError((error: Response) => of(setAccountDetails({} as IServiceAccountAPI)))
        ))
      );
  }
}
