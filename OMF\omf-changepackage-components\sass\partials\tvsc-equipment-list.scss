@import "mixins";
.bell-equipment-receiver-list {
    .bell-equipment-receiver {
        .bell-equipment-receiver-image,
        .bell-equipment-receiver-description,
        .bell-equipment-receiver-details,
        .bell-equipment-receiver-actions {
            max-width: 280px;
            margin: 0 16px
        }
        .bell-equipment-receiver-image {
            min-height: 200px;
            // Limit the image height to
            // prevent it from outgrowing
            // the frame
            max-height: 200px;
            position: relative;
            // Center the image in the container
            align-items: center;
            display: flex;
            >img {max-width: 180px;max-height: 200px}
            @media #{$media-mobile} {
                min-height: unset;
                text-align: center;
                >img {
                    top: unset;
                    left: unset;
                    position: static;
                    transform: none;
                    -webkit-transform: none;
                    -ms-transform: none;
                    -moz-transform: none;
                }
            }
        }
        .bell-equipment-receiver-description {
            >span {
            font-weight: bold;
            color: #fff;
            padding: 4px;
            background-color: #003778;
            font-size: 14px;
            text-align: center;
            line-height: 1;
            display: inline-block;
            }
        }
        .bell-equipment-receiver-details {
            .bell-equipment-receiver-details-row {
                margin-bottom: 10px;
            }
        }
    }
}