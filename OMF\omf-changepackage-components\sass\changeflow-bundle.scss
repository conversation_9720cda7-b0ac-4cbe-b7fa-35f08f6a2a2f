.brf {
    letter-spacing: -0.5px; // To match with mockups
    // @import "lib/virgin";
    @import "changeflow/custom";
    // @import "lib/allBrowsers_framework";
    // @import "lib/bootstrap.min";
    @import "partials/tvsc-base";
    @import "partials/tvsc-posters";
    @import "partials/tvsc-slider";
    @import "changeflow/navigator-tray";
    @import "changeflow/slider";
    @import "changeflow/base-pack";
    @import "changeflow/package";
    @import "changeflow/channel";
    @import "changeflow/details";
    @import "changeflow/expandable-tray";
    @import "changeflow/filters";
    @import "changeflow/menu";
    @import "changeflow/panel";
    @import "changeflow/movie-pack";
    @import "changeflow/individual-channels";
    @import "changeflow/additional-channels";
    @import "changeflow/qcp-favorites";
    @import "changeflow/progress-steps";
    @import "changeflow/progress-steps-genesis";
    @import "changeflow/banner-tabs";
    @import "changeflow/review-programming";
    @import "changeflow/mobile-layouts";
    @import "changeflow/search";
    @import "changeflow/channel-details-lightbox";
    @import "changeflow/widget-loading";
    @import "partials/installation";
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth
}

body,
#pageContainer {
    background-color: #E5E5E5;
}

// Lightboxes
@import "partials/brf-lightbox";
// Change flow loading indicator must
// block user interactions
.loader-responsive-overlay:not(.hidden) {
    top: 0;
    width: 100%;
    height: 100%;
    position: fixed;
    pointer-events: all;
    cursor: default;
    z-index: 9999;
}

// Modals must be below the loader
.modal {
    z-index: 1150 !important;
}

// Remove chat!!!
#inqC2CImgContainer_AnchoredV,
#inqC2CImgContainer_AnchoredV2 {
    display: none!important;
}

.brf  .btn-default .modifychannel:hover .brf .btn-default .modifychannel:focus {
        color: #FFF;
        border-color: #000;
        background-color: #000;
    
    }


// Move CSS from TV Widget -> Navigation Component
.brf {
    .bell-tv-navigator {
        position: sticky;
        top: 15px;

        @media screen and (max-width: 767px) {
            height: 100%;
            overflow-y: auto;
        }
    }
}