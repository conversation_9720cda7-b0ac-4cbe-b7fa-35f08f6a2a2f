import { Actions, Components, Omniture, FormattedHTMLMessage } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";
import { Localization } from "../../Localization";
import { IStoreState } from "../../models";

const {
  Modal
} = Components;

interface IComponentProps {
}

interface IComponentDispatches {
  onContinueClick: () => void;
  closeLightbox: () => void;
}

export const ModalId: string = "APPLICATION_RESET";

const Component: React.FC<IComponentProps & IComponentDispatches> = ({
  onContinueClick,
  closeLightbox
}) => <Modal
  modalId={ModalId}
  onShown={() => {
    Omniture.useOmniture().trackFragment({
      id: "exitLightbox",
      s_oAPT: {
        actionId: 104
      },
      s_oPRM: Localization.getLocalizedString("APPLICATION_RESET_TITLE"),
      s_oLBC: Localization.getLocalizedString("APPLICATION_RESET_TEXT")
    });
  }}
  title={<FormattedMessage id="APPLICATION_RESET_TITLE" />}>
  <div className="pad-30">
    <FormattedHTMLMessage id="APPLICATION_RESET_TEXT" />
  </div>
  <div className="spacer1 bgGrayLight6" aria-hidden="true"></div>
  <div className="bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg">
    <button id="APP_RESET_CONTINUE" className="btn btn-primary fill-xs" onClick={onContinueClick}><FormattedMessage id="APPLICATION_RESET_CONTINUE" /></button>
    <div className="vSpacer15" aria-hidden="true"></div>
    <button id="APP_RESET_CLOSE" className="btn btn-default fill-xs" onClick={closeLightbox}><FormattedMessage id="APPLICATION_RESET_CLOSE" /></button>
  </div>
</Modal>;

export const ApplicationResetLightbox = connect<IComponentProps, IComponentDispatches>(
  ({ }: IStoreState) => ({}),
  (dispatch) => ({
    onContinueClick: () => dispatch(Actions.applicationReset()),
    closeLightbox: () => dispatch(Actions.closeLightbox(ModalId)),
  })
)(Component);
