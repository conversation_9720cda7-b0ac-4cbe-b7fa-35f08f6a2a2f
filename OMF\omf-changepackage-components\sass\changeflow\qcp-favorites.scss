/*
* QCP Favorites banner combo style override
* in QCP Favorites the combo banner
* must look saelectable even when it's disabled
*/
.bell-banner-Favorite {
    .bell-tv-package.disabled {
        background-color: #fff;
        border: 1px solid #d4d4d4;
        .graphical_ctrl input:disabled~.ctrl_element {
            background: #fff;
            opacity: 1;
            border: 1px solid #ccc;
            pointer-events: all;
        }
    }
}