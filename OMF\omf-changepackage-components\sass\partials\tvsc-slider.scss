@import "mixins";
.bell-slider {
    display: block;
    position: relative;
    .bell-slider-arrow {
        width: 78px;
        height: 100%;
        top: 0;
        text-align: center;
        font-size: 26px;
        color: #00549a;
        background: #fff;
        border: none;
        display: block;
        position: absolute;
        z-index: 2;
        &:after {
            content: "\e012";
            display: inline-block;
            font-family: 'bell-icon';
            position: relative;
            top: 42%;
        }
        &.bell-slider-arrow-left {
            left: -1px;
        }
        &.bell-slider-arrow-right {
            right: -1px;
        }
        &.bell-slider-box-shadow::before {
            box-shadow: -30px 0 20px -15px rgba(0, 0, 0, 0.6);
            content: " ";
            height: 100%;
            left: 0px;
            position: absolute;
            top: 0;
            width: 50px;
        }
    }
    .bell-slider-holder {
        width: 100%;
        display: table;
        overflow: hidden;
        table-layout: fixed;
        border-collapse: collapse;
        .bell-slider-image-wrapper {
            position: relative;
            white-space: nowrap;
        }
    }
    /* Arrows */
    /* Dots */
    &.bell-posters .bell-poster-image {
        .bell-movie-poster,
        .bell-poster-detail {
            max-width: 100%;
        }
    }
    &.slick-loading .slick-list {
        // background: #fff url("./ajax-loader.gif") center center no-repeat;
    }
    .slick-list {
        position: relative;
        display: block;
        overflow: hidden;
        margin: 0;
        padding: 0;
        .slick-track {
          //Align all item to the top to prevent
          //iOS from missaligning everything
            display: flex;
            flex-direction: row;
            align-items: start;
        }
    }
    .slick-prev,
    .slick-next {
        font-size: 0;
        line-height: 0;
        position: absolute;
        top: 50%;
        display: block;
        width: 40px;
        height: 80px;
        padding: 0;
        -webkit-transform: translate(0, -50%);
        -ms-transform: translate(0, -50%);
        transform: translate(0, -50%);
        cursor: pointer;
        color: transparent;
        border: none;
        outline: none;
        background: transparent;
        overflow: hidden;
        z-index: 2;
    }
    .slick-prev {
        &:hover,
        &:focus {
            color: transparent;
            outline: none;
            background: transparent;
        }
    }
    .slick-next {
        &:hover,
        &:focus {
            color: transparent;
            outline: none;
            background: transparent;
        }
    }
    .slick-prev {
        &:hover:before,
        &:focus:before {
            border-color: #00549a;
        }
    }
    .slick-next {
        &:hover:before,
        &:focus:before {
            border-color: #00549a;
        }
    }
    .slick-prev:before,
    .slick-next:before {
        font-size: 20px;
        line-height: 1;
        color: #00549a;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: #fff;
        width: 75px;
        height: 75px;
        border: 5px solid #e1e1e1;
        border-radius: 50%;
        display: block;
        padding: 22px 10px;
    }
    .slick-prev.slick-disabled:before,
    .slick-next.slick-disabled:before {
        opacity: 0;
        display: none;
    }
    .slick-prev {
        left: -30px;
        &:before {
            font-family: "bell-icon2";
            content: "\ea04";
            margin-left: -35px;
            text-align: right;
        }
    }
    [dir='rtl'] .slick-prev {
        right: -30px;
        left: auto;
        &:before {
            font-family: "bell-icon";
            content: "\e012";
            margin-left: 0;
            text-align: left;
        }
    }
    .slick-next {
        right: -30px;
        &:before {
            font-family: "bell-icon";
            content: "\e012";
            margin-left: 0;
            text-align: left;
        }
    }
    [dir='rtl'] .slick-next {
        right: auto;
        left: -30px;
        &:before {
            font-family: "bell-icon2";
            content: "\ea04";
            margin-left: -35px;
            text-align: right;
        }
    }
    .slick-dots {
        display: block;
        width: 100%;
        padding: 0;
        margin: 0;
        list-style: none;
        text-align: center;
        li {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            margin: 0 5px;
            padding: 0;
            cursor: pointer;
            button {
                font-size: 0;
                line-height: 0;
                display: block;
                width: 10px;
                height: 10px;
                padding: 5px;
                cursor: pointer;
                color: transparent;
                border: 0;
                outline: none;
                background: transparent;
                border: 1px solid #999;
                border-radius: 50%;
                &:hover,
                &:focus {
                    outline: none;
                    border: 1px solid #00549a;
		}
            }
            &.slick-active button {
                background: #999;
            }
        }
    }
    .bell-slider-wrapper {
        display: inline-block; // transform: translateX(50%);
    }
}

@media screen and (max-width: 999px) and (min-width: 768px) {
    .bell-slider .bell-slider-arrow {
        width: 60px;
    }
}

@media screen and (max-width: 767px) {
    .bell-slider .bell-slider-arrow {
        width: 45px;
    }
}

@media screen and (max-width: 767px) {
    .bell-slider .slick-prev {
        left: -15px;
    }
}

@media screen and (max-width: 767px) {
    .bell-slider [dir='rtl'] .slick-prev {
        right: -15px;
    }
}

@media screen and (max-width: 767px) {
    .bell-slider .slick-next {
        right: -15px;
    }
}

@media screen and (max-width: 767px) {
    .bell-slider [dir='rtl'] .slick-next {
        left: -15px;
    }
}