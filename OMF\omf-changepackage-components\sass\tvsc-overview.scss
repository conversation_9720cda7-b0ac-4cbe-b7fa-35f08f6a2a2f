@import "includes/mixins";
.bell-tv-overview-page {
    @import "partials/tvsc-posters";
    @import "partials/tvsc-slider";
    @import "partials/tvsc-equipment-list";
    @import "partials/tvsc-service-tiles";
    @import "partials/tvsc-support-links";
    overflow: -webkit-paged-x;
    .section-my-services {
        >.grid {
            min-height: 380px;
        }
        .account-details-container {
            >div:nth-child(1) {
                min-height: 130px;
                @media #{$media-tab-mobile} {
                    min-height: unset;
                }
            }
            >div:nth-child(2) {
                @media #{$media-tablet} {
                    text-align: right;
                }
            }
            table.account-details {
                padding: 0;
                margin: 0;
                width: 100%;
                td,
                th {
                    padding: 4px 0;
                    vertical-align: top
                }
                th {
                    width: 130px;
                    padding-right: 15px;
                }
                tr:first-child {
                    td,
                    th {
                        padding-top: 0
                    }
                }
                tr:last-child {
                    td,
                    th {
                        padding-bottom: 0
                    }
                }
                @media #{$media-mobile} {
                    tbody,
                    tr,
                    td,
                    th {
                        display: block;
                        padding: 0;
                        width: 100%;
                    }
                    td {
                        padding-bottom: 4px
                    }
                    tr:last-child {
                        td {
                            padding-bottom: 0
                        }
                    }
                }
            }
        }
        .bell-tv-popular-content {
            .bell-slider {
                opacity: 0;
                display: none;
                &.slick-initialized {
                    opacity: 1;
                    display: block;
                }
            }
            .bell-slider.bell-posters:not(.slick-initialized) {
                .GoogleActiveViewClass {
                    display: none
                }
            }
            .bell-slider.bell-posters img {
                max-width: 100%; // max-height: 220px;
                width: 100%;
                height: auto;
                // @media #{$media-tablet} {
                //     width: 390px;
                // }
                // @media #{$media-mobile} {
                //     width: 535px;
                // }
            }
        }
    }
    .section-my-equipment {
        >.grid {
            .col-md-4.col-xs-12>.grid>div:last-child {
                padding-bottom: 0
            }
        }
        .bell-equipment-receiver-list {
            .bell-slider {
                opacity: 0;
                &.slick-initialized {
                    opacity: 1
                }
            }
            .bell-equipment-receiver {
                padding: 15px;
                .bell-equipment-receiver-image {
                    @media #{$media-mobile} {
                        min-height: 220px;
                    }
                }
            }
        }
    }
    .section-support-links {
        .bell-support-link {
            .bell-support-link-description {
                a.icon-link {
                    position: relative;
                    padding-left: 25px;
                    display: inline-block;
                    .icon-align-left {
                        position: absolute;
                        top: -5px;
                        left: 0;
                        margin-left: 0;
                    }
                }
            }
        }
    }
    .section-ad-banner {
        .panel-body {
            padding: 0
        }
        img {
            max-width: 100%!important;
            height: auto;
        }
    }
    .section-tv-guide {
        .panel-body {
            @media #{$media-mobile} {
                padding: 25px 30px;
            }
        }
    }
    .loader-responsive-overlay {
        padding: 15px 20px;
    }
    ._ad_Blocked {
        .hide.content-fallback {
            display: block!important
        }
    }
}

.dialog-succes {
    padding: 10px;
    box-shadow: 0px 0px 5px grey;
    margin: 0 auto;
    border-style: solid;
    border-color: green;
    border-width: 0 0 3px 0;
    overflow: hidden;
    white-space: nowrap;
}

.dialog-container {
    position: fixed;
    z-index: 1;
    left: 50%;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    
}

.close-button {
    top: -10px;
    position: relative;
}
