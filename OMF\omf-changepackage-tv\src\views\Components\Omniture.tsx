import * as React from "react";
import { useDispatch } from "react-redux";
import { Actions } from "omf-changepackage-components";

interface IComponentConnectedProps {
  name: string;
  data?: any;
  children?: any;
}

export const OmniturePage: React.FC<IComponentConnectedProps> = ({
  name,
  data,
  children
}) => {
  const dispatch = useDispatch();
  // Fire omniture call for a page when view initializes
  React.useEffect(() => {
    dispatch(Actions.omniPageLoaded(name, data));
  }, []);
  return <>{children}</>;
};
