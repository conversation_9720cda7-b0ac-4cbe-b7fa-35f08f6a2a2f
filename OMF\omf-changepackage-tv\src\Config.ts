import { Injectable, CommonFeatures } from "bwtk";
import { Models } from "omf-changepackage-components";

const { BaseConfig, configProperty } = CommonFeatures;

interface IAppConfig extends Models.IBaseConfig {
  flowType: string;
  filters: {
    languages: Array<string>;
    genres: Array<string>;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
}

interface IAppAPI extends Models.IBaseWidgetAPI {
  catalogAPI: string;
  addCatalogAPI: string;
  bundleCatalogAPI: string;
  serviceAccountAPI: string;
}

/**
 * Widget configuration provider
 * Allows the external immutable
 * config setting
 * @export
 * @class Config
 * @extends {BaseConfig<IAppConfig>}
 */
@Injectable
export class Config extends BaseConfig<IAppConfig> {
  @configProperty({
    languages: ["English", "French"],
    genres: ["Family", "Movies", "News", "Sports"],
    sortBy: "name",
    sortOrder: "desc"
  }) filters: string;
  @configProperty("") flowType: string;
  @configProperty({}) environmentVariables: any;
  @configProperty({}) mockdata: any;
  @configProperty({}) headers: any;
  @configProperty({ base: "http://127.0.0.1:8881"}) api: IAppAPI;
}
