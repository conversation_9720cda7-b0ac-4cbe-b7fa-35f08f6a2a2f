import * as React from "react";
import { connect } from "react-redux";
import { Volt, ValueOf, Components, FormattedHTMLMessage } from "omf-changepackage-components";
import { FormattedMessage } from "react-intl";
import { IStoreState } from "../../models";

import Combo from "../Components/Combo";
import { Footer } from "../Components/Legal";
import { sortOfferings } from "../../utils/Characteristics";
import { OmniturePage } from "../Components/Omniture";

interface IComponentConnectedProps {
  packages: Array<Volt.IProductOffering>;
}

interface IComponentDispatches {
}

export const Component: React.FC<IComponentConnectedProps & IComponentDispatches> = ({
  packages
}) => <OmniturePage name="Movies and Series">
  <h2 className="virginUltraReg txtSize28 noMargin txtBlack2 text-uppercase txtSize26-xs">
    <FormattedMessage id="Movies and Series page" />
  </h2>
  <FormattedHTMLMessage id="Movies and Series page Description">
    {
      (__html: any) => <Components.Visible when={Boolean(__html)}>
        <div className="spacer5"></div>
        <p className="noMargintxtSize14">{__html}</p>
      </Components.Visible>
    }
  </FormattedHTMLMessage>
  <div className="spacer20" />
  <div className="section-bell-tv-packages accss-focus-outline-override-white-bg">
    {
      sortOfferings(packages).map(combo => <Combo key={combo.id} {...combo} />)
    }
  </div>
  <Footer pageName={Volt.EDIsplayGroupKey.MOVIE}
    label={`LEGAL_LABEL_${Volt.EDIsplayGroupKey.BASE_PROGRAMMING}`}
    content={`LEGAL_COPY_${Volt.EDIsplayGroupKey.MOVIE}`} />
</OmniturePage>;

export default connect<IComponentConnectedProps, IComponentDispatches>(
  ({ catalog }: IStoreState) => ({
    refresh: Math.random() * 1000,
    packages: ValueOf<Array<Volt.IProductOffering>>(catalog, "offerings." + Volt.EDIsplayGroupKey.MOVIE, [])
  })
)(Component);
