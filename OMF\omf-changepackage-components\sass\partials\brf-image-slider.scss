@import "mixins";
.image-slider {
    max-width: 100%;
    overflow: hidden;
    ul,
    li {
        margin: 0;
        padding: 0;
    }
    .image-slider-previous,
    .image-slider-next {
        top: 0;
        width: 48px;
        height: 100%;
        z-index: 2;
        opacity: 0;
        border: none;
        background: none;
        position: absolute;
        background-color: #fff;
        transition: opacity 150ms;
    }
    &.active-previous .image-slider-previous {opacity: 1}
    &.active-next .image-slider-next {opacity: 1}
    .image-slider-previous {
        left: 0;
        box-shadow: 10px 0 30px -10px black
    }
    .image-slider-next {
        right: 0;
        box-shadow: -10px 0 30px -10px black
    }
    .image-slider-container {
        width: 100%;
        display: table;
        overflow: hidden;
        table-layout: fixed;
        border-collapse: collapse;
        ul {
            list-style: none;
            transition: left 150ms;
            li {
                display: table-cell;
                padding: 15px;
                &:first-child {
                    padding-left: 0
                }
                &:last-child {
                    padding-right: 0
                }
                figure {
                    width: 124px;
                    img,
                    figcaption {
                        width: 100%;
                    }
                    img {}
                    figcaption {
                        padding: 10px;
                    }
                }
            }
        }
    }
    // Special fix for iOS overflow issue
    .slick-track {
        display: flex;
        align-content: flex-start;
    }
}