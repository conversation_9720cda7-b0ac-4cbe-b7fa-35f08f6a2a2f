import { CommonFeatures, Injectable } from "bwtk";
import { Models } from "omf-changepackage-components";

const { BaseConfig, configProperty } = CommonFeatures;

interface IAppConfig extends Models.IBaseConfig {
}

interface IAppAPI extends Models.IBaseWidgetAPI {
  orderSummaryAPI: string;
  internetOrderDetailsAPI: string;
  internetOrderSubmitAPI: string;
  tvOrderDetailsAPI: string;
  tvOrderSubmitAPI: string;
  bundleOrderDetailsAPI: string;
  bundleOrderSubmitAPI: string;
  tvAddDetailsAPI: string;
  tvAddSubmitAPI: string;
}

/**
 * Widget configuration provider
 * Allows the external immutable
 * config setting
 * @export
 * @class Config
 * @extends {BaseConfig<IAppConfig>}
 */
@Injectable
export class Config extends BaseConfig<IAppConfig> {
  @configProperty("") flowType: string;
  @configProperty({}) environmentVariables: any;
  @configProperty({}) mockdata: any;
  @configProperty({}) headers: any;
  @configProperty("/styles/pdf/") pdfDownloadPath: string;
  @configProperty({ base: "" }) api: IAppAPI;
}
