@import "mixins";
.bell-tv-search-result {
    .bell-tv-search-result-count {
        align-items: baseline;
    }
    .bell-tv-search-result-list {
        padding-bottom: 15px;
        .bell-tv-search-show-thumb {
            max-width: 128px;
        }
        .bell-tv-package-footer {
            &:before {
                left: 157px;
            }
            &:after {
                left: 157px;
            }
        }
        .bell-tv-channel-picker {
            .bell-tv-channel{
                height: 171px;
                @media #{$media-mobile} {
                    height: unset;
                }
            }
        }
        article.bell-tv-search-result-show-list {
            padding-bottom: 15px;
            &:last-of-type {
                padding-bottom: 0;
            }
        }
    }
}
.modal-search-result {
    .bell-posters {
        .bell-poster-image {
            padding: 7.5px;
            .bell-poster-detail {
                background: transparent;
                color: #111;
                padding: 5px 0 0 0;
                white-space: nowrap;
                overflow: hidden;
            }
        }
    }
    .txtCurrency {
        line-height: 1;
    }
    .bell-tv-movie-pack .bell-tv-package-body {
        padding: 15px;
    }
}
.tv-cp-search {
    .bell-tv-search-result {
        form > nav {
            margin-top: -58px;
            @media #{$media-mobile} {
                margin-top: -38px;
            }
        }
    }
}