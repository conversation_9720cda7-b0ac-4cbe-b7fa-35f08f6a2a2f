import { Injectable } from "bwtk";
import { Epic, combineEpics } from "redux-observable";
import { EWidgetStatus, Actions, Utils } from "omf-changepackage-components";
import { NavigationEpics } from "./Epics/Navigation";
import { setFlowType } from "./Actions";
import { filter, mergeMap } from "rxjs";
// import { OmnitureEpics } from "./Epics/Omniture";

const {
  setWidgetStatus,
  // refreshTotals
} = Actions;

// const { concat } = ActionsObservable;

@Injectable
export class Epics {
  constructor(
    public navigationEpics: NavigationEpics,
    // public omnitureEpic: OmnitureEpics
  ) {}

  combineEpics() {
    return combineEpics(
      this.onWidgetStatusEpic,
    );
  }

  private get onWidgetStatusEpic(): GeneralEpic {
    return (action$: any) =>
      action$.pipe(
        filter((action: any) => action.type === setWidgetStatus.toString()),
        filter(({ payload }: ReduxActions.Action<EWidgetStatus>) => payload === EWidgetStatus.INIT),
        mergeMap(() => [
          setFlowType(Utils.getFlowType()),
          setWidgetStatus(EWidgetStatus.RENDERED)
        ])
      );
  }

}

type GeneralEpic = Epic<ReduxActions.Action<any>, any, any>;
