import { Injectable } from "bwtk";
import { Epic, combineEpics , StateObservable } from "redux-observable";
import { EWidgetStatus, AjaxResponse } from "omf-changepackage-components";
import { filter, mergeMap, catchError , of, Observable } from "rxjs";


import { Client } from "../../Client";
import {
  IStoreState, IServiceAccountAPI,
} from "../../models";
import {
  getAccountDetails,
  setAccountDetails,
  getCatalog
} from "../Actions";
import { Config } from "../../Config";

@Injectable
export class UserAccountEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.requestDataEpic,
    );
  }

  private get requestDataEpic(): UserAccountEpic {
    return (action$: Observable<ReduxActions.Action<any>>, state$: StateObservable<IStoreState>) =>
      action$.pipe(
        filter((action: ReduxActions.Action<any>) => action.type === getAccountDetails.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => this.client.get<AjaxResponse<IServiceAccountAPI>>(this.config.api.serviceAccountAPI).pipe(
          mergeMap(({ data }: AjaxResponse<IServiceAccountAPI>) => [
            setAccountDetails(data),
            getCatalog()
          ]),
          catchError(() => of(getCatalog()))
        ))
      );
  }
}

type UserAccountEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, any>;
