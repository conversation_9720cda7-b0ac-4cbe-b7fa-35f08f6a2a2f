default:
  image: shared-docker-remote.artifactory.int.bell.ca/maven:3-eclipse-temurin-17
  tags:
    - shared-docker
    - shared-managed-runner-01-ug-prd

variables:
  VAULT_SERVER_URL: https://vault.netsec.int.bell.ca/
  VAULT_ROLE: jwt_read_uxp
  VAULT_NAMESPACE: uxp
  VAULT_AUTH_ROLE: jwt_read_uxp
  VAULT_AUTH_PATH: gitlab
  CX_BARCODE:  "BEA"
  SAST_SERVER: "https://cwypwa-368.bell.corp.bce.ca"
  CX_TEAM: "/CxServer/Bell/DCX/Bell.ca"
  SAST_USER: 'BELL\fidWEBDOQTools'
  CXFLOW_VENDOR: "Internal"
  CXFLOW_TITANIUM: BELLCA
  CXFLOW_JIRA_COMPONENT: "widgetDefect"
  CXFLOW_ENVIRONMENT_TYPE: Prod
  CXFLOW_JIRA_PROJECT_ID: "UXPGITREL"
  CXFLOW_JIRA_PARENT: "UXPGITREL"

stages:
- scanning

.source: &source
    project: 'smartcore-pnt/shared/devex/pipeline-templates'
    ref: 'latest'

include: 
  - <<: *source
    file:
      - 'ci-templates/.scan-checkmarx-template.yml'

get-artifact-path:
  stage: .pre
  script:
    - export ARTIFACTORY_PATH=$ARTIFACTORY_REPOSITORY/com/example/demo/$BUILD_VERSION
    - echo "ARTIFACTORY_PATH=$ARTIFACTORY_PATH" > artifactpath.env
    - echo "ARTIFACT_PATH=$ARTIFACTORY_PATH/demo-$BUILD_VERSION.war" >> artifactpath.env
  artifacts:
    reports:
      dotenv: artifactpath.env

checkmarx-check-sast:
#  if you are going to use Vault

  id_tokens:
    VAULT_ID_TOKEN:
      aud: https://gitlab.int.bell.ca
  secrets:
#    SAST_USER:
#      vault: sastUserPass/username@team-secrets
#      file: false
    SAST_PASSWORD:
      vault: uxp/38437/shared/CHECKMARX_PASSWORD@team-secrets
      file: false
# if using CxFlow and JIRA
    JIRA_USER:
      vault: uxp/38437/shared/JIRA_USER@team-secrets
      file: false
    JIRA_PASSWORD:
      vault: uxp/38437/shared/JIRA_PASSWORD@team-secrets
      file: false
###### Variables 
  extends: .scan-sast

checkmarx-check-sca:
#  if you are going to use Vault
  id_tokens:
    VAULT_ID_TOKEN:
      aud: https://gitlab.int.bell.ca
  secrets:
    SCA_USER:
      vault: uxp/38437/shared/SCA_USER@team-secrets
      file: false
    SCA_PASSWORD:
      vault: uxp/38437/shared/SCA_PASSWORD@team-secrets
      file: false
#    ARTIFACTORY_USERNAME:
#      vault: artUserPass/username@team-secrets
#      file: false
#    ARTIFACTORY_PASSWORD:
#      vault: artUserPass/password@team-secrets
#      file: false
# if using CxFlow and JIRA
    JIRA_USER:
      vault: uxp/38437/shared/JIRA_USER@team-secrets
      file: false
    JIRA_PASSWORD:
      vault: uxp/38437/shared/CHECKMARX_TOKEN@team-secrets
      file: false
#############################
  extends: .scan-sca
  before_script:
      # specify maven version. Default 3.6.3 is baked into the image.  Available versions are 
      # 3.9.4
      # 3.8.8     
      # example
      - export "PATH=$MAVEN_BASE_DIR/apache-maven-3.9.4/bin:$PATH"
      ##############
      # specify gradle version. Default 4.4.1 is baked into the image.  Available versions are 
      # 5.4.1
      # 8.2.1     
      # example
      # export "GRADLE_HOME=$GRADLE_BASE_DIR/gradle-5.4.1" # configure version
      # export "PATH=$GRADLE_HOME/bin:$PATH" # add to PATH
      ##############
      # specify npm profile for package resolution. Must come before 'nvm use' if switching. Available options are 
      # artifactory-former-it
      # artifactory-former-network
      # external
      - npmrc artifactory-former-it
      # specify node/npm version. Default node:16.13.1/npm:8.1.2 is baked into the image.  Available versions are 
      # node:14.17.0/npm:6.14.13
      # example 
      # nvm use 14.17.0
            ###- source $NVM_DIR/nvm.sh
      - nvm use 16.13.1
      # specify maven profile for package resolution.  Available options are 
      # ARTIFACTORY_FORMER_NETWORK
      # ARTIFACTORY_FORMER_IT
      # EXTERNAL
      # example:
      # export "ARTIFACTORY_FORMER_NETWORK=true"            
      - export "ARTIFACTORY_FORMER_IT=true"
