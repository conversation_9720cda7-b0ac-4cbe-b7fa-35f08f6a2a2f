@import "mixins";
/* -- QCP -- */

.bell-tv-banner-tab-container {
    margin-bottom: 30px;
    &.noMargin {
        margin: 0;
    }
    .bell-tv-banner-tab {
        flex-basis: 50%;
        .bell-tv-banner-tab-header {
            min-height: 18px;
            line-height: 18px;
        }
        .bell-tv-banner-tab-main {
            padding: 15px;
            border: 2px solid #D4D4D4;
            box-shadow: 0 2px 3px 0 rgba(0,0,0,0.1);
        }
        &.selected {
            @media #{$media-mobile} {
                margin-top: 15px;
                &:first-child {
                    margin-top:0;
                }
            }
            .bell-tv-banner-tab-header {
                background-color: #00549a;
                color: #fff;
            }
            .bell-tv-banner-tab-main {
                color: #00549A;
                border: 2px solid #00549A;
            }
        }
    }
}