export interface IAssertOptions {
  error: string;
  failEmptyString: Boolean;
  failNullValue: Boolean;
  failZeroValue: Boolean;
  test: (target: any) => Boolean;
}

const defaultOptions: IAssertOptions = {
  error: "ReferenceError: test is not defined",
  failEmptyString: true,
  failNullValue: true,
  failZeroValue: false,
  test: () => false
};

/**
 * Insure the target has a value
 * or throw error if it does not
 * @export
 * @param {*} target value to be tested
 * @param {(IAssertOptions | string)} options test options, or error message string to be thrown
 * interface IAssertOptions {
 *    error: string; - error message string to be thrown
 *    failEmptyString: Boolean; - failt when value is an empty string ("")
 *    failNullValue: Boolean; - fail when value is null
 *    failZeroValue: Boolean; - fail when vaule is a zero (0)
 *    test: (target: any) => Boolean; - custom test to run (test must return true ONLY when value contains an error)
 * }
 * @returns {*} target
 * @example
 * const test = null;
 * Assert(test, "test value can not be Null!");
 * ... will throw console error:
 * test value can not be Null!
 */
export function Assert(target: any, options: IAssertOptions | string) {
  const _opts: IAssertOptions = {} as IAssertOptions;
  Object.assign(_opts, defaultOptions, typeof options === "string" ? {error: options} : options);
  if (
    target === undefined ||
        _opts.failNullValue && target === null ||
        _opts.failZeroValue && target === 0 ||
        _opts.failEmptyString && target === "" ||
        _opts.test(target)
  ) throw _opts.error;
  return target;
}
