import {
  Components,
  ValueOf,
  WidgetContext,
  Omniture
} from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
// import { connect } from "react-redux";
import { ITVChannel } from "../../models";
import {
  handlePropFilter,
  handleVoidFilter,
  IFilterDispatcher,
  IFilteredContentFilters,
  useChannelsFilter,
  useFilterDispatcher
} from "../../utils/Filter";
import Channel from "./Channel";
import { ProgressiveLoader } from "./ProgressiveLoad";

const { Visible } = Components;

interface IComponentProps {
  // refresh: any;
  channels: Array<ITVChannel>;
  label?: any;
  groupName: string;
  forceSelectable?: boolean;
  allowSelection?: boolean;
  allowMultipleWaysToAdd?: boolean;
  showHeader?: any;
  showFilters?: any;
}

interface IComponentConnectedProps { }

interface IComponentDispatches { }

interface IAdvancedFiltersProps {
  filters: IFilteredContentFilters;
  Filter: IFilterDispatcher;
  toggleTray: any;
}

const ErrorMessage = () => (
  <div className="pad-30">
    <div className="flexBlock flexRow">
      <div className="">
        <span className="virgin-icon icon-BIG_WARNING txtSize38">
          <span className="virgin-icon path1"></span>
          <span className="virgin-icon path2"></span>
        </span>
      </div>
      <div className="pad-15-left">
        <h3 className="noMargin txtSize20 pad-15-bottom">
          <FormattedMessage id="NO_CHANNELS_FOUND_MESSAGE" />
        </h3>
        <p className="noMargin">
          <FormattedMessage id="NO_CHANNELS_FOUND_DESCRIPTION" />
        </p>
      </div>
    </div>
  </div>
);

const AdvancedFilters: React.FC<IAdvancedFiltersProps> = ({
  Filter,
  filters,
  toggleTray
}) => {
  const _Filter = useFilterDispatcher(Filter.getState())[1];
  function onFormSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    Omniture.useOmniture().trackAction({
      id: "advanceFilterApply",
      s_oAPT: {
        actionId: 647
      },
      s_oBTN: "Advance Filters"
    });
    Filter.setState(_Filter.getState());
    requestAnimationFrame(() => toggleTray(false));
  }
  function onFormReset(e: React.FormEvent<HTMLFormElement>) {
    Filter.reset();
    requestAnimationFrame(() => toggleTray(false));
  }

  return (
    <div
      className="bell-tv-package-filters-tray"
      style={{ maxHeight: "9999em" }}
    >
      <div className="spacer1 bgGray" aria-hidden="true" />
      <form
        className="bell-tv-package-filters-row bgGray19"
        onSubmit={onFormSubmit}
        onReset={onFormReset}
      >
        <div className="flexRow flex-justify-space-between txtSize12-xs">
          <p className="txtBold txtBlack1">
            <FormattedMessage id="Refine by" />
          </p>
        </div>
        <div className="flexRow flex-justify-space-between flexCol-xs">
          <div className="flexCol" role="group" aria-labelledby="refineGenresLabel">
            <p className="txtBlack1" id="refineGenresLabel">
              <FormattedMessage id="Genres" />
            </p>
            <ul className="noMargin noBullets">
              {ValueOf<Array<string>>(filters, "genres", [])
                .sort()
                .map(filter => (
                  <li key={filter}>
                    <label
                      id={`checkboxLabel_${filter}`}
                      className="graphical_ctrl graphical_ctrl_checkbox pointer"
                      onClick={e => handlePropFilter(e, filter, _Filter.toggleGenre)}
                      onKeyDown={e => handlePropFilter(e, filter, _Filter.toggleGenre)}
                    >
                      <input
                        id={`checkbox_${filter}`}
                        type="checkbox"
                        name="genres"
                        value={filter}
                        checked={_Filter.hasGenre(filter)}
                      />
                      <FormattedMessage id={filter} />
                      <span className="ctrl_element chk_radius" />
                    </label>
                    <div className="spacer10" aria-hidden="true" />
                  </li>
                ))}
            </ul>
          </div>
          <div className="flexCol" role="group" aria-labelledby="refineLanguageLabel">
            <p className="txtBlack1" id="refineLanguageLabel">
              <FormattedMessage id="Language" />
            </p>
            <ul className="noMargin noBullets">
              {ValueOf<Array<string>>(filters, "languages", [])
                .sort()
                .map(filter => (
                  <li key={filter}>
                    <label
                      id={`label_Lang_${filter}`}
                      className="graphical_ctrl graphical_ctrl_checkbox pointer"
                      onClick={e => handlePropFilter(e, filter, _Filter.toggleLanguage)}
                      onKeyDown={e => handlePropFilter(e, filter, _Filter.toggleLanguage)}
                    >
                      <input
                        id={`checkbox_Lang_${filter}`}
                        type="checkbox"
                        name="languages"
                        value={filter}
                        checked={_Filter.hasLanguage(filter)}
                      />
                      <FormattedMessage id={filter} />
                      <span className="ctrl_element chk_radius" />
                    </label>
                    <div className="spacer10" aria-hidden="true" />
                  </li>
                ))}
            </ul>
          </div>
          <div className="flexCol flex-justify-space-between" role="group" aria-labelledby="refineOtherLabel">
            <div>
              <p className="txtBlack1" id="refineOtherLabel">
                <FormattedMessage id="Other" />
              </p>
              <ul className="noMargin noBullets">
                <li>
                  <label
                    id="label_dont_have"
                    className="graphical_ctrl graphical_ctrl_checkbox pointer"
                    onClick={e => handleVoidFilter(e, _Filter.toggleDontHave)}
                    onKeyDown={(e) => handleVoidFilter(e, _Filter.toggleDontHave)}
                  >
                    <input
                      id="checkbox_dont_have"
                      type="checkbox"
                      name="dontHave"
                      value="yes"
                      checked={_Filter.hasDontHave()}
                    />
                    <span>
                      <FormattedMessage id="Channels I dont have" />
                    </span>
                    <span className="ctrl_element chk_radius" />
                  </label>
                  <div className="spacer10" aria-hidden="true" />
                </li>
                <li>
                  <label
                    id="label_have_channels"
                    className="graphical_ctrl graphical_ctrl_checkbox pointer"
                    onClick={e => handleVoidFilter(e, _Filter.toggleHave)}
                    onKeyDown={(e) => handleVoidFilter(e, _Filter.toggleHave)}
                  >
                    <input
                      id="checkbox_have_channels"
                      type="checkbox"
                      name="have"
                      value="yes"
                      checked={_Filter.hasHave()}
                    />
                    <span>
                      <FormattedMessage id="Channels I have" />
                    </span>
                    <span className="ctrl_element chk_radius" />
                  </label>
                  <div className="spacer10" aria-hidden="true" />
                </li>
              </ul>
            </div>
            <div className=" txtRight">
              <button
                id="RESET_FILTER"
                type="reset"
                className="btn btn-link txtVirginBlueFix txtDecoration_hover"
              >
                <FormattedMessage id="Reset filters" />
              </button>
              <span className="vSpacer15" aria-hidden="true" />
              <button
                id="APPLY_BTN"
                type="submit"
                className="btn btn-primary txtSize12-xs"
              >
                <FormattedMessage id="Apply" />
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export const Component: React.FC<IComponentProps &
  IComponentConnectedProps &
  IComponentDispatches> = ({
  allowMultipleWaysToAdd,
  allowSelection,
  forceSelectable,
  channels,
  label,
  groupName,
  showHeader,
  showFilters
}) => {
  const [expaned, toggleTray] = React.useState(false);
  const [filtered, Filter] = useChannelsFilter(channels);
  const {
    config: { filters }
  }: any = React.useContext(WidgetContext);
  const filteredChannels = ValueOf<Array<ITVChannel>>(filtered, "channels", []);
  const disabled = !(channels && channels.length > 0);

  return (
    <div className="panel panel-shadow">
      <Visible when={showHeader}>
        <nav className="bell-tv-package-filters-row bgWhite flexRow border-radius-top flexCenter" role="tablist">
          <ul className="noMargin noBullets bell-tv-package-filters-nav margin-10-bottom-xs" role="presentation">
            <li tabIndex={Filter.hasGenre() ? 0 : -1} className={`table-cell ${Filter.hasGenre() ? "active" : ""}`} role="tab" onClick={() => Filter.setGenre()} onKeyUp={() => Filter.setGenre()} aria-selected={ Filter.hasGenre() ? "true" : "false"}>
              <label id="filter_All" className="pointer">
                <FormattedMessage id="FILTER_TEXT_All" />
              </label>
            </li>
            {ValueOf<Array<string>>(filtered, "filters.genres", [])
              .filter(filter => filters.genres.indexOf(filter) > -1)
              .map(filter => (
                <li
                  tabIndex={Filter.onlyGenre(filter) ? 0 : -1}
                  role="tab"
                  id={filter}
                  key={filter}
                  onKeyUp={(e) => {
                    if (e.keyCode === undefined || e.keyCode === 13) {
                      Filter.setGenre(filter);
                      toggleTray(false);
                    }
                  }}
                  onClick={() => {
                    Filter.setGenre(filter);
                    toggleTray(false);
                  }}
                  className={`table-cell ${
                    Filter.onlyGenre(filter) ? "active" : ""
                  }`}
                  aria-selected={ Filter.onlyGenre(filter) ? "true" : "false"}
                >
                  <label className="pointer" id={`filter_${filter}`}>
                    <FormattedMessage id={`FILTER_TEXT_${filter}`} />
                  </label>
                </li>
              ))}
          </ul>
          <div
            className="spacer2 fill-xs border-filter bgVirginCustomGray1 d-block d-sm-none"
            aria-hidden="true"
          ></div>
          <div className="vSpacer10 flexGrow hidden-xs" aria-hidden="true" />
          <div className="spacer10" aria-hidden="true" />
          <ul className="noMargin noBullets bell-tv-package-filters-nav" role="presentation">
            <li>
              <button
                id="Advanced_Filters"
                onClick={() => toggleTray(!expaned)}
                aria-expanded={expaned}
                disabled={disabled}
                className="btn btn-link no-pad txtDecorationNoneHover"
              >
                <span className="sans-serif icon-blue showText txtDecoration_hover links-blue-on-bg-white">
                  <FormattedMessage id="Advanced Filters" />
                &nbsp;&nbsp;
                  <span
                    className={`volt-icon txtSize16 icon-blue icon-${
                      expaned ? "Collapse" : "Expand"
                    }`}
                  >
                    <span
                      className={`volt-icon path1 icon-${
                        expaned ? "Collapse" : "Expand"
                      }`}
                    />
                    <span
                      className={`volt-icon path2 icon-${
                        expaned ? "Collapse" : "Expand"
                      }`}
                    />
                  </span>
                </span>
              </button>
            </li>
          </ul>
        </nav>
      </Visible>
      <Visible when={expaned}>
        <AdvancedFilters
          filters={filtered.filters}
          Filter={Filter}
          toggleTray={toggleTray}
        />
      </Visible>
      <div className="bell-tv-package-filters-row bgGray19 block-xs">
        <div className="spacer6 visible-xs" aria-hidden="true" />
        <div className="txtSize18 flexGrow">
          <Visible
            when={Boolean(label)}
            placeholder={
              <span>
                <FormattedMessage id="Number of channels" /> (
                {ValueOf(filtered, "channels.length", 0)})
              </span>
            }
          >
            {label}
          </Visible>
          <Visible when={showFilters}>
            <div className="spacer15" />
          </Visible>
        </div>
        <Visible when={showFilters}>
          <div className="flexRow block-xs flexCenter">
            <div className="flexStatic" role="group" aria-labelledby="languageLabel">
              <span className="txtBold txtBlack" id="languageLabel">
                <FormattedMessage id="Language" />
              </span>
              <span className="vSpacer15" aria-hidden="true" />
              {ValueOf<Array<string>>(filtered, "filters.languages", [])
                .filter(filter => filters.languages.indexOf(filter) > -1)
                .map((filter, i) => (
                  <React.Fragment key={i}>
                    <label
                      id={`lang_${filter}`}
                      onClick={e => {
                        handlePropFilter(e, filter, Filter.toggleLanguage);
                        toggleTray(false);
                      }}
                      onKeyDown={e => {
                        handlePropFilter(e, filter, Filter.toggleLanguage);
                        toggleTray(false);
                      }}
                      className="graphical_ctrl graphical_ctrl_checkbox pointer"
                    >
                      <input
                        id={`input_${filter}`}
                        type="checkbox"
                        name="packages"
                        disabled={disabled}
                        checked={Filter.hasLanguage(filter)}
                      />
                      <FormattedMessage id={filter} />
                      <span className="ctrl_element chk_radius" />
                    </label>
                    <span className="vSpacer30" aria-hidden="true" />
                  </React.Fragment>
                ))}
            </div>
            <div className="flexGrow spacer15" aria-hidden="true" />
            <div className="flexStatic">
              <span className="txtBold txtBlack">
                <FormattedMessage id="Sort by" />
              </span>
              {/* <span className="vSpacer15" aria-hidden="true" /> */}
              <button
                id="sortBy_num"
                onClick={() => Filter.setSort("channelNumber")}
                disabled={disabled}
                className="btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray"
              >
                <span className="sr-only">
                  <FormattedMessage id="Sort by" />
                </span>
                <Visible
                  when={Filter.whichSort() === "channelNumberdesc"}
                  placeholder={<FormattedMessage id="0-9" />}
                >
                  <FormattedMessage id="9-0" />
                </Visible>
              </button>
              <button
                id="sortBy_alpha"
                onClick={() => Filter.setSort("name")}
                disabled={disabled}
                className="btn btn-link noBorder bgTransparent txtVirginBlue txtDecoration_hover filter_arrow links-blue-on-bg-gray"
              >
                <span className="sr-only">
                  <FormattedMessage id="Sort by" />
                </span>
                <Visible
                  when={Filter.whichSort() === "namedesc"}
                  placeholder={<FormattedMessage id="A-Z" />}
                >
                  <FormattedMessage id="Z-A" />
                </Visible>
              </button>
            </div>
          </div>
        </Visible>
      </div>
      <div
        role="group"
        aria-labelledby={`filter_${Filter.selectedGenre()}`}
        className="bell-tv-package-filters-row bgWhite"
      >
        <Visible when={!disabled} placeholder={<ErrorMessage />}>
          <Visible
            when={ValueOf(filteredChannels, "length", 0) > 0}
            placeholder={<ErrorMessage />}
          >
            <div className="bell-tv-channels bell-tv-channel-picker flexRow">
              <ProgressiveLoader list={filteredChannels} formatter={channel => (
                <Channel
                  key={channel.id}
                  {...channel}
                  isSelectable={forceSelectable || (channel.isSelectable && !!allowSelection)}
                  multipleWaysToAdd={
                    allowMultipleWaysToAdd ? channel.multipleWaysToAdd : []
                  }
                />
              )} />
            </div>
          </Visible>
        </Visible>
      </div>
    </div>
  );
};

export default Component;
