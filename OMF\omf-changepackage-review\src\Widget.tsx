import * as React from "react";
import * as ReactRedux from "react-redux";
import { EWidgetStatus, Actions, ContextProvider, Components } from "omf-changepackage-components";
import { ViewWidget, Widget, ParamsProvider } from "bwtk";
import { Store } from "./store";
import { IWidgetProps } from "./models";
import { Pipe } from "./Pipe";
import { Config } from "./Config";
import { App } from "./App";
import { Root } from "react-dom/client";

const {
    setWidgetProps,
    setWidgetStatus
  } = Actions,
  StoreProvider = ReactRedux.Provider as any;
@Widget({ namespace: "Ordering" })
export default class WidgetContainer extends ViewWidget {
  constructor(private store: Store, private params: ParamsProvider<IWidgetProps, any>, private config: Config, private pipe: Pipe) {
    super();
  }

  /**
   * Initialize widget flow
   * please do not place any startup login in here
   * all logic should reside in Epics.onWidgetStatusEpic
   * @memberof WidgetContainer
   */
  init() {
    this.pipe.subscribe(Pipe.Subscriptions(this.store));
    this.store.dispatch(setWidgetProps(this.config));
    this.store.dispatch(setWidgetProps(this.params.props));
    this.store.dispatch(setWidgetStatus(EWidgetStatus.RENDERED));
  }

  /**
   * Deinitialize widget flow
   * Destroy all listeneres and connections
   * @memberof WidgetContainer
   */
  destroy() {
    this.pipe.unsubscribe();
    this.store.destroy();
  }

  /**
   * Render widget
   * Set all contextual providers:
   * * ContextProvider: top-most wrapper used to propagate all *immutable* state params
   * * StoreProvider: redux store wrapper used to propagate all *mutable* state params
   * @param {Element} root
   * @memberof WidgetContainer
   */
  render(root: Root) {
    const { store } = this;
    root.render(
      <ContextProvider value={{ config: this.config, mode: this.params.props.mode } as any}>
        <StoreProvider {...{ store }}>
          <Components.PersistGate render={() => <App />} store={store} />
        </StoreProvider>
      </ContextProvider>
    );
  }
}
