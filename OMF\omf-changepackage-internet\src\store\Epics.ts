import { Injectable } from "bwtk";
import { Epic, combineEpics, ofType } from "redux-observable";
import { filter, mergeMap } from "rxjs";
import { EWidgetStatus, Actions, Omniture, Utils, EFlowType } from "omf-changepackage-components";

import { IStoreState } from "../models";
import { getAccountDetails } from "./Actions";
import { CatalogEpics } from "./Epics/Catalog";
import { UserAccountEpics } from "./Epics/UserAccount";
import { OmnitureEpics } from "./Epics/Omniture";

const {
  setWidgetStatus
} = Actions;

// const { concat } = ActionsObservable;

@Injectable
export class Epics {
  constructor(
    public catalogEpics: CatalogEpics,
    public userAccountEpics: UserAccountEpics,
    public omniture: OmnitureEpics
  ) { }

  combineEpics() {
    return combineEpics(
      this.onWidgetStatusEpic,
    );
  }

  private get onWidgetStatusEpic(): GeneralEpic {
    return (action$) =>
      action$.pipe(
        ofType(setWidgetStatus.toString()),
        filter((action: ReduxActions.Action<EWidgetStatus>) => action.payload === EWidgetStatus.INIT),
        mergeMap(() => {
          let action, s_oSS2 = "~";
          switch (Utils.getFlowType()) {
            case EFlowType.INTERNET:
              action = 523;
              s_oSS2 = "Internet";
              break;
            case EFlowType.BUNDLE:
              s_oSS2 = "Bundle";
              break;
            default:
              // Use default values for other flow types
              break;
          }
          Omniture.useOmniture().updateContext({
            s_oSS1: "~", s_oSS2,
            s_oSS3: "Change package",
            s_oPGN: "Setup your service",
            s_oAPT: {
              actionId: action
            }
          });
          return [
            getAccountDetails()
          ];
        }));
  }

}

type GeneralEpic = Epic<ReduxActions.Action<any>, ReduxActions.Action<any>, void, IStoreState>;
