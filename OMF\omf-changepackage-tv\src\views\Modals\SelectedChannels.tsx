import {
  Components,
  Volt,
  Omniture
} from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import { toggleSelection } from "../../store/Actions";
import { toCharacteristicsJSON } from "../../utils/Characteristics";

const { Modal } = Components;

interface IComponentProps { }

interface IConnectedProps {
  // refresh: any;
  channels: Array<ITVChannel>;
  totalPrice: any;
}

interface IComponentDispatches {
  onRemoveChannel: (action: Volt.IHypermediaAction) => void;
}

export const ModalId: string = "SELECTED_CANNELS";

const getSelectedChannels = (
  channels: Array<ITVChannel>
): Array<ITVChannel> => channels.filter(channel => channel.isSelected);

const Component: React.FC<IComponentProps &
  IComponentDispatches &
  IConnectedProps> = ({
  channels,
  totalPrice,
  onRemoveChannel
}) =>   {
  const intl = useIntl();
  return <Modal
    modalId={ModalId}
    className={channels.length > 8 ? "do-not-center-in" : ""}
    onShown={() => {
      Omniture.useOmniture().trackFragment({
        id: "selectedChannelsLightbox",
        s_oAPT: {
          actionId: 104
        },
        s_oPRM: "Selected channels"
      });
    }}
    onDismiss={() => {
      Omniture.useOmniture().trackAction({
        id: "selectedChannelsLightbox",
        s_oAPT: {
          actionId: 647
        },
        s_oBTN: "Close"
      });
    }}
    flexDisplay={true}
    title={<FormattedMessage id="SELECTED_CANNELS_TITLE" />}
  >
    <div className="pad-30 pad-15-left-right-xs">
      <p className="no-margin txtSize18 txtBold vm-dark-grey2 line-height-18">
        {channels ? (
          <FormattedMessage
            id="SELECTED_CANNELS_LABEL"
            values={{
              total: getSelectedChannels(channels).length,
              price: totalPrice
            }}
          />
        ) : null}
      </p>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="flexRow flexCol-xs flexWrap ">
        {channels &&
              getSelectedChannels(channels).map(
                ({ id, name, imagePath, offeringAction, characteristics }) => {
                  const { callsign } = toCharacteristicsJSON(characteristics);
                  return (
                    <div className="selectchannelBlock pad-15 fill-xs txtCenter-xs margin-15-right pad-0-bottom">
                      <button
                        id={`${id}RemoveChannel`}
                        onClick={() => onRemoveChannel(offeringAction)}
                        type="button"
                        className="no-pad close no-margin"
                        aria-label={`${intl.formatMessage({id: "REMOVE_TEXT"})} ${name}`}
                      >
                        <span className="volt-icon icon-big_X txtSize16 close-channel"></span>
                      </button>
                      <div className="channelImage margin-15-bottom margin-10-left justify-center no-margin-xs fill-xs">
                        <img src={imagePath} alt={name} />
                      </div>
                      <div className="pad-5-left">
                        <p className="channelName no-margin txtSize14 txtVirginBlue line-height-18 txtUnderline">
                          {name}
                        </p>
                        <div className="spacer10" aria-hidden="true" />
                        <p className="margin-15-bottom">{callsign}</p>
                      </div>
                    </div>
                  );
                }
              )}
        <div className="spacer30 clear" aria-hidden="true" />
      </div>
    </div>
    <div className="modal-footer bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg">
      {/* <p className="txtBold"><FormattedMessage id="SAVE_SELECTION_NOTE" /></p> */}
      <div className="col1 flexRow">
        <button
          id="SEL_CANNELS_CLOSE"
          className="btn btn-primary"
          onClick={() => {
            Omniture.useOmniture().trackAction({
              id: "selectedChannelsLightbox",
              s_oAPT: {
                actionId: 647
              },
              s_oBTN: "Close"
            });
          }}
          data-dismiss="modal"
        >
          <FormattedMessage id="SELECTED_CANNELS_CLOSE" />
        </button>
      </div>
    </div>
  </Modal>;}
;

const SelectedChannels = connect<IComponentProps, IComponentDispatches>(
  ({ }: IStoreState) => ({} as any),
  dispatch => ({
    onRemoveChannel: (action: Volt.IHypermediaAction) =>
      dispatch(toggleSelection(action))
  })
)(Component);

export default SelectedChannels;
