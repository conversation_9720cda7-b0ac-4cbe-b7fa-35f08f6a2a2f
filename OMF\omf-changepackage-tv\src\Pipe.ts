import { CommonFeatures } from "bwtk";
import { Actions } from "omf-changepackage-components";
import { Action } from "redux-actions";
import { Store } from "./store";
import { CleanupPopoverStack } from "./views/Components/Tooltip";

const { BasePipe } = CommonFeatures;

/**
 * rxjs pipe provider
 * this fascilitates the direct connection
 * between widgets through rxjs Observable
 * @export
 * @class Pipe
 * @extends {BasePipe}
 */
export class <PERSON>pe extends BasePipe {
  static Subscriptions(store: Store) {
    return {
      // [Actions.historyGo.toString()]: ({ payload }: Action<string>) => {
      //     debugger;
      // },
      [Actions.handleNav.toString()]: ({ payload }: Action<boolean>) => {
        CleanupPopoverStack();
        store.dispatch(Actions.handleNav(payload));
      },
      [Actions.onContinue.toString()]: () => {
        CleanupPopoverStack();
        store.dispatch(Actions.omniPageSubmit());
        Actions.broadcastUpdate(Actions.historyForward());
      }
    };
  }
  /**
     *Creates a static instance of Pipe.
     * @param {*} arg
     * @memberof Pipe
     */
  static instance: Pipe;
  constructor(arg: any) {
    super(arg);
    Pipe.instance = this;
  }
}
