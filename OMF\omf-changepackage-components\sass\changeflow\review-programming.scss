@import "mixins";

/***
*** Styles for Lightbox Scrollbar and Review/Confirmation page ***
***/

.review-programming-container {
    flex-direction: row;
    @media #{$media-mobile} {
        flex-direction: column;
    }
    .tv-price {
        sup {
            font-size: 14px;
            top: 5px;
            vertical-align: top;
            // &:first-child {
            //     right: -4px;
            // }
            // &:last-child {
            //     left: -3px;
            // }
        }
    }
    // a:hover {
    //     color: #23527c;
    // }
    .collapsable-container {
        display: none;
        @media #{$media-desktop-tab} {
            display: block;
        }
    }
    .collapsable-container.expanded {
        display: block;
    }
    .review-programming-panel {
        display: flex;
        flex-basis: 50%;
        position: relative;
        background: #FFF;
        border: 1px solid #D4D4D4;
        border-top: none;
        &.future {
            background-color: $virginBlack;
        }
        .review-programming-panel-header {
            height: 102px;
            border-bottom: 1px solid $virginGray;
            border-top: 1px solid $virginGray;
            background-color: #F4F4F4;
            color: $bellBlack;
            line-height: 22px;
            padding: 25px 30px;
            width: 100%;
            &.future {
                background-color: $virginBlack;
                color: $colorWhite;
                line-height: 1;
                a {
                    color: $colorWhite;
                    &:hover {
                        color: $colorWhite;
                    }
                }
            }
            @media #{$media-desktop-tab} {
                text-align: center;
            }
            @media #{$media-mobile} {
                padding: 15px;
                cursor: pointer;
            }
        }
        .review-programming-panel-footer {
            // height: 102px;
            background-color: #F4F4F4;
            line-height: 22px;
            // position: absolute;
            width: 100%;
            // bottom: 0;
            @media #{$media-mobile} {
                position: relative;
                border-top: 1px solid #D4D4D4;
            }
            .tv-price {
                color: #074088;
            }
            &.future {
                background-color: $virginBlack;
                color: #FFF;
                .tv-price {
                    color: #FFF;
                }
            }
            .tv-price {
                padding-top: 3px;
                padding-left: 5px;
            }
            .review-programming-footer-container {
                padding: 30px;
                @media #{$media-mobile} {
                    padding: 20px 15px;
                }
            }
        }
        .section-seasonal-programming {
            padding: 30px;
            @media #{$media-mobile} {
                padding: 30px 15px;
            }
        }
        .review-programming-panel-body {
            padding: 30px;
            line-height: 22px;
            // padding-bottom: 150px;
            @media #{$media-mobile} {
                padding: 20px 15px 30px;
            }
            .tv-banner {
                .tv-price {
                    color: #074088;
                }
                hr {
                    border-color: #D4D4D4;
                }
                .tv-compartment {
                    // width: 210px;
                    display: inline-block;
                    @media #{$media-mobile} {
                        display: block;
                    }
                }
                .edit-link {
                    margin-left: 10px;
                    font-weight: normal;
                }
            }
        }
        @media #{$media-desktop-tab} {
            &:first-child {
                border-right: none;
            }
        }
    }
    .compare-package-panel {
        color: $colorWhite;
        .compare-package-panel-header {
            background-color: $virginBlack;
            height: 62px;
            border: 1px solid $virginBlack;
            &.both {
                background-color: $virginGray;
                border: 1px solid $virginGray;
            }
            h5 {
                @media #{$media-mobile} {
                    text-align: left;
                }
            }
            i:before {
                top: 1px;
            }
            @media #{$media-mobile} {
                cursor: pointer;
            }
        }
        .compare-package-details {
            @media #{$media-mobile} {
                display: none;
            }
            &.expanded {
                display: block;
            }
            .bell-tv-qcp-channels {
                li {
                    padding: 10px;
                    background-color: $colorWhite;
                    border: 1px solid $virginGray;
                    border-top: none;
                    .bell-tv-channel-icon {
                        width: 40px;
                        height: 40px;
                        img {
                            max-width: 100%;
                        }
                    }
                    .bell-tv-channel-description {
                        padding-left: 10px;
                        .bell-tv-channel-title {
                            max-width: 116px;
                            overflow: hidden;
                            white-space: nowrap;
                            display: block;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }
            &.both {
                li {
                    @media #{$media-desktop-tab} {
                        border-left: none;
                        border-right: none;
                    }
                }
            }
        }
    }
    .offering-modified {
        color: #FFF;
        padding: 4px 4px;
        text-transform: uppercase;
        margin-left: 5px;
        font-size: 10px;
        background: $colorWhite;
        white-space: nowrap;
        &.removed {
            background-color: #555;
        }
        &.added {
            background-color: #00549A;
        }
    }
}


/* -- Lightbox specific styles --*/

.tv-cp-review .modal-padding {
    @media #{$media-mobile} {
        padding: 15px;
    }
}

.modal-content {
    .review-programming-container {
        padding-bottom: 0;
        width: 100%;
        border: 1px solid #DDD;
        margin: 0 20px;
        @media #{$media-mobile} {
            display: block;
        }
        .review-programming-panel-footer {
            min-height: 102px;
            .review-programming-footer-container {
                padding: 20px 15px;
            }
        }
        .section-seasonal-programming {
            padding: 20px 15px;
            .edit-link {
                flex-basis: 5%;
            }
        }
        .review-programming-panel-body {
            padding: 30px 16px;
            @media #{$media-mobile} {
                padding: 15px 16px;
            }
        }
    }
    .review-programming-details {
        // padding-bottom: 150px;
        @media #{$media-mobile} {
            // padding-bottom: 0;             
        }
    }
}


/*-- Terms & Service Section Styles --*/

.bell-tv-term-of-service {
    a {
        text-decoration: underline;
    }
    .bell-tv-term-of-service-body {
        padding: 30px 30px 20px 30px;
        @media #{$media-mobile} {
            padding: 30px 15px;
        }
        hr {
            width: 98%;
            margin-left: 0px;
        }
        .scrollable-content {
            max-height: 60px;
            @media #{$media-mobile} {
                max-height: 130px;
            }
            &.off {
                max-height: 280px;
            }
            -moz-transition: max-height 0.5s ease;
            -webkit-transition: max-height 0.5s ease;
            -o-transition: max-height 0.5s ease;
            transition: max-height 0.5s ease;
        }
    }
    .bell-tv-term-of-service-footer {
        padding: 15px;
        .accept-terms {
            text-align: right;
            @media #{$media-mobile} {
                text-align: center;
            }
        }
        @media #{$media-desktop-tab} {
            .tv-cancel-btn {
                float: right;
                margin-right: 30px;
                margin-top: 10px;
            }
        }
    }
}

.section-packages-listing {
    .notification,
    h1 {
        line-height: 22px;
    }
    .notification {
        margin: 10px 0;
        .icon.icon-exclamation,
        .icon.icon-check-light {
            top: 2px;
        }
    }
    &.notification-container {
        padding-left: 20px;
        padding-right: 20px;
        display: block;
    }
}

.review-div-section {
    /* Two values: flex-grow | flex-basis */
    flex: 1 auto;
}

.review-label {
    border-radius: 2px;
    padding: 3px 10px;
    margin-left: 15px;
    margin-bottom: 5px;
}

.bgGrey1 {
    background: $virginGrayLigh1;
}

.icon-warning-yellow:before {
    color: #E99E00;
}