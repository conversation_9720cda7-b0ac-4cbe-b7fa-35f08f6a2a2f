@import "mixins";
@import "./tvsc-filter-trigger";

.bell-filters {
    .bell-filters-tray {
        @media #{$media-tab-mobile} {
            overflow: hidden;
            max-height: 0;
            transition: max-height 150ms;
        }
        .form-control.form-control-select {
            line-height: normal;
            @media #{$media-mobile} {
                font-size: 16px;
                padding-top: 0;
                padding-bottom: 0;
            }
        }
    }
    .bell-filters-shadow {
        background: linear-gradient(transparent, black);
        bottom: 0;
        opacity: 0;
        z-index: 0;
        left: -30px;
        right: -30px;
        transition: opacity 100ms;
    }
}
.collapsable-container.expanded {
    .bell-filters-tray {
        max-height: 480px;
        z-index: 1;
    }
    .bell-filters-shadow {
        opacity: 0.1;
    }
}

@media screen and (max-width: 300px) {
    .col-xxs-8 {
        float: left;
        width: 66.66666667%;
    }
}