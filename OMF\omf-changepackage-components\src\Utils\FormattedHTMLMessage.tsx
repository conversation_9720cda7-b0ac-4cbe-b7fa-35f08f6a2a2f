import React from 'react';
import { useIntl } from 'react-intl';

export interface FormattedHTMLMessageProps {
  id: string;
  values?: Record<string, any>;
  defaultMessage?: string;
  children?: any;
}

export const FormattedHTMLMessage: React.FC<FormattedHTMLMessageProps> = ({
  id,
  values = {},
  defaultMessage
}) => {
  const intl = useIntl();

  try {
    let formatted = intl.formatMessage({ id, defaultMessage }, values);

    // Replace any {key, number, CAD} manually with intl.formatNumber
    const currencyRegex = /\{(\w+),\s*number,\s*CAD\}/g;
    formatted = formatted.replace(currencyRegex, (_, key) => {
      const value = values[key];
      if (value != null && !isNaN(value)) {
        return intl.formatNumber(value, {
          style: 'currency',
          currency: 'CAD',
        });
      }
      return '';
    });

    // Fallback manual replacement for simple {key}
    if (formatted.includes('{') && formatted.includes('}') && values) {
      Object.entries(values).forEach(([key, value]) => {
        const placeholder = `{${key}}`;
        formatted = formatted.replace(
          new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
          String(value)
        );
      });
    }

    return <span dangerouslySetInnerHTML={{ __html: formatted }} />;
  } catch (e) {
    console.warn(`Error formatting HTML message: ${id}`, e);
    const fallbackText = intl.messages[id] || defaultMessage || id;
    return <span dangerouslySetInnerHTML={{ __html: fallbackText }} />;
  }
};
