import * as React from "react";
import { Volt, Components, Utils, EFlowType } from "omf-changepackage-components";
import { FormattedMessage } from "react-intl";

interface IComponentProps {
  totalCharge: Volt.IPriceDetail;
  isNew?: boolean;
  isTV?: boolean;
}
const CurrentFlowType = Utils.getFlowType();

const TotalCharge: React.FunctionComponent<IComponentProps> = React.memo(({ totalCharge, isNew, isTV }) => (
  <div className={`flexBlock border-radius-bottom flex-justify-space-between pad-30-left pad-40-right pad-10-top pad-10-bottom padding-25-xs rate-plan-total borderGrayLight6-top ${isNew ? "bgOrange txtWhite" : "bgGrey1 txtBlack totalLeft"}`}>
    <div>
      <div className="spacer15" aria-hidden="true" />
      <span className="block txtSize18">
        {(CurrentFlowType === EFlowType.BUNDLE) && <FormattedMessage id={`${isNew ? "New Total" : "Total"}`} />}
        {(CurrentFlowType === EFlowType.ADDTV) && <FormattedMessage id={"ADDTV_TOTAL"} />}
        {(CurrentFlowType === EFlowType.TV) && <FormattedMessage id={`${(isNew ? "NEW_TV_TOTAL" : "CURRENT_TV_TOTAL")}`} /> }
        {(CurrentFlowType === EFlowType.INTERNET) && <FormattedMessage id={`${isNew ? "NEW_INT_TOTAL" : "CURRENT_INT_TOTAL"}`} /> }
      </span>
    </div>
    <div className="floatR">
      <div className="spacer5" aria-hidden="true" />
      <span className={`txtCurrency item-price pad-5-left bellSlimSemibold ${isNew ? "txtWhite" : "txtBlack"} txtSize40 no-margin-bottom virginUltraReg`}>
        <Components.Currency value={totalCharge.price || 0} monthly={totalCharge.priceType === "Recurring"} />
      </span>
    </div>
  </div>
));

export default TotalCharge;
