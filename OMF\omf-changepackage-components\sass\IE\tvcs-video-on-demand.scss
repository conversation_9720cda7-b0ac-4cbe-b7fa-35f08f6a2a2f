@import "mixins";

/*IE11 specific fixes*/
.bell-tv-video-on-demand-page {
    .bell-filters {
        .bell-reset-link-md {
            @media #{$media-desktop-tab} {
                position: relative;
                top: 37px;
            }
        }
        .bell-filters-flex-container {
            @media #{$media-desktop} {
                display: flex;
            }
        }
    }
    .tv-listing.bell-posters {
        max-width: 1168px;
        .bell-poster-image {
            max-width: 233px;
        }
    }
}