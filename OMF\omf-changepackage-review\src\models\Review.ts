import { Volt } from "omf-changepackage-components";
import { IObject } from "../models";

export interface IOrderSummary {
  currentTotal: Volt.IPriceDetail;
  newTotal: Volt.IPriceDetail;
  lineOfBusiness: IObject<IObject<Array<Volt.IProductOfferingGroup>>>;
}

export interface IAppointmentDetails {
  appointmentDetails: Volt.IAppointmentDetail;
  customerInformation: any;
}

export interface IOrderConfirmation {
  confirmationNumber: string | null;
  orderDate: string | null;
}
