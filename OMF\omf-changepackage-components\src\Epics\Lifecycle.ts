import { Epic, combineEpics, ofType } from "redux-observable";
import { mergeMap, tap } from "rxjs";
import { Actions } from "../Actions";
import { Models, EWidgetStatus } from "../Models";
import { Utils } from "../Utils";

const {
  setWidgetStatus,
  showHideLoader,
  errorOccured,
  clearCachedState
} = Actions;

/**
 * Basic widget lifecycle events
 * * Status update
 * * Error handeling
 * @export
 * @class LifecycleEpics
 */
export class LifecycleEpics {
  combineEpics(): any {
    return combineEpics(
      this.onWidgetStatusEpic,
      this.onErrorOccuredEpic,
      this.clearCachedStateEpic
    );
  }

  /**
   * Epic activated on widget state changes
   *
   * @readonly
   * @private
   * @type {GeneralEpic}
   * @memberof Epics
   */
  private get onWidgetStatusEpic(): GeneralEpic {
    return (action$) =>
      action$.pipe(
        ofType(setWidgetStatus.toString()),
        mergeMap((action: ReduxActions.Action<EWidgetStatus>) => {
          switch (action.payload) {
            case EWidgetStatus.INIT: return [showHideLoader(true)];
            case EWidgetStatus.UPDATING: return [showHideLoader(true)];
            case EWidgetStatus.RENDERED: return [showHideLoader(false)];
            case EWidgetStatus.ERROR: return [showHideLoader(false)];
            default: return [];
          }
        })
      );
  }

  /**
   * Epic activated in case of widget falure
   *
   * @readonly
   * @private
   * @type {ErrorOccuredEpic}
   * @memberof Epics
   */
  private get onErrorOccuredEpic(): ErrorOccuredEpic {
    return (action$) =>
      action$.pipe(
        ofType(errorOccured.toString()),
        mergeMap(({ payload }: ReduxActions.Action<Models.IErrorHandlerProps>) => [
          setWidgetStatus(EWidgetStatus.ERROR)
        ])
      );
  }

  private get clearCachedStateEpic(): GeneralEpic {
    return (action$) =>
      action$.pipe(
        ofType(clearCachedState.toString()),
        tap(({ payload }: ReduxActions.Action<string[]>) => {
          Utils.clearCachedState(payload);
        }),
        // Epics must return an action, even after side effects
        mergeMap(() => [])
      );
  }

}

type GeneralEpic = Epic<ReduxActions.Action<any>, any>;
type ErrorOccuredEpic = Epic<ReduxActions.Action<any>, any>;
