import * as React from "react";
// import { Components, EFlowType } from "omf-changepackage-components";
import CurrentItems from "./ItemsCurrent";
import NewItems from "./ItemsNew";

function computeFullSectionheight(container: HTMLDivElement): number {
  try {
    let acc = 0;
    Array.from(container.childNodes).forEach(
      (node: HTMLDivElement) => acc += node.getBoundingClientRect().height
    );
    return acc;
  } catch (e) {
    return 0;
  }
}

export const Summary: React.FunctionComponent<{ isConfirmationStep: boolean }> = ({
  isConfirmationStep
}) => {
  // const flowType = sessionStorage.getItem("omf:Flowtype") as EFlowType;
  // const isCurrentItems = flowType !== EFlowType.ADDTV;
  React.useEffect(
    () => {
      window.requestAnimationFrame(
        () => {
          const sections = Array.from(document.querySelectorAll(".review-programming-container .review-panel-body"));
          const maxHeight: number = sections.map(computeFullSectionheight).reduce(
            (maxHeight, height) => Math.max(height, maxHeight), 0
          );
          sections.forEach(
            (section: HTMLDivElement) => section.style.height = maxHeight > 0 ? maxHeight + "px" : "auto"
          );
        }
      );
    }
  );
  return (
    <section className="container-flex-box-wrap noPadding">
      <div className="flexBlock review-programming-container bgWhite panel-border noBorder-xs fullWidth border-radius-top">
        {/* <Components.Visible when={isCurrentItems}> */}
        <CurrentItems isConfirmationStep={isConfirmationStep} />
        {/* </Components.Visible> */}
        <NewItems isConfirmationStep={isConfirmationStep} />
      </div>
    </section>
  );
};
