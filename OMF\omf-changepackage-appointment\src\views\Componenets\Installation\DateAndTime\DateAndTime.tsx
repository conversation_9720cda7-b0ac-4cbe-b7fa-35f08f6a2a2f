import * as React from "react";
import { FormattedMessage, FormattedDate } from "react-intl";
// import { useSelector } from "react-redux";
import { IAvailableDates } from "../../../../models";
import { useFormContext } from "react-hook-form";

export interface IDateAndTimeProps {
  handleChange: Function;
  preferredDate: IAvailableDates;
  checked: null | IAvailableDates | true;
}

export const DateAndTime: React.FunctionComponent<IDateAndTimeProps> = React.memo((props: any) => {
  const { handleChange, preferredDate, checked } = props,
    { register } = useFormContext();

  return <>
    <label id={"dateAndTime" + preferredDate.date} className="graphical_ctrl pointer ctrl_radioBtn margin-10-bottom">
      <input
        type="radio"
        ref={register({ required: true })}
        id={"timeOption" + preferredDate.date}
        name="dateAndTime"
        value={JSON.stringify(preferredDate)}
        onChange={(e) => handleChange(e)}
        checked={checked.date === preferredDate.date}
      />
      <label className="block no-margin" htmlFor={"timeOption" + preferredDate.date}>
        {Boolean(preferredDate.date) ?
          <FormattedDate value={preferredDate.date as string} year="numeric" weekday="long" month="long" day="2-digit" timeZone="UTC" /> :
          "No Appointment Details"}
      </label>
      {Boolean(preferredDate.timeSlots.length) ? <span className="txtNormal block"><FormattedMessage id={preferredDate.timeSlots.find((item: any) => item.isAvailable)?.intervalType} /></span> : null}
      <span className="ctrl_element"></span>
    </label>
  </>;
});
