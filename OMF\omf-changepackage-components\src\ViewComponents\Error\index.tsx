import * as React from "react";
import { FormattedMessage } from "react-intl";
import { ContainerComponent, PanelComponent } from "../Container";
import { Models, EFlowType } from "../../Models";
import { Omniture } from "../../Omniture";
import { ValueOf, Utils } from "../../Utils";

export interface IErrorComponentProps {
  details: Models.ErrorHandler;
}

export interface IErrorComponent extends React.FC<IErrorComponentProps> {
}

export const ErrorComponent: IErrorComponent = ({ details }) => {
  React.useEffect(() => {
    let action = 0;
    switch (Utils.getFlowType()) {
      case EFlowType.INTERNET:
        action = 543;
        break;
      case EFlowType.TV:
        action = 394;
        break;
      default:
        // Use default action value (0) for other flow types
        break;
    }
    if (details.response ? details.response.url.includes("ProductOrder/Summary") : 0)
      action = 104;
    switch (details.type) {
      case "API":
        Omniture.useOmniture().trackError({
          code: "API" + ValueOf(details, "response.status", "500"),
          type: Omniture.EErrorType.Technical,
          layer: Omniture.EApplicationLayer.Backend,
          description: {
            ref: "TechnicalErrorMessage"
          },
          ajax: true
        }, action);
        break;
      case "widget":
        Omniture.useOmniture().trackError({
          code: "WIDGET400",
          type: Omniture.EErrorType.Technical,
          layer: Omniture.EApplicationLayer.Frontend,
          description: {
            ref: "TechnicalErrorMessage"
          }
        }, action);
        break;
      case "logic":
      default:
        Omniture.useOmniture().trackError({
          code: "LOGIC500",
          type: Omniture.EErrorType.Technical,
          layer: Omniture.EApplicationLayer.Frontend,
          description: {
            ref: "TechnicalErrorMessage"
          }
        }, action);
        break;
    }
  }, []);
  return <ContainerComponent className="error margin-30-bottom">
    <div className="spacer30" aria-hidden="true" />
    <PanelComponent className="border bgWgite borderGray4 pad-30">
      <div className="row">
        <div className="inlineBlock icon-width-40 valign-middle text-center-xs">
          <span className="txtRed txtSize32 icons icons-info" />
        </div>
        <div className="spacer15" aria-hidden="true" />
        <div className="inlineBlock pad-20-left no-pad-left-xs content-width valign-middle">
          <span className="txtBlack2 block txtSize20" id="TechnicalErrorMessage">
            <FormattedMessage id="TECHNICAL_ERROR" />
          </span>
        </div>
      </div>
      <div className="margin-20-top" style={{ display: details.debug ? "block" : "none" }}>
        {
          ((type) => {
            switch (type) {
              case "API":
                return <React.Fragment>
                  <p className="margin-10-top">API Request failed {ValueOf(details, "response.status", "unknown")} ({ValueOf(details, "response.statusText", "unknown")})</p>
                  <p className="margin-10-top" style={{ wordBreak: "break-all" }}>URL: {ValueOf(details, "response.url", "unknown")}</p>
                  <p className="margin-10-top" style={{ wordBreak: "break-all" }}>Response: {JSON.stringify(ValueOf(details, "response.data", "Null"), null, " ")}</p>
                </React.Fragment>;
              case "widget":
                return <React.Fragment>
                  <p className="margin-10-top">Widget render failed</p>
                  <p className="margin-10-top">Component: <pre>{details.componentStack}</pre></p>
                </React.Fragment>;
              case "logic":
              default:
                return <p className="margin-10-top">General logic falure</p>;
            }
          })(details.type)
        }
        <p className="margin-10-top">Stack trace: <pre>{JSON.stringify(ValueOf(details, "stack"), null, " ")}</pre></p>
      </div>
    </PanelComponent>
  </ContainerComponent>;
};
