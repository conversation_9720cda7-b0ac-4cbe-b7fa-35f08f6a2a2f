import { Components, FormattedHTMLMessage, Omniture } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";

interface IComponentProps { }

const Expander: React.FC<IComponentProps> = () => {
  const [expanded, toggleState] = React.useState(false);
  React.useEffect(() => {
    expanded &&
            Omniture.useOmniture().trackAction({
              id: "learmMOreElectronicDeliveryClick",
              s_oAPT: {
                actionId: 648
              },
              s_oEPN: {
                ref: "electronic_delivery_more"
              }
            });
  }, [expanded]);
  return <div className="accss-focus-outline-override-pad" id="electronic_delivery">
    <button id="electronic_delivery_more" className="btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray" onClick={() => toggleState(!expanded)} aria-expanded={expanded}>
      <span className={`volt-icon ${expanded ? "icon-collapse_m" : "icon-expand_m"}`} aria-hidden="true" />&nbsp;&nbsp;
      <FormattedMessage id="electronic_delivery_more" />
    </button>
    <Components.Visible when={expanded}>
      <div className="spacer15 clear" aria-hidden="true" />
      <div className="spacer15 d-sm-block clear" aria-hidden="true" />
      <div className="moreInfoBox bgWhite pad30 margin-30-bottom">
        <FormattedHTMLMessage id="ABOUT_SELECTRONIC_DELIVERY_TEXT" />
      </div>
    </Components.Visible>
  </div>;
};

export default Expander;
