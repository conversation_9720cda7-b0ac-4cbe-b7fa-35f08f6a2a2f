import { Injectable } from "bwtk";
import { Actions, AjaxResponse, EFlowType, EWidgetRoute, EWidgetStatus, FilterRestrictionObservable, Models, Utils, ValueOf, Volt } from "omf-changepackage-components";
import { Action } from "redux-actions";
import { combineEpics, Epic , StateObservable } from "redux-observable";
import { Client } from "../../Client";
import { Config } from "../../Config";
import { useHistory, useTVHistory } from "../../utils/History";
import { ModalId as ExitModalId } from "../../views/modals/ApplicationExit";
import { checkRestrictions, setFlowType } from "../Actions";
import { Store } from "../Store";
import { catchError, filter, mergeMap, concat, of } from "rxjs";


const {
  showHideLoader,
  continueFlow,
  historyBack,
  historyForward,
  historyGo,
  openLightbox,
  applicationExit,
  applicationLogout,
  setWidgetStatus,
  applicationReset,
  closeLightbox,
  onContinue,
} = Actions;

function getBaseRoute(route: string): string {
  return (route || "").replace(/\/Ordering|\/Packages|\/Movies|\/Addons|\/Alacarte|\/International|\/Combos|\/Browse|\/Search/i, "");
}

function getCurrentRoute(): string {
  const history = useHistory();
  return getBaseRoute(history.location.pathname);
}

@Injectable
export class NavigationEpics {
  widgetState: EWidgetStatus;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.historyGoEpic,
      this.historyForwardEpic,
      this.historyBackEpic,
      this.onContinueEpic,
      this.applicationExitEpic,
      this.applicationLogoutEpic,
      this.checkRestrictionsEpic,
      this.applicationResetEpic
    );
  }

  /**
     * Check for possible pending restrictions before
     * historyForward can be completed
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get checkRestrictionsEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === checkRestrictions.toString()),
        filter(({ payload }: Action<Volt.IHypermediaAction>) => Boolean(payload)),
        mergeMap(({ payload }: Action<Volt.IHypermediaAction>) => 
          concat(
            [setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)],
            this.client.action<AjaxResponse<Volt.IRestrictionAPIResponse>>(payload).pipe(
              mergeMap((response) => FilterRestrictionObservable(response, [
                historyGo(response.data.redirectURLKey)
              ]))
            )
          )
        ),
        catchError(Models.ErrorHandlerObservable(checkRestrictions))
      );
  }

  /**
     * Navigates user to an appropriate page
     * in the flow. Also updates flowType, based on
     * the redirection type
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get historyGoEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === historyGo.toString()),
        filter(({ payload }: Action<string>) => (typeof payload === "string")),
        mergeMap(({ payload }: Action<string>) => {
          let { flowType } = (state$ as any).value;
          let destination = payload;
          let destinationRoute = "";
          switch (payload) {
            case "APPOINTMENT":
            case EWidgetRoute.APPOINTMENT:
              destination = Utils.constructPageRoute(EWidgetRoute.APPOINTMENT);
              destinationRoute = EWidgetRoute.APPOINTMENT;
              break;
            case "INTERNET_REVIEW":
            case "TV_REVIEW":
            case "BUNDLE_REVIEW":
            case "REVIEW":
            case EWidgetRoute.REVIEW:
              destination = Utils.constructPageRoute(EWidgetRoute.REVIEW);
              destinationRoute = EWidgetRoute.REVIEW;
              break;
            case "INTERNET_CONFIRMATION":
            case "TV_CONFIRMATION":
            case "BUNDLE_CONFIRMATION":
            case "CONFIRMATION":
            case EWidgetRoute.CONFIRMATION:
              destination = Utils.constructPageRoute(EWidgetRoute.CONFIRMATION);
              destinationRoute = EWidgetRoute.CONFIRMATION;
              break;
            case "ADD_TV_REVIEW":
              destination = Utils.constructPageRoute(EWidgetRoute.REVIEW, EFlowType.ADDTV);
              destinationRoute = EWidgetRoute.REVIEW;
              break;
            case "INTERNET_CP":
            case EWidgetRoute.INTERNET:
              destination = Utils.constructPageRoute(EWidgetRoute.INTERNET);
              destinationRoute = EWidgetRoute.INTERNET;
              break;
            case "TV_CP":
            case EWidgetRoute.TV:
              destination = Utils.constructPageRoute(EWidgetRoute.TV);
              destinationRoute = EWidgetRoute.TV;
              break;
            case "ADD_TV":
              destination = Utils.constructPageRoute(EWidgetRoute.TV, EFlowType.ADDTV);
              destinationRoute = EWidgetRoute.TV;
              flowType = EFlowType.ADDTV;
              break;
            case "BUNDLE_TV":
              destination = Utils.constructPageRoute(EWidgetRoute.TV, EFlowType.BUNDLE);
              destinationRoute = EWidgetRoute.TV;
              flowType = EFlowType.BUNDLE;
              break;
            case Volt.EDIsplayGroupKey.BASE_PROGRAMMING:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Packages;
              destinationRoute = EWidgetRoute.TV;
              break;
            case Volt.EDIsplayGroupKey.ADD_ON:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Addons;
              destinationRoute = EWidgetRoute.TV;
              break;
            case Volt.EDIsplayGroupKey.ALACARTE:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Alacarte;
              destinationRoute = EWidgetRoute.TV;
              break;
            case Volt.EDIsplayGroupKey.MOVIE:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_MoviesSeries;
              destinationRoute = EWidgetRoute.TV;
              break;
            case Volt.EDIsplayGroupKey.INTERNATIONAL:
            case Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_InternationalCombos;
              destinationRoute = EWidgetRoute.TV;
              break;
            case Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_InternationalAlacarte;
              destinationRoute = EWidgetRoute.TV;
              break;
            case Volt.EDIsplayGroupKey.TV_BROWSE_ALL:
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Browse;
              destinationRoute = EWidgetRoute.TV;
              break;
            case "TV_SEARCH":
              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Search;
              destinationRoute = EWidgetRoute.TV;
              break;

            case "INTERNET_OVERVIEW":
            case "TV_OVERVIEW":
              return [
                applicationExit()
              ];
            default:
              // No specific handling for unknown navigation payloads
              break;
          }
          if (Utils.getPageRoute() === EWidgetRoute.TV && destinationRoute === EWidgetRoute.TV) {
            const history = useTVHistory();
            window.requestAnimationFrame(() => history.push(destination.replace(/\/Ordering|\/Changepackage|\/TV|\/Add\b|\/Bundle/gi, "")));
            return [
              showHideLoader(null)
            ];
          } else if (Utils.getPageRoute() === EWidgetRoute.INTERNET && destinationRoute === EWidgetRoute.INTERNET) {
            return [
              showHideLoader(null)
            ];
          } else {
            const history = useHistory();
            window.requestAnimationFrame(() => history.push(destination));
            return ([
              setFlowType(flowType)
            ]);
          }
        })
      );
  }

  /**
     * Allows the flow sequence to progress forth.
     * This step is dependent on the nextAction property
     * returned by the API.
     * If nextAction contains a valid redirectURLKey, user
     * is redirected to that redirectURLKey
     * If nextAction contains a vlid href,
     * checkRestrictions epic is triggered to verify we can go forth
     * otherwise system tries to determine where to go.
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get historyForwardEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => 
          action.type === historyForward.toString() || 
          action.type === continueFlow.toString()
        ),
        mergeMap(() => {
          const history = useHistory();
          const state: any = state$.value;
          const { routes } = state;
          const nextAction = ValueOf<Volt.IHypermediaAction>(state, "summary.nextAction", {});
          switch (true) {
            case Boolean(nextAction.href):
              return [
                checkRestrictions(nextAction)
              ];
            case Boolean(nextAction.redirectURLKey):
              return [
                historyGo(nextAction.redirectURLKey)
              ];
            default:
              let route = history.location.pathname;
              let index = routes.findIndex((r: string) => route.indexOf(r) > -1);
              // We are at the last step
              if (index === routes.length - 1) {
                return [];
              }
              // Traverse back
              index += 1;
              route = routes[index];
              history.push(route);
              return [];
          }
        })
      );
  }

  /**
   * Handles the onContinue action from preview modal
   * This intelligently navigates to the appropriate next page based on context
   * @readonly
   * @private
   * @type {NavigationEpic}
   * @memberof NavigationEpics
   */
  private get onContinueEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === onContinue.toString()),
        mergeMap(() => {
          const history = useHistory();
          const state: any = state$.value;
          const { routes } = state;
          const nextAction = ValueOf<Volt.IHypermediaAction>(state, "summary.nextAction", {});

          // If there's a nextAction with redirectURLKey, use it
          if (nextAction.redirectURLKey) {
            return of(
              historyGo(nextAction.redirectURLKey)
            );
          }

          // If there's a nextAction with href, check restrictions
          if (nextAction.href) {
            return of(
              checkRestrictions(nextAction)
            );
          }

          // Determine next route based on current location and routes array
          const route = history.location.pathname;
          const index = routes.findIndex((r: string) => route.indexOf(r) > -1);

          // If we found the current route in the routes array, go to next
          if (index >= 0 && index < routes.length - 1) {
            const nextRoute = routes[index + 1];
            history.push(nextRoute);
            return of();
          }

          // Fallback: determine destination based on current page
          const currentPage = Utils.getPageRoute();
          if (currentPage === EWidgetRoute.REVIEW) {
            // From review page, go to confirmation
            return of(
              historyGo(EWidgetRoute.CONFIRMATION)
            );
          } else {
            // Default: go to review page
            return of(
              historyGo(EWidgetRoute.REVIEW)
            );
          }
        })
      );
  }

  /**
     * Sends user back one step
     * UI driven
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get historyBackEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === historyBack.toString()),
        mergeMap(({ payload }: Action<any>) => {
          const history = useHistory();
          const state: any = state$.value;
          const { routes } = state;
          const backAction = ValueOf<Volt.IHypermediaAction>(state, "summary.backAction", {});
          switch (true) {
            case Boolean(backAction.href):
              return [
                checkRestrictions(backAction)
              ];
            case Boolean(backAction.redirectURLKey):
              if (backAction.redirectURLKey === "INTERNET_OVERVIEW" ||
                  backAction.redirectURLKey === "TV_OVERVIEW") {
                return [
                  showHideLoader(false),
                  openLightbox({ lightboxId: ExitModalId, data: { relativeId: payload } })
                ];
              } else {
                return [
                  historyGo(backAction.redirectURLKey)
                ];
              }
            default:
              let route = getCurrentRoute();
              let index = routes.findIndex((r: string) => route === r);
              // We are at the step 1 already
              if (index === 0) {
                return [
                  showHideLoader(false),
                  openLightbox({ lightboxId: ExitModalId, data: { relativeId: payload } })
                ];
              }
              // Traverse back
              index -= 1;
              if (route) {
                route = routes[index];
                if (route.indexOf("Appointment") > -1 && !sessionStorage.getItem("omf:hasAppointmentRoute")) {
                  // Do not show appointment route
                  index -= 1;
                  route = routes[index];
                }
                history.push(route);
              }
              return [
              ];
          }
        }));
  }

  /**
     * Triggers when user attempts to exit the app
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get applicationExitEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === applicationExit.toString()),
        mergeMap(() => {
          sessionStorage.clear();
          window.location = ValueOf(this.config, "linkURL.exitURL", "");
          return [
            showHideLoader(true)
          ];
        }));
  }

  private get applicationResetEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === applicationReset.toString()),
        mergeMap(() => {
          const state = state$.value;
          const nextAction = ValueOf<Volt.IHypermediaAction>(state, "summary.resetAction", {});
          return concat(
            [showHideLoader(true)],
            this.client.post(nextAction.href, nextAction.messageBody).pipe(
              mergeMap((resp: any) => [
                closeLightbox("APPLICATION_RESET"),
                window.location.reload()
              ])
            )
          );
        })
      );
  }

  /**
     * Triggers whenever user attempts to logout
     * @readonly
     * @private
     * @type {NavigationEpic}
     * @memberof NavigationEpics
     */
  private get applicationLogoutEpic(): NavigationEpic {
    return (action$: any, state$: StateObservable<Store>) =>
      action$.pipe(
        filter((action: Action<any>) => action.type === applicationLogout.toString()),
        mergeMap(() => {
          sessionStorage.clear();
          window.location = ValueOf(this.config, "linkURL.logoutURL", "");
          return [
            showHideLoader(true)
          ];
        }));
  }
}

type NavigationEpic = Epic<Action<any>, any, Store>;
