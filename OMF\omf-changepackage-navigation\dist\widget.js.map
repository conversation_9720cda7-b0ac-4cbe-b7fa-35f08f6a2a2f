{"version": 3, "file": "widget.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAAjD,IAMMC,EACIC,EANT,GAAsB,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUH,EAAQK,QAAQ,QAASA,QAAQ,gCAAiCA,QAAQ,SAAUA,QAAQ,eAAgBA,QAAQ,oBAAqBA,QAAQ,cAAeA,QAAQ,aAAcA,QAAQ,SAAUA,QAAQ,iBAAkBA,QAAQ,oBAAqBA,QAAQ,cACpR,GAAqB,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,OAAQ,+BAAgC,QAAS,cAAe,mBAAoB,aAAc,YAAa,QAAS,gBAAiB,mBAAoB,QAASN,QAG9K,IAAQE,KADJD,EAAuB,iBAAZE,QAAuBH,EAAQK,QAAQ,QAASA,QAAQ,gCAAiCA,QAAQ,SAAUA,QAAQ,eAAgBA,QAAQ,oBAAqBA,QAAQ,cAAeA,QAAQ,aAAcA,QAAQ,SAAUA,QAAQ,iBAAkBA,QAAQ,oBAAqBA,QAAQ,SAAWL,EAAQD,EAAW,KAAGA,EAAiC,2BAAGA,EAAY,MAAGA,EAAiB,WAAGA,EAAqB,eAAGA,EAAgB,UAAGA,EAAe,SAAGA,EAAY,MAAGA,EAAmB,aAAGA,EAAsB,gBAAGA,EAAW,OACvf,iBAAZI,QAAuBA,QAAUJ,GAAMG,GAAKD,EAAEC,EAEvE,CATD,CASGM,KAAM,SAASC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAgCC,EAAkCC,EAAkCC,EAAkCC,GACpW,O,wBCNA,SAASC,EAAoBC,GAA7B,IAOKjB,EALAkB,EAAeC,GAAyBF,GAC5C,YAAqBG,IAAjBF,EACIA,EAAanB,SAGjBC,EAASmB,GAAyBF,GAAY,CAGjDlB,QAAS,CAAC,GAIXsB,GAAoBJ,GAAUjB,EAAQA,EAAOD,QAASiB,GAG/ChB,EAAOD,QACf,CCCO,SAASuB,EAAUC,EAAGC,GAI3B,SAASC,IAAOC,KAAKC,YAAcJ,CAAG,CAHtC,GAAiB,mBAANC,GAA0B,OAANA,EAC3B,MAAM,IAAII,UAAU,uBAAyBC,OAAOL,GAAK,iCAC7DM,EAAcP,EAAGC,GAEjBD,EAAEQ,UAAkB,OAANP,EAAaQ,OAAOC,OAAOT,IAAMC,EAAGM,UAAYP,EAAEO,UAAW,IAAIN,EACjF,CAyBO,SAASS,EAAWC,EAAYC,EAAQC,EAAKC,GAA7C,IACsHf,EAE7GzB,EAFVyC,EAAIC,UAAUC,OAAQC,EAAIH,EAAI,EAAIH,EAAkB,OAATE,EAAgBA,EAAON,OAAOW,yBAAyBP,EAAQC,GAAOC,EACrH,GAAuB,iBAAZM,SAAoD,mBAArBA,QAAQC,SAAyBH,EAAIE,QAAQC,SAASV,EAAYC,EAAQC,EAAKC,QACpH,IAASxC,EAAIqC,EAAWM,OAAS,EAAG3C,GAAK,EAAGA,KAASyB,EAAIY,EAAWrC,MAAI4C,GAAKH,EAAI,EAAIhB,EAAEmB,GAAKH,EAAI,EAAIhB,EAAEa,EAAQC,EAAKK,GAAKnB,EAAEa,EAAQC,KAASK,GAChJ,OAAOH,EAAI,GAAKG,GAAKV,OAAOc,eAAeV,EAAQC,EAAKK,GAAIA,CAC9D,CAmDO,SAASK,EAAWC,EAAaC,GACtC,GAAuB,iBAAZL,SAAoD,mBAArBA,QAAQM,SAAyB,OAAON,QAAQM,SAASF,EAAaC,EAClH,CCxEO,SAASE,IACd,OAAOC,CACT,C,QD1BItB,EAeOuB,EA0OPC,E,YCtQAF,EACAG,EACAC,EAESC,E,ECJLC,EAGR,ECEEC,EAWWC,EAEPC,EA2BOC,ECrCA,EAkEAC,ECrEX,EAWW,EAEP,EA0BOC,ECtBA,EA4CAC,EC7DX,EAWW,EAEP,EA2BOC,ECxCX,EAeW,EAEP,EAsBOC,ECtCAC,GCDAC,GCAAC,GCAAC,GCAAC,G,GCFXC,GACAC,GASIC,GAwCN,GC5CE,GACA,GAGIC,GAyCAC,GAwCA,GA6GOC,GC1LXC,GAIAC,GACAC,GAaIC,GA8EN,GAcaC,GC7HXC,GAGWC,GCLLC,GAAYC,GAmBpB,G,SChBaC,GACAC,GACAC,GCKb,G,GCCEC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAaF,GC7BE,GAOF,GCDQC,GAAWC,GAGjBC,GAEI,GACJ,GACA,GAIF,GCpBQC,GAUR,GCJE,GACA,GAIF,G,qBCjBA1G,EAAOD,QAAUY,C,kBCAjBX,EAAOD,QAAUM,C,kBCAjBL,EAAOD,QAAUgB,C,kBCAjBf,EAAOD,QAAUW,C,kBCAjBV,EAAOD,QAAUQ,C,kBCAjBP,EAAOD,QAAUO,C,kBCAjBN,EAAOD,QAAUc,C,kBCAjBb,EAAOD,QAAUU,C,kBCAjBT,EAAOD,QAAUa,C,kBCAjBZ,EAAOD,QAAUe,C,kBCAjBd,EAAOD,QAAUS,C,GrCCbW,GAA2B,CAAC,E,OsCAhCH,EAAoBO,EAAI,SAASxB,EAAS4G,GACzC,IAAI,IAAItE,KAAOsE,EACX3F,EAAoB4F,EAAED,EAAYtE,KAASrB,EAAoB4F,EAAE7G,EAASsC,IAC5EL,OAAOc,eAAe/C,EAASsC,EAAK,CAAEwE,YAAY,EAAMC,IAAKH,EAAWtE,IAG3E,ECPArB,EAAoB4F,EAAI,SAASG,EAAKC,GAAQ,OAAOhF,OAAOD,UAAUkF,eAAeC,KAAKH,EAAKC,EAAO,ECCtGhG,EAAoB0B,EAAI,SAAS3C,GACX,oBAAXoH,QAA0BA,OAAOC,aAC1CpF,OAAOc,eAAe/C,EAASoH,OAAOC,YAAa,CAAEC,MAAO,WAE7DrF,OAAOc,eAAe/C,EAAS,aAAc,CAAEsH,OAAO,GACvD,E,yLvCUIvF,EAAgB,SAASP,EAAGC,GAI9B,OAHAM,EAAgBE,OAAOsF,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUjG,EAAGC,GAAKD,EAAEgG,UAAY/F,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAIiG,KAAKjG,EAAOQ,OAAOD,UAAUkF,eAAeC,KAAK1F,EAAGiG,KAAIlG,EAAEkG,GAAKjG,EAAEiG,GAAI,EAC7F3F,EAAcP,EAAGC,EAC1B,EAUW6B,EAAW,WAQpB,OAPAA,EAAWrB,OAAO0F,QAAU,SAAkBC,GAAlB,IACfC,EAAG9H,EAAO+H,EAENJ,EAFb,IAAY3H,EAAI,EAAG+H,EAAIrF,UAAUC,OAAQ3C,EAAI+H,EAAG/H,IAE5C,IAAS2H,KADTG,EAAIpF,UAAU1C,GACOkC,OAAOD,UAAUkF,eAAeC,KAAKU,EAAGH,KAAIE,EAAEF,GAAKG,EAAEH,IAE9E,OAAOE,CACX,EACOtE,EAASyE,MAAMpG,KAAMc,UAC9B,EAgH6BR,OAAOC,OA2GXD,OAAOC,OAM5BqB,EAAU,SAASsD,GAMrB,OALAtD,EAAUtB,OAAO+F,qBAAuB,SAAUnB,GAAV,IAE7BoB,EADLC,EAAK,GACT,IAASD,KAAKpB,EAAO5E,OAAOD,UAAUkF,eAAeC,KAAKN,EAAGoB,KAAIC,EAAGA,EAAGxF,QAAUuF,GACjF,OAAOC,CACT,EACO3E,EAAQsD,EACjB,EAuDkD,mBAApBsB,iBAAiCA,gB,6CCpU3D9E,EAA2B,KAC3BG,EAA6B,KAC7BC,GAAwB,GAET,MAChB,EAAA2E,UAAUC,UAAW,CACpB,0BACA,sCACA,iCACA,wCAEF,EAAC,EAAAD,UAAU3D,IAAK,CACd,oBACA,2BACA,kCAEF,EAAC,EAAA2D,UAAUE,OAAQ,CACjB,UACA,iBACA,wBAEF,EAAC,EAAAF,UAAUG,QAAS,CAClB,mBACA,+BACA,aACA,iBACA,wBAtBS7E,E,WCJLC,EAAqB,EAAA6E,eAAc,iBAG3C,2B,8CAOA,C,MAAA,OAPkC,O,EAArBC,EAEJ,EAAAC,mBAAP,SAA0BC,GACxB,EAAaC,SAAW,EAAaA,UAAY,EAAAC,eAAeC,SAASC,WAAW,EAAAC,eAAeP,cACnG,IAAMK,EAAgB,EAAaF,SACnC,OAAOE,EAAWA,EAASJ,mBAAmB,EAAAO,YAAYC,WAAYP,EAAIG,EAASK,QAAUR,CAC/F,EALO,EAAAC,SAAW,KADK,KADxB,EAAAQ,YACYX,E,CAAb,CAAkC9E,GCEhCC,EACE,EAAAyF,WAAU,MAUDxF,EAAkB,qBAEzBC,EAA8D,SAAC,G,IACnEwF,EAAe,kBACfhD,EAAa,gBACT,uBAAC1C,EAAK,CACV2F,QAAS1F,EACT2F,QAAS,WACP,EAAAC,SAASC,cAAcC,cAAc,CACnChB,GAAI,iBACJiB,OAAQ,CACNC,SAAU,KAEZC,OAAQrB,EAAaC,mBAAmB,4BACxCqB,OAAQtB,EAAaC,mBAAmB,4BAE5C,EACAsB,MAAO,gBAAC,EAAAC,iBAAgB,CAACtB,GAAG,8BAC5B,uBAAKuB,UAAU,UACb,gBAAC,EAAAC,qBAAoB,CAACxB,GAAG,6BAE3B,uBAAKuB,UAAU,uBAAsB,cAAa,SAClD,uBAAKA,UAAU,qGACb,0BAAQvB,GAAG,sBAAsBuB,UAAU,0BAA0BE,QAASd,GAAiB,gBAAC,EAAAW,iBAAgB,CAACtB,GAAG,iCACpH,uBAAKuB,UAAU,YAAW,cAAa,SACvC,0BAAQvB,GAAG,mBAAmBuB,UAAU,0BAA0BE,QAAS9D,GAAe,gBAAC,EAAA2D,iBAAgB,CAACtB,GAAG,+BApB7G,EAwBO5E,GAA4B,IAAAsG,SACvC,SAAC,GAAqB,OAAG,CAAH,EACtB,SAACC,GAAa,OACZhB,gBAAiB,WACf,EAAAG,SAASC,cAAca,YAAY,CACjC5B,GAAI,iBACJiB,OAAQ,CACNC,SAAU,KAEZW,OAAQ,CACNC,IAAK,yBAGTH,EAAS,EAAAI,QAAQvE,oBACnB,EACAG,cAAe,WACb,EAAAmD,SAASC,cAAca,YAAY,CACjC5B,GAAI,iBACJiB,OAAQ,CACNC,SAAU,KAEZW,OAAQ,CACNC,IAAK,sBAGTH,EAAS,EAAAI,QAAQpE,cAAczC,GACjC,EAxBY,EAFyB,CA4BvCC,GCjEW,EAA4C,SAAC,G,IAAE6G,EAAa,gBAAO,OAC9E,gBAAC,EAAAC,QAAO,KACL,SAAC,G,IAAE,IAAAC,OAAUC,EAAO,UAAEC,EAAmB,sBAAS,OACjD,0BAAQb,UAAU,wCAChB,qBAAGvB,GAAG,aAAaqC,KAAK,eAAed,UAAU,qBAC/C,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,0BAEvB,uBAAKuB,UAAU,4IACb,uBAAKA,UAAU,gBACb,sBAAIA,UAAU,wDACZ,sBAAIA,UAAU,iCACZ,qBAAGvB,GAAG,UAAUqC,KAAMF,EAAQG,WAAYf,UAAU,yGAClD,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,cAMzB,sBAAIuB,UAAU,iCACZ,qBAAGvB,GAAG,gBAAgBqC,KAAMF,EAAQI,SAAUhB,UAAU,yGACtD,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,YAGA,SAAxBoC,GACC,sBAAIb,UAAU,iCACZ,qBACEA,UAAU,wEACViB,KAAK,SACLH,KAAK,qBACLrC,GAAG,cAEH,wBAAMuB,UAAU,0DACd,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,uBAK7B,sBAAIuB,UAAU,wBACZ,qBAAGvB,GAAG,WAAWqC,KAAMF,EAAQM,YAAalB,UAAU,yGACpD,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,gBAI3B,uBAAKuB,UAAU,WAAU,cAAa,SACtC,uBAAKA,UAAU,2BACb,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,gBAGzB,uBAAKuB,UAAU,mBACb,wBAAMA,UAAU,6BAA4B,cAAa,SACzD,0BAAQvB,GAAG,eAAeyB,QAAS,WAAM,OAAAO,EAAc,eAAd,EAA+BT,UAAU,yDAAyDmB,KAAK,UAC9I,gBAAC,EAAApB,iBAAgB,CAACtB,GAAG,aAEvB,uBAAKuB,UAAU,uBACf,wBAAMA,UAAU,6BAA4B,cAAa,SACzD,uBAAKA,UAAU,qCACb,uBAAKA,UAAU,6BAA6BiB,KAAK,OAAOG,SAAU,EAAGC,IAAKT,EAAQU,cAAeC,IAAI,qBAI3G,uBAAKvB,UAAU,WAAU,cAAa,SA1DS,EAFyB,EAkEnElG,GAAS,IAAAqG,SACpB,SAAC,GAAoB,OAAG,CAAH,EACrB,SAACC,GAAa,OACZK,cAAe,SAAChC,GAAY,OAAA2B,EAAS,EAAAI,QAAQzE,aAAa,CAAEyF,WAAY,EAAaC,KAAM,CAAEC,WAAYjD,KAA7E,EADhB,EAFM,CAKpB,GC1EA,EACE,EAAAU,WAAU,MAUD,EAAkB,mBAEzB,EAA8D,SAAC,G,IACnEC,EAAe,kBACfhD,EAAa,gBACT,uBAAC,EAAK,CACViD,QAAS,EACTC,QAAS,WACP,EAAAC,SAASC,cAAcC,cAAc,CACnChB,GAAI,eACJiB,OAAQ,CACNC,SAAU,KAEZC,OAAQrB,EAAaC,mBAAmB,0BACxCqB,OAAQtB,EAAaC,mBAAmB,0BAE5C,EACAsB,MAAO,gBAAC,EAAAC,iBAAgB,CAACtB,GAAG,4BAC5B,uBAAKA,GAAG,wBAAwBuB,UAAU,+BACxC,gBAAC,EAAAC,qBAAoB,CAACxB,GAAG,2BAE3B,uBAAKuB,UAAU,qGACb,0BAAQvB,GAAG,iBAAiBuB,UAAU,0BAA0BE,QAAS9D,GAAe,gBAAC,EAAA2D,iBAAgB,CAACtB,GAAG,4BAC7G,uBAAKuB,UAAU,YAAW,cAAa,SACvC,0BAAQvB,GAAG,oBAAoBuB,UAAU,0BAA0BE,QAASd,GAAiB,gBAAC,EAAAW,iBAAgB,CAACtB,GAAG,gCAnBhH,EAuBO1E,GAA0B,IAAAoG,SACrC,SAAC,GAAqB,OAAG,CAAH,EACtB,SAACC,GAAa,OACZhB,gBAAiB,WACf,EAAAG,SAASC,cAAca,YAAY,CACjC5B,GAAI,eACJiB,OAAQ,CACNC,SAAU,KAEZW,OAAQ,CACNC,IAAK,uBAGTH,EAAS,EAAAI,QAAQxE,kBACnB,EACAI,cAAe,WACb,EAAAmD,SAASC,cAAca,YAAY,CACjC5B,GAAI,eACJiB,OAAQ,CACNC,SAAU,KAEZW,OAAQ,CACNC,IAAK,oBAGTH,EAAS,EAAAI,QAAQpE,cAAc,GACjC,EAxBY,EAFuB,CA4BrC,GClDW,EAAmF,SAAC,GAAD,IAJ7EuF,EAKjBC,EAAQ,WACIC,EAAQ,oBACpBC,EAAW,cACXC,EAAW,cAELC,EAAqB,EAAAC,MAAMC,iBAAmB,EAAAC,aAAaC,aAC3DC,ELqBD,SAAqBR,GAC1B,QAAQ,GACN,KAAKA,EAASS,QAAQ,UAAY,EAAG,MAAO,SAC5C,KAAKT,EAASS,QAAQ,WAAa,EAAG,MAAO,eAC7C,KAAKT,EAASS,QAAQ,WAAa,EAAG,MAAO,cAC7C,KAAKT,EAASS,QAAQ,YAAc,EAAG,MAAO,WAC9C,KAAKT,EAASS,QAAQ,MAAQ,EAAG,MAAO,KACxC,QAAS,MAAO,GAEpB,CK9BeC,CAAYV,GACnBzJ,EAAM,UAAGiK,EAAI,gBAZFV,EAYmBC,GAX5BD,GAAO,IAAIa,QAAQ,UAAW,IAAIC,gBAWG,OAC3C,CAAC,EAAAN,aAAaO,OAAQ,EAAAP,aAAaC,cAAcE,QAAQ,EAAAL,MAAMC,iBAA0B,GLmCpF3I,EKlCyB,IAAM,IAEhCoJ,EAAwD,4CAA3CC,OAAOC,SAAShB,SAASiB,cAA+D,iBAA+D,qCAA3CF,OAAOC,SAAShB,SAASiB,cAAwD,mBAAqB,OACrO,OAAO,gBAAC,EAAApC,QAAO,KACZ,SAAC,G,IAAYE,EAAO,iBAAS,iCAAQZ,UAAU,sDAAsDiB,KAAK,UACzG,uBAAKjB,UAAU,oHACb,uBAAKA,UAAU,gFACXgC,GAAsB,qBAAGvD,GAAG,OAAOyB,QAAS,SAAC6C,GAAM,OAAAjB,EAAYiB,EAAG,OAAf,EAAsB,aAAcJ,EAAW7B,KAAMF,EAAQoC,QAAShD,UAAU,qEACnI,wBAAMA,UAAU,oDAAmD,cAAa,SAChF,wBAAMA,UAAU,yDAAwD,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,YAGjG,uBAAKuB,UAAU,mHAAkH,YAAW,aAC1I,uBAAKA,UAAU,uCACb,sBAAIA,UAAU,wFACZ,gBAAC,EAAAC,qBAAoB,CAACxB,GAAI,wBAAiBrG,OAE3C4J,GAAsB,gBAAC,EAAA/B,qBAAoB,CAACxB,GAAI,yBAAkBrG,IAEhE,SAACuJ,GAAgB,uBAAC,EAAAxC,WAAW3E,QAAO,CAACyI,OAAQtB,GAAOA,IAAQ,yBAAkBvJ,IAC5E,qBAAG4H,UAAU,wEAAwEkD,wBAAyB,CAAEC,OAAQxB,KADzG,KAOzB,uBAAK3B,UAAU,6DACb,0BAAQvB,GAAG,OAAOyB,QAAS,WAAM,OAAA6B,EAAY,OAAQC,EAApB,EAAuC,YAAapB,EAAQoC,QAAShD,UAAU,6CAA4C,gBAAC,EAAAD,iBAAgB,CAACtB,GAAI,gBAvB1J,EA4BlC,EAEazE,GAAS,IAAAmG,SACpB,SAAC,GAA4B,OAAGiD,OAAvB,SAAoB,EAC7B,SAAChD,GAAa,OACZ0B,YAAa,SAACiB,EAAGxC,GACfwC,EAAEM,iBACFN,EAAEO,kBACFlD,EAAS,EAAAI,QAAQ5E,YAAY2E,GAC/B,EACAwB,YAAa,SAACtD,EAAYuD,GACA5B,EAApB4B,EAA6B,EAAAxB,QAAQxE,kBAC3B,EAAAwE,QAAQzE,aAAa,CAAEyF,WAAY,EAAaC,KAAM,CAAEC,WAAYjD,KACpF,EATY,EAFM,CAapB,GC1EA,EACE,EAAAU,WAAU,MAUD,EAAkB,oBAEzB,EAA8D,SAAC,G,IACnEC,EAAe,kBACfhD,EAAa,gBACT,uBAAC,EAAK,CACViD,QAAS,EACTC,QAAS,WACP,EAAAC,SAASC,cAAcC,cAAc,CACnChB,GAAI,eACJiB,OAAQ,CACNC,SAAU,KAEZC,OAAQrB,EAAaC,mBAAmB,2BACxCqB,OAAQtB,EAAaC,mBAAmB,2BAE5C,EACAsB,MAAO,gBAAC,EAAAC,iBAAgB,CAACtB,GAAG,6BAC5B,uBAAKuB,UAAU,UACb,gBAAC,EAAAC,qBAAoB,CAACxB,GAAG,4BAE3B,uBAAKuB,UAAU,uBAAsB,cAAa,SAClD,uBAAKA,UAAU,qGACb,0BAAQvB,GAAG,qBAAqBuB,UAAU,0BAA0BE,QAASd,GAAiB,gBAAC,EAAAW,iBAAgB,CAACtB,GAAG,gCACnH,uBAAKuB,UAAU,YAAW,cAAa,SACvC,0BAAQvB,GAAG,kBAAkBuB,UAAU,0BAA0BE,QAAS9D,GAAe,gBAAC,EAAA2D,iBAAgB,CAACtB,GAAG,8BApB5G,EAwBOxE,GAA2B,IAAAkG,SACtC,SAAC,GAAqB,OAAG,CAAH,EACtB,SAACC,GAAa,OACZhB,gBAAiB,WAAM,OAAAgB,EAAS,EAAAI,QAAQrE,mBAAjB,EACvBC,cAAe,WAAM,OAAAgE,EAAS,EAAAI,QAAQpE,cAAc,GAA/B,EAFT,EAFwB,CAMtC,GC9CA,EACE,EAAA+C,WAAU,MAcD,EAAkB,EAAAoE,QAAQC,aAEjC,EAA8D,SAAC,G,IACnEC,EAAM,SACNC,EAAa,gBACbC,EAAiB,oBACjBvE,EAAe,kBACfhD,EAAa,gBACbwH,EAAe,kBACX,uBAAC,EAAK,CACVvE,QAAS,EACTW,UAAW,mBACX6D,UAAWD,EACX9D,MAAO,gBAAC,EAAAC,iBAAgB,CAACtB,GAAI,UAAG,EAAO,aACtCgF,GAAU,gBAAC,EAAAK,aAAY,CAACC,OAAQ,EAAAhF,YAAYiF,QAASC,KAAM,EAAAC,YAAYC,QAASC,YAAY,IAAAC,SAAgBX,EAAe,UAC5H,uBAAK1D,UAAU,WAAU,cAAa,SACtC,uBAAKA,UAAU,uBAAsB,cAAa,SAClD,uBAAKA,UAAU,qGACb,0BAAQvB,GAAI,0BAAmB,GAAW6F,UAAWX,EAAmB3D,UAAU,0BAA0BE,QAASd,GAAiB,gBAAC,EAAAW,iBAAgB,CAACtB,GAAI,UAAG,EAAO,gBACtK,wBAAMuB,UAAU,YAAW,cAAa,SACxC,0BAAQvB,GAAI,uBAAgB,GAAWuB,UAAU,4BAA4BE,QAAS9D,GAAe,gBAAC,EAAA2D,iBAAgB,CAACtB,GAAI,UAAG,EAAO,cAXnI,EAeOvE,GAAkB,IAAAiG,SAC7B,SAAC,G,IAAEoE,EAAY,eAAEC,EAAO,UAAoB,OAC1Cd,eAAe,IAAAW,SAAgCG,EAAS,gBAAiB,MACzEb,qBAAqB,IAAAU,SAAQG,EAAS,cAAc,GACpDf,OAAQc,GAAgBA,EAAaE,WAAa,EAHR,EAK5C,SAACrE,GAAa,OACZhB,gBAAiB,WACfgB,EAAS,EAAAI,QAAQpE,cAAc,IAC/BgE,EAAS,EAAAI,QAAQkE,gBAAgB,EAAAlE,QAAQnE,cAC3C,EACAD,cAAe,WACb,EAAAmD,SAASC,cAAca,YAAY,CACjC5B,GAAI,kBACJiB,OAAQ,CACNC,SAAU,KAEZW,OAAQ,CACNC,IAAK,uBAAgB,MAGzBH,EAAS,EAAAI,QAAQpE,cAAc,GACjC,EACAwH,gBAAiB,WACf,EAAArE,SAASC,cAAca,YAAY,CACjC5B,GAAI,kBACJiB,OAAQ,CACNC,SAAU,KAEZW,OAAQ,UAEVF,EAAS,EAAAI,QAAQmE,gBAAgB,IACnC,EA1BY,EANe,CAkC7B,GCxEWxK,GAA+B,SAAC,G,IAAE2F,EAAK,QAMlD,OALA,YAAgB,WRwChB8E,eAAeC,QAAQ,0BAA2B,OQrChDC,SAAShF,MAAQ,UAAGA,EAAK,cAAMgF,SAAShF,MAC1C,EAAG,IACI,gBAAC,EAAAgE,aAAY,CAACC,OAAQ,EAAAhF,YAAYgG,aAC3C,ECRa3K,GAAgC,SAAC,G,IAAE0F,EAAK,QAGnD,OADAgF,SAAShF,MAAQ,UAAGA,EAAK,cAAMgF,SAAShF,OACjC,gBAAC,EAAAgE,aAAY,CAACC,OAAQ,EAAAhF,YAAYqD,aAAc6B,KAAM,EAAA9B,aAAaC,cAC5E,ECJa/H,GAA4B,SAAC,G,IAAEyF,EAAK,QAG/C,OADAgF,SAAShF,MAAQ,UAAGA,EAAK,cAAMgF,SAAShF,OACjC,gBAAC,EAAAgE,aAAY,CAACC,OAAQ,EAAAhF,YAAYZ,UAC3C,ECJa7D,GAA0B,SAAC,G,IAAEwF,EAAK,QAG7C,OADAgF,SAAShF,MAAQ,UAAGA,EAAK,cAAMgF,SAAShF,OACjC,gBAAC,EAAAgE,aAAY,CAACC,OAAQ,EAAAhF,YAAY2D,OAAQuB,KAAM,EAAA9B,aAAaO,QACtE,ECJanI,GAAsB,SAAC,G,IAAEuF,EAAK,QAGzC,OADAgF,SAAShF,MAAQ,UAAGA,EAAK,cAAMgF,SAAShF,OACjC,gBAAC,EAAAgE,aAAY,CAACC,OAAQ,EAAAhF,YAAYxE,IAC3C,E,QCNEC,GAEE,EAAA2E,WAAU,QADZ1E,GACE,EAAA0E,WAAU,SAQRzE,GAA6C,SAAC,G,IAClD8J,EAAO,UACPb,EAAiB,oBACjBvE,EAAe,kBACX,uCACJ,gBAAC5E,GAAO,CAACyI,MAAM,IAAAoB,SAAQG,EAAS,MAAM,IACpC,gBAAChK,GAAO,CAACyI,MAAM,IAAAoB,SAAQG,EAAS,oBAC9B,uBAAKxE,UAAU,gEACb,qBAAGA,UAAU,wCAAuC,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,eACzE,wBAAMuB,UAAU,2DACd,gBAACvF,GAAQ,CAAC2C,OAAO,IAAAiH,SAAQG,EAAS,wBAAyB,GAAIQ,SAAS,EAAMC,gBAAiB,yBAA0BC,kBAAmB,iBAIlJ,gBAAC1K,GAAO,CAACyI,MAAM,IAAAoB,SAAQG,EAAS,gBAC9B,uBAAKxE,UAAU,iEACb,qBAAGA,UAAU,wCAAuC,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,WACzE,wBAAMuB,UAAU,2DACd,gBAACvF,GAAQ,CAAC2C,OAAO,IAAAiH,SAAQG,EAAS,oBAAqB,GAAIQ,SAAS,EAAMC,gBAAiB,yBAA0BC,kBAAmB,kBAKhJ,uBAAKlF,UAAU,sEACb,0BAAQE,QAASd,EAAiBkF,UAAWX,EAAmBlF,GAAG,mBACjEuB,UAAW,sDAA+C2D,EAAoB,GAAK,aACnF,gBAAC,EAAA5D,iBAAgB,CAACtB,GAAG,mBACrB,gBAACjE,GAAO,CAACyI,MAAM,IAAAoB,SAAQG,EAAS,wBAAwB,IACtD,wBAAMxE,UAAU,uFACd,6BAAO,IAAAqE,SAAQG,EAAS,uBAAwB,QAzBpD,EAoCN,MC5CE,GAEE,EAAArF,WAAU,QADZ,GACE,EAAAA,WAAU,SAERxE,GAAmB,eACjBwK,GAAQ,EAAAlD,MAAMC,gBAAkB,IAAIM,QAAQ,IAAK,IAAIC,cACrD2C,EAAY,kBAAWD,GACvB,EfiKD,SAAgBxI,EAAGiB,GAAnB,IAGD/H,EAAe4C,EAAGuF,EAAS+E,EAF3BsC,EAAsB,mBAAXnI,QAAyBP,EAAEO,OAAOoI,UACjD,IAAKD,EAAG,OAAO1I,EACX9G,EAAIwP,EAAEpI,KAAKN,GAAOqB,EAAK,GAC3B,IACI,WAAc,IAANJ,GAAgBA,KAAM,MAAQnF,EAAI5C,EAAE0P,QAAQC,MAAMxH,EAAGyH,KAAKhN,EAAE2E,MACxE,CACA,MAAOsI,GAAS3C,EAAI,CAAE2C,MAAOA,EAAS,CACtC,QACI,IACQjN,IAAMA,EAAE+M,OAASH,EAAIxP,EAAU,SAAIwP,EAAEpI,KAAKpH,EAClD,CACA,QAAU,GAAIkN,EAAG,MAAMA,EAAE2C,KAAO,CACpC,CACA,OAAO1H,CACT,CehLQ,CAAuB,YAAe,GAAM,GAA3C2H,EAAK,KAAEC,EAAW,KACzB,OAAO,0BAAQnH,GAAG,eAChByB,QAAS,WAAM,OAAA0F,GAAaD,EAAb,EACfE,QAAS,WAAM,OAAAD,GAAaD,EAAb,EACfG,YAAa,WAAM,OAAAF,GAAY,EAAZ,EACnBG,WAAY,WAAM,OAAAH,GAAY,EAAZ,EAClB5F,UAAU,kFAAiF,gBAC7E,QACd,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,aACrB,gBAAC,GAAO,CAACwE,KAAM0C,GACb,gBAAC,EAAA5F,iBAAgB,CAACtB,GAAI2G,GAElB,SAACzD,GAAa,OAAAqE,QAAQrE,IAAQA,IAAQyD,EACpC,uBAAKpF,UAAU,mCAAmCiB,KAAK,UAAUxC,GAAG,gBAAgBwH,MAAO,CAAEC,SAAU,WAAYC,MAAO,QAASC,IAAK,SAAUC,KAAM,MAAOC,UAAW,qBACxK,uBAAKtG,UAAU,QAAQiG,MAAO,CAAEI,KAAM,SACtC,uBAAKrG,UAAU,gBAAgBiG,MAAO,CAAEE,MAAO,SAC7C,uBAAKnG,UAAU,4BACb,uBAAKA,UAAU,mDACb,wBAAMA,UAAU,+BAChB,wBAAMA,UAAU,qBAElB,uBAAKA,UAAU,gBAAe,gBAAC,EAAAD,iBAAgB,CAACtB,GAAI2G,QAGjD,4BAZG,IAiBxB,EASMxK,GAAoC,SAAC,G,IACzC2L,EAAK,QACLC,EAAK,QACLC,EAAY,eACZ,IAAAzG,UAAAA,OAAS,IAAG,KAAE,EACV,8BAAKA,UAAW,mEAAqEA,GACzF,qBAAGA,UAAU,+BAA+BuG,GAC5C,2BACE,gBAAC,GAAO,CAACtD,MAAM,IAAAoB,SAAQoC,OAActP,GAAW,IAC9C,qBAAG6I,UAAU,+BAA8B,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,yBAElE,wBAAMuB,UAAU,2DACd,gBAAC,GAAQ,CAAC5C,OAAO,IAAAiH,SAAQmC,EAAO,QAAS,GAAIxB,SAAS,EAAMC,gBAAiB,yBAA0BC,kBAAmB,eAE5H,gBAAC,GAAO,CAACjC,MAAM,IAAAoB,SAAQoC,OAActP,GAAW,IAC9C,qBAAG6I,UAAU,+BACX,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,gBAAgBiI,QAAQ,IAAArC,SAAQoC,OAActP,QAXrE,EAmCA,GAAyF,SAAC,GAAD,IAC7FqN,EAAO,UAEP3B,GADQ,WACA,YACRc,EAAiB,oBACjBgD,EAAc,iBACdC,EAAa,gBACbxH,EAAe,kBAEfyH,GADiB,oBACR,aAGHC,EAAWlE,OAAOC,SAAS/B,KAAKwB,QAAQ,EAAAH,aAAa5H,KAAO,EAC5DwM,IAAclE,EAAShB,SAASS,QAAQ,EAAAH,aAAaO,QAAU,GACnEG,EAAShB,SAASS,QAAQ,EAAAH,aAAaC,cAAgB,GACnD4E,EAAqBlC,SAASmC,eAAe,6BAEnD,OAAO,gBAAC,GAAO,CAAChE,KAAM8D,GAAW,2BAC/B,uBAAK/G,UAAU,iCACb,uBAAKA,UAAU,sDAAsDiG,MAAO,CAACiB,QAAS,QACpF,uBAAKlH,UAAU,6EACb,uBAAKA,UAAU,oBACb,gBAAC,GAAO,CAACiD,MAAM,IAAAoB,SAAQG,EAAS,YAAY,IAC1C,gBAAC,GAAO,CAACvB,MAAM,IAAAoB,SAAQG,EAAS,2BAC9B,gBAAC5J,GAAU,CAAC2L,MAAO,gBAAC,EAAAxG,iBAAgB,CAACtB,GAAG,YACtC+H,OAAO,IAAAnC,SAAQG,EAAS,0BACxBiC,cAAc,IAAApC,SAAQG,EAAS,oCAEnC,gBAAC,GAAO,CAACvB,MAAM,IAAAoB,SAAQG,EAAS,uBAC9B,gBAAC5J,GAAU,CAAC2L,MAAO,gBAAC,EAAAxG,iBAAgB,CAACtB,GAAG,gBACtCuB,UAAU,WACVwG,OAAO,IAAAnC,SAAQG,EAAS,sBACxBiC,cAAc,IAAApC,SAAQG,EAAS,iCAGrC,gBAAC,GAAO,CAACvB,MAAM,IAAAoB,SAAQG,EAAS,MAAM,IACpC,gBAAC,GAAO,CAACvB,MAAM,IAAAoB,SAAQG,EAAS,qBAiB9B,gBAAC5J,GAAU,CAAC2L,MAAO,gBAAC,EAAAxG,iBAAgB,CAACtB,GAAG,cACtC+H,OAAO,IAAAnC,SAAQG,EAAS,oBACxBiC,cAAc,IAAApC,SAAQG,EAAS,8BAEnC,gBAAC,GAAO,CAACvB,MAAM,IAAAoB,SAAQG,EAAS,iBAC9B,gBAAC5J,GAAU,CAAC2L,MAAO,gBAAC,EAAAxG,iBAAgB,CAACtB,GAAG,UACtCuB,UAAU,WACVwG,OAAO,IAAAnC,SAAQG,EAAS,gBACxBiC,cAAc,IAAApC,SAAQG,EAAS,4BAIvC,gBAAC,GAAO,CAACvB,MAAM,IAAAoB,SAAQG,EAAS,kBAC9B,uBAAKxE,UAAU,6CACb,0BAAQvB,GAAG,cAAcyB,QAAS,WAAM,OAAAyG,EAAe,cAAf,EAA+B3G,UAAU,qFAAoF,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,eAG9L,gBAAC,GAAO,CAACwE,MAAM,IAAAoB,SAAQG,EAAS,gBAC9B,uBAAKxE,UAAU,qCACb,0BAAQvB,GAAG,cAAcyB,QAAS,WAAM,OAAA0G,EAAc,cAAd,EAA8B5G,UAAU,+DAA8D,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,aAGvK,uBAAKuB,UAAU,qBAAoB,cAAa,SAChD,uBAAKA,UAAU,aACf,uBAAKA,UAAU,2FACb,gBAAC,GAAO,CAACiD,KAAM6D,GACb,uBAAK9G,UAAU,gFACb,0BAAQvB,GAAG,gBAAgBuB,UAAU,iCAAiCE,QAAS2G,GAC7E,gBAAC,EAAA9G,iBAAgB,CAACtB,GAAG,qBAI3B,uBAAKuB,UAAU,sEACb,gBAAC,GAAO,CAACiD,KAAMU,EAAmBwD,YAAa,gBAACxM,GAAgB,OAC9D,0BAAQuF,QAASd,EACfX,GAAG,gBAAgBuB,UAAU,sFAC7B,gBAAC,EAAAD,iBAAgB,CAACtB,GAAG,aACrB,gBAAC,GAAO,CAACwE,KAAM6D,IAAY,IAAAzC,SAAQG,EAAS,wBAAwB,IAClE,wBAAMxE,UAAU,uFACd,6BAAO,IAAAqE,SAAQG,EAAS,uBAAwB,aAWhEwC,GACA,gBAAsB,gBAAC,GAAe,CAACxC,QAASA,EAASb,kBAAmBA,EAAmBvE,gBAAiBA,IAAqB4H,IAI3I,EACanM,IAAe,IAAAsF,SAC1B,SAAC,G,IAAEqE,EAAO,UAAoB,OAC5BA,QAAO,EACPb,qBAAqB,IAAAU,SAAQG,EAAS,cAAc,GAFxB,EAI9B,SAACpE,GAAa,OACZuG,eAAgB,SAAClI,GAAY,OAAA2B,EAAS,EAAAI,QAAQzE,aAAa,CAAEyF,WAAY,EAAgBC,KAAM,CAAEC,WAAYjD,EAAIgG,SAAU,KAA9F,EAC7BmC,cAAe,SAACnI,GAAY,OAAA2B,EAAS,EAAAI,QAAQzE,aAAa,CAAEyF,WAAY,EAAcC,KAAM,CAAEC,WAAYjD,KAA9E,EAC5B2I,kBAAmB,WAAM,OAAAhH,EAAS,EAAAI,QAAQkE,gBAAgB,EAAAlE,QAAQ6G,0BAAzC,EACzBjI,gBAAiB,WAAM,OAAAgB,EAAS,EAAAI,QAAQkE,gBAAgB,EAAAlE,QAAQnE,cAAzC,EACvBwK,UAAW,WAAM,OAAAzG,EAAS,EAAAI,QAAQkE,gBAAgB,EAAAlE,QAAQqG,WAAU,IAAnD,EALL,EALY,CAY1B,ICtMA/L,GACE,EAAAqE,WAAU,iBAGZpE,GAEE,EAAAyF,QAAO,aADTxF,GACE,EAAAwF,QAAO,qBAYLvF,GAAuC,SAACqM,GAAD,IACrCzE,GAAW,IAAA0E,eACAC,EAA+B,aAAiB,EAAAC,eAAc,4BAC/E,OAAO,gBAAC,WAAc,KACpB,6BACG,uTAUH,gBAACzN,EAAM,KAAKsN,EAAK,CAAEzE,SAAUA,KAE7B,gBAAC,EAAA6E,MAAK,CAACrF,KAAK,IAAIsF,UAAW,SAAC,GAI1B,Of1B6BC,EesBM,UfrBvCzO,EAAWA,GAAYyO,EeyBZ,Kf1BN,IAA4BA,Ce2B/B,IACA,gBAAC,EAAAC,OAAM,KACL,gBAAC,EAAAH,MAAK,CAACrF,KAAM,CACX,sCACA,iCAEA,gBAAClI,GAAW,CAAC2F,MAAM,iBAErB,gBAAC,EAAA4H,MAAK,CAACrF,KAAM,CACX,iCACA,2BACA,iBACA,mBAEA,gBAAC/H,GAAM,CAACwF,MAAM,YAEhB,gBAAC,EAAA4H,MAAK,CAACrF,KAAM,CACX,uCACA,iCACA,uBACA,yBAEA,gBAACjI,GAAY,CAAC0F,MAAM,kBAItB,gBAAC,EAAA4H,MAAK,CAACrF,KAAM,CACX,0BACA,qBAEA,gBAAChI,GAAQ,CAACyF,MAAM,cAElB,gBAAC,EAAA4H,MAAK,CAACrF,KAAM,CACX,oBACA,aACA,YAEA,gBAAC9H,GAAE,CAACuF,MAA0C,OAAlC0H,EAAqBM,SAAoB,8BAAgC,8BAGvF,gBAAC,EAAAJ,MAAK,CAACrF,KAAK,KACV,gBAAC,EAAA0F,SAAQ,CAACC,GAAIV,EAAMW,iBAGxB,gBAACpN,GAAY,KAAKyM,EAAK,CAAEzE,SAAUA,KACnC,gBAAC/I,EAAM,MACP,gBAACI,EAAe,MAChB,gBAACY,GAAgB,CAAC2D,GAAG,iCACrB,gBAACxE,EAAwB,MACzB,gBAACF,EAAuB,MACxB,gBAACF,EAAyB,MAC1B,uBAAKmG,UAAU,8BAA6B,cAAa,SACzD,uBAAKA,UAAU,8BAA6B,cAAa,SAE7D,EAEA,4B,8CAYA,QAZwB,OACtB,YAAAkI,kBAAA,SAAkBC,GAChB1Q,KAAK6P,MAAMc,mBAAmBD,EAChC,EAEA,YAAAE,kBAAA,WACE5Q,KAAK6P,MAAMtM,qBAAqB,+BAClC,EAEA,YAAAsN,OAAA,WACE,OAAQ,gBAAC,EAAAC,cAAa,CAACC,SAAS,aAAY,gBAACvN,GAAS,KAAKxD,KAAK6P,QAClE,EACF,EAZA,CAAwB,aAcXpM,IAAc,IAAAiF,SACzB,SAAC,GAA4C,OAAG8H,aAAjC,eAA+CrG,SAArC,WAAoB,EAC7C,SAACxB,GAAa,OACZgI,mBAAoB,SAAC1C,GAAe,OAAAtF,EAASrF,GAAa2K,GAAtB,EACpC1K,qBAAsB,WAAM,OAAAoF,EAASpF,KAAT,EAFhB,EAFW,CAMzB,ICnIAG,GACE,EAAAgE,WAAU,gBAED/D,GAAM,WAAM,uBAACD,GAAe,KACvC,gBAACD,GAAW,MADW,ECLjBG,GAA+B,EAAAiD,eAAc,WAAjChD,GAAmB,EAAAgD,eAAc,eAmBrD,4B,8CASA,QAT4B,OACN,GAAnBhD,GAAe,I,wDACI,GAAnBA,GAAe,CAAC,G,oEACG,GAAnBA,GAAe,CAAC,G,wDACG,GAAnBA,GAAe,CAAC,G,uDACkC,GAAlDA,GAAe,CAAEmN,KAAM,0B,mDACJ,GAAnBnN,GAAe,I,4DACI,GAAnBA,GAAe,CAAC,G,uDACG,GAAnBA,GAAe,I,mEARC,GADlB,EAAA4D,YACYwJ,E,CAAb,CAA4BrN,I,8BChBfE,IAAc,KAAAoN,cAAwB,gBqBO5C,SAAuBxH,GAG5B,OADAyD,eAAeC,QAAQ,eAAgB1D,GAC/BA,GACN,KAAK,EAAAjD,UAAUC,SACb,EAAAoB,SAASC,cAAcoJ,cAAc,CACnCC,OAAQ,aAEV,MACF,KAAK,EAAA3K,UAAU3D,GACb,EAAAgF,SAASC,cAAcoJ,cAAc,CACnCC,OAAQ,mBAEV,MACF,KAAK,EAAA3K,UAAUE,MACb,EAAAmB,SAASC,cAAcoJ,cAAc,CACnCC,OAAQ,WAEV,MACF,KAAK,EAAA3K,UAAUG,OACb,EAAAkB,SAASC,cAAcoJ,cAAc,CACnCC,OAAQ,WAOd,OAAO1H,CACT,GrBnCa3F,IAAmB,KAAAmN,cAAuB,0BqBJhD,SAA8BlH,GAA9B,IACCqH,GAAgB,IAAAzE,SAAQ5C,EAAM,gBAAiB,IAMrD,OAL0B,OACrBA,GAAI,CACPpH,SAAUyO,EAAcC,KAAK,SAACvC,GAAgC,MAAmB,aAAnBA,EAAM5E,QAAN,GAC9DrH,GAAIuO,EAAcC,KAAK,SAACvC,GAAgC,MAAmB,OAAnBA,EAAM5E,QAAN,IAG5D,GrBHanG,IAAoB,KAAAkN,cAAqC,iCCKtE,eACE,WAAYK,EAA0BrI,GACpC,SAAK,UAACqI,EAAYrI,IAAO,IAC3B,CACF,OAJ4B,OAAT,GADlB,EAAAzB,W,uBAEyB,EAAA+J,aAAsBP,MADnCQ,E,CAAb,CAA4B,EAAAC,Y,UCC1BzN,GAYE,EAAA8E,QAAO,eAXT7E,GAWE,EAAA6E,QAAO,aAVT5E,GAUE,EAAA4E,QAAO,YATT3E,GASE,EAAA2E,QAAO,eART1E,GAQE,EAAA0E,QAAO,UAPTzE,GAOE,EAAAyE,QAAO,aANTxE,GAME,EAAAwE,QAAO,gBALTvE,GAKE,EAAAuE,QAAO,kBAJTtE,GAIE,EAAAsE,QAAO,gBAHTrE,GAGE,EAAAqE,QAAO,iBAFTpE,GAEE,EAAAoE,QAAO,cADTnE,GACE,EAAAmE,QAAO,WAYX,cAGE,WAAoB4I,EAAwBzI,GAAxB,KAAAyI,OAAAA,EAAwB,KAAAzI,OAAAA,CAAkB,CAkZhE,OAhZE,YAAA0I,aAAA,WACE,OAAO,KAAAA,cACL5R,KAAK6R,cACL7R,KAAK8R,mBACL9R,KAAK+R,gBACL/R,KAAKgS,eACLhS,KAAKiS,oBACLjS,KAAKkS,sBACLlS,KAAKmS,sBACLnS,KAAKoS,qBAET,EAUA,sBAAY,oCAAqB,C,IAAjC,sBACE,OAAO,SAACC,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAAS1F,GAAkB0O,UAAlC,IAChC,KAAAF,QAAO,SAAC,G,IAAEG,EAAO,UAAuC,OAAApE,QAAQoE,EAAR,IACxD,KAAAC,UAAS,SAAC,G,IAAED,EAAO,UACjB,YAAAE,QACE,CAACpO,GAAgB,EAAKqO,YAAc,EAAAC,cAAcC,WAClD,EAAKrB,OAAOc,OAAmDE,GAASJ,MACtE,KAAAK,UAAS,SAACK,GAAa,WAAAC,6BAA4BD,EAAU,CAC3D5O,GAAU4O,EAASjJ,KAAKmJ,iBADH,IAH3B,IASF,KAAAC,YAAW,EAAAC,OAAOC,uBAAuBtP,KAb3C,CAeJ,E,gCAWA,sBAAY,4BAAa,C,IAAzB,WACE,OAAO,SAACqO,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAASrF,GAAUqO,UAA1B,IAChC,KAAAF,QAAO,SAAC,GAAgC,MAAoB,iBAA3C,SAAuB,IACxC,KAAAI,UAAS,SAAC,GAAD,IA8FC,EAUA,EAxGED,EAAO,UACXxI,EAAcmI,EAAe3M,MAAK,SACpC4N,EAAcZ,EACda,EAAmB,GACvB,OAAQb,GACN,IAAK,cACL,KAAK,EAAAjI,aAAa4C,YAChBiG,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa4C,aACpDkG,EAAmB,EAAA9I,aAAa4C,YAChC,MACF,IAAK,kBACL,IAAK,YACL,IAAK,gBACL,IAAK,SACL,KAAK,EAAA5C,aAAaO,OAChBsI,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAaO,QACpDuI,EAAmB,EAAA9I,aAAaO,OAChC,MACF,IAAK,wBACL,IAAK,kBACL,IAAK,sBACL,IAAK,eACL,KAAK,EAAAP,aAAaC,aAChB4I,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAaC,cACpD6I,EAAmB,EAAA9I,aAAaC,aAChC,MACF,IAAK,gBACH4I,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAaO,OAAQ,EAAAxE,UAAUE,OACtE6M,EAAmB,EAAA9I,aAAaO,OAChC,MACF,IAAK,cACL,KAAK,EAAAP,aAAahE,SAChB6M,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAahE,UACpD8M,EAAmB,EAAA9I,aAAahE,SAChC,MACF,IAAK,QACL,KAAK,EAAAgE,aAAa5H,GAChByQ,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IACpD0Q,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,IAAK,SACHyQ,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,GAAI,EAAA2D,UAAUE,OAClE6M,EAAmB,EAAA9I,aAAa5H,GAChCqH,EAAW,EAAA1D,UAAUE,MACrB,MACF,IAAK,YACH4M,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,GAAI,EAAA2D,UAAUG,QAClE4M,EAAmB,EAAA9I,aAAa5H,GAChCqH,EAAW,EAAA1D,UAAUG,OACrB,MACF,KAAK,EAAA8M,KAAKC,iBAAiBC,iBACzBL,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAamJ,YACvEL,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,KAAK,EAAA4Q,KAAKC,iBAAiBG,OACzBP,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAaqJ,UACvEP,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,KAAK,EAAA4Q,KAAKC,iBAAiBK,SACzBT,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAauJ,YACvET,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,KAAK,EAAA4Q,KAAKC,iBAAiBO,MACzBX,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAayJ,gBACvEX,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,KAAK,EAAA4Q,KAAKC,iBAAiBS,cAC3B,KAAK,EAAAV,KAAKC,iBAAiBU,qBACzBd,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAa4J,uBACvEd,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,KAAK,EAAA4Q,KAAKC,iBAAiBY,uBACzBhB,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAa8J,yBACvEhB,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,KAAK,EAAA4Q,KAAKC,iBAAiBc,cACzBlB,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAagK,UACvElB,EAAmB,EAAA9I,aAAa5H,GAChC,MACF,IAAK,YACHyQ,EAAc,EAAA/I,MAAMiJ,mBAAmB,EAAA/I,aAAa5H,IAAM,EAAA4H,aAAaiK,UACvEnB,EAAmB,EAAA9I,aAAa5H,GAChC,MAEF,IAAK,oBACL,IAAK,cACH,MAAO,CACLyB,MAMN,OAAI,EAAAiG,MAAMC,iBAAmB,EAAAC,aAAa5H,IAAM0Q,IAAqB,EAAA9I,aAAa5H,IAC1E,EpBlJTjB,EoBmJGsJ,OAAOyJ,sBAAsB,WAAM,SAAQ5G,KAAKuF,EAAYxI,QAAQ,qDAAsD,IAAvF,GAC5B,CACL9G,GAAe,QAER,EAAAuG,MAAMC,iBAAmB,EAAAC,aAAahE,UAAY8M,IAAqB,EAAA9I,aAAahE,SACtF,CACLzC,GAAe,QAGX,EAAUxC,IAChB0J,OAAOyJ,sBAAsB,WAAM,SAAQ5G,KAAKuF,EAAb,GAC5B,CACLzP,GAAYqG,IAGlB,GAjHF,CAmHJ,E,gCAgBA,sBAAY,iCAAkB,C,IAA9B,WACE,OAAO,SAACkI,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GACN,OAAAA,EAAO/I,OAAStF,GAAesO,YAC/BD,EAAO/I,OAASxF,GAAawO,UAD7B,IAGF,KAAAE,UAAS,eAeC,EACAiC,EAfF1E,EAAU1O,IACVqT,EAAaxC,EAAO3M,MAClBgG,EAAWmJ,EAAK,OAClBC,GAAa,IAAAnI,SAAgCkI,EAAO,qBAAsB,CAAC,GACjF,QAAQ,GACN,KAAKvG,QAAQwG,EAAW1L,MACtB,MAAO,CACLrF,GAAkB+Q,IAEtB,KAAKxG,QAAQwG,EAAW5B,gBACtB,MAAO,CACL9O,GAAU0Q,EAAW5B,iBAEzB,QAIE,OAHI,EAAQhD,EAAQ/E,SAAShB,UACzByK,EAAQlJ,EAAOqJ,UAAU,SAAChU,GAAc,SAAM6J,QAAQ7J,IAAM,CAApB,MAE9B2K,EAAO5K,OAAS,IAK9B,EAAQ4K,EADRkJ,GAAS,GAET1E,EAAQnC,KAAK,IALJ,GAQf,GAhCF,CAkCJ,E,gCAUA,sBAAY,6BAAc,C,IAA1B,WACE,OAAO,SAACqE,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAAS9E,GAAW8N,UAA3B,IAChC,KAAAE,UAAS,eAqBDqC,EACAJ,EAIEK,EAzBF/E,EAAU1O,IACVqT,EAAaxC,EAAO3M,MAClBgG,EAAWmJ,EAAK,OAClBC,GAAa,IAAAnI,SAAgCkI,EAAO,qBAAsB,CAAC,GAGjF,OAAIC,EAAW5B,gBACN,KAAAgC,IACL9Q,GAAU0Q,EAAW5B,iBAKrB4B,EAAW1L,MACN,KAAA8L,IACLnR,GAAkB+Q,KAKhBE,EAAQ9E,EAAQ/E,SAAShB,UACzByK,EAAQlJ,EAAOqJ,UAAU,SAAChU,GAAc,OAAAiU,EAAMpK,QAAQ7J,IAAM,CAApB,KAGjC,GAAK6T,EAAQlJ,EAAO5K,OAAS,GAClCmU,EAAYvJ,EAAOkJ,EAAQ,GACjC1E,EAAQnC,KAAKkH,IACN,KAAAC,OAIW,EAAA3K,MAAMC,iBACN,EAAAC,aAAaO,QAExB,KAAAkK,IACL9Q,GAAU,EAAAqG,aAAaC,gBAIlB,KAAAwK,IACL9Q,GAAU,EAAAqG,aAAaO,SAG7B,GA9CF,CAgDJ,E,gCAUA,sBAAY,8BAAe,C,IAA3B,WACE,OAAO,SAACoH,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAASvF,GAAYuO,UAA5B,IAChC,KAAAE,UAAS,SAAC,GAAD,IAuBC,EACAiC,EAxBElC,EAAO,UACXxC,EAAU1O,IACVqT,EAAaxC,EAAO3M,MAClBgG,EAAWmJ,EAAK,OAClBM,GAAa,IAAAxI,SAAgCkI,EAAO,qBAAsB,CAAC,GACjF,QAAQ,GACN,KAAKvG,QAAQ6G,EAAW/L,MACtB,MAAO,CACLrF,GAAkBoR,IAEtB,KAAK7G,QAAQ6G,EAAWjC,gBACtB,MAAkC,sBAA9BiC,EAAWjC,gBACmB,gBAA9BiC,EAAWjC,eACN,CACLlP,IAAe,GACfK,GAAa,CAAEyF,WAAY,EAAaC,KAAM,CAAEC,WAAY0I,MAGvD,CACLtO,GAAU+Q,EAAWjC,iBAG3B,QAIE,OAHI,GArUA1R,IACY2J,SAAShB,UALpB,IAAIW,QAAQ,iGAAkG,IA4UrG,KAFV8J,EAAQlJ,EAAOqJ,UAAU,SAAChU,GAAc,WAAUA,CAAV,IAGnC,CACLiD,IAAe,GACfK,GAAa,CAAEyF,WAAY,EAAaC,KAAM,CAAEC,WAAY0I,OAIhEkC,GAAS,EACL,KACF,EAAQlJ,EAAOkJ,IACLhK,QAAQ,gBAAkB,IAAMsC,eAAekI,QAAQ,6BAG/D,EAAQ1J,EADRkJ,GAAS,IAGX1E,EAAQnC,KAAK,IAER,IAGb,GAhDF,CAiDJ,E,gCASA,sBAAY,kCAAmB,C,IAA/B,sBACE,OAAO,SAACqE,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAASnF,GAAgBmO,UAAhC,IAChC,KAAAE,UAAS,WAGP,OAFAzF,eAAemI,QACfnK,OAAOC,UAAW,IAAAwB,SAAQ,EAAK1D,OAAQ,kBAAmB,IACnD,CACLjF,IAAe,GAEnB,GARF,CASJ,E,gCAEA,sBAAY,mCAAoB,C,IAAhC,sBACE,OAAO,SAACoO,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAAShF,GAAiBgO,UAAjC,IAChC,KAAAE,UAAS,eACDkC,EAAQxC,EAAO3M,MACfoP,GAAa,IAAAnI,SAAgCkI,EAAO,sBAAuB,CAAC,GAClF,OAAO,KAAAjC,QACL,CAAC5O,IAAe,IAChB,EAAK0N,OAAO4D,KAAKR,EAAW1L,KAAM0L,EAAWS,aAAajD,MACxD,KAAAK,UAAS,SAAC6C,GAAc,OACtB9Q,GAAc,qBACdwG,OAAOC,SAASsK,SAFM,IAM9B,GAdF,CAgBJ,E,gCASA,sBAAY,oCAAqB,C,IAAjC,sBACE,OAAO,SAACrD,EAAcC,GACpB,OAAAD,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAwB,OAAAA,EAAO/I,OAASlF,GAAkBkO,UAAlC,IAChC,KAAAE,UAAS,WAGP,OAFAzF,eAAemI,QACfnK,OAAOC,UAAW,IAAAwB,SAAQ,EAAK1D,OAAQ,oBAAqB,IACrD,CACLjF,IAAe,GAEnB,GARF,CASJ,E,gCApZ0B,GAD3B,EAAAwD,W,uBAI6BgK,GAAwBR,MAHzC0E,E,CAAb,GC7BE,GAEE,EAAA5M,QAAO,gBAKX,cACE,WACS6M,GAAA,KAAAA,gBAAAA,CAEN,CAoBL,OAlBE,YAAAhE,aAAA,WACE,OAAO,KAAAA,cACL5R,KAAK6V,mBAET,EAEA,sBAAY,iCAAkB,C,IAA9B,WACE,OAAO,SAACxD,GACN,OAAAA,EAAQE,MACN,KAAAC,QAAO,SAACC,GAAgB,OAAAA,EAAO/I,OAAS,GAAgBgJ,UAAhC,IACxB,KAAAF,QAAO,SAAC,GAAoD,OAA3C,YAAuD,EAAAO,cAAc+C,IAA1B,IAC5D,KAAAlD,UAAS,WAAM,OACb9O,GAAY,EAAA0G,MAAMuL,eAClB,GAAgB,EAAAhD,cAAciD,UAFjB,GAHjB,CAQJ,E,gCAtBgB,GADjB,EAAAvO,W,uBAG2BkO,MAFfM,E,CAAb,GCDQpR,GAA6C,EAAAgC,eAAc,UAAhD/B,GAAkC,EAAA+B,eAAc,8BAGjE9B,GACED,GAA8B,EAAAiE,SAAQ,eACpC,GAGFjE,GAA8B,GAFhC,GAAW,eACX,GAAgB,oBAIlB,eACE,WAAoB6M,EAAgBuE,EAA0BC,EAAsBC,GAClF,QAAK,UAACF,IAAM,K,OADM,EAAAvE,OAAAA,EAA0C,EAAAwE,MAAAA,EAAsB,EAAAC,aAAAA,E,CAEpF,CAsCF,OAzC2B,OAKzB,sBAAI,sBAAO,C,IAAX,W,YACE,OAAO,KAAAC,iBAAgB,WAElB,EAAAC,SAASC,oBAAoBvW,KAAKoW,eAClC,EAAAE,SAASE,oBACT,EAAAF,SAASG,sBAAoB,CAGhCjG,cAAc,KAAAkG,gBAAa,KACzB,EAAC3R,IAAiB,SAAC+P,EAAO,G,IAAEnC,EAAO,UAA6B,OAACA,GAAWA,EAAQnC,cAAiBsE,CAArC,E,GAC/D,KACH3K,UAAU,KAAAuM,gBAAa,KACrB,EAAC3R,IAAiB,SAAC+P,EAAO,G,IAAEnC,EAAO,UAA6B,OAACA,GAAWA,EAAQxI,UAAa2K,CAAjC,EAChE,EAAC,IAAc,SAACA,EAAO,G,IAAEnC,EAAO,UAA0B,OAACA,GAAWA,GAAYmC,CAAxB,E,GACzD,IACHnJ,QAAQ,KAAA+K,gBAAa,KACnB,EAAC3R,IAAiB,SAAC+P,EAAO,G,IAAEnC,EAAO,UAAoB,OAACA,GAAWA,EAAQxI,UAAYpI,EAAO4Q,EAAQxI,WAA2B2K,CAA1E,EACvD,EAAC,IAAc,SAACA,EAAO,G,IAAEnC,EAAO,UAAoB,OAACA,GAAW5Q,EAAO4Q,IAA0BmC,CAA7C,E,GACnD,IACH/H,SAAS,KAAA2J,gBAAa,KACpB,EAAC,IAAmB,SAAC5B,EAAO,G,IAAEnC,EAAO,UAAyB,OAACA,GAAWA,GAAYmC,CAAxB,E,GAC7D,CAAC,KAER,E,gCASA,sBAAI,0BAAW,C,IAAf,WACE,OAAO,KAAAlD,cAAa5R,KAAKmW,MAAMP,gBAAgBhE,eAAgB5R,KAAKmW,MAAMvE,gBAAgB,IAAI,EAAA+E,YAAa/E,eACzG,IAAI,EAAAgF,kBAAkB5W,KAAK2R,OAAQ,gCAAgCC,gBAAgB,IAAI,EAAAiF,gBAAiBjF,eAC5G,E,gCAxCgB,GADjB,EAAAnK,W,uBAE6BgK,GAAe,QAA0BwE,GAA6BnP,KADvFgQ,E,CAAb,CAA2BjS,ICpBnBG,GAAa,EAAA6B,eAAc,SAUnC,eAoEE,WAAYkQ,GACV,QAAK,UAACA,IAAI,K,OACVC,EAAK7P,SAAW,E,CAClB,CACF,OAxE0B,OACjB,EAAA8P,cAAP,SAAqBf,G,MACnB,OAAO,EAAP,IACG,EAAAnN,QAAQ1E,UAAUqO,YAAa,SAAC,G,IAAEC,EAAO,UAAM,OAC9CuD,EAAMvN,SAAS,EAAAI,QAAQ1E,UAAUsO,GACnC,EACA,EAAC,EAAA5J,QAAQ5E,YAAYuO,YAAa,WAChCwD,EAAMvN,SAAS,EAAAI,QAAQ5E,cACzB,EACA,EAAC,EAAA4E,QAAQ3E,eAAesO,YAAa,WACnCwD,EAAMvN,SAAS,EAAAI,QAAQ3E,iBACzB,EACA,EAAC,EAAA2E,QAAQnE,WAAW8N,YAAa,WAC/BwD,EAAMvN,SAAS,EAAAI,QAAQnE,aACzB,EACA,EAAC,EAAAmE,QAAQmO,kBAAkBxE,YAAa,SAAC,GAAD,IAM9ByE,EACAC,EAPiCzE,EAAO,UAE5CA,GAAWA,EAAQQ,eACrB+C,EAAMvN,SAAS,EAAAI,QAAQ1E,UAAUsO,EAAQQ,kBAGnCgE,EAAe9J,SAASgK,cAAc,kBACtCD,EAAmB/J,SAASgK,cAAc,iCAE3CF,GAAgBA,EAAaG,UAAUC,SAAS,SAChDH,GAAoBA,EAAiBE,UAAUC,SAAS,SAE3DrB,EAAMvN,SAAS,EAAAI,QAAQpE,cAAc,kBACrCuR,EAAMvN,SAAS,EAAAI,QAAQpE,cAAc,iCAErCuR,EAAMvN,SAAS,EAAAI,QAAQ1E,UAAU,YAGjC6R,EAAMvN,SAAS,EAAAI,QAAQ1E,UAAU,WAGvC,EACA,EAAC,EAAA0E,QAAQxE,gBAAgBmO,YAAa,WACpCwD,EAAMvN,SAAS,EAAAI,QAAQxE,kBACzB,EACA,EAAC,EAAAwE,QAAQvE,kBAAkBkO,YAAa,WACtCwD,EAAMvN,SAAS,EAAAI,QAAQvE,oBACzB,EACA,EAAC,EAAAuE,QAAQyO,cAAc9E,YAAa,WAClCwD,EAAMvN,SAAS,EAAAI,QAAQyO,gBACzB,EACA,EAAC,EAAAzO,QAAQ0O,6BAA6B/E,YAAa,SAAC,G,IAClDC,EAAO,UAEPuD,EAAMvN,SAAS5E,GAAiB4O,GAClC,EACA,EAAC,EAAA5J,QAAQpE,cAAc+N,YAAa,SAAC,G,IAAEC,EAAO,UAC5CuD,EAAMvN,SAAS,EAAAI,QAAQpE,cAAcgO,GACvC,EACA,EAAC,EAAA5J,QAAQ2O,mBAAmBhF,YAAa,SAAC,G,IAAEC,EAAO,UvBjCvD9Q,EuBkC2B8Q,GvBlCH9Q,CuBmCpB,EACA,EAAC,EAAAkH,QAAQ4O,sBAAsBjF,YAAa,WvBRhD5Q,GAAwB,CuBUpB,E,CAEJ,EAWF,EAxEA,CAA0BkD,ICJxB,GAEE,EAAA+D,QAAO,eADT,GACE,EAAAA,QAAO,gBAGX,eACE,WAAoBmN,EAAsB0B,EAAmD1O,EAAwBqJ,GACnH,QAAK,YAAE,K,OADW,EAAA2D,MAAAA,EAAsB,EAAA0B,OAAAA,EAAmD,EAAA1O,OAAAA,EAAwB,EAAAqJ,KAAAA,E,CAErH,CA0CF,OA7C6C,OAW3C,YAAAsF,KAAA,WACE7X,KAAKuS,KAAKuF,UAAUd,GAAKC,cAAcjX,KAAKkW,QAC5ClW,KAAKkW,MAAMvN,SAAS,GAAe3I,KAAKkJ,SACxClJ,KAAKkW,MAAMvN,SAAS,GAAe3I,KAAK4X,OAAO/H,QAC/C7P,KAAKkW,MAAMvN,SAAS,GAAgB,EAAAoK,cAAc+C,MACpD,EAOA,YAAAiC,QAAA,WACE/X,KAAKuS,KAAKyF,cACVhY,KAAKkW,MAAM6B,SACb,EAUA,YAAAlH,OAAA,SAAO5S,GAAP,IACUiY,EAAUlW,KAAI,MAChB4K,EAAOO,OAAOC,SAAShB,SAAS6N,MAAM,KAC5Cha,EAAK4S,OACH,gBAAC,EAAAqH,gBAAe,CAACvS,MAAO,CAAEuD,OAAQlJ,KAAKkJ,OAAQsD,KAAM,WAAI5B,EAAKA,EAAK7J,OAAS,MAC1E,gBAAC,WAAa,CAAOmV,MAAK,GAAI,gBAACvS,GAAG,QAGxC,EA5CkC,IADnC,IAAAwU,QAAO,CAAEC,UAAW,a,uBAEQtB,GAAuB,EAAAuB,eAAmDpH,GAAsB+F,MADxGsB,E,CAArB,CAA6C,EAAAC,Y", "sources": ["omf-changepackage-navigation:///webpack/universalModuleDefinition?", "omf-changepackage-navigation:///webpack/bootstrap?", "omf-changepackage-navigation:///./tslib/tslib.es6.mjs?", "omf-changepackage-navigation:///../src/utils/History.ts?", "omf-changepackage-navigation:///../src/Localization.ts?", "omf-changepackage-navigation:///../src/views/modals/ApplicationLogout.tsx?", "omf-changepackage-navigation:///../src/views/footer/index.tsx?", "omf-changepackage-navigation:///../src/views/modals/ApplicationExit.tsx?", "omf-changepackage-navigation:///../src/views/header/index.tsx?", "omf-changepackage-navigation:///../src/views/modals/ApplicationReset.tsx?", "omf-changepackage-navigation:///../src/views/modals/Summary.tsx?", "omf-changepackage-navigation:///../src/views/pages/Appointment.tsx?", "omf-changepackage-navigation:///../src/views/pages/Confirmation.tsx?", "omf-changepackage-navigation:///../src/views/pages/Internet.tsx?", "omf-changepackage-navigation:///../src/views/pages/Review.tsx?", "omf-changepackage-navigation:///../src/views/pages/TV.tsx?", "omf-changepackage-navigation:///../src/views/summary/TvSummaryPortal.tsx?", "omf-changepackage-navigation:///../src/views/summary/index.tsx?", "omf-changepackage-navigation:///../src/views/index.tsx?", "omf-changepackage-navigation:///../src/App.tsx?", "omf-changepackage-navigation:///../src/Config.ts?", "omf-changepackage-navigation:///../src/store/Actions.ts?", "omf-changepackage-navigation:///../src/Client.ts?", "omf-changepackage-navigation:///../src/store/Epics/Navigation.ts?", "omf-changepackage-navigation:///../src/store/Epics.ts?", "omf-changepackage-navigation:///../src/store/Store.ts?", "omf-changepackage-navigation:///../src/Pipe.ts?", "omf-changepackage-navigation:///../src/Widget.tsx?", "omf-changepackage-navigation:///external umd {\"root\":\"ReactDOM\",\"commonjs2\":\"react-dom\",\"commonjs\":\"react-dom\",\"amd\":\"react-dom\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"ReactRouterDOM\",\"commonjs2\":\"react-router-dom\",\"commonjs\":\"react-router-dom\",\"amd\":\"react-router-dom\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}?", "omf-changepackage-navigation:///external umd {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}?", "omf-changepackage-navigation:///webpack/runtime/define property getters?", "omf-changepackage-navigation:///webpack/runtime/hasOwnProperty shorthand?", "omf-changepackage-navigation:///webpack/runtime/make namespace object?", "omf-changepackage-navigation:///../src/mutators/index.ts?"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"bwtk\"), require(\"omf-changepackage-components\"), require(\"react\"), require(\"react-redux\"), require(\"react-router-dom\"), require(\"react-intl\"), require(\"react-dom\"), require(\"redux\"), require(\"redux-actions\"), require(\"redux-observable\"), require(\"rxjs\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"bwtk\", \"omf-changepackage-components\", \"react\", \"react-redux\", \"react-router-dom\", \"react-intl\", \"react-dom\", \"redux\", \"redux-actions\", \"redux-observable\", \"rxjs\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"bwtk\"), require(\"omf-changepackage-components\"), require(\"react\"), require(\"react-redux\"), require(\"react-router-dom\"), require(\"react-intl\"), require(\"react-dom\"), require(\"redux\"), require(\"redux-actions\"), require(\"redux-observable\"), require(\"rxjs\")) : factory(root[\"bwtk\"], root[\"OMFChangepackageComponents\"], root[\"React\"], root[\"ReactRedux\"], root[\"ReactRouterDOM\"], root[\"ReactIntl\"], root[\"ReactDOM\"], root[\"Redux\"], root[\"ReduxActions\"], root[\"ReduxObservable\"], root[\"rxjs\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function(__WEBPACK_EXTERNAL_MODULE__102__, __WEBPACK_EXTERNAL_MODULE__446__, __WEBPACK_EXTERNAL_MODULE__442__, __WEBPACK_EXTERNAL_MODULE__999__, __WEBPACK_EXTERNAL_MODULE__634__, __WEBPACK_EXTERNAL_MODULE__419__, __WEBPACK_EXTERNAL_MODULE__3__, __WEBPACK_EXTERNAL_MODULE__750__, __WEBPACK_EXTERNAL_MODULE__541__, __WEBPACK_EXTERNAL_MODULE__769__, __WEBPACK_EXTERNAL_MODULE__418__) {\nreturn ", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { History } from \"history\";\r\nimport { EFlowType } from \"omf-changepackage-components\";\r\n\r\nlet _history: History | null = null;\r\nlet _tvhistory: History | null = null;\r\nlet _isAppointmentVisited = false;\r\n\r\nexport const Routes = {\r\n  [EFlowType.INTERNET]: [\r\n    \"/Changepackage/Internet\",\r\n    \"/Changepackage/Internet/Appointment\",\r\n    \"/Changepackage/Internet/Review\",\r\n    \"/Changepackage/Internet/Confirmation\"\r\n  ],\r\n  [EFlowType.TV]: [\r\n    \"/Changepackage/TV\",\r\n    \"/Changepackage/TV/Review\",\r\n    \"/Changepackage/TV/Confirmation\"\r\n  ],\r\n  [EFlowType.ADDTV]: [\r\n    \"/Add/TV\",\r\n    \"/Add/TV/Review\",\r\n    \"/Add/TV/Confirmation\"\r\n  ],\r\n  [EFlowType.BUNDLE]: [\r\n    \"/Bundle/Internet\",\r\n    \"/Bundle/Internet/Appointment\",\r\n    \"/Bundle/TV\",\r\n    \"/Bundle/Review\",\r\n    \"/Bundle/Confirmation\"\r\n  ]\r\n};\r\n\r\nexport function setHistoryProvider(history: History) {\r\n  _history = _history || history;\r\n}\r\nexport function setTVHistoryProvider(history: History) {\r\n  _tvhistory = history || _tvhistory;\r\n}\r\n\r\nexport function useHistory(): History {\r\n  return _history as History;\r\n}\r\n\r\nexport function useTVHistory(): History {\r\n  return _tvhistory as History;\r\n}\r\n\r\n\r\nexport function enableAppointementRoute() {\r\n  sessionStorage.setItem(\"omf:hasAppointmentRoute\", \"yes\");\r\n}\r\n\r\nexport function getPageName(pathname: string): string {\r\n  switch (true) {\r\n    case pathname.indexOf(\"Review\") > 0: return \"REVIEW\";\r\n    case pathname.indexOf(\"Confirm\") > 0: return \"CONFIRMATION\";\r\n    case pathname.indexOf(\"Appoint\") > 0: return \"APPOINTMENT\";\r\n    case pathname.indexOf(\"Internet\") > 0: return \"INTERNET\";\r\n    case pathname.indexOf(\"TV\") > 0: return \"TV\";\r\n    default: return \"\";\r\n  }\r\n}\r\n\r\nexport function setAppointmentVisited() {\r\n  _isAppointmentVisited = true;\r\n}\r\n\r\nexport function checkAppointmentVisited() {\r\n  return _isAppointmentVisited;\r\n}\r\n", "import { Injectable, CommonFeatures, ServiceLocator, CommonServices } from \"bwtk\";\r\nimport { EWidgetName } from \"omf-changepackage-components\";\r\n\r\nconst { BaseLocalization } = CommonFeatures;\r\n\r\n@Injectable\r\nexport class Localization extends BaseLocalization {\r\n  static Instance = null;\r\n  static getLocalizedString(id: string): string {\r\n    Localization.Instance = Localization.Instance || ServiceLocator.instance.getService(CommonServices.Localization);\r\n    const instance: any = Localization.Instance;\r\n    return instance ? instance.getLocalizedString(EWidgetName.NAVIGATION, id, instance.locale) : id;\r\n  }\r\n}\r\n", "import { Actions, Components, FormattedHTMLMessage, Omniture } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { Localization } from \"../../Localization\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nconst {\r\n  Modal\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onContinueClick: () => void;\r\n  closeLightbox: () => void;\r\n}\r\n\r\nexport const ModalId: string = \"APPLICATION_LOGOUT\";\r\n\r\nconst Component: React.FC<IComponentProps & IComponentDispatches> = ({\r\n  onContinueClick,\r\n  closeLightbox\r\n}) => <Modal\r\n  modalId={ModalId}\r\n  onShown={() => {\r\n    Omniture.useOmniture().trackFragment({\r\n      id: \"logoutLightbox\",\r\n      s_oAPT: {\r\n        actionId: 104\r\n      },\r\n      s_oPRM: Localization.getLocalizedString(\"APPLICATION_LOGOUT_TITLE\"),\r\n      s_oLBC: Localization.getLocalizedString(\"APPLICATION_LOGOUT_TEXT\")\r\n    });\r\n  }}\r\n  title={<FormattedMessage id=\"APPLICATION_LOGOUT_TITLE\" />}>\r\n  <div className=\"pad-30\">\r\n    <FormattedHTMLMessage id=\"APPLICATION_LOGOUT_TEXT\" />\r\n  </div>\r\n  <div className=\"spacer1 bgGrayLight6\" aria-hidden=\"true\"></div>\r\n  <div className=\"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\">\r\n    <button id=\"APP_LOGOUT_CONTINUE\" className=\"btn btn-primary fill-xs\" onClick={onContinueClick}><FormattedMessage id=\"APPLICATION_LOGOUT_CONTINUE\" /></button>\r\n    <div className=\"vSpacer15\" aria-hidden=\"true\"></div>\r\n    <button id=\"APP_LOGOUT_CLOSE\" className=\"btn btn-default fill-xs\" onClick={closeLightbox}><FormattedMessage id=\"APPLICATION_LOGOUT_CLOSE\" /></button>\r\n  </div>\r\n</Modal>;\r\n\r\nexport const ApplicationLogoutLightbox = connect<IComponentProps, IComponentDispatches>(\r\n  ({ }: IStoreState) => ({}),\r\n  (dispatch) => ({\r\n    onContinueClick: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"logoutLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: \"APP_LOGOUT_CONTINUE\"\r\n        }\r\n      });\r\n      dispatch(Actions.applicationLogout());\r\n    },\r\n    closeLightbox: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"logoutLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: \"APP_LOGOUT_CLOSE\"\r\n        }\r\n      });\r\n      dispatch(Actions.closeLightbox(ModalId));\r\n    },\r\n  })\r\n)(Component);\r\n", "import { Actions, Context } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState } from \"../../models\";\r\nimport { ModalId as ExitModalId } from \"../modals/ApplicationLogout\";\r\n\r\ninterface IComponentDispatches {\r\n  onLogoutClick: (payload: any) => void;\r\n}\r\n\r\nexport const Component: React.FC<IComponentDispatches> = ({ onLogoutClick }) => (\r\n  <Context>\r\n    {({ config: { linkURL, isOneTrustDPEnabled } }) => (\r\n      <footer className=\"accss-focus-outline-override-grey-bg\">\r\n        <a id=\"skipToMain\" href=\"#mainContent\" className=\"skip-to-main-link\">\r\n          <FormattedMessage id=\"Skip to main content\" />\r\n        </a>\r\n        <div className=\"simplified-footer pad-15-top pad-30-top-xs container container-fluid flex flex-justify-space-between flexCol-xs pad-20-left pad-20-right\">\r\n          <div className=\"flex-vCenter\">\r\n            <ul className=\"footer-links flex list-unstyled no-margin flexCol-xs\">\r\n              <li className=\"width-100-percent-xs noBorder\">\r\n                <a id=\"privacy\" href={linkURL.privacyURL} className=\"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray\">\r\n                  <FormattedMessage id=\"Privacy\" />\r\n                </a>\r\n              </li>\r\n              {/* <li className=\"width-100-percent-xs noBorder\">\r\n                            <a href={linkURL.securityURL} className=\"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right\"><FormattedMessage id=\"Security\" /></a>\r\n                        </li> */}\r\n              <li className=\"width-100-percent-xs noBorder\">\r\n                <a id=\"legal_context\" href={linkURL.legalURL} className=\"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray\">\r\n                  <FormattedMessage id=\"Legal\" />\r\n                </a>\r\n              </li>\r\n              {isOneTrustDPEnabled === \"true\" && (\r\n                <li className=\"width-100-percent-xs noBorder\">\r\n                  <a\r\n                    className=\"vrui-no-underline sm:vrui-mx-8 vrui-align-middle ot-sdk-show-settings\"\r\n                    role=\"button\"\r\n                    href=\"javascript:void(0)\"\r\n                    id=\"ot-sdk-btn\"\r\n                  >\r\n                    <span className=\"vrui-font-poppins-Regular vrui-text-12 vrui-leading-14\">\r\n                      <FormattedMessage id=\"COOKIE_SETTINGS\" />\r\n                    </span>\r\n                  </a>\r\n                </li>\r\n              )}\r\n              <li className=\"width-100-percent-xs\">\r\n                <a id=\"feedback\" href={linkURL.feedbackURL} className=\"txtSize14 no-break-white-space block-xs txtCenter-xs sans-serif margin-20-right links-blue-on-bg-gray\">\r\n                  <FormattedMessage id=\"FEEDBACK\" />\r\n                </a>\r\n              </li>\r\n            </ul>\r\n            <div className=\"spacer15\" aria-hidden=\"true\" />\r\n            <div className=\"txtSize14 txtCenter-xs \">\r\n              <FormattedMessage id=\"Copyright\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flexCol-xs\">\r\n            <span className=\"spacer30 d-block d-sm-none\" aria-hidden=\"true\"></span>\r\n            <button id=\"footerLogout\" onClick={() => onLogoutClick(\"footerLogout\")} className=\"btn btn-secondary flex middle-align-self line-height-1\" type=\"button\">\r\n              <FormattedMessage id=\"Log out\" />\r\n            </button>\r\n            <div className=\"vSpacer30 hidden-m\"></div>\r\n            <span className=\"spacer30 d-block d-sm-none\" aria-hidden=\"true\"></span>\r\n            <div className=\"width-100-percent-xs txtCenter-xs\">\r\n              <img className=\"img-responsive logo-footer\" role=\"link\" tabIndex={0} src={linkURL.entrustIMGURL} alt=\"Entrust label\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"spacer40\" aria-hidden=\"true\"></div>\r\n      </footer>\r\n    )}\r\n  </Context>\r\n);\r\n\r\nexport const Footer = connect<{}, IComponentDispatches>(\r\n  ({}: IStoreState) => ({}),\r\n  (dispatch) => ({\r\n    onLogoutClick: (id: any) => dispatch(Actions.openLightbox({ lightboxId: ExitModalId, data: { relativeId: id } })),\r\n  })\r\n)(Component);\r\n", "import { Actions, Components, FormattedHTMLMessage, Omniture } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { Localization } from \"../../Localization\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nconst {\r\n  Modal\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onContinueClick: () => void;\r\n  closeLightbox: () => void;\r\n}\r\n\r\nexport const ModalId: string = \"APPLICATION_EXIT\";\r\n\r\nconst Component: React.FC<IComponentProps & IComponentDispatches> = ({\r\n  onContinueClick,\r\n  closeLightbox\r\n}) => <Modal\r\n  modalId={ModalId}\r\n  onShown={() => {\r\n    Omniture.useOmniture().trackFragment({\r\n      id: \"exitLightbox\",\r\n      s_oAPT: {\r\n        actionId: 104\r\n      },\r\n      s_oPRM: Localization.getLocalizedString(\"APPLICATION_EXIT_TITLE\"),\r\n      s_oLBC: Localization.getLocalizedString(\"APPLICATION_EXIT_TEXT\")\r\n    });\r\n  }}\r\n  title={<FormattedMessage id=\"APPLICATION_EXIT_TITLE\" />}>\r\n  <div id=\"APPLICATION_EXIT_TEXT\" className=\"pad-30 pad-15-left-right-xs\">\r\n    <FormattedHTMLMessage id=\"APPLICATION_EXIT_TEXT\" />\r\n  </div>\r\n  <div className=\"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\">\r\n    <button id=\"APP_EXIT_CLOSE\" className=\"btn btn-primary fill-xs\" onClick={closeLightbox}><FormattedMessage id=\"APPLICATION_EXIT_CLOSE\" /></button>\r\n    <div className=\"vSpacer15\" aria-hidden=\"true\"></div>\r\n    <button id=\"APP_EXIT_CONTINUE\" className=\"btn btn-default fill-xs\" onClick={onContinueClick}><FormattedMessage id=\"APPLICATION_EXIT_CONTINUE\" /></button>\r\n  </div>\r\n</Modal>;\r\n\r\nexport const ApplicationExitLightbox = connect<IComponentProps, IComponentDispatches>(\r\n  ({ }: IStoreState) => ({}),\r\n  (dispatch) => ({\r\n    onContinueClick: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"exitLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: \"APP_EXIT_CONTINUE\"\r\n        }\r\n      });\r\n      dispatch(Actions.applicationExit());\r\n    },\r\n    closeLightbox: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"exitLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: \"APP_EXIT_CLOSE\"\r\n        }\r\n      });\r\n      dispatch(Actions.closeLightbox(ModalId));\r\n    },\r\n  })\r\n)(Component);\r\n", "import { Actions, Components, Context, EFlowType, Utils, EWidgetRoute, FormattedHTMLMessage } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState } from \"../../models\";\r\nimport { getPageName, checkAppointmentVisited } from \"../../utils/History\";\r\nimport { ModalId as ExitModalId } from \"../modals/ApplicationExit\";\r\n\r\ninterface IHeaderProps {\r\n  flowType: EFlowType;\r\n  location: any;\r\n}\r\n\r\ninterface IHeaderConnectedProps {\r\n  routes: Array<string>;\r\n}\r\ninterface IComponentDispatches {\r\n  onBackClick: (e: any, ref: any) => void;\r\n  onExitClick: (e: string, isConfirmationStep: boolean) => void;\r\n}\r\n\r\nfunction normalize(txt: string): string {\r\n  return (txt || \"\").replace(/[\\W_]+/g, \"\").toUpperCase();\r\n}\r\n\r\nexport const Component: React.FC<IHeaderConnectedProps & IHeaderProps & IComponentDispatches> = ({\r\n  flowType,\r\n  location: { pathname },\r\n  onBackClick,\r\n  onExitClick\r\n}) => {\r\n  const isConfirmationStep = Utils.getPageRoute() === EWidgetRoute.CONFIRMATION;\r\n  const path = getPageName(pathname);\r\n  const key = `${path}_AT_${normalize(flowType)}${\r\n    [EWidgetRoute.REVIEW, EWidgetRoute.CONFIRMATION].indexOf(Utils.getPageRoute() as any) > -1 &&\r\n      checkAppointmentVisited() ? \"+\" : \"\"\r\n  }`;\r\n  const backLabel = (window.location.pathname.toLowerCase() === \"/ordering/changepackage/internet/review\") ? \"Back to step 1\" : (window.location.pathname.toLowerCase() === \"/ordering/changepackage/internet\") ? \"Back to Overview\" : \"Back\";\r\n  return <Context>\r\n    {({ config: { linkURL } }) => <header className=\"bgPrimary simplified-header container-flex-box-wrap\" role=\"banner\">\r\n      <div className=\"container container-fluid container-flex-box-wrap flex-justify-space-between accss-focus-outline-override-red-bg\">\r\n        <div className=\"page-back-button container-flex-box-wrap fullHeight align-items-center flex\">\r\n          {!isConfirmationStep && <a id=\"back\" onClick={(e) => onBackClick(e, \"back\")} aria-label={backLabel} href={linkURL.exitURL} className=\"responsive-simplified-header-back txtDecorationNoneHover txtWhite\">\r\n            <span className=\"virgin-icon icon-Left_arrow txtSize15 inlineBlock\" aria-hidden=\"true\"></span>\r\n            <span className=\"txtSize14 hidden-m margin-10-left txtDecoration_hover\"><FormattedMessage id=\"Back\" /></span>\r\n          </a>}\r\n        </div>\r\n        <div className=\"page-heading container-flex-box-wrap fullHeight overflow-ellipsis-parent container-flex-grow-fill justify-center\" aria-live=\"assertive\">\r\n          <div className=\"middle-align-self overflow-ellipsis\">\r\n            <h1 className=\"virginUltraReg txtWhite no-margin overflow-ellipsis txtCenter txtSize22 txtUppercase\">\r\n              <FormattedHTMLMessage id={`PAGE_NAME_FOR_${key}`} />\r\n            </h1>\r\n            {!isConfirmationStep && <FormattedHTMLMessage id={`STEP_COUNT_FOR_${key}`}>\r\n              {\r\n                (txt: string) => <Components.Visible when={!!txt && txt !== `STEP_COUNT_FOR_${key}`}>\r\n                  <p className=\"txtWhite txtSize14 no-margin-bottom sans-serif txtCenter header-steps\" dangerouslySetInnerHTML={{ __html: txt }} />\r\n                </Components.Visible>\r\n              }\r\n            </FormattedHTMLMessage>}\r\n          </div>\r\n        </div>\r\n        <div className=\"page-right-button flex-vCenter d-none d-md-flex d-lg-flex\">\r\n          <button id=\"exit\" onClick={() => onExitClick(\"exit\", isConfirmationStep)} data-href={linkURL.exitURL} className=\"btn btn-secondary-inverted margin-5-right\"><FormattedMessage id={`EXIT_CTA`} /></button>\r\n        </div>\r\n      </div>\r\n    </header>}\r\n  </Context>;\r\n};\r\n\r\nexport const Header = connect<IHeaderConnectedProps, IComponentDispatches, IHeaderProps>(\r\n  ({ routes }: IStoreState) => ({ routes }),\r\n  (dispatch) => ({\r\n    onBackClick: (e, ref) => {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n      dispatch(Actions.historyBack(ref));\r\n    },\r\n    onExitClick: (id: string, isConfirmationStep: boolean) => {\r\n      if (isConfirmationStep) dispatch(Actions.applicationExit());\r\n      else dispatch(Actions.openLightbox({ lightboxId: ExitModalId, data: { relativeId: id } }));\r\n    }\r\n  })\r\n)(Component);\r\n", "import { Actions, Components, Omniture, FormattedHTMLMessage } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { Localization } from \"../../Localization\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nconst {\r\n  Modal\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onContinueClick: () => void;\r\n  closeLightbox: () => void;\r\n}\r\n\r\nexport const ModalId: string = \"APPLICATION_RESET\";\r\n\r\nconst Component: React.FC<IComponentProps & IComponentDispatches> = ({\r\n  onContinueClick,\r\n  closeLightbox\r\n}) => <Modal\r\n  modalId={ModalId}\r\n  onShown={() => {\r\n    Omniture.useOmniture().trackFragment({\r\n      id: \"exitLightbox\",\r\n      s_oAPT: {\r\n        actionId: 104\r\n      },\r\n      s_oPRM: Localization.getLocalizedString(\"APPLICATION_RESET_TITLE\"),\r\n      s_oLBC: Localization.getLocalizedString(\"APPLICATION_RESET_TEXT\")\r\n    });\r\n  }}\r\n  title={<FormattedMessage id=\"APPLICATION_RESET_TITLE\" />}>\r\n  <div className=\"pad-30\">\r\n    <FormattedHTMLMessage id=\"APPLICATION_RESET_TEXT\" />\r\n  </div>\r\n  <div className=\"spacer1 bgGrayLight6\" aria-hidden=\"true\"></div>\r\n  <div className=\"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\">\r\n    <button id=\"APP_RESET_CONTINUE\" className=\"btn btn-primary fill-xs\" onClick={onContinueClick}><FormattedMessage id=\"APPLICATION_RESET_CONTINUE\" /></button>\r\n    <div className=\"vSpacer15\" aria-hidden=\"true\"></div>\r\n    <button id=\"APP_RESET_CLOSE\" className=\"btn btn-default fill-xs\" onClick={closeLightbox}><FormattedMessage id=\"APPLICATION_RESET_CLOSE\" /></button>\r\n  </div>\r\n</Modal>;\r\n\r\nexport const ApplicationResetLightbox = connect<IComponentProps, IComponentDispatches>(\r\n  ({ }: IStoreState) => ({}),\r\n  (dispatch) => ({\r\n    onContinueClick: () => dispatch(Actions.applicationReset()),\r\n    closeLightbox: () => dispatch(Actions.closeLightbox(ModalId)),\r\n  })\r\n)(Component);\r\n", "import { WidgetLoader } from \"bwtk\";\r\nimport { Actions, Components, EModals, EReviewMode, EWidgetName, Omniture, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState } from \"../../models\";\r\n\r\nconst {\r\n  Modal\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n  summaryAction: Volt.IHypermediaAction;\r\n  isContinueEnabled: boolean;\r\n  isOpen: boolean;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onContinueClick: () => void;\r\n  closeLightbox: () => void;\r\n  dismissLightbox: () => void;\r\n}\r\n\r\nexport const ModalId: string = EModals.PREVIEWMODAL;\r\n\r\nconst Component: React.FC<IComponentProps & IComponentDispatches> = ({\r\n  isOpen,\r\n  summaryAction,\r\n  isContinueEnabled,\r\n  onContinueClick,\r\n  closeLightbox,\r\n  dismissLightbox\r\n}) => <Modal\r\n  modalId={ModalId}\r\n  className={\"do-not-center-in\"}\r\n  onDismiss={dismissLightbox}\r\n  title={<FormattedMessage id={`${ModalId}_TITLE`} />}>\r\n  {isOpen && <WidgetLoader widget={EWidgetName.PREVIEW} mode={EReviewMode.Summary} summaryAPI={ValueOf<string>(summaryAction, \"href\")} />}\r\n  <div className=\"spacer30\" aria-hidden=\"true\" />\r\n  <div className=\"spacer1 bgGrayLight6\" aria-hidden=\"true\" />\r\n  <div className=\"bgGray19 pad-30 pad-15-left-right-xs border-1-top-GrayLight6 accss-focus-outline-override-grey-bg\">\r\n    <button id={`BUTTON_CONTINUE_${ModalId}`} disabled={!isContinueEnabled} className=\"btn btn-primary fill-xs\" onClick={onContinueClick}><FormattedMessage id={`${ModalId}_CONTINUE`} /></button>\r\n    <span className=\"vSpacer15\" aria-hidden=\"true\" />\r\n    <button id={`BUTTON_CLOSE_${ModalId}`} className=\"btn btn-secondary fill-xs\" onClick={closeLightbox}><FormattedMessage id={`${ModalId}_CLOSE`} /></button>\r\n  </div>\r\n</Modal>;\r\n\r\nexport const PreviewLightbox = connect<IComponentProps, IComponentDispatches>(\r\n  ({ lightboxData, summary }: IStoreState) => ({\r\n    summaryAction: ValueOf<Volt.IHypermediaAction>(summary, \"summaryAction\", null),\r\n    isContinueEnabled: !!ValueOf(summary, \"nextAction\", false),\r\n    isOpen: lightboxData && lightboxData.lightbox === ModalId\r\n  }),\r\n  (dispatch) => ({\r\n    onContinueClick: () => {\r\n      dispatch(Actions.closeLightbox(ModalId));\r\n      dispatch(Actions.broadcastUpdate(Actions.onContinue()));\r\n    },\r\n    closeLightbox: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"previewLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: {\r\n          ref: `BUTTON_CLOSE_${ModalId}`\r\n        }\r\n      });\r\n      dispatch(Actions.closeLightbox(ModalId));\r\n    },\r\n    dismissLightbox: () => {\r\n      Omniture.useOmniture().trackAction({\r\n        id: \"previewLightbox\",\r\n        s_oAPT: {\r\n          actionId: 647\r\n        },\r\n        s_oBTN: \"Close\"\r\n      });\r\n      dispatch(Actions.setlightboxData(\"\"));\r\n    }\r\n  })\r\n)(Component);\r\n", "import { WidgetLoader } from \"bwtk\";\r\nimport { EWidgetName } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { enableAppointementRoute } from \"../../utils/History\";\r\n\r\ninterface IPage {\r\n  title: string;\r\n}\r\n\r\nexport const Appointment: React.FC<IPage> = ({ title }) => {\r\n  React.useEffect(() => {\r\n    enableAppointementRoute();\r\n    // Set page title\r\n    document.title = `${title} - ${document.title}`;\r\n  }, []);\r\n  return <WidgetLoader widget={EWidgetName.APPOINTMENT} />;\r\n};\r\n", "import { WidgetLoader } from \"bwtk\";\r\nimport { EWidgetName, EWidgetRoute } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\n\r\ninterface IPage {\r\n  title: string;\r\n}\r\n\r\nexport const Confirmation: React.FC<IPage> = ({ title }) => {\r\n  // Set page title\r\n  document.title = `${title} - ${document.title}`;\r\n  return <WidgetLoader widget={EWidgetName.CONFIRMATION} mode={EWidgetRoute.CONFIRMATION} />;\r\n};\r\n", "import { WidgetLoader } from \"bwtk\";\r\nimport { EWidgetName } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\n\r\ninterface IPage {\r\n  title: string;\r\n}\r\n\r\nexport const Internet: React.FC<IPage> = ({ title }) => {\r\n  // Set page title\r\n  document.title = `${title} - ${document.title}`;\r\n  return <WidgetLoader widget={EWidgetName.INTERNET} />;\r\n};\r\n", "import { WidgetLoader } from \"bwtk\";\r\nimport { EWidgetName, EWidgetRoute } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\n\r\ninterface IPage {\r\n  title: string;\r\n}\r\n\r\nexport const Review: React.FC<IPage> = ({ title }) => {\r\n  // Set page title\r\n  document.title = `${title} - ${document.title}`;\r\n  return <WidgetLoader widget={EWidgetName.REVIEW} mode={EWidgetRoute.REVIEW} />;\r\n};\r\n", "import { WidgetLoader } from \"bwtk\";\r\nimport { EWidgetName } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\n\r\ninterface IPage {\r\n  title: string;\r\n}\r\n\r\nexport const TV: React.FC<IPage> = ({ title }) => {\r\n  // Set page title\r\n  document.title = `${title} - ${document.title}`;\r\n  return <WidgetLoader widget={EWidgetName.TV} />;\r\n};\r\n", "import { Components, ValueOf } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { ISummary } from \"../../models\";\r\n\r\nconst {\r\n  Visible,\r\n  Currency,\r\n} = Components;\r\n\r\ninterface IComponentProps {\r\n  summary: ISummary;\r\n  isContinueEnabled: boolean;\r\n  onContinueClick: () => void;\r\n}\r\n\r\nconst TVSummaryPortal: React.FC<IComponentProps> = ({\r\n  summary,\r\n  isContinueEnabled,\r\n  onContinueClick\r\n}) => <>\r\n  <Visible when={ValueOf(summary, \"TV\", false)}>\r\n    <Visible when={ValueOf(summary, \"TV.currentPrice\")}>\r\n      <div className=\"virgin-menu-dockbar flexStatic flexJustifyBetween-sm bgBlack\">\r\n        <p className=\"noMargin txtSize12 flexGrow txtWhite\"><FormattedMessage id=\"CurrentTV\" /></p>\r\n        <span className=\"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\">\r\n          <Currency value={ValueOf(summary, \"TV.currentPrice.price\", 0)} monthly={true} prefixClassName={\"txtSize16 txtUppercase\"} fractionClassName={\"txtSize16\"} />\r\n        </span>\r\n      </div>\r\n    </Visible>\r\n    <Visible when={ValueOf(summary, \"TV.newPrice\")}>\r\n      <div className=\"virgin-menu-dockbar flexStatic bgOrange flexJustifyBetween-sm\">\r\n        <p className=\"noMargin txtSize12 flexGrow txtWhite\"><FormattedMessage id=\"NewTV\" /></p>\r\n        <span className=\"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\">\r\n          <Currency value={ValueOf(summary, \"TV.newPrice.price\", 0)} monthly={true} prefixClassName={\"txtSize16 txtUppercase\"} fractionClassName={\"txtSize16\"} />\r\n        </span>\r\n      </div>\r\n    </Visible>\r\n  </Visible>\r\n  <div className=\"flexBlock preview-btn bgBlack flexJustify pad-25-top pad-25-bottom\">\r\n    <button onClick={onContinueClick} disabled={!isContinueEnabled} id=\"mobileTVContinue\"\r\n      className={`btn btn-primary txtWhite txtSize16 relative ${isContinueEnabled ? \"\" : \"disabled\"}`}>\r\n      <FormattedMessage id=\"Review changes\" />\r\n      <Visible when={ValueOf(summary, \"productOfferingCount\", false)}>\r\n        <span className=\"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification\">\r\n          <span>{ValueOf(summary, \"productOfferingCount\", 0)}</span>\r\n        </span>\r\n      </Visible>\r\n    </button>\r\n  </div>\r\n  {/* <div className=\"flexBlock preview-btn bgBlack flexJustify pad-10-top\">\r\n      <button className=\"btn btn-link txtUnderline txtWhite txtSize12\">Reset\r\n                                    changes</button>\r\n    </div> */}\r\n</>;\r\n\r\nexport default TVSummaryPortal;\r\n", "import { Actions, Components, E<PERSON>lowType, EWidgetRoute, ValueOf, Utils, Volt } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport * as ReactDOM from \"react-dom\";\r\n// import { useRouteMatch } from \"react-router-dom\";\r\nimport { FormattedMessage } from \"react-intl\";\r\nimport { connect } from \"react-redux\";\r\nimport { IStoreState, ISummary } from \"../../models\";\r\nimport { ModalId as ResetModalId } from \"../modals/ApplicationReset\";\r\nimport { ModalId as SummaryModalId } from \"../modals/Summary\";\r\nimport TVSummaryPortal from \"./TvSummaryPortal\";\r\n\r\nconst {\r\n  Visible,\r\n  Currency,\r\n} = Components;\r\n\r\nconst DisabledContinue = () => {\r\n  const page = (Utils.getPageRoute() || \"\").replace(\"/\", \"\").toUpperCase();\r\n  const messageid = `TOOLTIP_${page}`;\r\n  const [hover, toggleHover] = React.useState(false);\r\n  return <button id=\"tierContinue\"\r\n    onClick={() => toggleHover(!hover)}\r\n    onKeyUp={() => toggleHover(!hover)}\r\n    onMouseOver={() => toggleHover(true)}\r\n    onMouseOut={() => toggleHover(false)}\r\n    className=\"btn btn-primary fill-xs tooltip-interactive relative alignIconWithText disabled\"\r\n    aria-disabled=\"true\">\r\n    <FormattedMessage id=\"Continue\" />\r\n    <Visible when={hover}>\r\n      <FormattedMessage id={messageid}>\r\n        {\r\n          (txt: any) => Boolean(txt) && txt !== messageid ?\r\n            <div className=\"tooltip fade bs-tooltip-top show\" role=\"tooltip\" id=\"tooltip504192\" style={{ position: \"absolute\", width: \"240px\", top: \"-110px\", left: \"50%\", transform: \"translateX(-50%)\" }}>\r\n              <div className=\"arrow\" style={{ left: \"50%\" }} />\r\n              <div className=\"tooltip-inner\" style={{ width: \"100%\" }}>\r\n                <div className=\"flexRow bgWhite txtBlack\">\r\n                  <div className=\"olt-icon icon-warning txtSize22 margin-10-right\">\r\n                    <span className=\"volt-icon path1 yellowIcon\"></span>\r\n                    <span className=\"volt-icon path2\"></span>\r\n                  </div>\r\n                  <div className=\"margin-5-top\"><FormattedMessage id={messageid} /></div>\r\n                </div>\r\n              </div>\r\n            </div> : <span></span>\r\n        }\r\n      </FormattedMessage>\r\n    </Visible>\r\n  </button>;\r\n};\r\n\r\ninterface IPriceBlock {\r\n  label: any;\r\n  price: Volt.IPriceDetail;\r\n  regularPrice?: Volt.IPriceDetail;\r\n  className?: string;\r\n}\r\n\r\nconst PriceBlock: React.FC<IPriceBlock> = ({\r\n  label,\r\n  price,\r\n  regularPrice,\r\n  className = \"\"\r\n}) => <div className={\"virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify \" + className}>\r\n  <p className=\"noMargin txtSize12 txtWhite\">{label}</p>\r\n  <div>\r\n    <Visible when={ValueOf(regularPrice, undefined, false)}>\r\n      <p className=\"noMargin txtSize12 txtWhite\"><FormattedMessage id=\"Price after credit\" /></p>\r\n    </Visible>\r\n    <span className=\"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\">\r\n      <Currency value={ValueOf(price, \"price\", 0)} monthly={true} prefixClassName={\"txtSize16 txtUppercase\"} fractionClassName={\"txtSize16\"} />\r\n    </span>\r\n    <Visible when={ValueOf(regularPrice, undefined, false)}>\r\n      <p className=\"noMargin txtSize12 txtWhite\">\r\n        <FormattedMessage id=\"Current price\" values={ValueOf(regularPrice, undefined)} />\r\n      </p>\r\n    </Visible>\r\n  </div>\r\n</div>;\r\n\r\ninterface IComponentConnectedProps {\r\n  summary: ISummary;\r\n  isContinueEnabled: boolean;\r\n}\r\n\r\ninterface IComponentProps {\r\n  flowType: EFlowType;\r\n  location: any;\r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onSummaryClick: (data: any) => void;\r\n  onCancelClick: (data: any) => void;\r\n  onCategoriesClick: () => void;\r\n  onContinueClick: () => void;\r\n  handleNav: () => void;\r\n}\r\n\r\nconst Component: React.FC<IComponentProps & IComponentConnectedProps & IComponentDispatches> = ({\r\n  summary,\r\n  flowType,\r\n  location,\r\n  isContinueEnabled,\r\n  onSummaryClick,\r\n  onCancelClick,\r\n  onContinueClick,\r\n  onCategoriesClick,\r\n  handleNav\r\n}) => {\r\n  // const isTVStep = Boolean(useRouteMatch(EWidgetRoute.TV));\r\n  const isTVStep = window.location.href.indexOf(EWidgetRoute.TV) > -1;\r\n  const isVisible = !(location.pathname.indexOf(EWidgetRoute.REVIEW) > 0 ||\r\n    location.pathname.indexOf(EWidgetRoute.CONFIRMATION) > 0);\r\n  const tvSummaryContainer = document.getElementById(\"tv-sedebar-summary-portal\") as HTMLDivElement;\r\n\r\n  return <Visible when={isVisible}><nav>\r\n    <div className=\"virgin-dockbar col1 scrollTop\">\r\n      <div className=\"nopad bgBlack accss-focus-outline-override-black-bg\" style={{opacity: \"92%\"}}>\r\n        <div className=\"virgin-dockbar-panel flexRow block-xs container container-fluid no-pad-xs\">\r\n          <div className=\"flexRow block-xs\">\r\n            <Visible when={ValueOf(summary, \"Internet\", false)}>\r\n              <Visible when={ValueOf(summary, \"?Internet.currentPrice\")}>\r\n                <PriceBlock label={<FormattedMessage id=\"Current\" />}\r\n                  price={ValueOf(summary, \"?Internet.currentPrice\")}\r\n                  regularPrice={ValueOf(summary, \"?Internet.regularCurrentPrice\")} />\r\n              </Visible>\r\n              <Visible when={ValueOf(summary, \"?Internet.newPrice\")}>\r\n                <PriceBlock label={<FormattedMessage id=\"NewInternet\" />}\r\n                  className=\"bgOrange\"\r\n                  price={ValueOf(summary, \"?Internet.newPrice\")}\r\n                  regularPrice={ValueOf(summary, \"?Internet.regularNewPrice\")} />\r\n              </Visible>\r\n            </Visible>\r\n            <Visible when={ValueOf(summary, \"TV\", false)}>\r\n              <Visible when={ValueOf(summary, \"?TV.currentPrice\")}>\r\n                {/* <div className=\"virgin-dockbar-row flexStatic flexJustifyBetween-xs flexJustify\">\r\n                  <p className=\"noMargin txtSize12 txtWhite\"><FormattedMessage id=\"CurrentTV\" /></p>\r\n                  <div>\r\n                    <Visible when={ValueOf(summary, \"?TV.regularCurrentPrice\", false)}>\r\n                      <p className=\"noMargin txtSize12 txtWhite\"><FormattedMessage id=\"Price after credit\" /></p>\r\n                    </Visible>\r\n                    <span className=\"virginUltraReg txtCurrency txtSize28 pad-5-top txtWhite\">\r\n                      <Currency value={ValueOf(summary, \"?TV.currentPrice\")} monthly={true} prefixClassName={\"txtSize16 txtUppercase\"} fractionClassName={\"txtSize16\"} />\r\n                    </span>\r\n                    <Visible when={ValueOf(summary, \"?TV.regularCurrentPrice\")}>\r\n                      <p className=\"noMargin txtSize12 txtWhite\">\r\n                        <FormattedMessage id=\"Current price\" values={ValueOf(summary, \"?TV.regularCurrentPrice\")} />\r\n                      </p>\r\n                    </Visible>\r\n                  </div>\r\n                </div> */}\r\n                <PriceBlock label={<FormattedMessage id=\"CurrentTV\" />}\r\n                  price={ValueOf(summary, \"?TV.currentPrice\")}\r\n                  regularPrice={ValueOf(summary, \"?TV.regularCurrentPrice\")} />\r\n              </Visible>\r\n              <Visible when={ValueOf(summary, \"?TV.newPrice\")}>\r\n                <PriceBlock label={<FormattedMessage id=\"NewTV\" />}\r\n                  className=\"bgOrange\"\r\n                  price={ValueOf(summary, \"?TV.newPrice\")}\r\n                  regularPrice={ValueOf(summary, \"?TV.regularNewPrice\")} />\r\n              </Visible>\r\n            </Visible>\r\n          </div>\r\n          <Visible when={ValueOf(summary, \"summaryAction\")}>\r\n            <div className=\"virgin-dockbar-row flexCol-xs preview-btn\">\r\n              <button id=\"orderReview\" onClick={() => onSummaryClick(\"orderReview\")} className=\"btn btn-link txtUnderline txtWhite txtSize12 pad-10-left accss-changeplan-preview\"><FormattedMessage id=\"Preview\" /></button>\r\n            </div>\r\n          </Visible>\r\n          <Visible when={ValueOf(summary, \"resetAction\")}>\r\n            <div className=\"flexStatic flexCol-xs preview-btn\">\r\n              <button id=\"orderCancel\" onClick={() => onCancelClick(\"orderCancel\")} className=\"btn btn-link txtWhite txtUnderline txtSize12 dockbar-cancel\"><FormattedMessage id=\"Reset\" /></button>\r\n            </div>\r\n          </Visible>\r\n          <div className=\"spacer10 visible-m\" aria-hidden=\"true\" />\r\n          <div className=\"flexGrow\" />\r\n          <div className=\"flex dockbar-buttons continue-button fullWidth-xs align-items-center flexCol-xs bgBlack\">\r\n            <Visible when={isTVStep}>\r\n              <div className=\"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs d-md-none\">\r\n                <button id=\"TV_CATEGORIES\" className=\"btn btn-secondary-inverted p-2\" onClick={handleNav}>\r\n                  <FormattedMessage id=\"TV_CATEGORIES\" />\r\n                </button>\r\n              </div>\r\n            </Visible>\r\n            <div className=\"virgin-dockbar-row flexStatic align-items-xs fill-xs no-pad-top-xs\">\r\n              <Visible when={isContinueEnabled} placeholder={<DisabledContinue />}>\r\n                <button onClick={onContinueClick}\r\n                  id=\"tier_Continue\" className=\"btn btn-primary fill-xs tooltip-interactive alignIconWithText pointer relative p-2\">\r\n                  <FormattedMessage id=\"Continue\" />\r\n                  <Visible when={isTVStep && ValueOf(summary, \"productOfferingCount\", false)}>\r\n                    <span className=\"bgWhite txtSize12 pointer sans-serif txtBold txtGray txtCenter dockbar-notification\">\r\n                      <span>{ValueOf(summary, \"productOfferingCount\", 0)}</span>\r\n                    </span>\r\n                  </Visible>\r\n                </button>\r\n              </Visible>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    {\r\n      tvSummaryContainer &&\r\n      ReactDOM.createPortal(<TVSummaryPortal summary={summary} isContinueEnabled={isContinueEnabled} onContinueClick={onContinueClick} />, tvSummaryContainer)\r\n    }\r\n  </nav>\r\n  </Visible>;\r\n};\r\nexport const SummaryPanel = connect<IComponentConnectedProps, IComponentDispatches, IComponentProps>(\r\n  ({ summary }: IStoreState) => ({\r\n    summary,\r\n    isContinueEnabled: !!ValueOf(summary, \"nextAction\", false)\r\n  }),\r\n  (dispatch) => ({\r\n    onSummaryClick: (id: any) => dispatch(Actions.openLightbox({ lightboxId: SummaryModalId, data: { relativeId: id, lightbox: SummaryModalId } })),\r\n    onCancelClick: (id: any) => dispatch(Actions.openLightbox({ lightboxId: ResetModalId, data: { relativeId: id } })),\r\n    onCategoriesClick: () => dispatch(Actions.broadcastUpdate(Actions.toggleTVCategoriesTray())),\r\n    onContinueClick: () => dispatch(Actions.broadcastUpdate(Actions.onContinue())),\r\n    handleNav: () => dispatch(Actions.broadcastUpdate(Actions.handleNav(true)))\r\n  })\r\n)(Component);\r\n", "import { Actions, Components, EFlowType, EWidgetRoute, WidgetContext,  } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { connect } from \"react-redux\";\r\nimport { BrowserRouter, Redirect, Route, Switch, useLocation } from \"react-router-dom\";\r\nimport { IStoreState } from \"../models\";\r\nimport { setHistoryProvider } from \"../utils/History\";\r\nimport { Footer } from \"./footer\";\r\nimport { Header } from \"./header\";\r\nimport { ApplicationExitLightbox } from \"./modals/ApplicationExit\";\r\nimport { ApplicationLogoutLightbox } from \"./modals/ApplicationLogout\";\r\nimport { ApplicationResetLightbox } from \"./modals/ApplicationReset\";\r\nimport { PreviewLightbox } from \"./modals/Summary\";\r\nimport { Appointment } from \"./pages/Appointment\";\r\nimport { Confirmation } from \"./pages/Confirmation\";\r\nimport { Internet } from \"./pages/Internet\";\r\nimport { Review } from \"./pages/Review\";\r\nimport { TV } from \"./pages/TV\";\r\nimport { SummaryPanel } from \"./summary\";\r\n\r\nconst {\r\n  RestrictionModal\r\n} = Components;\r\n\r\nconst {\r\n  errorOccured,\r\n  widgetRenderComplete\r\n} = Actions;\r\n\r\ninterface IComponentProps {\r\n  defaultRoute: EWidgetRoute;\r\n  flowType: EFlowType;    \r\n}\r\n\r\ninterface IComponentDispatches {\r\n  onErrorEncountered: Function;\r\n  widgetRenderComplete: Function;\r\n}\r\n\r\nconst AppRouter: React.FC<IComponentProps> = (props) => {\r\n  const location = useLocation();\r\n  const { config: {environmentVariables} }: any = React.useContext(WidgetContext);\r\n  return <React.Fragment>\r\n    <style>\r\n      {`\r\n                .brf .modal.fade.do-not-center-in .modal-dialog {\r\n                    transform: none!important;\r\n                    top: auto;\r\n                }\r\n                .brf .modal.fade.do-not-center-in .modal-content {\r\n                    min-height: 460px;\r\n                }\r\n            `}\r\n    </style>\r\n    <Header {...props} location={location} />\r\n    {/* Generic catch-all route */}\r\n    <Route path=\"*\" component={({ history }: any) => {\r\n      // Set history object for navigation from components\r\n      setHistoryProvider(history);\r\n      // Should the device be missing from config, jump back to step one\r\n      return null;\r\n    }} />\r\n    <Switch>\r\n      <Route path={[\r\n        \"/Changepackage/Internet/Appointment\",\r\n        \"/Bundle/Internet/Appointment\"\r\n      ]}>\r\n        <Appointment title=\"Appointment\" />\r\n      </Route>\r\n      <Route path={[\r\n        \"/Changepackage/Internet/Review\",\r\n        \"/Changepackage/TV/Review\",\r\n        \"/Add/TV/Review\",\r\n        \"/Bundle/Review\"\r\n      ]}>\r\n        <Review title=\"Review\" />\r\n      </Route>\r\n      <Route path={[\r\n        \"/Changepackage/Internet/Confirmation\",\r\n        \"/Changepackage/TV/Confirmation\",\r\n        \"/Add/TV/Confirmation\",\r\n        \"/Bundle/Confirmation\"\r\n      ]}>\r\n        <Confirmation title=\"Confirmation\" />\r\n      </Route>\r\n      {/* Order is very important!!!\r\n                Internet + TV routes MUST be last */}\r\n      <Route path={[\r\n        \"/Changepackage/Internet\",\r\n        \"/Bundle/Internet\"\r\n      ]}>\r\n        <Internet title=\"Internet\" />\r\n      </Route>\r\n      <Route path={[\r\n        \"/Changepackage/TV\",\r\n        \"/Bundle/TV\",\r\n        \"/Add/TV\"\r\n      ]}>\r\n        <TV title= {environmentVariables.language === \"fr\" ? \"Configurez vos service - TV\" : \"Set up your service - TV\"} />\r\n      </Route>\r\n      {/* Every unknown path should redirect to base route */}\r\n      <Route path=\"*\">\r\n        <Redirect to={props.defaultRoute} />\r\n      </Route>\r\n    </Switch>\r\n    <SummaryPanel {...props} location={location} />\r\n    <Footer />\r\n    <PreviewLightbox />\r\n    <RestrictionModal id=\"NAVIGATION_RESTRICTION_MODAL\" />\r\n    <ApplicationResetLightbox />\r\n    <ApplicationExitLightbox />\r\n    <ApplicationLogoutLightbox />\r\n    <div className=\"spacer20 hidden-xs hidden-m\" aria-hidden=\"true\" />\r\n    <div className=\"spacer60 hidden-xs hidden-m\" aria-hidden=\"true\" />\r\n  </React.Fragment>;\r\n};\r\n\r\nclass Component extends React.Component<IComponentProps & IComponentDispatches> {\r\n  componentDidCatch(err: any) {\r\n    this.props.onErrorEncountered(err);\r\n  }\r\n\r\n  componentDidMount() {\r\n    this.props.widgetRenderComplete(\"omf-changepackage-navigation\");\r\n  }\r\n\r\n  render() {\r\n    return (<BrowserRouter basename=\"/Ordering\"><AppRouter {...this.props} /></BrowserRouter>);\r\n  }\r\n}\r\n\r\nexport const Application = connect<IComponentProps, IComponentDispatches>(\r\n  ({ defaultRoute, flowType }: IStoreState) => ({ defaultRoute, flowType }),\r\n  (dispatch) => ({\r\n    onErrorEncountered: (error: any) => dispatch(errorOccured(error)),\r\n    widgetRenderComplete: () => dispatch(widgetRenderComplete())\r\n  })\r\n)(Component);\r\n", "import * as React from \"react\";\r\nimport { Components } from \"omf-changepackage-components\";\r\nimport { Application } from \"./views\";\r\n\r\nconst {\r\n  ApplicationRoot\r\n} = Components;\r\n\r\nexport const App = () => <ApplicationRoot>\r\n  <Application />\r\n</ApplicationRoot>;\r\n\r\n", "import { Injectable, CommonFeatures } from \"bwtk\";\r\nimport { Models, EWidgetRoute } from \"omf-changepackage-components\";\r\n\r\nconst { BaseConfig, configProperty } = CommonFeatures;\r\n\r\ninterface IAppConfig extends Models.IBaseConfig {\r\n  defaultRoute: EWidgetRoute;\r\n  linkURL: { [key: string]: string };\r\n}\r\n\r\ninterface IAppAPI extends Models.IBaseWidgetAPI {\r\n}\r\n\r\n/**\r\n * Widget configuration provider\r\n * Allows the external immutable\r\n * config setting\r\n * @export\r\n * @class Config\r\n * @extends {BaseConfig<IAppConfig>}\r\n */\r\n@Injectable\r\nexport class Config extends BaseConfig<IAppConfig> {\r\n  @configProperty(\"\") flowType: string;\r\n  @configProperty({}) environmentVariables: any;\r\n  @configProperty({}) mockdata: any;\r\n  @configProperty({}) headers: any;\r\n  @configProperty({ base: \"http://127.0.0.1:8881\" }) api: IAppAPI;\r\n  @configProperty(\"\") defaultRoute: EWidgetRoute;\r\n  @configProperty({}) linkURL: { [key: string]: string };\r\n  @configProperty(\"\") isOneTrustDPEnabled: string;\r\n}\r\n", "import { createAction, Action } from \"redux-actions\";\r\nimport { EFlowType, Volt } from \"omf-changepackage-components\";\r\nimport { ISummary, IAPIResponse } from \"../models\";\r\nimport { summaryTransformerFn, setFlowTypeFn } from \"../mutators\";\r\n\r\n// Widget actions\r\nexport const setFlowType = createAction<EFlowType>(\"SET_FLOW_TYPE\", setFlowTypeFn as any) as (type: EFlowType) => Action<EFlowType>;\r\nexport const setSummaryTotals = createAction<ISummary>(\"SET_FLOW_SUMMARY_TOTALS\", summaryTransformerFn as any) as (type: IAPIResponse) => Action<ISummary>;\r\nexport const checkRestrictions = createAction<Volt.IHypermediaAction>(\"CHECK_NAVIGATION_RESTRICTIONS\") as (action: Volt.IHypermediaAction) => Action<Volt.IHypermediaAction>;\r\n\r\n// Piped actions\r\n\r\n", "import { Injectable, AjaxServices } from \"bwtk\";\r\nimport { BaseClient } from \"omf-changepackage-components\";\r\n\r\nimport { Config } from \"./Config\";\r\n\r\n/**\r\n * Base client implementation\r\n * for AJAX calls\r\n * @export\r\n * @class Client\r\n * @extends {BaseClient}\r\n */\r\n@Injectable\r\nexport class Client extends BaseClient {\r\n  constructor(ajaxClient: AjaxServices, config: Config) {\r\n    super(ajaxClient, config);\r\n  }\r\n}\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Actions, AjaxResponse, EFlowType, EWidgetRoute, EWidgetStatus, FilterRestrictionObservable, Models, Utils, ValueOf, Volt } from \"omf-changepackage-components\";\r\nimport { Action } from \"redux-actions\";\r\nimport { combineEpics, Epic , StateObservable } from \"redux-observable\";\r\nimport { Client } from \"../../Client\";\r\nimport { Config } from \"../../Config\";\r\nimport { useHistory, useTVHistory } from \"../../utils/History\";\r\nimport { ModalId as ExitModalId } from \"../../views/modals/ApplicationExit\";\r\nimport { checkRestrictions, setFlowType } from \"../Actions\";\r\nimport { Store } from \"../Store\";\r\nimport { catchError, filter, mergeMap, concat, of } from \"rxjs\";\r\n\r\n\r\nconst {\r\n  showHideLoader,\r\n  continueFlow,\r\n  historyBack,\r\n  historyForward,\r\n  historyGo,\r\n  openLightbox,\r\n  applicationExit,\r\n  applicationLogout,\r\n  setWidgetStatus,\r\n  applicationReset,\r\n  closeLightbox,\r\n  onContinue,\r\n} = Actions;\r\n\r\nfunction getBaseRoute(route: string): string {\r\n  return (route || \"\").replace(/\\/Ordering|\\/Packages|\\/Movies|\\/Addons|\\/Alacarte|\\/International|\\/Combos|\\/Browse|\\/Search/i, \"\");\r\n}\r\n\r\nfunction getCurrentRoute(): string {\r\n  const history = useHistory();\r\n  return getBaseRoute(history.location.pathname);\r\n}\r\n\r\n@Injectable\r\nexport class NavigationEpics {\r\n  widgetState: EWidgetStatus;\r\n\r\n  constructor(private client: Client, private config: Config) { }\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.historyGoEpic,\r\n      this.historyForwardEpic,\r\n      this.historyBackEpic,\r\n      this.onContinueEpic,\r\n      this.applicationExitEpic,\r\n      this.applicationLogoutEpic,\r\n      this.checkRestrictionsEpic,\r\n      this.applicationResetEpic\r\n    );\r\n  }\r\n\r\n  /**\r\n     * Check for possible pending restrictions before\r\n     * historyForward can be completed\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get checkRestrictionsEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === checkRestrictions.toString()),\r\n        filter(({ payload }: Action<Volt.IHypermediaAction>) => Boolean(payload)),\r\n        mergeMap(({ payload }: Action<Volt.IHypermediaAction>) => \r\n          concat(\r\n            [setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)],\r\n            this.client.action<AjaxResponse<Volt.IRestrictionAPIResponse>>(payload).pipe(\r\n              mergeMap((response) => FilterRestrictionObservable(response, [\r\n                historyGo(response.data.redirectURLKey)\r\n              ]))\r\n            )\r\n          )\r\n        ),\r\n        catchError(Models.ErrorHandlerObservable(checkRestrictions))\r\n      );\r\n  }\r\n\r\n  /**\r\n     * Navigates user to an appropriate page\r\n     * in the flow. Also updates flowType, based on\r\n     * the redirection type\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get historyGoEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === historyGo.toString()),\r\n        filter(({ payload }: Action<string>) => (typeof payload === \"string\")),\r\n        mergeMap(({ payload }: Action<string>) => {\r\n          let { flowType } = (state$ as any).value;\r\n          let destination = payload;\r\n          let destinationRoute = \"\";\r\n          switch (payload) {\r\n            case \"APPOINTMENT\":\r\n            case EWidgetRoute.APPOINTMENT:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.APPOINTMENT);\r\n              destinationRoute = EWidgetRoute.APPOINTMENT;\r\n              break;\r\n            case \"INTERNET_REVIEW\":\r\n            case \"TV_REVIEW\":\r\n            case \"BUNDLE_REVIEW\":\r\n            case \"REVIEW\":\r\n            case EWidgetRoute.REVIEW:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.REVIEW);\r\n              destinationRoute = EWidgetRoute.REVIEW;\r\n              break;\r\n            case \"INTERNET_CONFIRMATION\":\r\n            case \"TV_CONFIRMATION\":\r\n            case \"BUNDLE_CONFIRMATION\":\r\n            case \"CONFIRMATION\":\r\n            case EWidgetRoute.CONFIRMATION:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.CONFIRMATION);\r\n              destinationRoute = EWidgetRoute.CONFIRMATION;\r\n              break;\r\n            case \"ADD_TV_REVIEW\":\r\n              destination = Utils.constructPageRoute(EWidgetRoute.REVIEW, EFlowType.ADDTV);\r\n              destinationRoute = EWidgetRoute.REVIEW;\r\n              break;\r\n            case \"INTERNET_CP\":\r\n            case EWidgetRoute.INTERNET:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.INTERNET);\r\n              destinationRoute = EWidgetRoute.INTERNET;\r\n              break;\r\n            case \"TV_CP\":\r\n            case EWidgetRoute.TV:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV);\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case \"ADD_TV\":\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV, EFlowType.ADDTV);\r\n              destinationRoute = EWidgetRoute.TV;\r\n              flowType = EFlowType.ADDTV;\r\n              break;\r\n            case \"BUNDLE_TV\":\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV, EFlowType.BUNDLE);\r\n              destinationRoute = EWidgetRoute.TV;\r\n              flowType = EFlowType.BUNDLE;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.BASE_PROGRAMMING:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Packages;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.ADD_ON:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Addons;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.ALACARTE:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Alacarte;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.MOVIE:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_MoviesSeries;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.INTERNATIONAL:\r\n            case Volt.EDIsplayGroupKey.INTERNATIONAL_COMBOS:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_InternationalCombos;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.INTERNATIONAL_ALACARTE:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_InternationalAlacarte;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case Volt.EDIsplayGroupKey.TV_BROWSE_ALL:\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Browse;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n            case \"TV_SEARCH\":\r\n              destination = Utils.constructPageRoute(EWidgetRoute.TV) + EWidgetRoute.TV_Search;\r\n              destinationRoute = EWidgetRoute.TV;\r\n              break;\r\n\r\n            case \"INTERNET_OVERVIEW\":\r\n            case \"TV_OVERVIEW\":\r\n              return [\r\n                applicationExit()\r\n              ];\r\n            default:\r\n              // No specific handling for unknown navigation payloads\r\n              break;\r\n          }\r\n          if (Utils.getPageRoute() === EWidgetRoute.TV && destinationRoute === EWidgetRoute.TV) {\r\n            const history = useTVHistory();\r\n            window.requestAnimationFrame(() => history.push(destination.replace(/\\/Ordering|\\/Changepackage|\\/TV|\\/Add\\b|\\/Bundle/gi, \"\")));\r\n            return [\r\n              showHideLoader(null)\r\n            ];\r\n          } else if (Utils.getPageRoute() === EWidgetRoute.INTERNET && destinationRoute === EWidgetRoute.INTERNET) {\r\n            return [\r\n              showHideLoader(null)\r\n            ];\r\n          } else {\r\n            const history = useHistory();\r\n            window.requestAnimationFrame(() => history.push(destination));\r\n            return ([\r\n              setFlowType(flowType)\r\n            ]);\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n     * Allows the flow sequence to progress forth.\r\n     * This step is dependent on the nextAction property\r\n     * returned by the API.\r\n     * If nextAction contains a valid redirectURLKey, user\r\n     * is redirected to that redirectURLKey\r\n     * If nextAction contains a vlid href,\r\n     * checkRestrictions epic is triggered to verify we can go forth\r\n     * otherwise system tries to determine where to go.\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get historyForwardEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => \r\n          action.type === historyForward.toString() || \r\n          action.type === continueFlow.toString()\r\n        ),\r\n        mergeMap(() => {\r\n          const history = useHistory();\r\n          const state: any = state$.value;\r\n          const { routes } = state;\r\n          const nextAction = ValueOf<Volt.IHypermediaAction>(state, \"summary.nextAction\", {});\r\n          switch (true) {\r\n            case Boolean(nextAction.href):\r\n              return [\r\n                checkRestrictions(nextAction)\r\n              ];\r\n            case Boolean(nextAction.redirectURLKey):\r\n              return [\r\n                historyGo(nextAction.redirectURLKey)\r\n              ];\r\n            default:\r\n              let route = history.location.pathname;\r\n              let index = routes.findIndex((r: string) => route.indexOf(r) > -1);\r\n              // We are at the last step\r\n              if (index === routes.length - 1) {\r\n                return [];\r\n              }\r\n              // Traverse back\r\n              index += 1;\r\n              route = routes[index];\r\n              history.push(route);\r\n              return [];\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Handles the onContinue action from preview modal\r\n   * This intelligently navigates to the appropriate next page based on context\r\n   * @readonly\r\n   * @private\r\n   * @type {NavigationEpic}\r\n   * @memberof NavigationEpics\r\n   */\r\n  private get onContinueEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === onContinue.toString()),\r\n        mergeMap(() => {\r\n          const history = useHistory();\r\n          const state: any = state$.value;\r\n          const { routes } = state;\r\n          const nextAction = ValueOf<Volt.IHypermediaAction>(state, \"summary.nextAction\", {});\r\n\r\n          // If there's a nextAction with redirectURLKey, use it\r\n          if (nextAction.redirectURLKey) {\r\n            return of(\r\n              historyGo(nextAction.redirectURLKey)\r\n            );\r\n          }\r\n\r\n          // If there's a nextAction with href, check restrictions\r\n          if (nextAction.href) {\r\n            return of(\r\n              checkRestrictions(nextAction)\r\n            );\r\n          }\r\n\r\n          // Determine next route based on current location and routes array\r\n          const route = history.location.pathname;\r\n          const index = routes.findIndex((r: string) => route.indexOf(r) > -1);\r\n\r\n          // If we found the current route in the routes array, go to next\r\n          if (index >= 0 && index < routes.length - 1) {\r\n            const nextRoute = routes[index + 1];\r\n            history.push(nextRoute);\r\n            return of();\r\n          }\r\n\r\n          // Fallback: determine destination based on current page\r\n          const currentPage = Utils.getPageRoute();\r\n          if (currentPage === EWidgetRoute.REVIEW) {\r\n            // From review page, go to confirmation\r\n            return of(\r\n              historyGo(EWidgetRoute.CONFIRMATION)\r\n            );\r\n          } else {\r\n            // Default: go to review page\r\n            return of(\r\n              historyGo(EWidgetRoute.REVIEW)\r\n            );\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n     * Sends user back one step\r\n     * UI driven\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get historyBackEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === historyBack.toString()),\r\n        mergeMap(({ payload }: Action<any>) => {\r\n          const history = useHistory();\r\n          const state: any = state$.value;\r\n          const { routes } = state;\r\n          const backAction = ValueOf<Volt.IHypermediaAction>(state, \"summary.backAction\", {});\r\n          switch (true) {\r\n            case Boolean(backAction.href):\r\n              return [\r\n                checkRestrictions(backAction)\r\n              ];\r\n            case Boolean(backAction.redirectURLKey):\r\n              if (backAction.redirectURLKey === \"INTERNET_OVERVIEW\" ||\r\n                  backAction.redirectURLKey === \"TV_OVERVIEW\") {\r\n                return [\r\n                  showHideLoader(false),\r\n                  openLightbox({ lightboxId: ExitModalId, data: { relativeId: payload } })\r\n                ];\r\n              } else {\r\n                return [\r\n                  historyGo(backAction.redirectURLKey)\r\n                ];\r\n              }\r\n            default:\r\n              let route = getCurrentRoute();\r\n              let index = routes.findIndex((r: string) => route === r);\r\n              // We are at the step 1 already\r\n              if (index === 0) {\r\n                return [\r\n                  showHideLoader(false),\r\n                  openLightbox({ lightboxId: ExitModalId, data: { relativeId: payload } })\r\n                ];\r\n              }\r\n              // Traverse back\r\n              index -= 1;\r\n              if (route) {\r\n                route = routes[index];\r\n                if (route.indexOf(\"Appointment\") > -1 && !sessionStorage.getItem(\"omf:hasAppointmentRoute\")) {\r\n                  // Do not show appointment route\r\n                  index -= 1;\r\n                  route = routes[index];\r\n                }\r\n                history.push(route);\r\n              }\r\n              return [\r\n              ];\r\n          }\r\n        }));\r\n  }\r\n\r\n  /**\r\n     * Triggers when user attempts to exit the app\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get applicationExitEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === applicationExit.toString()),\r\n        mergeMap(() => {\r\n          sessionStorage.clear();\r\n          window.location = ValueOf(this.config, \"linkURL.exitURL\", \"\");\r\n          return [\r\n            showHideLoader(true)\r\n          ];\r\n        }));\r\n  }\r\n\r\n  private get applicationResetEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === applicationReset.toString()),\r\n        mergeMap(() => {\r\n          const state = state$.value;\r\n          const nextAction = ValueOf<Volt.IHypermediaAction>(state, \"summary.resetAction\", {});\r\n          return concat(\r\n            [showHideLoader(true)],\r\n            this.client.post(nextAction.href, nextAction.messageBody).pipe(\r\n              mergeMap((resp: any) => [\r\n                closeLightbox(\"APPLICATION_RESET\"),\r\n                window.location.reload()\r\n              ])\r\n            )\r\n          );\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n     * Triggers whenever user attempts to logout\r\n     * @readonly\r\n     * @private\r\n     * @type {NavigationEpic}\r\n     * @memberof NavigationEpics\r\n     */\r\n  private get applicationLogoutEpic(): NavigationEpic {\r\n    return (action$: any, state$: StateObservable<Store>) =>\r\n      action$.pipe(\r\n        filter((action: Action<any>) => action.type === applicationLogout.toString()),\r\n        mergeMap(() => {\r\n          sessionStorage.clear();\r\n          window.location = ValueOf(this.config, \"linkURL.logoutURL\", \"\");\r\n          return [\r\n            showHideLoader(true)\r\n          ];\r\n        }));\r\n  }\r\n}\r\n\r\ntype NavigationEpic = Epic<Action<any>, any, Store>;\r\n", "import { Injectable } from \"bwtk\";\r\nimport { Epic, combineEpics } from \"redux-observable\";\r\nimport { EWidgetStatus, Actions, Utils } from \"omf-changepackage-components\";\r\nimport { NavigationEpics } from \"./Epics/Navigation\";\r\nimport { setFlowType } from \"./Actions\";\r\nimport { filter, mergeMap } from \"rxjs\";\r\n// import { OmnitureEpics } from \"./Epics/Omniture\";\r\n\r\nconst {\r\n  setWidgetStatus,\r\n  // refreshTotals\r\n} = Actions;\r\n\r\n// const { concat } = ActionsObservable;\r\n\r\n@Injectable\r\nexport class Epics {\r\n  constructor(\r\n    public navigationEpics: NavigationEpics,\r\n    // public omnitureEpic: OmnitureEpics\r\n  ) {}\r\n\r\n  combineEpics() {\r\n    return combineEpics(\r\n      this.onWidgetStatusEpic,\r\n    );\r\n  }\r\n\r\n  private get onWidgetStatusEpic(): GeneralEpic {\r\n    return (action$: any) =>\r\n      action$.pipe(\r\n        filter((action: any) => action.type === setWidgetStatus.toString()),\r\n        filter(({ payload }: ReduxActions.Action<EWidgetStatus>) => payload === EWidgetStatus.INIT),\r\n        mergeMap(() => [\r\n          setFlowType(Utils.getFlowType()),\r\n          setWidgetStatus(EWidgetStatus.RENDERED)\r\n        ])\r\n      );\r\n  }\r\n\r\n}\r\n\r\ntype GeneralEpic = Epic<ReduxActions.Action<any>, any, any>;\r\n", "import { combineReducers } from \"redux\";\r\nimport { Action, handleActions } from \"redux-actions\";\r\nimport { combineEpics } from \"redux-observable\";\r\nimport { Actions, Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics, EFlowType } from \"omf-changepackage-components\";\r\n\r\nimport { Store as BwtkStore, Injectable, CommonFeatures } from \"bwtk\";\r\n\r\nimport * as actions from \"./Actions\";\r\n\r\nimport { IStoreState, IWidgetProps, ISummary } from \"../models\";\r\nimport { Epics } from \"./Epics\";\r\nimport { Localization } from \"../Localization\";\r\nimport { Routes } from \"../utils/History\";\r\nimport { Client } from \"../Client\";\r\n\r\nconst { BaseStore, actionsToComputedPropertyName } = CommonFeatures;\r\n\r\nconst {\r\n  setWidgetProps\r\n} = actionsToComputedPropertyName(Actions);\r\nconst {\r\n  setFlowType,\r\n  setSummaryTotals\r\n} = actionsToComputedPropertyName(actions);\r\n\r\n@Injectable\r\nexport class Store extends BaseStore<IStoreState> {\r\n  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {\r\n    super(store);\r\n  }\r\n\r\n  get reducer() {\r\n    return combineReducers({\r\n      // =========== Widget lifecycle methods =============\r\n      ...Reducers.WidgetBaseLifecycle(this.localization) as any,\r\n      ...Reducers.WidgetLightboxes(),\r\n      ...Reducers.WidgetRestrictions(),\r\n      // =========== Widget data ===============\r\n      // Sample reducer that sets all widget props on to the store\r\n      defaultRoute: handleActions<any | string>({\r\n        [setWidgetProps]: (state, { payload }: Action<IWidgetProps>) => (payload && payload.defaultRoute) || state,\r\n      }, \"/\"),\r\n      flowType: handleActions<any | string>({\r\n        [setWidgetProps]: (state, { payload }: Action<IWidgetProps>) => (payload && payload.flowType) || state,\r\n        [setFlowType]: (state, { payload }: Action<EFlowType>) => (payload && payload) || state,\r\n      }, \"\"),\r\n      routes: handleActions<Array<string>>({\r\n        [setWidgetProps]: (state, { payload }: Action<any>) => (payload && payload.flowType && Routes[payload.flowType as EFlowType]) || state,\r\n        [setFlowType]: (state, { payload }: Action<any>) => (payload && Routes[payload as EFlowType]) || state,\r\n      }, []),\r\n      summary: handleActions<any | string>({\r\n        [setSummaryTotals]: (state, { payload }: Action<ISummary>) => (payload && payload) || state,\r\n      }, {}),\r\n    }) as any;\r\n  }\r\n\r\n  /**\r\n   * Middlewares are collected bottom-to-top\r\n   * so, the bottom-most epic will receive the\r\n   * action first, while the top-most -- last\r\n   * @readonly\r\n   * @memberof Store\r\n   */\r\n  get middlewares(): any {\r\n    return combineEpics(this.epics.navigationEpics.combineEpics(), this.epics.combineEpics(), new ModalEpics().combineEpics(), \r\n      new RestricitonsEpics(this.client, \"NAVIGATION_RESTRICTION_MODAL\").combineEpics(), new LifecycleEpics().combineEpics());\r\n  }\r\n}\r\n", "import { CommonFeatures } from \"bwtk\";\r\nimport { Actions, Volt } from \"omf-changepackage-components\";\r\nimport { Action } from \"redux-actions\";\r\nimport { setSummaryTotals, Store } from \"./store\";\r\nimport { setTVHistoryProvider, setAppointmentVisited } from \"./utils/History\";\r\n\r\nconst { BasePipe } = CommonFeatures;\r\n\r\n/**\r\n * rxjs pipe provider\r\n * this fascilitates the direct connection\r\n * between widgets through rxjs Observable\r\n * @export\r\n * @class Pipe\r\n * @extends {BasePipe}\r\n */\r\nexport class Pipe extends BasePipe {\r\n  static Subscriptions(store: Store) {\r\n    return {\r\n      [Actions.historyGo.toString()]: ({ payload, meta }: any) => {\r\n        store.dispatch(Actions.historyGo(payload));\r\n      },\r\n      [Actions.historyBack.toString()]: () => {\r\n        store.dispatch(Actions.historyBack());\r\n      },\r\n      [Actions.historyForward.toString()]: () => {\r\n        store.dispatch(Actions.historyForward());\r\n      },\r\n      [Actions.onContinue.toString()]: () => {\r\n        store.dispatch(Actions.onContinue());\r\n      },\r\n      [Actions.acceptRestriction.toString()]: ({ payload }: any) => {\r\n        // Handle restriction acceptance by navigating to appropriate page\r\n        if (payload && payload.redirectURLKey) {\r\n          store.dispatch(Actions.historyGo(payload.redirectURLKey));\r\n        } else {\r\n          // Check if we're coming from a summary modal (preview flow)\r\n          const previewModal = document.querySelector('#PREVIEW_MODAL');\r\n          const restrictionModal = document.querySelector('#NAVIGATION_RESTRICTION_MODAL');\r\n\r\n          if ((previewModal && previewModal.classList.contains('show')) ||\r\n              (restrictionModal && restrictionModal.classList.contains('show'))) {\r\n            // Coming from summary/preview flow - close modals first\r\n            store.dispatch(Actions.closeLightbox(\"PREVIEW_MODAL\"));\r\n            store.dispatch(Actions.closeLightbox(\"NAVIGATION_RESTRICTION_MODAL\"));\r\n            // Navigate directly to review page (same as other working flows)\r\n            store.dispatch(Actions.historyGo(\"REVIEW\"));\r\n          } else {\r\n            // Default: navigate to review page for most restriction cases\r\n            store.dispatch(Actions.historyGo(\"REVIEW\"));\r\n          }\r\n        }\r\n      },\r\n      [Actions.applicationExit.toString()]: () => {\r\n        store.dispatch(Actions.applicationExit());\r\n      },\r\n      [Actions.applicationLogout.toString()]: () => {\r\n        store.dispatch(Actions.applicationLogout());\r\n      },\r\n      [Actions.refreshTotals.toString()]: () => {\r\n        store.dispatch(Actions.refreshTotals());\r\n      },\r\n      [Actions.setProductConfigurationTotal.toString()]: ({\r\n        payload\r\n      }: Action<Volt.IProductConfigurationTotal>) => {\r\n        store.dispatch(setSummaryTotals(payload));\r\n      },\r\n      [Actions.closeLightbox.toString()]: ({ payload }: any) => {\r\n        store.dispatch(Actions.closeLightbox(payload));\r\n      },\r\n      [Actions.setHistoryProvider.toString()]: ({ payload }: any) => {\r\n        setTVHistoryProvider(payload);\r\n      },\r\n      [Actions.setAppointmentVisited.toString()]: () => {\r\n        setAppointmentVisited();\r\n      },\r\n    };\r\n  }\r\n  /**\r\n     *Creates a static instance of Pipe.\r\n     * @param {*} arg\r\n     * @memberof Pipe\r\n     */\r\n  static instance: Pipe;\r\n  constructor(arg: any) {\r\n    super(arg);\r\n    Pipe.instance = this;\r\n  }\r\n}\r\n", "import { ParamsProvider, ViewWidget, Widget } from \"bwtk\";\r\nimport { Actions, ContextProvider, EWidgetStatus } from \"omf-changepackage-components\";\r\nimport * as React from \"react\";\r\nimport { Provider as StoreProvider } from \"react-redux\";\r\nimport { App } from \"./App\";\r\nimport { Config } from \"./Config\";\r\nimport { IWidgetProps } from \"./models\";\r\nimport { Pipe } from \"./Pipe\";\r\nimport { Store } from \"./store\";\r\nimport { Root } from \"react-dom/client\";\r\n\r\nconst {\r\n  setWidgetProps,\r\n  setWidgetStatus\r\n} = Actions;\r\n\r\n@Widget({ namespace: \"Ordering\" })\r\nexport default class WidgetContainer extends ViewWidget {\r\n  constructor(private store: Store, private params: ParamsProvider<IWidgetProps, any>, private config: Config, private pipe: Pipe) {\r\n    super();\r\n  }\r\n\r\n  /**\r\n   * Initialize widget flow\r\n   * please do not place any startup login in here\r\n   * all logic should reside in Epics.onWidgetStatusEpic\r\n   * @memberof WidgetContainer\r\n   */\r\n  init() {\r\n    this.pipe.subscribe(Pipe.Subscriptions(this.store));\r\n    this.store.dispatch(setWidgetProps(this.config));\r\n    this.store.dispatch(setWidgetProps(this.params.props));\r\n    this.store.dispatch(setWidgetStatus(EWidgetStatus.INIT));\r\n  }\r\n\r\n  /**\r\n   * Deinitialize widget flow\r\n   * Destroy all listeneres and connections\r\n   * @memberof WidgetContainer\r\n   */\r\n  destroy() {\r\n    this.pipe.unsubscribe();\r\n    this.store.destroy();\r\n  }\r\n\r\n  /**\r\n   * Render widget\r\n   * Set all contextual providers:\r\n   * * ContextProvider: top-most wrapper used to propagate all *immutable* state params\r\n   * * StoreProvider: redux store wrapper used to propagate all *mutable* state params\r\n   * @param {Element} root\r\n   * @memberof WidgetContainer\r\n   */\r\n  render(root: Root) {\r\n    const { store } = this;\r\n    const path = window.location.pathname.split(\"/\");\r\n    root.render(\r\n      <ContextProvider value={{ config: this.config, mode: `/${path[path.length - 1]}` } as any}>\r\n        <StoreProvider {...{ store }}><App /></StoreProvider>\r\n      </ContextProvider>\r\n    );\r\n  }\r\n}\r\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__3__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__102__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__418__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__419__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__442__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__446__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__541__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__634__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__750__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__769__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__999__;", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { Volt, ValueOf, Omniture, EFlowType } from \"omf-changepackage-components\";\r\nimport { ISummary, IAPIResponse } from \"../models\";\r\n\r\nexport function summaryTransformerFn(data: ISummary | IAPIResponse): ISummary {\r\n  const priceOvertime = ValueOf(data, \"priceOvertime\", []);\r\n  const summary: ISummary = {\r\n    ...data,\r\n    Internet: priceOvertime.find((price: Volt.ILineOfBusiness) => price.flowType === \"Internet\"),\r\n    TV: priceOvertime.find((price: Volt.ILineOfBusiness) => price.flowType === \"TV\")\r\n  } as ISummary;\r\n  return summary;\r\n}\r\n\r\nexport function setFlowTypeFn(type: string): string {\r\n  // Update flow type in session storage:\r\n  sessionStorage.setItem(\"omf:Flowtype\", type);\r\n  switch (type) {\r\n    case EFlowType.INTERNET:\r\n      Omniture.useOmniture().updateContext({\r\n        s_oSS2: \"Internet\"\r\n      });\r\n      break;\r\n    case EFlowType.TV:\r\n      Omniture.useOmniture().updateContext({\r\n        s_oSS2: \"Change package\"\r\n      });\r\n      break;\r\n    case EFlowType.ADDTV:\r\n      Omniture.useOmniture().updateContext({\r\n        s_oSS2: \"Add TV\"\r\n      });\r\n      break;\r\n    case EFlowType.BUNDLE:\r\n      Omniture.useOmniture().updateContext({\r\n        s_oSS2: \"Bundle\"\r\n      });\r\n      break;\r\n    default:\r\n      // No specific Omniture context update for unknown flow types\r\n      break;\r\n  }\r\n  return type;\r\n}\r\n"], "names": ["root", "factory", "a", "i", "exports", "module", "require", "define", "amd", "self", "__WEBPACK_EXTERNAL_MODULE__102__", "__WEBPACK_EXTERNAL_MODULE__446__", "__WEBPACK_EXTERNAL_MODULE__442__", "__WEBPACK_EXTERNAL_MODULE__999__", "__WEBPACK_EXTERNAL_MODULE__634__", "__WEBPACK_EXTERNAL_MODULE__419__", "__WEBPACK_EXTERNAL_MODULE__3__", "__WEBPACK_EXTERNAL_MODULE__750__", "__WEBPACK_EXTERNAL_MODULE__541__", "__WEBPACK_EXTERNAL_MODULE__769__", "__WEBPACK_EXTERNAL_MODULE__418__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_module_cache__", "undefined", "__webpack_modules__", "__extends", "d", "b", "__", "this", "constructor", "TypeError", "String", "extendStatics", "prototype", "Object", "create", "__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "defineProperty", "__metadata", "metadataKey", "metadataValue", "metadata", "useHistory", "_history", "__assign", "ownKeys", "_tvhistory", "_isAppointmentVisited", "Routes", "BaseLocalization", "Modal", "ModalId", "Component", "ApplicationLogoutLightbox", "Footer", "ApplicationExitLightbox", "Header", "ApplicationResetLightbox", "PreviewLightbox", "Appointment", "Confirmation", "Internet", "Review", "TV", "Visible", "<PERSON><PERSON><PERSON><PERSON>", "TVSummaryPortal", "DisabledContinue", "PriceBlock", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RestrictionModal", "errorOccured", "widgetRenderComplete", "AppRouter", "Application", "ApplicationRoot", "App", "BaseConfig", "configProperty", "setFlowType", "setSummaryTotals", "checkRestrictions", "showHideLoader", "continueFlow", "historyBack", "historyForward", "historyGo", "openLightbox", "applicationExit", "applicationLogout", "setWidgetStatus", "applicationReset", "closeLightbox", "onContinue", "BaseStore", "actionsToComputedPropertyName", "setWidgetProps", "BasePipe", "definition", "o", "enumerable", "get", "obj", "prop", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "setPrototypeOf", "__proto__", "Array", "p", "assign", "t", "s", "n", "apply", "getOwnPropertyNames", "k", "ar", "SuppressedError", "EFlowType", "INTERNET", "ADDTV", "BUNDLE", "CommonFeatures", "Localization", "getLocalizedString", "id", "Instance", "ServiceLocator", "instance", "getService", "CommonServices", "EWidgetName", "NAVIGATION", "locale", "Injectable", "Components", "onContinueClick", "modalId", "onShown", "Omniture", "useOmniture", "trackFragment", "s_oAPT", "actionId", "s_oPRM", "s_oLBC", "title", "FormattedMessage", "className", "FormattedHTMLMessage", "onClick", "connect", "dispatch", "trackAction", "s_oBTN", "ref", "Actions", "onLogoutClick", "Context", "config", "linkURL", "isOneTrustDPEnabled", "href", "privacyURL", "legalURL", "role", "feedbackURL", "type", "tabIndex", "src", "entrustIMGURL", "alt", "lightboxId", "data", "relativeId", "txt", "flowType", "pathname", "onBackClick", "onExitClick", "isConfirmationStep", "Utils", "getPageRoute", "EWidgetRoute", "CONFIRMATION", "path", "indexOf", "getPageName", "replace", "toUpperCase", "REVIEW", "<PERSON><PERSON><PERSON><PERSON>", "window", "location", "toLowerCase", "e", "exitURL", "when", "dangerouslySetInnerHTML", "__html", "routes", "preventDefault", "stopPropagation", "EModals", "PREVIEWMODAL", "isOpen", "summaryAction", "isContinueEnabled", "dismissLightbox", "on<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "widget", "PREVIEW", "mode", "EReviewMode", "Summary", "summaryAPI", "ValueOf", "disabled", "lightboxData", "summary", "lightbox", "broadcastUpdate", "setlightboxData", "sessionStorage", "setItem", "document", "APPOINTMENT", "monthly", "prefixClassName", "fractionClassName", "page", "messageid", "m", "iterator", "next", "done", "push", "error", "hover", "toggleHover", "onKeyUp", "onMouseOver", "onMouseOut", "Boolean", "style", "position", "width", "top", "left", "transform", "label", "price", "regularPrice", "values", "onSummaryClick", "onCancelClick", "handleNav", "isTVStep", "isVisible", "tvSummaryContainer", "getElementById", "opacity", "placeholder", "onCategoriesClick", "toggleTVCategoriesTray", "props", "useLocation", "environmentVariables", "WidgetContext", "Route", "component", "history", "Switch", "language", "Redirect", "to", "defaultRoute", "componentDidCatch", "err", "onErrorEncountered", "componentDidMount", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basename", "base", "Config", "createAction", "updateContext", "s_oSS2", "priceOvertime", "find", "ajaxClient", "AjaxServices", "Client", "BaseClient", "client", "combineEpics", "historyGoEpic", "historyForwardEpic", "historyBackEpic", "onContinueEpic", "applicationExitEpic", "applicationLogoutEpic", "checkRestrictionsEpic", "applicationResetEpic", "action$", "state$", "pipe", "filter", "action", "toString", "payload", "mergeMap", "concat", "widgetState", "EWidgetStatus", "UPDATING", "response", "FilterRestrictionObservable", "redirectURLKey", "catchError", "Models", "ErrorHandlerObservable", "destination", "destinationRoute", "constructPageRoute", "Volt", "EDIsplayGroupKey", "BASE_PROGRAMMING", "TV_Packages", "ADD_ON", "TV_Addons", "ALACARTE", "TV_Alacarte", "MOVIE", "TV_MoviesSeries", "INTERNATIONAL", "INTERNATIONAL_COMBOS", "TV_InternationalCombos", "INTERNATIONAL_ALACARTE", "TV_InternationalAlacarte", "TV_BROWSE_ALL", "TV_Browse", "TV_Search", "requestAnimationFrame", "index", "state", "nextAction", "findIndex", "route", "nextRoute", "of", "backAction", "getItem", "clear", "post", "messageBody", "resp", "reload", "NavigationEpics", "navigationEpics", "onWidgetStatusEpic", "INIT", "getFlowType", "RENDERED", "Epics", "store", "epics", "localization", "combineReducers", "Reducers", "WidgetBaseLifecycle", "WidgetLightboxes", "WidgetRestrictions", "handleActions", "ModalEpics", "RestricitonsEpics", "LifecycleEpics", "Store", "arg", "<PERSON><PERSON>", "Subscriptions", "acceptRestriction", "previewModal", "restrictionModal", "querySelector", "classList", "contains", "refreshTotals", "setProductConfigurationTotal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setAppointmentVisited", "params", "init", "subscribe", "destroy", "unsubscribe", "split", "ContextProvider", "Widget", "namespace", "ParamsProvider", "WidgetContainer", "ViewWidget"], "sourceRoot": ""}