import { Volt } from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import { useSelector } from "react-redux";
import { getLineOfBusiness, selectors } from "../../utils";
import AdditionalCharge from "./AdditionalCharge";
import NewInternet from "./internet/New";
import { Spacer } from "./Spacer";
import TotalCharge from "./TotalCharge";
import NewTV from "./tv/New";

const NewItems: React.FunctionComponent<{ isConfirmationStep: boolean }> = ({
  isConfirmationStep
}) => {
  const totalCharge: Volt.IPriceDetail = useSelector(selectors.totalCharge("newTotal")()),
    additionalCharge: Volt.IPriceDetail = useSelector(selectors.additionalCharge("New")()),
    TVOffering: Array<Volt.IProductOffering> = useSelector(selectors.productOfferings("New", "TV")()) || [],
    InternetOffering: Array<Volt.IProductOffering> = useSelector(selectors.productOfferings("New", "Internet")()) || [],
    renderTV: boolean = TVOffering.length > 0,
    renderInternet: boolean = InternetOffering.length > 0,
    lineOfBusiness = getLineOfBusiness({ renderTV, renderInternet });
  // let addTV = Utils.getFlowType();

  return renderInternet || renderTV ? (
    <div className="review-div-section">
      <div className="flexCol fullWidth">
        <div className="review-div-section">
          <div className="review-panel-body  border-radius-top fullHeight pad-30-left pad-40-right padding-25-xs accss-focus-outline-override-white-bg">
            <div>
              <div className="spacer30 d-none d-sm-block" aria-hidden="true" />
              <div className="spacer25 d-block d-sm-none" aria-hidden="true" />
              <h2 className="txtBlack txtSize22 noMargin differentTextureforHandset virginUltraReg txtUppercase">
                <FormattedMessage id={`NEW_${lineOfBusiness}`} />
              </h2>
              <Spacer />
            </div>
            {renderInternet && <NewInternet productOfferings={InternetOffering} />}
            {renderTV && <NewTV productOfferings={TVOffering} />}
          </div>
        </div>
        <AdditionalCharge {...{ additionalCharge }} />
        <TotalCharge {...{ totalCharge, isNew: true, isTV: renderTV }} />
      </div>
    </div>
  ) : null;
};

export default NewItems;
