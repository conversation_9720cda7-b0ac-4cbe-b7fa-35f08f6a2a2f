@import "mixins";
/*Table layout*/

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    vertical-align: middle
}

.table>thead>tr>th {
    height: 75px;
    padding: 2px 20px 0;
    border-bottom: none;
    background-color: #003778;
    color: #fff;
    font-weight: normal;
    font-size: 18px;
    position: relative;
    /*Possible solution to overflow problem*/
    overflow: hidden;
    text-overflow: ellipsis;
}

.table>tbody>tr>td {
    height: 60px;
    padding: 0 20px;
}


/*media */

@media #{$media-tablet} {
    /*First and last column have special padding on table*/
    .table>thead>tr>th:first-child,
    .table>tbody>tr>td:first-child,
    .table>thead>tr>th:last-child,
    .table>tbody>tr>td:last-child {
        padding: 0 15px
    }
    .table>thead>tr>th {
        height: 55px;
        font-size: 16px
    }
    .table>tbody>tr>td {
        height: 60px
    }
}

@media #{$media-mobile} {
    .table>thead>tr>th,
    .table>tbody>tr>td {
        padding: 0 10px;
        font-size: 12px
    }
    /*First and last column have special padding on mobile*/
    .table>thead>tr>th:first-child,
    .table>tbody>tr>td:first-child,
    .table>thead>tr>th:last-child,
    .table>tbody>tr>td:last-child {
        padding-right: 10px
    }
    .table>thead>tr>th,
    .table>tbody>tr>td {
        height: 55px
    }
}


// /*Column sorting decorator*/
// commenting out since it causes issue in IOS devices... Element's become unclickable
// .table.table-sortable>thead>tr>th {
//     /* Prevent headers from being selected on click */
//     -webkit-user-select: none;
//     /* Chrome/Safari/Opera */
//     -khtml-user-select: none;
//     /* Konqueror */
//     -moz-user-select: none;
//     /* Firefox */
//     -ms-user-select: none;
//     /* Internet Explorer/Edge */
//     user-select: none;
//     /* Non-prefixed version, currently
//                                   not supported by any browser */
// }


/*.table.table-sortable>thead>tr>th[role="button"] {padding-right: 50px!important;}*/

.table.table-sortable>thead>tr>th[aria-sort="ascending"],
.table.table-sortable>thead>tr>th[aria-sort="descending"],
.table.table-sortable>thead>tr>th.sortedAsc,
.table.table-sortable>thead>tr>th.sortedDesc {
    background-color: #00549a;
    padding-right: 50px
}

.table.table-sortable>thead>tr>th .carret {
    position: absolute;
    top: 50%;
    right: 20px;
    font-size: 20px;
    margin-top: -12px;
}

.table.table-sortable>thead>tr>th[aria-sort="ascending"]>.carret:after,
.table.table-sortable>thead>tr>th[aria-sort="descending"]>.carret:after,
.table.table-sortable>thead>tr>th.sortedAsc>.carret:after,
.table.table-sortable>thead>tr>th.sortedDesc>.carret:after {
    font-family: 'bell-icon';
}

.table.table-sortable>thead>tr>th[aria-sort="ascending"]>.carret:after,
.table.table-sortable>thead>tr>th.sortedAsc>.carret:after {
    content: "\e91e"
}

.table.table-sortable>thead>tr>th[aria-sort="descending"]>.carret:after,
.table.table-sortable>thead>tr>th.sortedDesc>.carret:after {
    content: "\e91d"
}

@media #{$media-tablet} {
    .table.table-sortable>thead>tr>th .carret {
        right: 15px
    }
    .table.table-sortable>thead>tr>th[aria-sort="ascending"],
    .table.table-sortable>thead>tr>th[aria-sort="descending"],
    .table.table-sortable>thead>tr>th.sortedAsc,
    .table.table-sortable>thead>tr>th.sortedDesc {
        padding-right: 40px
    }
}

@media #{$media-mobile} {
    .table.table-sortable>thead>tr>th[aria-sort="ascending"],
    .table.table-sortable>thead>tr>th[aria-sort="descending"],
    .table.table-sortable>thead>tr>th.sortedAsc,
    .table.table-sortable>thead>tr>th.sortedDesc {
        padding-right: 20px
    }
    .table.table-sortable>thead>tr>th .carret {
        right: 10px;
        margin-top: -20px;
    }
    .table.table-sortable>thead>tr>th[aria-sort="ascending"]>.carret:after,
    .table.table-sortable>thead>tr>th[aria-sort="descending"]>.carret:after,
    .table.table-sortable>thead>tr>th.sortedAsc>.carret:after,
    .table.table-sortable>thead>tr>th.sortedDesc>.carret:after {
        font-family: 'bell-icon2';
        font-size: 4px;
    }
    .table.table-sortable>thead>tr>th[aria-sort="ascending"]>.carret:after,
    .table.table-sortable>thead>tr>th.sortedAsc>.carret:after {
        content: "\e940"
    }
    .table.table-sortable>thead>tr>th[aria-sort="descending"]>.carret:after,
    .table.table-sortable>thead>tr>th.sortedDesc>.carret:after {
        content: "\e93f"
    }
}


/*Alternating row color*/

.table.table-striped>tbody>tr:nth-of-type(odd),
.table>tbody>tr.altRow {
    background-color: #f4f4f4
}