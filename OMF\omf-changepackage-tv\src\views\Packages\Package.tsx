import {
  Components,
  Omniture,
  ValueOf,
  Volt
} from "omf-changepackage-components";
import * as React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { connect } from "react-redux";
import { IStoreState, ITVChannel } from "../../models";
import { toggleSelection } from "../../store";
import Filter from "../Components/Filter";
import Price from "../Components/Price";

const { Visible } = Components;

interface IComponentProps extends Volt.IProductOffering {
  isSingle: boolean;
  isDisabled: boolean;
}

interface IComponentConnectedProps { }

interface IComponentDispatches {
  onActionClick: (action: Volt.IHypermediaAction) => void;
}

export const Component: React.FC<IComponentProps &
  IComponentConnectedProps &
  IComponentDispatches> = ({
  id,
  name,
  shortDescription,
  longDescription,
  regularPrice,
  promotionDetails,
  // displayGroupKey,
  childOfferings,
  offeringAction,
  isSingle,
  isSelectable,
  isSelected,
  isDisabled,
  isCurrent,
  onActionClick  
}) => {
  const [expanded, Toggle] = React.useState(false);
  const intl = useIntl();
  React.useEffect(() => {
    expanded &&
        Omniture.useOmniture().trackAction({
          id: "showChannelsClick",
          s_oAPT: {
            actionId: 648
          },
          s_oEPN: "Show Channel"
        });
  }, [expanded]);
  const haveChildren = ValueOf(childOfferings, "length", 0) > 0;
  return (
    <div
      id={id}
      className={`bell-tv-package bell-tv-base-pack accss-focus-outline-override-white-bg ${
        isDisabled ? "disabled" : ""
      } ${isSingle || isSelected ? "selected" : ""}`}
    >
      <div className="bell-tv-package-main flexRow block-xs bgWhite">
        <div className="relative bell-tv-package-controls flexStatic no-margin-xs">
          <Visible when={isCurrent}>
            <div className="absolute pad-5-top pad-5-bottom pad-30-left pad-30-right bgOrange txtWhite current-flag">
              <FormattedMessage id="Current package" />
            </div>
            <div className="spacer30"></div>
          </Visible>
          <label
            id={`label_${id}`}
            className={`graphical_ctrl ctrl_radioBtn pointer ${
              isDisabled ? "disabled" : ""
            }`}
            onClick={() => !isDisabled && !isSelected && !isSingle && onActionClick(offeringAction)}
          >
            <input
              id={`radio_${id}`}
              type="radio"
              checked={isSingle || isSelected}
              disabled={isDisabled}
            />
            <span className="ctrl_element" />
            <span className="package-name block inlineBlock-xs txtSize18 txtNormal txtBlack">
              {name}
            </span>
            <Price regularPrice={regularPrice} promotionDetails={promotionDetails} />
          </label>
        </div>
        <div className="bell-tv-package-separator flexStatic"></div>
        <div className="bell-tv-package-description relative flexBlock flexWrap">
          <div className="bell-tv-package-channels-detail flexStatic flexBasis100 order1">
            <p className="noMargin txtSize14">
              {/* <span className="aria-visible">Package description: </span> */}
              {longDescription || shortDescription}
            </p>
          </div>
          <div className="spacer30 flexStatic flexBasis100 order2">&nbsp;</div>
          <div className="order4 flex1">
            <div className="spacer10 visible-sm"></div>
            <div
              className="bell-tv-package-icons noMargin flexBlock flexWrap"
              aria-hidden="true"
            >
              {ValueOf<Array<ITVChannel>>(childOfferings, undefined, [])
                .slice(0, 10)
                .map(channel => (
                  <div className="channel-item">
                    <img
                      src={ValueOf(channel, "imagePath", "")}
                      alt={ValueOf(channel, "name", "")}
                    />
                  </div>
                ))}
            </div>
            <div className="spacer15 col-xs-12 order5"></div>
            <Visible when={haveChildren}>
              <div className="col-xs-12 order6 flex-container">
                <button
                  id={`ACCORDION_ICON_${id}`}
                  onClick={() => Toggle(!expanded)}
                  aria-controls="div1-accessible"
                  data-toggle="collapse"
                  className="btn btn-link no-pad txtVirginBlue accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center"
                  aria-expanded={expanded}
                  aria-label={`${intl.formatMessage({id: "Show channels"})} ${intl.formatMessage({id: "FOR_TEXT"})} ${name}`}
                >
                  {/* <span className="sr-only accordion-label" aria-live="polite" aria-atomic="true" aria-hidden="false">collapsed</span> */}
                  <span
                    className={`volt-icon txtSize22 icon-blue icon-${
                      expanded ? "Collapse" : "Expand"
                    }`}
                  >
                    <span
                      className={`volt-icon path1 icon-${
                        expanded ? "Collapse" : "Expand"
                      }`}
                    ></span>
                    <span
                      className={`volt-icon path2 icon-${
                        expanded ? "Collapse" : "Expand"
                      }`}
                    ></span>
                  </span>
                  <span className="txtSize12 sans-serif txtBlue margin-10-left">
                    <FormattedMessage id="Show channels" />
                  </span>
                </button>
              </div>
            </Visible>
          </div>
          <div className="clear"></div>
        </div>
      </div>
      <Visible when={expanded}>
        <div className="bell-tv-package-footer bgGray19 expanded" role="region">
          <div className="spacer1 bgGray" />
          <Filter
            groupName={`radio_${id}`}
            channels={ValueOf(childOfferings, undefined, [])}
            label={<FormattedMessage id="Package channels" values={{ name }} />}
          />
        </div>
      </Visible>
    </div>
  );
};

export default connect<
  IComponentConnectedProps,
  IComponentDispatches,
  IComponentProps
>(
  ({ }: IStoreState) => ({}),
  dispatch => ({
    onActionClick: (action: Volt.IHypermediaAction) =>
      dispatch(toggleSelection(action))
  })
)(Component);
