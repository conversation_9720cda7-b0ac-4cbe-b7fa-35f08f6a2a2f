import * as React from "react";
import { Models } from "omf-changepackage-components";
import { FormattedMessage } from "react-intl";

export interface IFieldsetProps extends Models.IBaseComponentProps {
  legendAdditionalClass?: string;
  legend: string | false;
  accessibleLegend?: boolean;
  required?: boolean;
  additionalClass?: string;
  children?: any;
}

export interface IFieldsetComponent extends React.FC<IFieldsetProps> {
}

const Legend: IFieldsetComponent = ({ legend, required, accessibleLegend, legendAdditionalClass }) => legend ? <legend className={`installation-form-label ${required ? "form-required" : ""} ${accessibleLegend ? "sr-only" : ""} ${legendAdditionalClass}`}>
  <FormattedMessage id={legend} />
</legend> : null;

export const Fieldset: IFieldsetComponent = ({
  className, children, legend, accessibleLegend, legendAdditionalClass, required, additionalClass
}) => <fieldset className={`margin-15-bottom ${className}`}>
  {accessibleLegend ?
    <>
      <Legend legend={legend} required={required} accessibleLegend={accessibleLegend} legendAdditionalClass={legendAdditionalClass} />
      {children}
    </> :
    <div className={`flexBlock flexCol-xs ${additionalClass}`}>
      <Legend legend={legend} required={required} accessibleLegend={accessibleLegend} legendAdditionalClass={legendAdditionalClass} />
      {children}
    </div>}
</fieldset>;

