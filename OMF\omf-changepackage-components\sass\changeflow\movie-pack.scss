@import "mixins";

/* Bell movie pack module
 * 
 * Package is a base view with checkbox
 * button control. It's only used on
 * most TV Change pages
*/
.bell-tv-movie-pack {
  .bell-tv-package-body {
    // min-height: 175px;
    // @media #{$media-tablet} {
    //   min-height: 204px;
    // }
    @media #{$media-mobile} {
      flex-direction: column-reverse;
      // min-height: unset;
      padding: 0;
    }
    .bell-tv-package-left {
      width: 50%;
      @media #{$media-mobile} {
        width: 100%;
        padding: 15px;
        padding-left: 55px;
        padding-right: 20px;
      }
      .bell-tv-package-checkbox {
        margin-left: -30px;
        @media #{$media-tab-mobile} {
          margin-left: -40px;
        }
      }
    }
    .bell-tv-package-right {
      width: 50%;
      margin-top: -15px;
      margin-bottom: -15px;
      margin-right: -15px;
      @media #{$media-mobile} {
        width: 100%;
        margin: 0;
      }
      >img {
        height: 100%;
        width: 100%;
      }
    }
  }
  .bell-tv-package-footer {
    border-top: 1px solid #E1E1E1;
    position: relative;
    overflow: visible;
    &:before,
    &:after {
      position: absolute;
      content: " ";
      top: -15px;
      left: 43px;
      width: 0;
      height: 0;
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
      border-bottom: 15px solid #E1E1E1;
      @media #{$media-tab-mobile} {
        left: 47px;
      }
      @media #{$media-mobile} {
        left: 52px;
      }
    }
    &:after {
      top: -14px;
      border-bottom: 15px solid #f0f0f0;
    }
    &.starter-pack-special {
      background-color: #fff !important;
      &:after, &:before {
        border-bottom-color: #fff;
        left: 70px;
      }

    }
  }
  .selection{
    line-height: 18px;
    padding:5px 0;
  }
  .showChannels{
    bottom: 0;
  }
}

/* -- QCP Overright --*/

.section-bell-tv-qcp {
  .section-bell-tv-prepackaged-channels{
    .bell-tv-package-body {
      padding: 15px;
    }
  }
  .section-bell-tv-additional-channels {
    .bell-tv-movie-pack{
      .bell-tv-package-body {
        padding: 0;
      }
    }
  }
}